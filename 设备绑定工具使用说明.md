# 设备绑定工具类使用说明

`DeviceBindManager` 是一个专门用于管理耳机设备绑定的工具类，它封装了与设备绑定相关的所有API调用，提供统一的设备管理功能。

## 功能概览

1. **设备绑定**：将蓝牙设备信息上传至服务器并绑定至用户账号
2. **设备解绑**：解除用户与设备的绑定关系
3. **获取设备列表**：获取当前用户绑定的所有设备
4. **获取设备详情**：获取单个设备的详细信息
5. **本地设备同步**：将本地已配对的蓝牙设备批量同步到服务器

## 使用方法

### 初始化

在使用设备绑定功能前，需要先获取`DeviceBindManager`实例：

```kotlin
// 在Activity或Fragment中
val deviceBindManager = DeviceBindManager.getInstance(context)
```

### 绑定设备

将当前连接的蓝牙设备绑定至用户账号：

```kotlin
// 基本绑定（不包含位置信息）
viewModelScope.launch {
    val deviceName = "LLYA EarPods Pro" // 替换为实际设备名称
    val macAddress = "00:11:22:33:44:55" // 替换为实际MAC地址
    
    deviceBindManager.bindDevice(deviceName, macAddress, false)
        .collect { response ->
            if (response.status == 0) {
                // 绑定成功
                val deviceId = response.data?.deviceId
                val bindTime = response.data?.bindTime
                Log.d("DeviceBind", "设备绑定成功：$deviceId, 绑定时间：$bindTime")
            } else {
                // 绑定失败
                Log.e("DeviceBind", "设备绑定失败：${response.msg}")
            }
        }
}

// 包含位置信息的绑定（需要位置权限）
viewModelScope.launch {
    deviceBindManager.bindDevice("LLYA EarPods Pro", "00:11:22:33:44:55", true)
        .collect { response ->
            // 处理响应结果
        }
}
```

### 获取设备列表

获取当前用户已绑定的所有设备列表：

```kotlin
viewModelScope.launch {
    deviceBindManager.getDeviceList()
        .collect { response ->
            if (response.status == 0) {
                val devices = response.data?.devices ?: emptyList()
                Log.d("DeviceBind", "获取到${devices.size}个绑定设备")
                
                // 处理设备列表
                devices.forEach { device ->
                    Log.d("DeviceBind", "设备ID: ${device.deviceId}, 名称: ${device.deviceName}")
                }
            } else {
                Log.e("DeviceBind", "获取设备列表失败：${response.msg}")
            }
        }
}
```

### 获取设备详情

获取单个设备的详细信息：

```kotlin
viewModelScope.launch {
    val deviceId = "d987654321" // 替换为实际设备ID
    
    deviceBindManager.getDeviceInfo(deviceId)
        .collect { response ->
            if (response.status == 0 && response.data != null) {
                val device = response.data
                Log.d("DeviceBind", "设备名称: ${device.deviceName}")
                Log.d("DeviceBind", "MAC地址: ${device.macAddress}")
                Log.d("DeviceBind", "电池电量: ${device.battery}%")
                Log.d("DeviceBind", "固件版本: ${device.firmwareVersion}")
            } else {
                Log.e("DeviceBind", "获取设备详情失败：${response.msg}")
            }
        }
}
```

### 解绑设备

解除与特定设备的绑定关系：

```kotlin
viewModelScope.launch {
    val deviceId = "d987654321" // 替换为实际设备ID
    
    deviceBindManager.unbindDevice(deviceId)
        .collect { response ->
            if (response.status == 0) {
                Log.d("DeviceBind", "设备解绑成功")
            } else {
                Log.e("DeviceBind", "设备解绑失败：${response.msg}")
            }
        }
}
```

### 同步本地设备

将系统中已配对的蓝牙设备批量同步到服务器：

```kotlin
viewModelScope.launch {
    // 获取本地已配对设备列表
    val pairedDevices = bluetoothAdapter?.bondedDevices ?: emptySet()
    
    // 转换为设备名称和MAC地址的列表
    val deviceList = pairedDevices.map { 
        Pair(it.name ?: "未知设备", it.address) 
    }
    
    // 同步到服务器
    deviceBindManager.syncLocalDevices(deviceList)
        .collect { response ->
            if (response.status == 0) {
                Log.d("DeviceBind", "本地设备同步成功：${response.msg}")
            } else {
                Log.e("DeviceBind", "本地设备同步部分失败：${response.msg}")
            }
        }
}
```

## 错误处理

所有API调用都内置了错误处理机制，当发生异常时，会返回包含错误信息的`ApiResponse`对象：

```kotlin
viewModelScope.launch {
    deviceBindManager.bindDevice(deviceName, macAddress)
        .collect { response ->
            when (response.status) {
                0 -> {
                    // 成功
                }
                -1 -> {
                    // 客户端异常
                    Log.e("DeviceBind", "客户端异常：${response.msg}")
                }
                else -> {
                    // 服务器返回的其他错误码
                    when (response.status) {
                        1002 -> Log.e("DeviceBind", "设备不存在")
                        1003 -> Log.e("DeviceBind", "设备已被绑定")
                        1006 -> Log.e("DeviceBind", "蓝牙设备连接失败")
                        else -> Log.e("DeviceBind", "未知错误：${response.msg}")
                    }
                }
            }
        }
}
```

## 位置权限说明

如果启用位置信息（绑定设备时`includeLocation`参数设为`true`），需要在应用中申请位置权限：

```kotlin
// 在AndroidManifest.xml中添加权限
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

// 在代码中申请权限
private fun requestLocationPermissions() {
    ActivityCompat.requestPermissions(
        this,
        arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ),
        LOCATION_PERMISSION_REQUEST_CODE
    )
}
```

## 与蓝牙模块配合使用

推荐配合项目中的`BluetoothManager`和`BluetoothViewModel`一起使用：

```kotlin
// 在ViewModel中
fun bindCurrentDevice() {
    viewModelScope.launch {
        // 1. 获取当前连接的蓝牙设备
        val device = bluetoothManager.getCurrentlyConnectedDevice()
        
        if (device != null) {
            // 2. 获取设备名称和MAC地址
            val deviceName = device.name ?: "未知设备"
            val macAddress = device.address
            
            // 3. 调用设备绑定API
            deviceBindManager.bindDevice(deviceName, macAddress, true)
                .collect { response ->
                    if (response.status == 0) {
                        _uiState.update { it.copy(
                            isDeviceBound = true,
                            deviceId = response.data?.deviceId ?: ""
                        )}
                    } else {
                        _uiState.update { it.copy(
                            errorMessage = "设备绑定失败：${response.msg}"
                        )}
                    }
                }
        } else {
            _uiState.update { it.copy(
                errorMessage = "未找到连接的设备"
            )}
        }
    }
}
```

## 注意事项

1. 设备绑定需要用户已登录，确保在调用API前已完成用户登录
2. 设备解绑不会断开蓝牙连接，只会解除服务器端的绑定关系
3. 获取位置信息需要相应的权限，并且用户已授予权限
4. 批量同步本地设备时，如果设备已被其他用户绑定，会返回部分失败的结果 