package com.example.llya.translation

import android.util.Log
import com.microsoft.cognitiveservices.speech.ResultReason
import com.microsoft.cognitiveservices.speech.SpeechConfig
import com.microsoft.cognitiveservices.speech.SpeechSynthesisResult
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import java.util.concurrent.Semaphore

/**
 * 微软文本到语音转换
 * 使用官方Speech SDK实现文本到语音转换
 */
class MicrosoftTextToSpeech(
    config: MicrosoftSpeechConfig
) {
    companion object {
        private const val TAG = "MicrosoftTextToSpeech"
    }

    // 配置
    private var subscriptionKey = config.subscriptionKey
    private var region = config.region

    // 语音合成器
    private var synthesizer: SpeechSynthesizer? = null

    // TTS 状态
    private var isSpeaking = false

    // 语音队列
    private val speakQueue = mutableListOf<SpeakItem>()

    // 当前语音项的信号量
    private val synthesisCompleted = Semaphore(0)

    /**
     * 语音项
     */
    private data class SpeakItem(
        val text: String,
        val languageCode: String,
        val voiceName: String? = null
    )

    /**
     * 更新配置
     */
    fun updateConfig(config: MicrosoftSpeechConfig) {
        this.subscriptionKey = config.subscriptionKey
        this.region = config.region
    }

    /**
     * 文本转语音并播放
     * @param text 要合成的文本
     * @param languageCode 语言代码，如zh-CN
     * @param voiceName 语音人名称，如zh-CN-XiaoxiaoNeural
     */
    fun speak(text: String, languageCode: String, voiceName: String? = null) {
        // 将请求添加到队列
        val item = SpeakItem(text, languageCode, voiceName)
        synchronized(speakQueue) {
            speakQueue.add(item)
            
            // 如果当前没有在播放，则开始播放
            if (!isSpeaking) {
                processQueue()
            }
        }
    }

    /**
     * 处理队列
     */
    private fun processQueue() {
        synchronized(speakQueue) {
            if (speakQueue.isEmpty()) {
                isSpeaking = false
                return
            }
            
            isSpeaking = true
            val item = speakQueue.removeAt(0)
            synthesizeAndPlay(item)
        }
    }

    /**
     * 合成并播放
     */
    private fun synthesizeAndPlay(item: SpeakItem) {
        try {
            // 创建语音配置
            val speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region)
            
            // 设置语音
            val actualVoiceName = item.voiceName ?: getDefaultVoice(item.languageCode)
            speechConfig.speechSynthesisVoiceName = actualVoiceName
            
            // 创建音频配置（使用默认扬声器）
            val audioConfig = AudioConfig.fromDefaultSpeakerOutput()
            
            // 创建语音合成器
            synthesizer = SpeechSynthesizer(speechConfig, audioConfig)
            
            // 注册事件处理程序
            synthesizer?.SynthesisCompleted?.addEventListener { _, _ ->
                Log.d(TAG, "语音合成完成")
                synthesisCompleted.release()
            }
            
            synthesizer?.SynthesisStarted?.addEventListener { _, _ ->
                Log.d(TAG, "语音合成开始")
            }
            
            synthesizer?.Synthesizing?.addEventListener { _, _ ->
                Log.d(TAG, "语音合成中")
            }
            
            synthesizer?.SynthesisCanceled?.addEventListener { _, _ ->
                Log.d(TAG, "语音合成取消")
                synthesisCompleted.release()
            }
            
            // 开始合成
            Log.d(TAG, "开始合成文本: ${item.text}")
            val result = synthesizer?.SpeakText(item.text)
            
            // 检查结果
            if (result?.reason == ResultReason.SynthesizingAudioCompleted) {
                Log.d(TAG, "语音合成成功")
            } else {
                Log.e(TAG, "语音合成失败: ${result?.reason}")
            }
            
            // 等待合成完成
            synthesisCompleted.acquire()
            
            // 关闭合成器
            synthesizer?.close()
            synthesizer = null
            
            // 处理下一个
            processQueue()
            
        } catch (ex: Exception) {
            Log.e(TAG, "语音合成时出错", ex)
            synthesizer?.close()
            synthesizer = null
            
            // 尝试处理下一个
            processQueue()
        }
    }

    /**
     * 获取默认语音
     */
    private fun getDefaultVoice(languageCode: String): String {
        return MicrosoftSpeechConfig.VOICE_MAPPING[languageCode] ?: when {
            languageCode.startsWith("zh") -> "zh-CN-XiaoxiaoNeural"
            languageCode.startsWith("en") -> "en-US-JennyNeural"
            languageCode.startsWith("ja") -> "ja-JP-NanamiNeural"
            languageCode.startsWith("ko") -> "ko-KR-SunHiNeural"
            languageCode.startsWith("fr") -> "fr-FR-DeniseNeural"
            languageCode.startsWith("de") -> "de-DE-KatjaNeural"
            languageCode.startsWith("es") -> "es-ES-ElviraNeural"
            languageCode.startsWith("it") -> "it-IT-ElsaNeural"
            languageCode.startsWith("ru") -> "ru-RU-SvetlanaNeural"
            else -> "en-US-JennyNeural"
        }
    }

    /**
     * 停止语音播放
     */
    fun stop() {
        synchronized(speakQueue) {
            speakQueue.clear()
        }
        
        try {
            synthesizer?.StopSpeakingAsync()
            synthesizer?.close()
            synthesizer = null
            isSpeaking = false
        } catch (ex: Exception) {
            Log.e(TAG, "停止语音播放时出错", ex)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        stop()
    }
} 