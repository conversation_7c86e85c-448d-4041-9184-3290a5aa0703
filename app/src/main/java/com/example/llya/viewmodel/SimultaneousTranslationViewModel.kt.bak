package com.example.llya.viewmodel

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.audio.AudioPlayer
import com.example.llya.audio.AudioRecorder
import com.example.llya.audio.SilenceDetector
import com.example.llya.network.GoogleCloudApiClient
import com.example.llya.network.Language
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * 同声传译ViewModel
 * 负责管理语音识别、翻译和语音合成的状态
 */
class SimultaneousTranslationViewModel : ViewModel() {
    private val TAG = "SimulTranslateVM"
    
    // API客户端
    private lateinit var apiClient: GoogleCloudApiClient
    
    // 音频录制器
    private var audioRecorder: AudioRecorder? = null
    
    // 音频播放器
    private var audioPlayer: AudioPlayer? = null
    
    // 静音检测器
    private var silenceDetector: SilenceDetector? = null
    
    // 是否正在录音
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    // 是否正在播放语音
    private val _isPlaying = MutableStateFlow(false)
    val isPlaying: StateFlow<Boolean> = _isPlaying.asStateFlow()
    
    // 是否正在处理（翻译或语音合成）
    private val _isProcessing = MutableStateFlow(false)
    val isProcessing: StateFlow<Boolean> = _isProcessing.asStateFlow()
    
    // 是否正在加载语言列表
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 源语言选项列表
    private val _sourceLanguages = MutableStateFlow<List<Language>>(emptyList())
    val sourceLanguages: StateFlow<List<Language>> = _sourceLanguages.asStateFlow()
    
    // 目标语言选项列表
    private val _targetLanguages = MutableStateFlow<List<Language>>(emptyList())
    val targetLanguages: StateFlow<List<Language>> = _targetLanguages.asStateFlow()
    
    // 选中的源语言
    private val _selectedSourceLanguage = MutableStateFlow<Language?>(null)
    val selectedSourceLanguage: StateFlow<Language?> = _selectedSourceLanguage.asStateFlow()
    
    // 选中的目标语言
    private val _selectedTargetLanguage = MutableStateFlow<Language?>(null)
    val selectedTargetLanguage: StateFlow<Language?> = _selectedTargetLanguage.asStateFlow()
    
    // 识别的文本
    private val _recognizedText = MutableStateFlow("")
    val recognizedText: StateFlow<String> = _recognizedText.asStateFlow()
    
    // 翻译后的文本
    private val _translatedText = MutableStateFlow("")
    val translatedText: StateFlow<String> = _translatedText.asStateFlow()
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 临时PCM音频数据缓存
    private var tempAudioFile: File? = null
    private var fileOutputStream: FileOutputStream? = null
    
    // 当前录音段的文本
    private var currentUtterance = StringBuilder()
    
    // 是否应该执行实时语音转文字
    private var shouldPerformRealtimeStt = true
    
    // 上次实时语音识别的时间
    private var lastRealtimeSttTime = 0L
    
    // 实时语音识别时间间隔（毫秒）
    private val realTimeSttIntervalMs = 3000L
    
    // 当前识别的文本
    private var currentRecognizedText = ""
    
    // 添加防止重复请求的标记
    private var isTranslationRequested = false
    
    // 上次翻译的文本内容
    private var lastTranslatedText = ""
    
    // 最小有效录音时长（毫秒）
    private val MIN_VALID_RECORDING_MS = 2000L
    
    // 录音开始时间
    private var lastRecordingStartTime = 0L
    
    // 总录音时长
    private var totalRecordingDurationMs = 0L
    
    // 添加自动停止录音控制开关
    private val _autoStopEnabled = MutableStateFlow(true)
    val autoStopEnabled: StateFlow<Boolean> = _autoStopEnabled.asStateFlow()
    
    /**
     * 初始化ViewModel
     */
    fun initialize(context: Context) {
        // 初始化API客户端
        apiClient = GoogleCloudApiClient.getInstance("https://gogoapi.sdltws.com/")
        
        // 初始化音频播放器
        audioPlayer = AudioPlayer(
            context = context,
            onPlayStart = {
                _isPlaying.value = true
            },
            onPlayComplete = {
                _isPlaying.value = false
            },
            onError = { error ->
                Log.e(TAG, "音频播放错误: $error")
                _isPlaying.value = false
                _errorMessage.value = "音频播放错误: $error"
            }
        )
        
        // 添加录音时长追踪
        lastRecordingStartTime = 0L
        totalRecordingDurationMs = 0L
        
        // 初始化静音检测器
        silenceDetector = SilenceDetector(
            thresholdDb = -58, // 进一步降低阈值，使检测更敏感（从-50改为-58）
            minSilenceDurationMs = 1500, // 增加静音判定时间（从1200ms改为1500ms），避免说话间隙误判
            onSilenceDetected = {
                // 检测到静音时，执行翻译和语音合成
                Log.d(TAG, "☆☆☆ 静音检测器：检测到静音 ☆☆☆")
                
                // 只有录音时长超过了最小有效录音时长，才考虑停止录音
                val recordingDuration = if (lastRecordingStartTime > 0) {
                    System.currentTimeMillis() - lastRecordingStartTime
                } else {
                    0L
                }
                
                val isRecordingLongEnough = recordingDuration >= MIN_VALID_RECORDING_MS
                Log.d(TAG, "☆☆☆ 静音检测器：当前录音持续时长 ${recordingDuration}ms，最小有效时长 ${MIN_VALID_RECORDING_MS}ms，是否有效：$isRecordingLongEnough ☆☆☆")
                
                // 如果录音时间太短，则不停止录音
                if (!isRecordingLongEnough) {
                    Log.d(TAG, "☆☆☆ 静音检测器：录音时间太短，忽略静音事件 ☆☆☆")
                    return@SilenceDetector
                }
                
                // 无论当前是否有文本内容，都记录静音事件，设置标记
                shouldPerformRealtimeStt = false
                
                if (currentRecognizedText.isNotEmpty() && !isTranslationRequested) {
                    // 有文本内容时，执行翻译和语音合成
                    Log.d(TAG, "☆☆☆ 静音检测器：检测到静音，开始翻译和语音合成，当前文本：'$currentRecognizedText' ☆☆☆")
                    isTranslationRequested = true
                    translateAndSpeak(currentRecognizedText)
                } else {
                    // 没有文本内容时，记录日志，但仍然停止录音
                    if (currentRecognizedText.isEmpty()) {
                        Log.d(TAG, "☆☆☆ 静音检测器：检测到静音，但当前没有有效语音内容 ☆☆☆")
                    } else {
                        Log.d(TAG, "☆☆☆ 静音检测器：检测到静音，但翻译已请求 ☆☆☆")
                    }
                }
                
                // 语义完整性检查：实时识别的文本是否看起来是一个完整的句子
                val isSemanticComplete = currentRecognizedText.isNotEmpty() && 
                    (currentRecognizedText.endsWith("。") || 
                     currentRecognizedText.endsWith("？") || 
                     currentRecognizedText.endsWith("！") ||
                     currentRecognizedText.endsWith(".") || 
                     currentRecognizedText.endsWith("?") || 
                     currentRecognizedText.endsWith("!"))
                
                // 仅在自动停止功能启用时才停止录音
                if (_autoStopEnabled.value) {
                    // 无论如何，都自动停止录音，避免需要手动停止
                    viewModelScope.launch {
                        delay(500) // 等待500毫秒，确保有时间完成翻译请求
                        Log.d(TAG, "☆☆☆ 静音检测器：检测到静音后自动停止录音，语义是否完整: $isSemanticComplete ☆☆☆")
                        
                        // 强制设置录音状态标志，确保能执行停止操作
                        if (!_isRecording.value) {
                            Log.d(TAG, "☆☆☆ 静音检测器：录音状态异常，强制设置为录音中 ☆☆☆")
                            _isRecording.value = true
                        }
                        
                        // 添加额外检查并直接调用audioRecorder的停止方法
                        if (audioRecorder?.isRecording() == true) {
                            Log.d(TAG, "☆☆☆ 静音检测器：直接调用audioRecorder停止录音 ☆☆☆")
                            audioRecorder?.stopRecording()
                            _isRecording.value = false
                        }
                        
                        // 正常调用停止录音流程
                        stopRecording()
                    }
                } else {
                    Log.d(TAG, "☆☆☆ 静音检测器：自动停止录音功能已禁用，不会自动停止录音 ☆☆☆")
                }
            },
            onSoundDetected = {
                // 检测到声音开始，允许继续实时识别
                Log.d(TAG, "☆☆☆ 静音检测器：检测到声音，启用实时识别 ☆☆☆")
                shouldPerformRealtimeStt = true
            }
        )
        Log.d(TAG, "☆☆☆ 静音检测器初始化完成：阈值=${-58}dB，最小静音时长=${1500}ms ☆☆☆")
        
        // 加载语言选项
        loadLanguages()
    }
    
    /**
     * 加载语言选项
     */
    private fun loadLanguages() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // 先检查API服务是否可用
                apiClient.checkHealth().onSuccess { response ->
                    Log.d(TAG, "API服务正常: ${response.status}")
                    loadLanguageData()
                }.onFailure { error ->
                    Log.e(TAG, "API服务不可用", error)
                    _errorMessage.value = "API服务不可用，请检查网络连接或联系技术支持"
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载语言选项失败", e)
                _errorMessage.value = "加载语言选项失败: ${e.message}"
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载语言数据
     */
    private suspend fun loadLanguageData() {
        try {
            // 加载语音识别语言
            apiClient.getSpeechLanguages().onSuccess { languages ->
                _sourceLanguages.value = languages
                // 默认选择中文
                _selectedSourceLanguage.value = languages.find { it.code == "zh-CN" } ?: languages.firstOrNull()
            }.onFailure { error ->
                Log.e(TAG, "加载语音识别语言失败", error)
                _errorMessage.value = "加载语音识别语言失败: ${error.message}"
            }
            
            // 加载翻译语言
            apiClient.getTranslationLanguages().onSuccess { languages ->
                _targetLanguages.value = languages
                // 默认选择英语
                _selectedTargetLanguage.value = languages.find { it.code == "en" } ?: languages.firstOrNull()
            }.onFailure { error ->
                Log.e(TAG, "加载翻译语言失败", error)
                _errorMessage.value = "加载翻译语言失败: ${error.message}"
            }
            
            _isLoading.value = false
        } catch (e: Exception) {
            Log.e(TAG, "加载语言数据失败", e)
            _errorMessage.value = "加载语言数据失败: ${e.message}"
            _isLoading.value = false
        }
    }
    
    /**
     * 设置源语言
     */
    fun setSourceLanguage(language: Language) {
        _selectedSourceLanguage.value = language
    }
    
    /**
     * 设置目标语言
     */
    fun setTargetLanguage(language: Language) {
        _selectedTargetLanguage.value = language
    }
    
    /**
     * 开始录音
     */
    fun startRecording(context: Context) {
        if (_isRecording.value) return
        
        viewModelScope.launch {
            try {
                // 创建临时PCM音频文件
                tempAudioFile = createTempAudioFile(context)
                fileOutputStream = FileOutputStream(tempAudioFile)
                
                // 清空之前的识别结果
                _recognizedText.value = ""
                _translatedText.value = ""
                currentUtterance.clear()
                currentRecognizedText = ""
                lastTranslatedText = ""
                isTranslationRequested = false
                _isProcessing.value = false  // 确保处理状态也被重置
                
                // 重置静音检测器
                silenceDetector?.reset()
                Log.d(TAG, "☆☆☆ 开始录音：静音检测器已重置 ☆☆☆")
                
                // 记录录音开始时间
                lastRecordingStartTime = System.currentTimeMillis()
                totalRecordingDurationMs = 0
                
                // 初始化实时识别时间
                lastRealtimeSttTime = System.currentTimeMillis()
                shouldPerformRealtimeStt = true
                
                // 初始化数据包计数器
                var dataPacketCount = 0
                
                // 初始化音频录制器
                audioRecorder = AudioRecorder(
                    context = context,
                    onAudioBufferReady = { buffer ->
                        // 将音频数据写入临时文件
                        try {
                            // 计算音量并记录日志
                            val volume = calculateVolume(buffer)
                            dataPacketCount++
                            
                            if (dataPacketCount % 50 == 0) {
                                Log.d(TAG, "已记录音频数据包: $dataPacketCount, 当前音量: $volume dB")
                            }
                            
                            fileOutputStream?.write(buffer)
                            
                            // 处理静音检测
                            if (dataPacketCount % 20 == 0) {  // 降低日志频率，每20个包记录一次
                                Log.d(TAG, "☆☆☆ 调用静音检测器处理第${dataPacketCount}个音频数据包 ☆☆☆")
                            }
                            val silenceDetected = silenceDetector?.processSample(buffer) ?: false
                            if (silenceDetected) {
                                Log.d(TAG, "☆☆☆ 静音检测器返回值：检测到静音！ ☆☆☆")
                            }
                            
                            // 记录音量日志，每50个数据包记录一次
                            if (dataPacketCount % 50 == 0) {
                                Log.d(TAG, "☆☆☆ 静音检测：处理第${dataPacketCount}个音频包，当前音量：${volume}dB，静音阈值：-58dB ☆☆☆")
                            }
                            
                            // 检查是否应该进行实时语音转文字（如果之前检测到静音，则不需要）
                            val currentTime = System.currentTimeMillis()
                            if (shouldPerformRealtimeStt && !isTranslationRequested && currentTime - lastRealtimeSttTime > realTimeSttIntervalMs) {
                                lastRealtimeSttTime = currentTime
                                Log.d(TAG, "☆☆☆ 实时语音转文字：触发条件满足，开始执行 ☆☆☆")
                                processAudioForSpeechToText()
                            } else {
                                if (dataPacketCount % 50 == 0) {  // 降低日志频率
                                    if (!shouldPerformRealtimeStt) {
                                        Log.d(TAG, "☆☆☆ 实时语音转文字：已暂时禁用（可能因为检测到静音） ☆☆☆")
                                    } else if (isTranslationRequested) {
                                        Log.d(TAG, "☆☆☆ 实时语音转文字：翻译已请求，跳过 ☆☆☆")
                                    } else if (currentTime - lastRealtimeSttTime <= realTimeSttIntervalMs) {
                                        Log.d(TAG, "☆☆☆ 实时语音转文字：时间间隔未到(${currentTime - lastRealtimeSttTime}ms < ${realTimeSttIntervalMs}ms) ☆☆☆")
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "处理音频数据失败", e)
                        }
                    },
                    onError = { error ->
                        Log.e(TAG, "音频录制错误: $error")
                        _errorMessage.value = "音频录制错误: $error"
                        stopRecording()
                    }
                )
                
                // 开始录音
                Log.d(TAG, "开始录音，输出到文件: ${tempAudioFile?.absolutePath}")
                audioRecorder?.startRecording()
                _isRecording.value = true
                
            } catch (e: Exception) {
                Log.e(TAG, "开始录音失败", e)
                _errorMessage.value = "开始录音失败: ${e.message}"
                cleanup()
            }
        }
    }
    
    /**
     * 计算音频数据的分贝值
     */
    private fun calculateVolume(buffer: ByteArray): Int {
        var sum = 0.0
        val samples = buffer.size / 2 // 16位音频，每个样本2字节
        
        for (i in 0 until buffer.size step 2) {
            if (i + 1 < buffer.size) {
                // 将两个字节转换为16位有符号整数
                val sample = (buffer[i + 1].toInt() shl 8) or (buffer[i].toInt() and 0xFF)
                // 针对有符号整数，将值映射到[-32768, 32767]范围
                val signedSample = if (sample > 32767) sample - 65536 else sample
                sum += signedSample * signedSample
            }
        }
        
        // 计算RMS并转换为dB
        val rms = Math.sqrt(sum / samples)
        val db = (20 * Math.log10(rms / 32767)).toInt()
        
        // 每500个数据包记录一次音量详情
        if (Math.random() < 0.02) {  // 大约2%的概率记录详细日志
            Log.d(TAG, "☆☆☆ 音量计算：样本数=${samples}，RMS=${rms}，分贝值=${db}dB ☆☆☆")
        }
        
        return db
    }
    
    /**
     * 停止录音
     */
    fun stopRecording() {
        Log.d(TAG, "☆☆☆ 停止录音方法被调用，当前录音状态: ${_isRecording.value} ☆☆☆")
        
        // 如果已经不在录音状态，检查audioRecorder是否仍在录音
        if (!_isRecording.value && audioRecorder?.isRecording() == true) {
            Log.d(TAG, "☆☆☆ 状态不一致：isRecording=false但audioRecorder仍在录音，强制停止 ☆☆☆")
            _isRecording.value = true // 临时设为true以确保能执行停止流程
        } else if (!_isRecording.value) {
            Log.d(TAG, "☆☆☆ 已经不在录音状态，忽略本次停止录音请求 ☆☆☆")
            return
        }
        
        viewModelScope.launch {
            try {
                Log.d(TAG, "☆☆☆ 开始执行停止录音操作 ☆☆☆")
                
                // 立即更新UI状态，避免多次调用
                _isRecording.value = false
                
                // 停止录音
                if (audioRecorder?.isRecording() == true) {
                    Log.d(TAG, "☆☆☆ 调用audioRecorder.stopRecording() ☆☆☆")
                    audioRecorder?.stopRecording()
                } else {
                    Log.d(TAG, "☆☆☆ audioRecorder已经不在录音状态 ☆☆☆")
                }
                
                // 关闭文件输出流
                try {
                    Log.d(TAG, "刷新并关闭文件输出流")
                    fileOutputStream?.flush()
                    fileOutputStream?.close()
                    Log.d(TAG, "文件输出流已关闭，文件大小: ${tempAudioFile?.length() ?: 0} 字节")
                } catch (e: Exception) {
                    Log.e(TAG, "关闭文件输出流失败", e)
                }
                
                // 处理最后一段语音
                val audioFile = tempAudioFile
                if (audioFile != null && audioFile.exists() && audioFile.length() > 0) {
                    Log.d(TAG, "处理最后一段语音，执行最终的语音转文字和翻译")
                    
                    // 检查是否已经请求了翻译（例如，通过静音检测触发）
                    if (isTranslationRequested && currentRecognizedText.isNotEmpty()) {
                        Log.d(TAG, "已经请求了翻译，跳过最终语音识别和翻译")
                    } else {
                        try {
                            // 先进行一次最终的语音转文字
                            val sourceLanguageCode = _selectedSourceLanguage.value?.code ?: "zh-CN"
                            apiClient.speechToText(audioFile, sourceLanguageCode).onSuccess { response ->
                                if (response.success && response.text != null && response.text.isNotEmpty()) {
                                    val finalText = response.text
                                    Log.d(TAG, "最终语音识别成功: '$finalText'")
                                    
                                    // 更新识别文本
                                    currentRecognizedText = finalText
                                    _recognizedText.value = finalText
                                    
                                    // 翻译并语音合成
                                    if (!isTranslationRequested) {
                                        isTranslationRequested = true
                                        translateAndSpeak(finalText)
                                    } else {
                                        Log.d(TAG, "翻译已请求，跳过重复翻译")
                                    }
                                } else {
                                    val textStatus = if (response.text == null) "null" else if (response.text.isEmpty()) "空" else "长度为${response.text.length}"
                                    Log.d(TAG, "最终语音识别未返回有效结果: ${response.success}, 文本: $textStatus")
                                    // 对于协议异常，我们应该使用已有的识别文本

                                    // 如果API返回成功但没有文本内容，尝试使用之前已识别的文本
                                    if (response.success && (response.text == null || response.text.isEmpty()) && currentRecognizedText.isNotEmpty() && !isTranslationRequested) {
                                        // 使用现有的识别文本进行翻译
                                        Log.d(TAG, "使用现有识别文本进行翻译: '$currentRecognizedText'")
                                        isTranslationRequested = true
                                        translateAndSpeak(currentRecognizedText)
                                        return@onSuccess
                                    } else if (currentRecognizedText.isEmpty()) {
                                        Log.d(TAG, "没有现有的识别文本可用于翻译")
                                        // 只在用户明确期望有结果时才显示错误
                                        if (!isTranslationRequested) {
                                            _errorMessage.value = "未能识别到语音内容，请再试一次"
                                            // 3秒后自动清除错误消息
                                            viewModelScope.launch {
                                                delay(3000)
                                                if (_errorMessage.value == "未能识别到语音内容，请再试一次") {
                                                    _errorMessage.value = null
                                                }
                                            }
                                        }
                                    }
                                }
                            }.onFailure { error ->
                                Log.e(TAG, "最终语音转文字失败: ${error.javaClass.simpleName}", error)
                                
                                // 处理各类错误情况
                                when (error) {
                                    is java.net.ProtocolException -> {
                                        // 对于协议异常，我们应该使用已有的识别文本
                                        if (error.message?.contains("expected") == true && error.message?.contains("received") == true) {
                                            Log.d(TAG, "检测到内容长度不匹配问题，使用现有识别文本。错误详情: ${error.message}")
                                            
                                            // 如果响应成功但没有文本，我们应该使用已有的识别文本
                                            if (response.success && (response.text == null || response.text.isEmpty())) {
                                                Log.d(TAG, "检测到空文本响应，使用现有识别文本。")
                                            }
                                        }
                                        if (!isTranslationRequested) {
                                            _errorMessage.value = "语音识别服务连接异常，请稍后再试"
                                        }
                                    }
                                    is java.net.SocketTimeoutException -> {
                                        if (!isTranslationRequested) {
                                            _errorMessage.value = "语音识别服务连接超时，请检查网络"
                                        }
                                    }
                                    else -> {
                                        if (!isTranslationRequested) {
                                            _errorMessage.value = "最终语音转文字失败: ${error.message}"
                                        }
                                    }
                                }
                                
                                // 如果有之前的识别结果，尝试使用它
                                if (currentRecognizedText.isNotEmpty() && !isTranslationRequested) {
                                    Log.d(TAG, "尽管发生错误，但使用之前的识别结果进行翻译: '$currentRecognizedText'")
                                    isTranslationRequested = true
                                    translateAndSpeak(currentRecognizedText)
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "处理最后一段语音时出错", e)
                            if (!isTranslationRequested) {
                                _errorMessage.value = "处理语音失败: ${e.message}"
                            }
                            
                            // 如果有之前的识别结果，尝试使用它
                            if (currentRecognizedText.isNotEmpty() && !isTranslationRequested) {
                                Log.d(TAG, "尽管处理出错，但使用之前的识别结果进行翻译: '$currentRecognizedText'")
                                isTranslationRequested = true
                                translateAndSpeak(currentRecognizedText)
                            }
                        }
                    }
                } else {
                    Log.d(TAG, "没有有效的最终语音数据需要处理")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "停止录音失败", e)
                _errorMessage.value = "停止录音失败: ${e.message}"
            } finally {
                // 延迟清理资源，确保最后的处理有时间完成
                delay(2000)
                // 对于没有识别到文本且未请求翻译的情况，重置标志位以便下次录音可以正常工作
                if (currentRecognizedText.isEmpty() && !isTranslationRequested) {
                    Log.d(TAG, "未识别到文本且未请求翻译，重置状态以便下次录音")
                    isTranslationRequested = false
                }
                cleanup()
            }
        }
    }
    
    /**
     * 文字转语音并播放
     */
    private fun synthesizeSpeech(text: String, languageCode: String) {
        viewModelScope.launch {
            try {
                // 确定语言代码格式
                // 注意：文字转语音API使用的语言代码可能与翻译API不同
                // 例如，中文在翻译API可能是"zh-CN"，但在TTS API是"cmn-CN"
                val ttsLanguageCode = when(languageCode) {
                    "zh-CN" -> "cmn-CN"
                    "zh-TW" -> "cmn-TW"
                    "ja" -> "ja-JP"
                    "ko" -> "ko-KR"
                    "en" -> "en-US"
                    "es" -> "es-ES"
                    "fr" -> "fr-FR"
                    else -> languageCode
                }
                
                apiClient.textToSpeech(text, ttsLanguageCode).onSuccess { audioData ->
                    // 播放合成的语音
                    audioPlayer?.playAudio(audioData)
                    
                    // 处理完成，设置标志位
                    _isProcessing.value = false
                    isTranslationRequested = false
                    
                    // 清除当前语音段，准备下一段
                    currentUtterance.clear()
                }.onFailure { error ->
                    Log.e(TAG, "文字转语音失败", error)
                    _errorMessage.value = "文字转语音失败: ${error.message}"
                    _isProcessing.value = false
                    isTranslationRequested = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "文字转语音失败", e)
                _errorMessage.value = "文字转语音失败: ${e.message}"
                _isProcessing.value = false
                isTranslationRequested = false
            }
        }
    }
    
    /**
     * 创建临时音频文件
     */
    private suspend fun createTempAudioFile(context: Context): File = withContext(Dispatchers.IO) {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, "recording.pcm")
        if (file.exists()) {
            file.delete()
        }
        file.createNewFile()
        return@withContext file
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            fileOutputStream?.close()
            fileOutputStream = null
            
            audioRecorder?.release()
            audioRecorder = null
            
            tempAudioFile?.delete()
            tempAudioFile = null
        } catch (e: Exception) {
            Log.e(TAG, "清理资源失败", e)
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 释放资源
     */
    override fun onCleared() {
        super.onCleared()
        cleanup()
        audioPlayer?.releaseMediaPlayer()
        audioPlayer = null
    }
    
    /**
     * 刷新语言列表
     */
    fun refreshLanguages() {
        _errorMessage.value = null
        _isLoading.value = true
        viewModelScope.launch {
            try {
                _sourceLanguages.value = emptyList()
                _targetLanguages.value = emptyList()
                loadLanguages()
            } catch (e: Exception) {
                Log.e(TAG, "刷新语言列表失败", e)
                _errorMessage.value = "刷新语言列表失败: ${e.message}"
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 实时处理音频进行语音转文字
     */
    private fun processAudioForSpeechToText() {
        viewModelScope.launch {
            // 如果有正在处理的任务或者翻译已经请求，跳过实时语音转文字
            if (_isProcessing.value || isTranslationRequested) {
                Log.d(TAG, "上一个处理任务尚未完成或翻译已请求，跳过实时语音转文字")
                return@launch
            }
            
            // 如果不应该执行实时语音转文字，直接返回
            if (!shouldPerformRealtimeStt) {
                Log.d(TAG, "实时语音转文字暂时禁用，跳过本次请求")
                return@launch
            }
            
            try {
                val sourceLanguageCode = _selectedSourceLanguage.value?.code ?: "zh-CN"
                
                val audioFile = tempAudioFile
                if (audioFile != null && audioFile.exists() && audioFile.length() > 0) {
                    Log.d(TAG, "执行实时语音转文字，音频大小: ${audioFile.length()} 字节")
                    
                    // 语音转文字
                    apiClient.speechToText(audioFile, sourceLanguageCode).onSuccess { response ->
                        if (response.success && response.text != null && response.text.isNotEmpty()) {
                            val recognizedText = response.text
                            Log.d(TAG, "实时语音识别成功: '$recognizedText'")
                            
                            // 更新识别文本
                            currentRecognizedText = recognizedText
                            _recognizedText.value = recognizedText
                            
                            // 累积文本
                            currentUtterance.append(recognizedText)
                        } else {
                            val textStatus = if (response.text == null) "null" else if (response.text.isEmpty()) "空" else "长度为${response.text.length}"
                            Log.d(TAG, "实时语音识别未返回有效结果: ${response.success}, 文本: $textStatus")
                            // 即使API返回成功但文本为空，也不要清空之前的识别结果
                            if ((response.text == null || response.text.isEmpty()) && currentRecognizedText.isNotEmpty()) {
                                Log.d(TAG, "API返回空文本，但保留现有的识别文本: '$currentRecognizedText'")
                            }
                        }
                    }.onFailure { error ->
                        // 处理各种类型的错误
                        when (error) {
                            is java.net.ProtocolException -> {
                                // 处理协议异常，这类错误可能是连接问题，不一定是识别失败
                                Log.e(TAG, "实时语音转文字协议异常: ${error.message}", error)
                                // 我们不清空已识别的文本，只是显示一个友好的提示
                                if (error.message?.contains("expected") == true && error.message?.contains("received") == true) {
                                    // 这是一个已知的内容长度不匹配问题，可以适当忽略
                                    Log.d(TAG, "检测到内容长度不匹配问题，忽略此次请求。错误详情: ${error.message}")
                                    
                                    // 保存当前录音时间，减少下次实时识别的触发频率
                                    lastRealtimeSttTime = System.currentTimeMillis() + realTimeSttIntervalMs / 2
                                    
                                    // 显示一个小提示，让用户知道有轻微问题但不影响使用
                                    if (Math.random() < 0.3) { // 只有30%的概率显示提示，避免频繁弹出
                                        _errorMessage.value = "语音转写暂时遇到小问题，请继续说话"
                                        // 2秒后自动清除错误消息
                                        viewModelScope.launch {
                                            delay(2000)
                                            if (_errorMessage.value == "语音转写暂时遇到小问题，请继续说话") {
                                                _errorMessage.value = null
                                            }
                                        }
                                    }
                                } else {
                                    _errorMessage.value = "语音识别遇到网络问题，请稍后再试"
                                }
                            }
                            is java.net.SocketTimeoutException -> {
                                Log.e(TAG, "实时语音转文字超时", error)
                                _errorMessage.value = "语音识别连接超时，请检查网络"
                            }
                            is retrofit2.HttpException -> {
                                Log.e(TAG, "实时语音转文字HTTP错误: ${error.code()}", error)
                                _errorMessage.value = "语音识别服务返回错误(${error.code()})"
                            }
                            else -> {
                                Log.e(TAG, "实时语音转文字失败: ${error.javaClass.simpleName}", error)
                                _errorMessage.value = "语音识别失败: ${error.message}"
                            }
                        }
                    }
                } else {
                    Log.d(TAG, "无有效音频文件可用于实时语音转文字")
                }
            } catch (e: Exception) {
                Log.e(TAG, "实时语音转文字处理失败", e)
                _errorMessage.value = "语音转文字处理出错: ${e.message}"
            }
        }
    }
    
    /**
     * 翻译识别的文本并进行语音合成
     */
    private fun translateAndSpeak(text: String) {
        viewModelScope.launch {
            // 检查是否有正在进行的处理
            if (_isProcessing.value) {
                Log.d(TAG, "☆☆☆ 翻译流程：上一个翻译和语音合成任务尚未完成，已跳过 ☆☆☆")
                return@launch
            }
            
            // 检查是否重复翻译相同内容
            if (text == lastTranslatedText && text.isNotEmpty()) {
                Log.d(TAG, "☆☆☆ 翻译流程：检测到重复翻译请求，内容：'$text'，已跳过 ☆☆☆")
                isTranslationRequested = false
                return@launch
            }
            
            _isProcessing.value = true
            
            try {
                val sourceLanguageCode = _selectedSourceLanguage.value?.code ?: "zh-CN"
                val targetLanguageCode = _selectedTargetLanguage.value?.code ?: "en"
                
                Log.d(TAG, "☆☆☆ 翻译流程：开始翻译文本: '$text' ☆☆☆")
                
                // 保存当前翻译的文本，用于避免重复翻译
                lastTranslatedText = text
                
                // 翻译文字
                apiClient.translateText(text, sourceLanguageCode, targetLanguageCode).onSuccess { response ->
                    if (response.success && response.translatedText.isNotEmpty()) {
                        val translatedText = response.translatedText
                        Log.d(TAG, "☆☆☆ 翻译流程：翻译成功: '$translatedText' ☆☆☆")
                        
                        // 更新翻译文本
                        _translatedText.value = translatedText
                        
                        // 文字转语音
                        Log.d(TAG, "☆☆☆ 翻译流程：即将开始语音合成 ☆☆☆")
                        synthesizeSpeech(translatedText, targetLanguageCode)
                    } else {
                        Log.e(TAG, "☆☆☆ 翻译流程：翻译未返回有效结果 ☆☆☆")
                        _isProcessing.value = false
                        isTranslationRequested = false
                    }
                }.onFailure { error ->
                    Log.e(TAG, "☆☆☆ 翻译流程：翻译文本失败 ☆☆☆", error)
                    _errorMessage.value = "翻译失败: ${error.message}"
                    _isProcessing.value = false
                    isTranslationRequested = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "☆☆☆ 翻译流程：翻译和语音合成处理失败 ☆☆☆", e)
                _errorMessage.value = "翻译处理失败: ${e.message}"
                _isProcessing.value = false
                isTranslationRequested = false
            }
        }
    }
    
    // 切换自动停止录音功能
    fun toggleAutoStop(enabled: Boolean) {
        _autoStopEnabled.value = enabled
        Log.d(TAG, "☆☆☆ 自动停止录音功能已${if(enabled) "启用" else "禁用"} ☆☆☆")
    }
    
    /**
     * 强制停止录音 - 用于界面按钮调用，确保能够停止录音
     */
    fun forceStopRecording() {
        Log.d(TAG, "☆☆☆ 强制停止录音按钮被点击，准备停止录音 ☆☆☆")
        
        // 手动触发静音检测回调，确保翻译流程被触发
        if (currentRecognizedText.isNotEmpty() && !isTranslationRequested) {
            Log.d(TAG, "☆☆☆ 强制停止：有识别文本且未请求翻译，先触发翻译 ☆☆☆")
            silenceDetector?.forceDetectSilence()
        }
        
        viewModelScope.launch {
            // 确保UI状态更新
            _isRecording.value = false
            
            // 直接停止录音器
            if (audioRecorder?.isRecording() == true) {
                Log.d(TAG, "☆☆☆ 强制停止：直接调用audioRecorder.stopRecording() ☆☆☆")
                audioRecorder?.stopRecording()
            }
            
            // 调用标准流程处理剩余逻辑
            delay(200) // 短暂延迟，确保audioRecorder状态已更新
            stopRecording()
        }
    }
} 