package com.example.llya.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.viewmodel.UserViewModel
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log

// 国家区号数据类
data class CountryCode(val name: String, val code: String)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(navController: NavController? = null, currentLanguage: String = "简体中文") {
    // 获取UserViewModel
    val userViewModel: UserViewModel = viewModel()
    val uiState by userViewModel.uiState.collectAsState()
    
    // 检查用户是否已登录 - 使用StateFlow跟踪登录状态
    val isLoggedIn by userViewModel.isLoggedInState.collectAsState()
    
    // 初始化时检查一次登录状态
    LaunchedEffect(Unit) {
        Log.d("LoginScreen", "页面初始化，检查登录状态")
        // 检查是否已登录并更新状态流
        val loginStatus = userViewModel.isLoggedIn()
        
        // 如果已登录，直接获取用户信息并准备导航
        if (loginStatus && navController != null) {
            Log.d("LoginScreen", "检测到用户已登录，正在获取用户详细信息...")
            userViewModel.getUserDetail()
        }
    }
    
    // 如果已登录，调用getUserDetail获取用户信息并根据结果导航
    LaunchedEffect(isLoggedIn) {
        Log.d("LoginScreen", "登录状态发生变化: $isLoggedIn")
        if (isLoggedIn && navController != null) {
            // 显示加载指示器
            Log.d("LoginScreen", "用户已登录，正在获取用户详细信息...")
            
            // 调用API获取用户详情
            userViewModel.getUserDetail()
        }
    }
    
    // SnackBar状态
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    
    // 设置状态栏文字颜色为白色
    val systemUiController = rememberSystemUiController()
    // 背景颜色 - 深蓝色
    val backgroundColor = Color(0xFF1E1D2B)
    
    // 获取系统语言
    val context = LocalContext.current
    val systemLocale = context.resources.configuration.locales[0]
    
    DisposableEffect(systemUiController) {
        // 设置状态栏背景为深蓝色，文字为白色
        systemUiController.setSystemBarsColor(
            color = backgroundColor,
            darkIcons = false // false表示使用白色图标
        )
        onDispose {
            // 如果需要恢复默认状态，在这里处理
        }
    }
    
    // 判断是否为中文用户（根据系统语言）
    val isChineseUser = systemLocale.language == "zh" || systemLocale.toString().startsWith("zh")
    
    // 状态变量
    var phoneNumber by remember { mutableStateOf("") }
    var verificationCode by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    
    // 是否正在加载中
    var isLoading by remember { mutableStateOf(false) }
    
    // 验证码相关状态
    var phoneVerifyKey by remember { mutableStateOf("") }
    var emailVerifyKey by remember { mutableStateOf("") }
    var isCountingDown by remember { mutableStateOf(false) }
    var countdownSeconds by remember { mutableStateOf(60) }
    
    // 区号选择相关状态
    var countryCodeMenuExpanded by remember { mutableStateOf(false) }
    val chinaText = stringResource(R.string.china)
    val countryCodes = remember {
        listOf(
            CountryCode(chinaText, "+86"),
        )
    }
    var selectedCountryCode by remember { mutableStateOf(countryCodes[0]) }
    
    // 卡片背景颜色 - 稍浅的蓝色
    val cardBackgroundColor = Color(0xFF33324A)
    // 按钮颜色 - 紫色
    val buttonColor = Color(0xFF8771FF)
    
    // 监听UI状态变化
    LaunchedEffect(uiState) {
        when (uiState) {
            is UserViewModel.UiState.Loading -> {
                isLoading = true
            }
            is UserViewModel.UiState.VerifyCodeSent -> {
                isLoading = false
                val state = uiState as UserViewModel.UiState.VerifyCodeSent
                if (isChineseUser) {
                    phoneVerifyKey = state.key
                } else {
                    emailVerifyKey = state.key
                }
                // 开始倒计时
                isCountingDown = true
                countdownSeconds = 60
                
                val message = context.getString(R.string.verification_code_sent)
                coroutineScope.launch {
                    snackbarHostState.showSnackbar(message)
                }
            }
            is UserViewModel.UiState.LoginSuccess -> {
                isLoading = false
                // 登录成功后不再主动获取用户详情，因为isLoggedIn变化会触发LaunchedEffect
                // LaunchedEffect(isLoggedIn)已经处理了登录状态变化后的getUserDetail调用
                Log.d("LoginScreen", "登录成功，等待登录状态变化触发用户信息获取")
            }
            is UserViewModel.UiState.Success -> {
                isLoading = false
                val successMsg = (uiState as UserViewModel.UiState.Success).message
                
                // 如果是获取用户信息成功，检查设备状态并导航
                if (successMsg.contains("获取用户信息成功") || successMsg.contains("User info retrieved successfully")) {
                    val currentUser = userViewModel.currentUser.value
                    Log.d("LoginScreen", "用户信息获取成功: ${currentUser?.username}, 设备数量: ${currentUser?.devices?.size ?: 0}")
                    
                    if (currentUser != null && currentUser.devices.isNotEmpty()) {
                        // 有设备，导航到蓝牙设备页面
                        Log.d("LoginScreen", "检测到已绑定设备，跳转到蓝牙页面")
                        navController?.navigate("bluetooth") {
                            popUpTo("login") { inclusive = true }
                        }
                    } else {
                        // 无设备，导航到无设备页面
                        Log.d("LoginScreen", "未检测到绑定设备，跳转到无设备页面")
                        navController?.navigate("no_device") {
                            popUpTo("login") { inclusive = true }
                        }
                    }
                } else {
                    // 其他成功消息
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar(successMsg)
                    }
                }
            }
            is UserViewModel.UiState.Error -> {
                isLoading = false
                val errorMsg = (uiState as UserViewModel.UiState.Error).message
                
                // 如果是获取用户信息失败的错误，检查当前登录状态再决定是否导航
                if (errorMsg.contains("获取用户信息") || errorMsg.contains("user info")) {
                    // 再次检查登录状态，避免使用可能过时的isLoggedIn变量
                    val currentLoginStatus = userViewModel.isLoggedIn()
                    Log.d("LoginScreen", "获取用户信息失败，当前登录状态: $currentLoginStatus, 错误: $errorMsg")
                    
                    if (currentLoginStatus) {
                        // 确实已登录但获取信息失败，默认跳转到无设备页面
                        Log.d("LoginScreen", "已登录但获取用户信息失败，默认跳转到无设备页面")
                        navController?.navigate("no_device") {
                            popUpTo("login") { inclusive = true }
                        }
                    } else {
                        // 未登录，显示错误信息
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar(errorMsg)
                        }
                    }
                } else {
                    // 其他错误，显示错误信息
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar(errorMsg)
                    }
                }
            }
            else -> {
                isLoading = false
            }
        }
    }
    
    // 倒计时效果
    LaunchedEffect(isCountingDown) {
        if (isCountingDown) {
            while (countdownSeconds > 0) {
                delay(1000)
                // 在主线程中更新状态
                withContext(Dispatchers.Main) {
                    countdownSeconds--
                }
            }
            // 在主线程中更新状态
            withContext(Dispatchers.Main) {
                isCountingDown = false
            }
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .padding(10.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor),
            shape = RoundedCornerShape(20.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(18.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 头像和图标
                Box(
                    modifier = Modifier
                        .size(90.dp)
                        .background(
                            color = Color(0xFF4E4B79),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.app_icon),
                        contentDescription = stringResource(R.string.app_logo),
                        modifier = Modifier.size(50.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 登录账号标题
                Text(
                    text = if (isChineseUser) stringResource(R.string.login_account) else stringResource(R.string.sign_in),
                    color = Color.White,
                    fontSize = 22.sp,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(6.dp))
                
                // 突破语言障碍，连接世界
                Text(
                    text = if (isChineseUser) stringResource(R.string.login_slogan) else stringResource(R.string.login_slogan_en),
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 根据用户语言直接显示对应的登录表单
                if (isChineseUser) {
                   
                    
                    // 手机号登录表单
                    PhoneLoginForm(
                        phoneNumber = phoneNumber,
                        onPhoneNumberChange = { phoneNumber = it },
                        verificationCode = verificationCode,
                        onVerificationCodeChange = { verificationCode = it },
                        selectedCountryCode = selectedCountryCode,
                        countryCodeMenuExpanded = countryCodeMenuExpanded,
                        onCountryCodeMenuExpandedChange = { countryCodeMenuExpanded = it },
                        onCountryCodeSelected = { selectedCountryCode = it },
                        countryCodes = countryCodes,
                        buttonColor = buttonColor,
                        cardBackgroundColor = cardBackgroundColor,
                        isChineseUser = true,
                        isCountingDown = isCountingDown,
                        countdownSeconds = countdownSeconds,
                        onSendVerifyCode = {
                            userViewModel.sendSms(phoneNumber)
                        }
                    )
                } else {
                    // 显示登录方式标识
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Email,
                            contentDescription = stringResource(R.string.email_login_desc),
                            tint = buttonColor,
                            modifier = Modifier.size(24.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = stringResource(R.string.email_login),
                            color = buttonColor,
                            fontSize = 16.sp,
                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
                        )
                    }
                    
                    // 邮箱登录表单
                    EmailLoginForm(
                        email = email,
                        onEmailChange = { email = it },
                        password = password,
                        onPasswordChange = { password = it },
                        buttonColor = buttonColor,
                        cardBackgroundColor = cardBackgroundColor,
                        isChineseUser = false,
                        isCountingDown = isCountingDown,
                        countdownSeconds = countdownSeconds,
                        onSendVerifyCode = {
                            userViewModel.sendEmailCode(email)
                        }
                    )
                }
                
                Spacer(modifier = Modifier.height(18.dp))
                
                // 登录按钮
                Button(
                    onClick = {
                        if (isChineseUser) {
                            // 手机号登录
                            if (phoneNumber.isEmpty() || verificationCode.isEmpty()) {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(
                                        context.getString(R.string.please_fill_all_fields)
                                    )
                                }
                                return@Button
                            }
                            
                            userViewModel.login(
                                mobile = phoneNumber,
                                verifyId = phoneVerifyKey,
                                verify = verificationCode
                            )
                        } else {
                            // 邮箱登录
                            if (email.isEmpty() || password.isEmpty()) {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(
                                        context.getString(R.string.please_fill_all_fields)
                                    )
                                }
                                return@Button
                            }
                            
                            userViewModel.emailLogin(
                                email = email,
                                verifyId = emailVerifyKey,
                                verify = password
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = buttonColor
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = if (isChineseUser) stringResource(R.string.login) else stringResource(R.string.sign_in),
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
        
        // SnackBar显示
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        ) { data ->
            Snackbar(
                modifier = Modifier.padding(16.dp),
                containerColor = cardBackgroundColor,
                contentColor = Color.White,
            ) {
                Text(text = data.visuals.message)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhoneLoginForm(
    phoneNumber: String,
    onPhoneNumberChange: (String) -> Unit,
    verificationCode: String,
    onVerificationCodeChange: (String) -> Unit,
    selectedCountryCode: CountryCode,
    countryCodeMenuExpanded: Boolean,
    onCountryCodeMenuExpandedChange: (Boolean) -> Unit,
    onCountryCodeSelected: (CountryCode) -> Unit,
    countryCodes: List<CountryCode>,
    buttonColor: Color,
    cardBackgroundColor: Color,
    isChineseUser: Boolean,
    isCountingDown: Boolean,
    countdownSeconds: Int,
    onSendVerifyCode: () -> Unit
) {
    // 手机号码
    Text(
        text = stringResource(R.string.phone_number),
        color = Color.White,
        fontSize = 15.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 4.dp),
        textAlign = TextAlign.Start
    )
    
    // 区号选择和手机号输入框
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 区号选择器
        Box(modifier = Modifier) {
            Row(
                modifier = Modifier
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color.Transparent)
                    .border(
                        width = 1.dp,
                        color = Color.White.copy(alpha = 0.3f),
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clickable { onCountryCodeMenuExpandedChange(true) }
                    .padding(horizontal = 12.dp, vertical = 14.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = selectedCountryCode.code,
                    color = Color.White,
                    fontSize = 15.sp
                )
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = stringResource(R.string.select_country_code_desc),
                    tint = Color.White,
                    modifier = Modifier.padding(start = 4.dp)
                )
            }
            
            DropdownMenu(
                expanded = countryCodeMenuExpanded,
                onDismissRequest = { onCountryCodeMenuExpandedChange(false) },
                modifier = Modifier.background(cardBackgroundColor)
            ) {
                countryCodes.forEach { countryCode ->
                    DropdownMenuItem(
                        text = {
                            Text(
                                "${countryCode.name} ${countryCode.code}",
                                color = Color.White
                            )
                        },
                        onClick = {
                            onCountryCodeSelected(countryCode)
                            onCountryCodeMenuExpandedChange(false)
                        }
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 手机号输入框
        OutlinedTextField(
            value = phoneNumber,
            onValueChange = onPhoneNumberChange,
            placeholder = { Text(stringResource(R.string.enter_phone_number), color = Color.White.copy(alpha = 0.5f)) },
            modifier = Modifier.weight(1f),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                unfocusedBorderColor = Color.White.copy(alpha = 0.3f),
                focusedBorderColor = Color.White,
                focusedTextColor = Color.White,
                unfocusedTextColor = Color.White
            ),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
            shape = RoundedCornerShape(8.dp),
            singleLine = true
        )
    }
    
    Spacer(modifier = Modifier.height(12.dp))
    
    // 验证码
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(R.string.verification_code),
            color = Color.White,
            fontSize = 15.sp,
            modifier = Modifier.padding(bottom = 4.dp),
            textAlign = TextAlign.Start
        )
    }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // 验证码输入框
        OutlinedTextField(
            value = verificationCode,
            onValueChange = onVerificationCodeChange,
            placeholder = { Text(stringResource(R.string.enter_verification_code), color = Color.White.copy(alpha = 0.5f)) },
            modifier = Modifier.weight(1f),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                unfocusedBorderColor = Color.White.copy(alpha = 0.3f),
                focusedBorderColor = Color.White,
                focusedTextColor = Color.White,
                unfocusedTextColor = Color.White
            ),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            shape = RoundedCornerShape(8.dp),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 获取验证码按钮
        OutlinedButton(
            onClick = {
                if (!isCountingDown && phoneNumber.isNotEmpty()) {
                    onSendVerifyCode()
                }
            },
            modifier = Modifier.padding(top = 4.dp),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = buttonColor
            ),
            shape = RoundedCornerShape(8.dp),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.dp,
                color = buttonColor
            ),
            enabled = !isCountingDown && phoneNumber.isNotEmpty()
        ) {
            Text(
                text = if (isCountingDown) {
                    stringResource(R.string.retry_in_seconds, countdownSeconds)
                } else {
                    stringResource(R.string.get_verification_code)
                },
                fontSize = 13.sp,
                color = buttonColor // 设置文字颜色与主题色一致
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailLoginForm(
    email: String,
    onEmailChange: (String) -> Unit,
    password: String,
    onPasswordChange: (String) -> Unit,
    buttonColor: Color,
    cardBackgroundColor: Color,
    isChineseUser: Boolean,
    isCountingDown: Boolean,
    countdownSeconds: Int,
    onSendVerifyCode: () -> Unit
) {
    // 邮箱
    Text(
        text = stringResource(R.string.email_address),
        color = Color.White,
        fontSize = 15.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 4.dp),
        textAlign = TextAlign.Start
    )
    
    // 邮箱输入框
    OutlinedTextField(
        value = email,
        onValueChange = onEmailChange,
        placeholder = { Text(stringResource(R.string.enter_email), color = Color.White.copy(alpha = 0.5f)) },
        modifier = Modifier.fillMaxWidth(),
        colors = OutlinedTextFieldDefaults.colors(
            focusedContainerColor = Color.Transparent,
            unfocusedContainerColor = Color.Transparent,
            unfocusedBorderColor = Color.White.copy(alpha = 0.3f),
            focusedBorderColor = Color.White,
            focusedTextColor = Color.White,
            unfocusedTextColor = Color.White
        ),
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
        shape = RoundedCornerShape(8.dp),
        singleLine = true
    )
    
    Spacer(modifier = Modifier.height(12.dp))
    
    // 验证码
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(R.string.verification_code),
            color = Color.White,
            fontSize = 15.sp,
            modifier = Modifier.padding(bottom = 4.dp),
            textAlign = TextAlign.Start
        )
    }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // 验证码输入框
        OutlinedTextField(
            value = password,
            onValueChange = onPasswordChange,
            placeholder = { Text(stringResource(R.string.enter_verification_code), color = Color.White.copy(alpha = 0.5f)) },
            modifier = Modifier.weight(1f),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                unfocusedBorderColor = Color.White.copy(alpha = 0.3f),
                focusedBorderColor = Color.White,
                focusedTextColor = Color.White,
                unfocusedTextColor = Color.White
            ),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            shape = RoundedCornerShape(8.dp),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 获取验证码按钮
        OutlinedButton(
            onClick = {
                if (!isCountingDown && email.isNotEmpty()) {
                    onSendVerifyCode()
                }
            },
            modifier = Modifier.padding(top = 4.dp),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = buttonColor
            ),
            shape = RoundedCornerShape(8.dp),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.dp,
                color = buttonColor
            ),
            enabled = !isCountingDown && email.isNotEmpty()
        ) {
            Text(
                text = if (isCountingDown) {
                    stringResource(R.string.retry_in_seconds, countdownSeconds)
                } else {
                    stringResource(R.string.get_verification_code)
                },
                fontSize = 13.sp,
                color = buttonColor // 设置文字颜色与主题色一致
            )
        }
    }
} 