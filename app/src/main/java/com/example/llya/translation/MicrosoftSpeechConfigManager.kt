package com.example.llya.translation

import android.content.Context
import android.content.SharedPreferences

class MicrosoftSpeechConfigManager(private val context: Context) {
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "microsoft_speech_config", Context.MODE_PRIVATE
    )
    
    // 偏好设置的键
    private object PreferencesKeys {
        const val SUBSCRIPTION_KEY = "7lJ1e4p4LppCs62hMBvzyzxYv5vCOauJTzIKqnY3U0NVw7hFXiGaJQQJ99BEACYeBjFXJ3w3AAAYACOGuXCg"
        const val REGION = "eastus"
        const val TRANSLATION_KEY = "FAl1A2Eh06dVSf4HXfUhZuZphKpIMqt6jVL9i7cg1OlJ8XhZmwxAJQQJ99BEACULyCpXJ3w3AAAbACOGgW5l"
        const val TRANSLATION_REGION = "global"
    }
    
    /**
     * 获取配置
     */
    fun getConfig(): MicrosoftSpeechConfig {
        return MicrosoftSpeechConfig(
            subscriptionKey = prefs.getString(PreferencesKeys.SUBSCRIPTION_KEY, 
                MicrosoftSpeechConfig.DEFAULT_SUBSCRIPTION_KEY) ?: MicrosoftSpeechConfig.DEFAULT_SUBSCRIPTION_KEY,
            region = prefs.getString(PreferencesKeys.REGION, 
                MicrosoftSpeechConfig.DEFAULT_REGION) ?: MicrosoftSpeechConfig.DEFAULT_REGION,
            translationKey = prefs.getString(PreferencesKeys.TRANSLATION_KEY, null)
                ?: prefs.getString(PreferencesKeys.SUBSCRIPTION_KEY, MicrosoftSpeechConfig.DEFAULT_SUBSCRIPTION_KEY)
                ?: MicrosoftSpeechConfig.DEFAULT_SUBSCRIPTION_KEY,
            translationRegion = prefs.getString(PreferencesKeys.TRANSLATION_REGION, null)
                ?: prefs.getString(PreferencesKeys.REGION, MicrosoftSpeechConfig.DEFAULT_REGION)
                ?: MicrosoftSpeechConfig.DEFAULT_REGION
        )
    }
    
    /**
     * 更新配置
     */
    fun updateConfig(config: MicrosoftSpeechConfig) {
        prefs.edit().apply {
            putString(PreferencesKeys.SUBSCRIPTION_KEY, config.subscriptionKey)
            putString(PreferencesKeys.REGION, config.region)
            putString(PreferencesKeys.TRANSLATION_KEY, config.translationKey)
            putString(PreferencesKeys.TRANSLATION_REGION, config.translationRegion)
            apply()
        }
    }
} 