package com.example.llya.aliyunasi

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import okhttp3.*
import okio.ByteString
import org.json.JSONObject
import java.util.*
import java.util.concurrent.TimeUnit

class AliyunAsrClient(
    private val accessToken: String,
    private val appKey: String
) {
    companion object {
        private const val TAG = "AliyunAsrClient"
        private const val GATEWAY_URL = "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1"
    }

    private val client: OkHttpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    private var webSocket: WebSocket? = null
    private var isConnected = false
    private var transcriptionStarted = false
    private var taskId: String = UUID.randomUUID().toString().replace("-", "")
    private var audioDataSentCount = 0 // 添加计数器跟踪发送的音频包数量
    private var language: String = "zh_cn" // 默认中文普通话

    private val _recognitionResultFlow = MutableStateFlow<String>("")
    val recognitionResultFlow: StateFlow<String> = _recognitionResultFlow.asStateFlow()

    private val _connectionStatusFlow = MutableStateFlow<String>("未连接")
    val connectionStatusFlow: StateFlow<String> = _connectionStatusFlow.asStateFlow()

    private val _errorMessageFlow = MutableStateFlow<String>("")
    val errorMessageFlow: StateFlow<String> = _errorMessageFlow.asStateFlow()

    // 返回连接状态
    fun isConnected(): Boolean {
        return isConnected && transcriptionStarted
    }

    // 连接到阿里云语音识别服务
    fun connect() {
        if (isConnected) {
            Log.d(TAG, "已经连接到阿里云语音识别服务")
            return
        }

        // 严格按照阿里云文档，URL中需要加入token和appkey
        val url = "$GATEWAY_URL?token=$accessToken&appkey=$appKey"
        Log.d(TAG, "连接WebSocket URL: $url，使用语言: $language")
        
        val request = Request.Builder()
            .url(url)
            .build()

        _connectionStatusFlow.value = "正在连接..."

        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接已建立，响应码: ${response.code}，将使用语言: $language")
                isConnected = true
                _connectionStatusFlow.value = "已连接，准备开始转写"
                
                // 连接成功后，发送StartTranscription指令
                sendStartTranscriptionCommand()
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到文本消息: $text")
                handleTextMessage(text)
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                Log.d(TAG, "收到二进制消息，长度: ${bytes.size}")
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket正在关闭: $code, $reason")
                isConnected = false
                transcriptionStarted = false
                _connectionStatusFlow.value = "正在关闭连接"
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket已关闭: $code, $reason")
                isConnected = false
                transcriptionStarted = false
                _connectionStatusFlow.value = "连接已关闭"
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败: ${t.message}", t)
                isConnected = false
                transcriptionStarted = false
                _connectionStatusFlow.value = "连接失败"
                _errorMessageFlow.value = "连接失败: ${t.message}"
                
                // 打印更详细的错误信息
                response?.let {
                    Log.e(TAG, "错误状态码: ${it.code}, 消息: ${it.message}")
                    it.body?.let { body ->
                        try {
                            val errorBody = body.string()
                            Log.e(TAG, "错误详情: $errorBody")
                        } catch (e: Exception) {
                            Log.e(TAG, "无法读取错误详情", e)
                        }
                    }
                }
            }
        })
    }

    // 设置语音识别语言
    fun setLanguage(language: String) {
        this.language = language
        Log.d(TAG, "设置语音识别语言为: $language")
    }

    // 根据翻译源语言设置语音识别语言
    fun setLanguageByTranslationCode(translationCode: String) {
        // 添加详细日志，记录原始传入的翻译语言代码
        Log.d(TAG, "根据翻译语言代码设置语音识别语言，传入代码: $translationCode")
        
        val previousLanguage = this.language
        
        // 根据阿里云语音识别文档，实际上目前只支持中文（zh_cn）、英文（en_us）和粤语（zh_cantonese）
        // 参考：https://help.aliyun.com/zh/nls/developer-reference/speech-recognition-api
        when (translationCode) {
            "zh" -> this.language = "zh_cn" // 中文普通话
            "zh-tw" -> this.language = "zh_cn" // 中文台湾转为中文普通话
            "en" -> this.language = "en_us" // 英语(美国口音)
            // 以下语言实际不支持，但我们设置了日志提醒
            "ja" -> {
                this.language = "zh_cn" // 日语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持日语，已自动切换为中文")
            }
            "ko" -> {
                this.language = "zh_cn" // 韩语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持韩语，已自动切换为中文")
            }
            "fr" -> {
                this.language = "zh_cn" // 法语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持法语，已自动切换为中文") 
            }
            "es" -> {
                this.language = "zh_cn" // 西班牙语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持西班牙语，已自动切换为中文")
            }
            "it" -> {
                this.language = "zh_cn" // 意大利语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持意大利语，已自动切换为中文")
            }
            "de" -> {
                this.language = "zh_cn" // 德语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持德语，已自动切换为中文")
            }
            "ru" -> {
                this.language = "zh_cn" // 俄语不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持俄语，已自动切换为中文") 
            }
            "pt", "ar", "hi", "ms", "th", "vi", "tr" -> {
                this.language = "zh_cn" // 其他常用语言不支持，使用中文
                Log.w(TAG, "警告：阿里云语音识别不支持${translationCode}，已自动切换为中文")
            }
            else -> {
                this.language = "zh_cn" // 默认中文普通话
                Log.w(TAG, "警告：未知语言'${translationCode}'，使用默认的中文识别")
            }
        }
        
        // 记录语言转换结果
        if (translationCode != "zh" && translationCode != "en") {
            Log.d(TAG, "语言映射(注意：非中英语言将使用中文识别): 翻译语言'$translationCode' -> 语音识别语言'$language'")
        } else {
            Log.d(TAG, "语言映射: 翻译语言'$translationCode' -> 语音识别语言'$language'，之前为: '$previousLanguage'")
        }
    }

    // 发送开始转写的指令 - 严格按照阿里云文档格式
    private fun sendStartTranscriptionCommand() {
        if (!isConnected || webSocket == null) {
            Log.e(TAG, "未连接到WebSocket，无法发送开始转写指令")
            return
        }

        val messageId = UUID.randomUUID().toString().replace("-", "")
        
        // 检查语言设置，确保只使用阿里云支持的语言
        val actualLanguage = if (language == "zh_cn" || language == "en_us") {
            language
        } else {
            Log.w(TAG, "警告：使用了不支持的语言'$language'，自动切换为中文")
            "zh_cn"
        }
        
        Log.d(TAG, "准备发送StartTranscription指令，taskId: $taskId, messageId: $messageId, appKey: $appKey, language: $actualLanguage")
        
        // 创建基本配置
        val payloadJson = JSONObject().apply {
            put("format", "PCM")
            put("sample_rate", 16000)
            put("enable_intermediate_result", true)
            put("enable_punctuation_prediction", true)
            put("enable_inverse_text_normalization", true)
            put("enable_voice_detection", false)
            
            // 根据不同语言设置特定参数
            when (actualLanguage) {
                "zh_cn" -> {
                    put("language", "zh_cn")
                    // 中文特定配置
                    put("enable_itn", true) // 中文数字转换
                    Log.d(TAG, "已设置中文识别特定参数")
                }
                "en_us" -> {
                    put("language", "en_us")
                    // 英语特定配置
                    put("accent", "standard") // 标准口音，可选值包括"british"和"american"
                    Log.d(TAG, "已设置英语识别特定参数")
                }
                else -> {
                    // 默认使用中文
                    put("language", "zh_cn")
                    put("enable_itn", true)
                    Log.d(TAG, "使用默认参数（中文），language: $actualLanguage")
                }
            }
        }
        
        // 构建完整命令
        val command = JSONObject().apply {
            put("header", JSONObject().apply {
                put("message_id", messageId)
                put("task_id", taskId)
                put("namespace", "SpeechTranscriber")
                put("name", "StartTranscription")
                put("appkey", appKey)
            })
            put("payload", payloadJson)
        }.toString()

        Log.d(TAG, "发送StartTranscription命令: $command")
        
        CoroutineScope(Dispatchers.IO).launch {
            val sent = webSocket?.send(command) ?: false
            if (sent) {
                Log.d(TAG, "已发送开始转写指令，等待TranscriptionStarted事件，语言: $language")
                _connectionStatusFlow.value = "已发送开始转写指令，等待服务器响应"
            } else {
                Log.e(TAG, "发送开始转写指令失败")
                _errorMessageFlow.value = "发送开始转写指令失败"
            }
        }
    }

    // 发送结束转写的指令
    fun sendStopTranscriptionCommand() {
        if (!isConnected || webSocket == null) {
            Log.e(TAG, "未连接到WebSocket，无法发送结束转写指令")
            return
        }

        val messageId = UUID.randomUUID().toString().replace("-", "")
        
        val command = JSONObject().apply {
            put("header", JSONObject().apply {
                put("message_id", messageId)
                put("task_id", taskId)
                put("namespace", "SpeechTranscriber")
                put("name", "StopTranscription")
            })
            put("payload", JSONObject())
        }.toString()

        CoroutineScope(Dispatchers.IO).launch {
            webSocket?.send(command)
            Log.d(TAG, "已发送结束转写指令: $command")
        }
    }

    // 发送音频数据 - 严格在TranscriptionStarted事件后发送
    fun sendAudioData(audioData: ByteArray) {
        if (!isConnected || webSocket == null) {
            Log.e(TAG, "未连接到WebSocket，无法发送音频数据")
            return
        }
        
        // 只有收到TranscriptionStarted事件后才发送音频数据
        if (!transcriptionStarted) {
            if (Math.random() < 0.05) { // 减少日志输出，每20次只输出一次
                Log.d(TAG, "等待TranscriptionStarted事件，暂不发送音频数据")
            }
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val byteString = ByteString.of(*audioData)
                val success = webSocket?.send(byteString) ?: false
                
                if (success) {
                    audioDataSentCount++
                    
                    // 每20个包记录一次发送状态
                    if (audioDataSentCount % 20 == 0) {
                        Log.d(TAG, "已成功发送 $audioDataSentCount 个音频数据包，大小: ${audioData.size} 字节")
                    }
                } else {
                    Log.e(TAG, "音频数据发送失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送音频数据失败", e)
                _errorMessageFlow.value = "发送音频数据失败: ${e.message}"
            }
        }
    }

    // 处理文本消息
    private fun handleTextMessage(text: String) {
        try {
            val json = JSONObject(text)
            val header = json.getJSONObject("header")
            val name = header.getString("name")
            
            Log.d(TAG, "处理服务器事件: $name")
            
            when (name) {
                "TranscriptionStarted" -> {
                    // 处理转写开始事件 - 关键事件，收到后才能发送音频
                    Log.d(TAG, "收到TranscriptionStarted事件，开始发送音频数据")
                    transcriptionStarted = true
                    audioDataSentCount = 0 // 重置计数器
                    _connectionStatusFlow.value = "转写已开始，正在识别语音..."
                }
                "TranscriptionResultChanged" -> {
                    // 处理中间识别结果
                    val payload = json.getJSONObject("payload")
                    val result = payload.getString("result")
                    Log.d(TAG, "收到中间识别结果: $result")
                    _recognitionResultFlow.value = result
                }
                "SentenceEnd" -> {
                    // 处理一句话结束的事件
                    val payload = json.getJSONObject("payload")
                    val result = payload.getString("result")
                    Log.d(TAG, "收到句子结束事件，结果: $result")
                    _recognitionResultFlow.value = result
                }
                "TranscriptionCompleted" -> {
                    // 处理转写完成事件
                    Log.d(TAG, "转写已完成")
                    _connectionStatusFlow.value = "转写已完成"
                }
                "TaskFailed" -> {
                    // 处理任务失败事件
                    val statusText = header.getString("status_text")
                    Log.e(TAG, "任务失败: $statusText")
                    _errorMessageFlow.value = "任务失败: $statusText"
                    isConnected = false
                    transcriptionStarted = false
                    _connectionStatusFlow.value = "任务失败"
                }
                else -> {
                    Log.d(TAG, "收到未处理的事件: $name，完整消息: $text")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理文本消息失败", e)
            _errorMessageFlow.value = "处理文本消息失败: ${e.message}"
        }
    }

    // 关闭连接
    fun close() {
        if (isConnected && webSocket != null) {
            // 先发送停止转写指令
            sendStopTranscriptionCommand()
            
            // 然后关闭WebSocket
            webSocket?.close(1000, "正常关闭")
            webSocket = null
            isConnected = false
            transcriptionStarted = false
            _connectionStatusFlow.value = "未连接"
        }
    }
} 