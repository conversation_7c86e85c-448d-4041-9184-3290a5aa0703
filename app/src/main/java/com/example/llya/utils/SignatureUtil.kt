package com.example.llya.utils

import android.util.Base64
import android.util.Log
import org.apache.commons.codec.binary.Hex
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.security.InvalidKeyException
import java.security.NoSuchAlgorithmException
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.random.Random

object SignatureUtil {
    
    private const val TAG = "SignatureUtil"
    
    /**
     * 生成腾讯云API签名
     * @param secretId 密钥ID
     * @param secretKey 密钥Key
     * @param requestUrl 请求URL路径
     * @param params 请求参数
     * @return 经过URLEncode的签名
     */
    fun generateSignature(
        secretId: String,
        secretKey: String,
        requestUrl: String,
        params: Map<String, String>
    ): String {
        try {
            // 1. 将参数按key排序并拼接成字符串
            val sortedParams = params.toSortedMap()
            val paramStr = sortedParams.entries.joinToString("&") { (key, value) ->
                "$key=$value"
            }
            
            // 2. 拼接请求URL和参数
            val signStr = "$requestUrl?$paramStr"
            
            // 输出日志，方便调试
            Log.d(TAG, "待签名字符串: $signStr")
            
            // 3. 使用HmacSHA1算法计算签名
            val mac = Mac.getInstance("HmacSHA1")
            val secretKeySpec = SecretKeySpec(secretKey.toByteArray(StandardCharsets.UTF_8), "HmacSHA1")
            mac.init(secretKeySpec)
            val signBytes = mac.doFinal(signStr.toByteArray(StandardCharsets.UTF_8))
            
            // 4. Base64编码
            val base64Sign = android.util.Base64.encodeToString(signBytes, android.util.Base64.NO_WRAP)
            
            // 5. URL编码
            val encodedSign = URLEncoder.encode(base64Sign, "UTF-8")
            
            // 输出日志
            Log.d(TAG, "生成的签名: $encodedSign")
            
            return encodedSign
        } catch (e: NoSuchAlgorithmException) {
            Log.e(TAG, "算法不存在: ${e.message}", e)
            e.printStackTrace()
        } catch (e: InvalidKeyException) {
            Log.e(TAG, "无效的密钥: ${e.message}", e)
            e.printStackTrace()
        } catch (e: Exception) {
            Log.e(TAG, "签名生成异常: ${e.message}", e)
            e.printStackTrace()
        }
        return ""
    }
    
    /**
     * 生成腾讯云翻译API签名 - 使用最简单直接的方式
     * @param secretId 密钥ID
     * @param secretKey 密钥Key
     * @param host 主机域名
     * @param params 请求参数
     * @return 签名字符串
     */
    fun generateTranslationSignature(
        secretId: String,
        secretKey: String,
        host: String,
        params: Map<String, String>
    ): String {
        try {
            // 基本参数
            val timestamp = params["Timestamp"] ?: (System.currentTimeMillis() / 1000).toString()
            val service = "tmt"
            val algorithm = "TC3-HMAC-SHA256"
            
            // 使用SimpleDateFormat直接生成YYYYMMDD格式的日期字符串
            val dateFormat = java.text.SimpleDateFormat("yyyyMMdd")
            dateFormat.timeZone = java.util.TimeZone.getTimeZone("UTC")
            val date = dateFormat.format(java.util.Date(timestamp.toLong() * 1000))
            
            // 凭证范围
            val credentialScope = "${date}/${service}/tc3_request"
            
            // 构造规范请求串
            val httpRequestMethod = "POST"
            val canonicalUri = "/"
            val canonicalQueryString = ""
            val canonicalHeaders = "content-type:application/json\nhost:${host}\nx-tc-action:texttranslate\n"
            val signedHeaders = "content-type;host;x-tc-action"
            
            // 计算请求体哈希值
            val payload = """{"ProjectId":0,"Source":"${params["Source"]}","Target":"${params["Target"]}","SourceText":"${params["SourceText"]}"}"""
            val hashedRequestPayload = sha256Hex(payload.toByteArray()).lowercase()
            
            // 构造规范请求串
            val canonicalRequest = "${httpRequestMethod}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\n${hashedRequestPayload}"
            val hashedCanonicalRequest = sha256Hex(canonicalRequest.toByteArray()).lowercase()
            
            // 构造待签名字符串
            val stringToSign = "${algorithm}\n${timestamp}\n${credentialScope}\n${hashedCanonicalRequest}"
            
            // 计算签名密钥
            val secretDateKey = hmacSHA256(("TC3" + secretKey).toByteArray(), date.toByteArray())
            val secretServiceKey = hmacSHA256(secretDateKey, service.toByteArray())
            val secretSigningKey = hmacSHA256(secretServiceKey, "tc3_request".toByteArray())
            
            // 计算签名
            val signature = bytesToHex(hmacSHA256(secretSigningKey, stringToSign.toByteArray())).lowercase()
            
            // 构造授权头 - 确保格式完全正确
            val authorization = "${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}"
            
            // 输出详细日志
            Log.d(TAG, "==== 签名详情 ====")
            Log.d(TAG, "时间戳: ${timestamp}")
            Log.d(TAG, "UTC日期(YYYYMMDD): ${date}")
            Log.d(TAG, "凭证范围: ${credentialScope}")
            Log.d(TAG, "规范请求串: ${canonicalRequest}")
            Log.d(TAG, "待签名字符串: ${stringToSign}")
            Log.d(TAG, "签名: ${signature}")
            Log.d(TAG, "最终授权头: ${authorization}")
            Log.d(TAG, "==================")
            
            return authorization
            
        } catch (e: Exception) {
            Log.e(TAG, "生成翻译签名异常: ${e.message}", e)
            e.printStackTrace()
            return ""
        }
    }
    
    /**
     * 计算HMAC-SHA256
     */
    private fun hmacSHA256(key: ByteArray, data: ByteArray): ByteArray {
        val mac = Mac.getInstance("HmacSHA256")
        val secretKeySpec = SecretKeySpec(key, "HmacSHA256")
        mac.init(secretKeySpec)
        return mac.doFinal(data)
    }
    
    /**
     * 计算SHA256哈希值
     */
    private fun sha256Hex(data: ByteArray): String {
        val md = java.security.MessageDigest.getInstance("SHA-256")
        val digest = md.digest(data)
        return bytesToHex(digest)
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private fun bytesToHex(bytes: ByteArray): String {
        val hexArray = "0123456789abcdef".toCharArray()
        val hexChars = CharArray(bytes.size * 2)
        for (j in bytes.indices) {
            val v = bytes[j].toInt() and 0xFF
            hexChars[j * 2] = hexArray[v ushr 4]
            hexChars[j * 2 + 1] = hexArray[v and 0x0F]
        }
        return String(hexChars)
    }
    
    /**
     * 生成随机字符串作为nonce
     */
    fun generateNonce(): String {
        return Random.nextInt(100000, 999999).toString()
    }
    
    /**
     * 生成UUID作为voice_id
     */
    fun generateVoiceId(): String {
        val uuid = java.util.UUID.randomUUID().toString().replace("-", "")
        return uuid
    }
} 