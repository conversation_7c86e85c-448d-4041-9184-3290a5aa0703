package com.example.llya.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory

/**
 * 文本持久化管理器
 * 支持单个会议记录和会议记录历史列表
 */
class TextPersistenceManager(private val context: Context) {
    
    companion object {
        private const val TAG = "TextPersistenceManager"
        private const val MEETING_RECORD_FILENAME = "meeting_record.txt"
        private const val MEETING_HISTORY_FILENAME = "meeting_history.json"
    }
    
    // 使用Moshi进行JSON序列化/反序列化
    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()
    
    // 会议记录数据类
    data class MeetingRecord(
        val id: String = UUID.randomUUID().toString(),
        val timestamp: Long = System.currentTimeMillis(),
        val title: String,
        val content: String,
        val duration: Long = 0
    )
    
    /**
     * 保存会议记录文本（当前会话）
     */
    fun saveText(text: String) {
        try {
            val file = File(context.filesDir, MEETING_RECORD_FILENAME)
            
            // 创建文件输出流
            val fos = FileOutputStream(file)
            fos.write(text.toByteArray())
            fos.close()
            
            Log.d(TAG, "会议记录已保存到文件: ${file.absolutePath}")
        } catch (e: Exception) {
            Log.e(TAG, "保存会议记录失败: ${e.message}", e)
        }
    }
    
    /**
     * 读取当前会议记录文本
     * 如果文件不存在，返回空字符串
     */
    fun loadText(): String {
        try {
            val file = File(context.filesDir, MEETING_RECORD_FILENAME)
            if (!file.exists()) {
                Log.d(TAG, "会议记录文件不存在，返回空文本")
                return ""
            }
            
            // 创建文件输入流
            val fis = FileInputStream(file)
            val bytes = ByteArray(file.length().toInt())
            fis.read(bytes)
            fis.close()
            
            val text = String(bytes)
            Log.d(TAG, "已从文件加载会议记录: ${file.absolutePath}, 内容长度: ${text.length}")
            return text
        } catch (e: Exception) {
            Log.e(TAG, "读取会议记录失败: ${e.message}", e)
            return ""
        }
    }
    
    /**
     * 清空当前会议记录
     */
    fun clearText() {
        try {
            val file = File(context.filesDir, MEETING_RECORD_FILENAME)
            if (file.exists()) {
                file.delete()
                Log.d(TAG, "会议记录文件已删除: ${file.absolutePath}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除会议记录文件失败: ${e.message}", e)
        }
    }
    
    /**
     * 将新文本追加到已有的会议记录中
     */
    fun appendText(newText: String) {
        try {
            if (newText.isEmpty()) return
            
            val file = File(context.filesDir, MEETING_RECORD_FILENAME)
            
            // 如果文件不存在，则创建新文件
            if (!file.exists()) {
                saveText(newText)
                return
            }
            
            // 读取现有内容以确定是否需要添加
            val existingText = loadText()
            
            // 检查是否重复添加
            if (existingText.endsWith(newText)) {
                Log.d(TAG, "文本已存在，跳过追加: $newText")
                return
            }
            
            // 使用FileWriter的追加模式
            val writer = FileWriter(file, true)
            
            // 直接追加文本，不添加换行符
            writer.write(newText)
            
            writer.flush()
            writer.close()
            
            Log.d(TAG, "新文本已追加到会议记录: $newText")
        } catch (e: Exception) {
            Log.e(TAG, "追加文本失败: ${e.message}", e)
        }
    }
    
    /**
     * 将当前会议记录保存为历史记录
     * @param title 会议记录标题
     * @param duration 会议持续时间(毫秒)
     * @return 是否保存成功
     */
    fun saveCurrentMeetingToHistory(title: String = "", duration: Long = 0): Boolean {
        try {
            // 读取当前会议记录
            val content = loadText()
            if (content.isEmpty()) {
                Log.d(TAG, "当前没有会议记录，不保存到历史")
                return false
            }
            
            // 生成默认标题
            val actualTitle = if (title.isEmpty()) {
                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                val dateStr = dateFormat.format(Date())
                "会议记录 $dateStr"
            } else {
                title
            }
            
            // 创建会议记录对象
            val meetingRecord = MeetingRecord(
                title = actualTitle,
                content = content,
                duration = duration
            )
            
            // 读取现有历史记录
            val historyRecords = loadAllMeetingRecords().toMutableList()
            
            // 添加新记录
            historyRecords.add(meetingRecord)
            
            // 保存更新后的历史记录
            val type = Types.newParameterizedType(List::class.java, MeetingRecord::class.java)
            val adapter: JsonAdapter<List<MeetingRecord>> = moshi.adapter(type)
            val json = adapter.toJson(historyRecords)
            
            val historyFile = File(context.filesDir, MEETING_HISTORY_FILENAME)
            FileOutputStream(historyFile).use { it.write(json.toByteArray()) }
            
            Log.d(TAG, "会议记录已保存到历史: $actualTitle, ID: ${meetingRecord.id}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "保存会议记录到历史失败: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 获取所有历史会议记录
     * @return 会议记录列表，按时间倒序排列
     */
    fun loadAllMeetingRecords(): List<MeetingRecord> {
        try {
            val historyFile = File(context.filesDir, MEETING_HISTORY_FILENAME)
            if (!historyFile.exists()) {
                Log.d(TAG, "历史记录文件不存在，返回空列表")
                return emptyList()
            }
            
            val json = FileInputStream(historyFile).use { 
                val bytes = ByteArray(historyFile.length().toInt())
                it.read(bytes)
                String(bytes)
            }
            
            val type = Types.newParameterizedType(List::class.java, MeetingRecord::class.java)
            val adapter: JsonAdapter<List<MeetingRecord>> = moshi.adapter(type)
            val records = adapter.fromJson(json) ?: emptyList()
            
            // 按时间倒序排列，最新的记录排在前面
            return records.sortedByDescending { it.timestamp }
        } catch (e: Exception) {
            Log.e(TAG, "读取历史会议记录失败: ${e.message}", e)
            return emptyList()
        }
    }
    
    /**
     * 根据ID获取特定会议记录
     * @param id 会议记录ID
     * @return 会议记录，如果不存在则返回null
     */
    fun getMeetingRecordById(id: String): MeetingRecord? {
        Log.d(TAG, "正在查找会议记录，ID: $id")
        val allRecords = loadAllMeetingRecords()
        Log.d(TAG, "已加载所有会议记录，共${allRecords.size}条")
        
        val record = allRecords.find { it.id == id }
        if (record != null) {
            Log.d(TAG, "找到会议记录: ${record.title}, 内容长度: ${record.content.length}")
        } else {
            Log.e(TAG, "未找到ID为 $id 的会议记录")
        }
        
        return record
    }
    
    /**
     * 删除特定的会议记录
     * @param id 要删除的会议记录ID
     * @return 是否删除成功
     */
    fun deleteMeetingRecord(id: String): Boolean {
        try {
            val records = loadAllMeetingRecords().toMutableList()
            val initialSize = records.size
            
            // 移除指定ID的记录
            records.removeIf { it.id == id }
            
            if (records.size == initialSize) {
                Log.d(TAG, "未找到指定ID的会议记录: $id")
                return false
            }
            
            // 保存更新后的记录
            val type = Types.newParameterizedType(List::class.java, MeetingRecord::class.java)
            val adapter: JsonAdapter<List<MeetingRecord>> = moshi.adapter(type)
            val json = adapter.toJson(records)
            
            val historyFile = File(context.filesDir, MEETING_HISTORY_FILENAME)
            FileOutputStream(historyFile).use { it.write(json.toByteArray()) }
            
            Log.d(TAG, "已删除会议记录: $id")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "删除会议记录失败: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 加载特定会议记录到当前会话
     * @param id 会议记录ID
     * @return 是否加载成功
     */
    fun loadMeetingRecordToCurrent(id: String): Boolean {
        try {
            val record = getMeetingRecordById(id) ?: return false
            
            // 将记录内容保存为当前会话
            saveText(record.content)
            
            Log.d(TAG, "已加载会议记录到当前会话: ${record.title}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "加载会议记录到当前会话失败: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 清空所有会议历史记录
     * @return 是否清空成功
     */
    fun clearAllMeetingRecords(): Boolean {
        try {
            val historyFile = File(context.filesDir, MEETING_HISTORY_FILENAME)
            if (historyFile.exists()) {
                val result = historyFile.delete()
                Log.d(TAG, "已清空所有会议历史记录, 结果: $result")
                return result
            }
            return true // 如果文件不存在，认为清空成功
        } catch (e: Exception) {
            Log.e(TAG, "清空所有会议历史记录失败: ${e.message}", e)
            return false
        }
    }
} 