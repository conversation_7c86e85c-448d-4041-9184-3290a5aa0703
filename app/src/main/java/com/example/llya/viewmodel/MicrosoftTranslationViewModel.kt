package com.example.llya.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.translation.MicrosoftSpeechConfig
import com.example.llya.translation.MicrosoftSpeechRecognizer
import com.example.llya.translation.MicrosoftTextToSpeech
import com.example.llya.translation.MicrosoftSpeechConfigManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 微软语音翻译ViewModel
 * 管理语音识别、翻译和TTS状态
 */
class MicrosoftTranslationViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "MicrosoftTranslationVM"
    }

    // 语音服务配置
    private val speechConfig = MicrosoftSpeechConfig()
    
    // 配置管理器
    private val configManager = MicrosoftSpeechConfigManager(application.applicationContext)

    // 语音识别器
    private val speechRecognizer = MicrosoftSpeechRecognizer(
        context = application.applicationContext,
        config = configManager.getConfig()
    )

    // 文本到语音转换
    private val textToSpeech = MicrosoftTextToSpeech(
        config = configManager.getConfig()
    )

    // 是否正在识别
    private val _isRecognizing = MutableStateFlow(false)
    val isRecognizing: StateFlow<Boolean> = _isRecognizing.asStateFlow()

    // 识别结果
    private val _recognitionResult = MutableStateFlow("")
    val recognitionResult: StateFlow<String> = _recognitionResult.asStateFlow()

    // 翻译结果
    private val _translationResult = MutableStateFlow("")
    val translationResult: StateFlow<String> = _translationResult.asStateFlow()

    // 当前检测到的语言
    private val _detectedLanguage = MutableStateFlow<String?>(null)
    val detectedLanguage: StateFlow<String?> = _detectedLanguage.asStateFlow()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 源语言
    private val _sourceLanguage = MutableStateFlow("zh-CN")
    val sourceLanguage: StateFlow<String> = _sourceLanguage.asStateFlow()

    // 目标语言
    private val _targetLanguage = MutableStateFlow("en-US")
    val targetLanguage: StateFlow<String> = _targetLanguage.asStateFlow()

    // 翻译历史记录
    private val _translationHistory = MutableStateFlow<List<TranslationHistoryItem>>(emptyList())
    val translationHistory: StateFlow<List<TranslationHistoryItem>> = _translationHistory.asStateFlow()

    // 是否启用自动播放TTS
    private val _autoPlayTTS = MutableStateFlow(true)
    val autoPlayTTS: StateFlow<Boolean> = _autoPlayTTS.asStateFlow()

    // 是否自动检测语言
    private val _autoDetectLanguage = MutableStateFlow(true)
    val autoDetectLanguage: StateFlow<Boolean> = _autoDetectLanguage.asStateFlow()

    // 是否启用互译模式
    private val _bidirectionalMode = MutableStateFlow(false)
    val bidirectionalMode: StateFlow<Boolean> = _bidirectionalMode.asStateFlow()

    // 语言检测置信度
    private val _detectionConfidence = MutableStateFlow<Float?>(null)
    val detectionConfidence: StateFlow<Float?> = _detectionConfidence.asStateFlow()

    // 语言检测敏感度设置 (0: 高灵敏度, 1: 中等灵敏度, 2: 低灵敏度)
    private val _sensitivityLevel = MutableStateFlow(0)
    val sensitivityLevel: StateFlow<Int> = _sensitivityLevel.asStateFlow()

    // 累积的识别文本（用于显示完整对话历史）
    private val _accumulatedText = MutableStateFlow("")
    val accumulatedText: StateFlow<String> = _accumulatedText.asStateFlow()

    // 当前会话的识别历史
    private val recognitionHistory = mutableListOf<String>()

    // 可用的语言列表
    val availableLanguages = MicrosoftSpeechConfig.SUPPORTED_LANGUAGES

    // 翻译历史项
    data class TranslationHistoryItem(
        val sourceText: String,
        val translatedText: String,
        val sourceLanguage: String,
        val targetLanguage: String,
        val detectedLanguage: String? = null,
        val timestamp: String = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
    )

    init {
        // 监听识别结果
        viewModelScope.launch {
            speechRecognizer.recognitionResult.collectLatest { result ->
                if (result.isNotEmpty()) {
                    _recognitionResult.value = result
                }
            }
        }

        // 监听翻译结果
        viewModelScope.launch {
            speechRecognizer.translationResult.collectLatest { result ->
                if (result.isNotEmpty()) {
                    _translationResult.value = result
                    
                    // 如果启用了自动播放TTS，则播放翻译结果
                    if (_autoPlayTTS.value && result.isNotEmpty()) {
                        // 根据当前模式确定TTS语言
                        val ttsLanguage = if (_bidirectionalMode.value) {
                            // 互译模式下，根据检测到的语言决定TTS语言
                            getEffectiveTTSLanguage()
                        } else {
                            _targetLanguage.value
                        }
                        textToSpeech.speak(result, ttsLanguage)
                    }
                }
            }
        }

        // 监听检测到的语言
        viewModelScope.launch {
            speechRecognizer.detectedLanguage.collectLatest { language ->
                if (language != null) {
                    val previousLanguage = _detectedLanguage.value
                    _detectedLanguage.value = language
                    
                    // 记录语言切换事件
                    if (previousLanguage != null && previousLanguage != language) {
                        Log.d(TAG, "语言已切换: $previousLanguage -> $language")
                        
                        // 在互译模式下，语言切换时可能需要更新历史记录
                        if (_bidirectionalMode.value) {
                            updateHistoryOnLanguageSwitch(previousLanguage, language)
                        }
                    }
                    
                    // 如果是自动检测语言模式，更新源语言
                    if (_autoDetectLanguage.value) {
                        _sourceLanguage.value = language
                    }
                }
            }
        }

        // 监听错误信息
        viewModelScope.launch {
            speechRecognizer.errorMessage.collectLatest { errorMsg ->
                if (errorMsg != null) {
                    _errorMessage.value = errorMsg
                }
            }
        }

        // 监听识别状态
        viewModelScope.launch {
            speechRecognizer.isRecognizing.collectLatest { isRecognizing ->
                _isRecognizing.value = isRecognizing
            }
        }
    }

    /**
     * 开始识别和翻译
     */
    fun startTranslation() {
        if (_isRecognizing.value) {
            Log.d(TAG, "已经在识别中，忽略请求")
            return
        }
        
        // 清除上一次的结果
        _recognitionResult.value = ""
        _translationResult.value = ""
        _detectedLanguage.value = null
        
        // 清空当前会话的历史记录
        recognitionHistory.clear()
        _accumulatedText.value = ""
        
        try {
            // 获取当前敏感度阈值
            val sensitivityThreshold = when (_sensitivityLevel.value) {
                0 -> MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_HIGH
                1 -> MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_MEDIUM
                else -> MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_LOW
            }
            
            Log.d(TAG, "使用语言检测敏感度: ${_sensitivityLevel.value}, 阈值: $sensitivityThreshold")
            
            if (_bidirectionalMode.value) {
                // 使用互译模式 - 自动检测源语言或目标语言，并翻译成另一种语言
                Log.d(TAG, "启动互译模式 - 源语言: ${_sourceLanguage.value}, 目标语言: ${_targetLanguage.value}")
                speechRecognizer.startBidirectionalTranslation(
                    _sourceLanguage.value, 
                    _targetLanguage.value,
                    sensitivityThreshold
                )
            } else if (_autoDetectLanguage.value) {
                // 使用自动语言检测模式
                Log.d(TAG, "启动自动语言检测模式 - 目标语言: ${_targetLanguage.value}")
                speechRecognizer.startAutoDetectTranslation(
                    _targetLanguage.value,
                    sensitivityThreshold
                )
            } else {
                // 使用指定源语言和目标语言模式
                Log.d(TAG, "启动普通翻译模式 - 源语言: ${_sourceLanguage.value}, 目标语言: ${_targetLanguage.value}")
                speechRecognizer.startTranslation(_sourceLanguage.value, _targetLanguage.value)
            }
        } catch (e: Exception) {
            Log.e(TAG, "开始翻译时出错", e)
            _errorMessage.value = "开始翻译时出错: ${e.message}"
        }
    }

    /**
     * 停止识别和翻译
     */
    fun stopTranslation() {
        if (!_isRecognizing.value) {
            Log.d(TAG, "没有正在进行的识别，忽略停止请求")
            return
        }
        
        try {
            speechRecognizer.stopRecognition()
            
            // 如果有有效的识别和翻译结果，添加到历史记录
            val sourceText = _recognitionResult.value
            val translatedText = _translationResult.value
            
            if (sourceText.isNotEmpty() && translatedText.isNotEmpty()) {
                val historyItem = TranslationHistoryItem(
                    sourceText = sourceText,
                    translatedText = translatedText,
                    sourceLanguage = _sourceLanguage.value,
                    targetLanguage = _targetLanguage.value,
                    detectedLanguage = _detectedLanguage.value
                )
                
                // 添加到历史记录的开头
                _translationHistory.value = listOf(historyItem) + _translationHistory.value
                
                // 更新累积文本
                updateAccumulatedText(sourceText, translatedText)
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止翻译时出错", e)
            _errorMessage.value = "停止翻译时出错: ${e.message}"
        }
    }

    /**
     * 更新累积文本显示
     */
    private fun updateAccumulatedText(sourceText: String, translatedText: String) {
        val detectedLang = _detectedLanguage.value
        val sourceLangName = availableLanguages.find { it.first == (detectedLang ?: _sourceLanguage.value) }?.second ?: "未知"
        val targetLangName = availableLanguages.find { it.first == _targetLanguage.value }?.second ?: "未知"
        
        val displayText = "[$sourceLangName] $sourceText\n⟹ [$targetLangName] $translatedText"
        
        synchronized(recognitionHistory) {
            recognitionHistory.add(displayText)
            _accumulatedText.value = recognitionHistory.joinToString("\n\n")
        }
    }

    /**
     * 在语言切换时更新历史记录
     */
    private fun updateHistoryOnLanguageSwitch(previousLanguage: String, newLanguage: String) {
        // 在互译模式下，语言切换可能意味着对话方向的改变
        // 这里可以添加特殊的标记或处理逻辑
        Log.d(TAG, "互译模式下语言切换: $previousLanguage -> $newLanguage")
    }

    /**
     * 获取有效的TTS语言
     * 在互译模式下根据检测到的语言和翻译方向确定TTS语言
     */
    private fun getEffectiveTTSLanguage(): String {
        if (!_bidirectionalMode.value) {
            return _targetLanguage.value
        }
        
        val detectedLanguage = _detectedLanguage.value ?: _sourceLanguage.value
        
        // 如果检测到的语言是源语言，TTS使用目标语言
        // 如果检测到的语言是目标语言，TTS使用源语言
        val detectedShortCode = if (detectedLanguage.contains("-")) {
            detectedLanguage.split("-")[0]
        } else detectedLanguage
        
        val sourceShortCode = if (_sourceLanguage.value.contains("-")) {
            _sourceLanguage.value.split("-")[0]
        } else _sourceLanguage.value
        
        return if (detectedShortCode.equals(sourceShortCode, ignoreCase = true)) {
            _targetLanguage.value
        } else {
            _sourceLanguage.value
        }
    }

    /**
     * 添加实时识别结果到累积文本
     * 用于在识别过程中实时显示结果
     */
    fun addRealtimeResult(sourceText: String, translatedText: String) {
        if (sourceText.isNotEmpty() && translatedText.isNotEmpty()) {
            updateAccumulatedText(sourceText, translatedText)
        }
    }

    /**
     * 清除当前会话的累积文本
     */
    fun clearAccumulatedText() {
        recognitionHistory.clear()
        _accumulatedText.value = ""
    }

    /**
     * 获取支持的语言组合
     */
    fun getSupportedLanguageCombinations(): Map<String, List<String>> {
        return MicrosoftSpeechConfig.COMMON_LANGUAGE_GROUPS
    }

    /**
     * 设置语言组合
     */
    fun setLanguageCombination(groupName: String) {
        val languages = MicrosoftSpeechConfig.COMMON_LANGUAGE_GROUPS[groupName]
        if (languages != null && languages.size >= 2) {
            _sourceLanguage.value = languages[0]
            _targetLanguage.value = languages[1]
            Log.d(TAG, "设置语言组合 $groupName: ${languages[0]} -> ${languages[1]}")
        }
    }

    /**
     * 检查文本是否应该被处理
     * 与MicrosoftSpeechRecognizer中的逻辑保持一致
     */
    private fun shouldProcessText(text: String): Boolean {
        return text.length > 2 && !text.matches(Regex("^[\\p{Punct}\\s]+$"))
    }

    /**
     * 切换源语言和目标语言
     */
    fun switchLanguages() {
        val tempSourceLang = _sourceLanguage.value
        _sourceLanguage.value = _targetLanguage.value
        _targetLanguage.value = tempSourceLang
    }

    /**
     * 设置源语言
     */
    fun setSourceLanguage(languageCode: String) {
        _sourceLanguage.value = languageCode
    }

    /**
     * 设置目标语言
     */
    fun setTargetLanguage(languageCode: String) {
        _targetLanguage.value = languageCode
    }

    /**
     * 设置自动播放TTS
     */
    fun setAutoPlayTTS(enable: Boolean) {
        _autoPlayTTS.value = enable
    }

    /**
     * 设置自动检测语言
     */
    fun setAutoDetectLanguage(enable: Boolean) {
        _autoDetectLanguage.value = enable
    }

    /**
     * 播放源语言文本
     */
    fun speakSourceText(text: String? = null) {
        val textToSpeak = text ?: _recognitionResult.value
        if (textToSpeak.isNotEmpty()) {
            textToSpeech.speak(textToSpeak, _sourceLanguage.value)
        }
    }

    /**
     * 播放目标语言文本
     */
    fun speakTargetText(text: String? = null) {
        val textToSpeak = text ?: _translationResult.value
        if (textToSpeak.isNotEmpty()) {
            textToSpeech.speak(textToSpeak, _targetLanguage.value)
        }
    }

    /**
     * 清除历史记录
     */
    fun clearHistory() {
        _translationHistory.value = emptyList()
    }

    /**
     * 添加错误信息
     */
    fun addErrorMessage(message: String) {
        _errorMessage.value = message
    }

    /**
     * 清除错误信息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * 更新配置
     */
    fun updateConfig(subscriptionKey: String, region: String) {
        val config = MicrosoftSpeechConfig(
            subscriptionKey = subscriptionKey,
            region = region
        )
        
        configManager.updateConfig(config)
        textToSpeech.updateConfig(config)
    }

    /**
     * 设置互译模式
     * @param enabled 是否启用互译模式
     */
    fun setBidirectionalMode(enabled: Boolean) {
        _bidirectionalMode.value = enabled
        // 互译模式下自动启用自动检测语言
        if (enabled) {
            _autoDetectLanguage.value = true
        }
    }

    /**
     * 交换源语言和目标语言
     */
    fun swapLanguages() {
        val temp = _sourceLanguage.value
        _sourceLanguage.value = _targetLanguage.value
        _targetLanguage.value = temp
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        speechRecognizer.release()
        textToSpeech.release()
    }

    /**
     * 获取语言检测信息
     * 返回包含语言名称和检测模式的描述信息
     */
    fun getLanguageDetectionInfo(): String {
        val detectedLang = _detectedLanguage.value ?: return "等待检测语言..."
        
        val langName = availableLanguages.find { it.first == detectedLang }?.second ?: detectedLang
        
        val modeDesc = when {
            _bidirectionalMode.value -> "互译模式"
            _autoDetectLanguage.value -> "自动检测"
            else -> "固定语言"
        }
        
        return "检测到: $langName ($modeDesc)"
    }

    /**
     * 设置语言检测敏感度
     * @param level 敏感度级别 (0: 高, 1: 中, 2: 低)
     */
    fun setLanguageDetectionSensitivity(level: Int) {
        if (level in 0..2) {
            _sensitivityLevel.value = level
            
            // 获取对应的敏感度阈值
            val threshold = when (level) {
                0 -> MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_HIGH
                1 -> MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_MEDIUM
                else -> MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_LOW
            }
            
            Log.d(TAG, "设置语言检测敏感度: $level, 阈值: $threshold")
            
            // 更新语音识别器的敏感度设置
            // 注意：这里只是保存设置，需要重新启动识别才会生效
        }
    }

    /**
     * 获取当前语言检测敏感度描述
     */
    fun getLanguageDetectionSensitivityDescription(): String {
        return when (_sensitivityLevel.value) {
            0 -> "高灵敏度 (快速切换)"
            1 -> "中等灵敏度 (平衡)"
            else -> "低灵敏度 (减少误判)"
        }
    }
} 