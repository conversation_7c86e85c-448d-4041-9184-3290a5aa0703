# 语音文字翻译API移动端使用示例

本文档提供Android和iOS平台使用语音文字翻译API的具体代码示例。

## Android 示例

### 1. 初始化API服务

```java
// SpeechApiService.java
package com.example.speechapp.api;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Query;

public interface SpeechApiService {
    @GET("api/health")
    Call<HealthResponse> checkHealth();
    
    @Multipart
    @POST("api/speech-to-text")
    Call<SpeechToTextResponse> speechToText(
        @Part MultipartBody.Part audio,
        @Part("languageCode") RequestBody languageCode
    );
    
    @POST("api/text-to-speech")
    Call<ResponseBody> textToSpeech(@Body TextToSpeechRequest request);
    
    @POST("api/translate")
    Call<TranslateResponse> translateText(@Body TranslateRequest request);
    
    @GET("api/speech-languages")
    Call<LanguagesResponse> getSpeechLanguages();
    
    @GET("api/translation-languages")
    Call<LanguagesResponse> getTranslationLanguages();
    
    @GET("api/voices")
    Call<VoicesResponse> getVoices(@Query("languageCode") String languageCode);
}

// ApiClient.java
package com.example.speechapp.api;

import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class ApiClient {
    private static final String BASE_URL = "https://your-api-domain.com/";
    private static Retrofit retrofit = null;
    
    public static Retrofit getClient() {
        if (retrofit == null) {
            OkHttpClient client = new OkHttpClient.Builder().build();
            
            retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .client(client)
                .build();
        }
        return retrofit;
    }
    
    public static SpeechApiService getApiService() {
        return getClient().create(SpeechApiService.class);
    }
}
```

### 2. 响应模型类

```java
// 语音识别响应
public class SpeechToTextResponse {
    private boolean success;
    private String text;
    private String languageCode;
    private String model;
    private String error;
    
    // Getters and setters
}

// 翻译响应
public class TranslateResponse {
    private boolean success;
    private String translatedText;
    private String detectedSourceLanguage;
    private String targetLanguage;
    private String error;
    
    // Getters and setters
}

// 文字转语音请求
public class TextToSpeechRequest {
    private String text;
    private String languageCode;
    private String name;
    private String ssmlGender;
    private String audioEncoding;
    private float pitch;
    private float speakingRate;
    
    // Constructor, getters and setters
}
```

### 3. 语音识别示例

```java
import android.Manifest;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SpeechRecognitionActivity extends AppCompatActivity {
    
    private Button startRecordButton;
    private Button stopRecordButton;
    private TextView resultTextView;
    private AudioRecord audioRecord;
    private boolean isRecording = false;
    private File audioFile;
    
    private static final int PERMISSION_REQUEST_CODE = 200;
    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_speech_recognition);
        
        startRecordButton = findViewById(R.id.startRecordButton);
        stopRecordButton = findViewById(R.id.stopRecordButton);
        resultTextView = findViewById(R.id.resultTextView);
        
        startRecordButton.setOnClickListener(v -> {
            if (checkPermission()) {
                startRecording();
            } else {
                requestPermission();
            }
        });
        
        stopRecordButton.setOnClickListener(v -> {
            stopRecording();
            uploadAudio();
        });
    }
    
    private boolean checkPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    private void requestPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.RECORD_AUDIO}, 
                PERMISSION_REQUEST_CODE);
    }
    
    private void startRecording() {
        isRecording = true;
        
        int minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
        audioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC, SAMPLE_RATE, 
                CHANNEL_CONFIG, AUDIO_FORMAT, minBufferSize);
        
        audioFile = new File(getCacheDir(), "audio_record.pcm");
        
        audioRecord.startRecording();
        startRecordButton.setEnabled(false);
        stopRecordButton.setEnabled(true);
        
        new Thread(() -> {
            writeAudioDataToFile();
        }).start();
    }
    
    private void writeAudioDataToFile() {
        byte[] data = new byte[AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT)];
        FileOutputStream fos = null;
        
        try {
            fos = new FileOutputStream(audioFile);
            
            while (isRecording) {
                int read = audioRecord.read(data, 0, data.length);
                if (read != AudioRecord.ERROR_INVALID_OPERATION) {
                    fos.write(data, 0, read);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    private void stopRecording() {
        if (audioRecord != null) {
            isRecording = false;
            audioRecord.stop();
            audioRecord.release();
            audioRecord = null;
            startRecordButton.setEnabled(true);
            stopRecordButton.setEnabled(false);
        }
    }
    
    private void uploadAudio() {
        resultTextView.setText("识别中...");
        
        // 创建文件请求部分
        RequestBody requestFile = RequestBody.create(MediaType.parse("audio/raw"), audioFile);
        MultipartBody.Part body = MultipartBody.Part.createFormData("audio", audioFile.getName(), requestFile);
        
        // 创建语言代码部分
        RequestBody languageCode = RequestBody.create(MediaType.parse("text/plain"), "zh-CN");
        
        // 发送识别请求
        SpeechApiService apiService = ApiClient.getApiService();
        Call<SpeechToTextResponse> call = apiService.speechToText(body, languageCode);
        
        call.enqueue(new Callback<SpeechToTextResponse>() {
            @Override
            public void onResponse(Call<SpeechToTextResponse> call, Response<SpeechToTextResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    SpeechToTextResponse result = response.body();
                    if (result.isSuccess()) {
                        resultTextView.setText(result.getText());
                    } else {
                        resultTextView.setText("识别失败: " + result.getError());
                    }
                } else {
                    resultTextView.setText("请求失败: " + response.code());
                }
            }
            
            @Override
            public void onFailure(Call<SpeechToTextResponse> call, Throwable t) {
                resultTextView.setText("网络错误: " + t.getMessage());
            }
        });
    }
}
```

### 4. 文字转语音示例

```java
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TextToSpeechActivity extends AppCompatActivity {
    
    private EditText textInput;
    private Button convertButton;
    private MediaPlayer mediaPlayer;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_text_to_speech);
        
        textInput = findViewById(R.id.textInput);
        convertButton = findViewById(R.id.convertButton);
        
        convertButton.setOnClickListener(v -> {
            String text = textInput.getText().toString().trim();
            if (!text.isEmpty()) {
                convertTextToSpeech(text);
            } else {
                Toast.makeText(this, "请输入文本", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private void convertTextToSpeech(String text) {
        convertButton.setEnabled(false);
        
        TextToSpeechRequest request = new TextToSpeechRequest();
        request.setText(text);
        request.setLanguageCode("cmn-CN");
        request.setName("cmn-CN-Standard-A");
        request.setSsmlGender("FEMALE");
        request.setAudioEncoding("MP3");
        
        SpeechApiService apiService = ApiClient.getApiService();
        Call<ResponseBody> call = apiService.textToSpeech(request);
        
        call.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (response.isSuccessful() && response.body() != null) {
                    try {
                        File audioFile = new File(getCacheDir(), "tts_output.mp3");
                        FileOutputStream fos = new FileOutputStream(audioFile);
                        fos.write(response.body().bytes());
                        fos.close();
                        
                        playAudio(audioFile);
                        
                    } catch (IOException e) {
                        Toast.makeText(TextToSpeechActivity.this, 
                                "音频处理错误: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(TextToSpeechActivity.this, 
                            "请求失败: " + response.code(), Toast.LENGTH_SHORT).show();
                }
                convertButton.setEnabled(true);
            }
            
            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                Toast.makeText(TextToSpeechActivity.this, 
                        "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                convertButton.setEnabled(true);
            }
        });
    }
    
    private void playAudio(File audioFile) {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release();
            }
            
            mediaPlayer = new MediaPlayer();
            mediaPlayer.setAudioAttributes(
                    new AudioAttributes.Builder()
                            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                            .setUsage(AudioAttributes.USAGE_MEDIA)
                            .build()
            );
            
            mediaPlayer.setDataSource(audioFile.getPath());
            mediaPlayer.prepare();
            mediaPlayer.start();
            
        } catch (IOException e) {
            Toast.makeText(this, "播放错误: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mediaPlayer != null) {
            mediaPlayer.release();
            mediaPlayer = null;
        }
    }
}
```

### 5. 文本翻译示例

```java
import android.os.Bundle;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TranslationActivity extends AppCompatActivity {
    
    private EditText textInput;
    private Spinner targetLanguageSpinner;
    private Button translateButton;
    private TextView translatedTextView;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_translation);
        
        textInput = findViewById(R.id.textInput);
        targetLanguageSpinner = findViewById(R.id.targetLanguageSpinner);
        translateButton = findViewById(R.id.translateButton);
        translatedTextView = findViewById(R.id.translatedTextView);
        
        // 加载语言列表
        loadLanguages();
        
        translateButton.setOnClickListener(v -> {
            String text = textInput.getText().toString().trim();
            if (!text.isEmpty()) {
                String targetLanguage = targetLanguageSpinner.getSelectedItem().toString().split(" - ")[0];
                translateText(text, targetLanguage);
            } else {
                Toast.makeText(this, "请输入文本", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private void loadLanguages() {
        SpeechApiService apiService = ApiClient.getApiService();
        Call<LanguagesResponse> call = apiService.getTranslationLanguages();
        
        call.enqueue(new Callback<LanguagesResponse>() {
            @Override
            public void onResponse(Call<LanguagesResponse> call, Response<LanguagesResponse> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<String> languageItems = new ArrayList<>();
                    
                    for (Language language : response.body().getLanguages()) {
                        languageItems.add(language.getCode() + " - " + language.getName());
                    }
                    
                    ArrayAdapter<String> adapter = new ArrayAdapter<>(
                            TranslationActivity.this,
                            android.R.layout.simple_spinner_item,
                            languageItems
                    );
                    adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
                    targetLanguageSpinner.setAdapter(adapter);
                    
                    // 默认选择英语
                    for (int i = 0; i < languageItems.size(); i++) {
                        if (languageItems.get(i).startsWith("en ")) {
                            targetLanguageSpinner.setSelection(i);
                            break;
                        }
                    }
                    
                } else {
                    Toast.makeText(TranslationActivity.this, 
                            "获取语言列表失败", Toast.LENGTH_SHORT).show();
                }
            }
            
            @Override
            public void onFailure(Call<LanguagesResponse> call, Throwable t) {
                Toast.makeText(TranslationActivity.this, 
                        "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private void translateText(String text, String targetLanguage) {
        translateButton.setEnabled(false);
        translatedTextView.setText("翻译中...");
        
        TranslateRequest request = new TranslateRequest();
        request.setText(text);
        request.setSource("auto");
        request.setTarget(targetLanguage);
        
        SpeechApiService apiService = ApiClient.getApiService();
        Call<TranslateResponse> call = apiService.translateText(request);
        
        call.enqueue(new Callback<TranslateResponse>() {
            @Override
            public void onResponse(Call<TranslateResponse> call, Response<TranslateResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    TranslateResponse result = response.body();
                    if (result.isSuccess()) {
                        translatedTextView.setText(result.getTranslatedText());
                    } else {
                        translatedTextView.setText("翻译失败: " + result.getError());
                    }
                } else {
                    translatedTextView.setText("请求失败: " + response.code());
                }
                translateButton.setEnabled(true);
            }
            
            @Override
            public void onFailure(Call<TranslateResponse> call, Throwable t) {
                translatedTextView.setText("网络错误: " + t.getMessage());
                translateButton.setEnabled(true);
            }
        });
    }
}
```
