package com.example.llya.network

import com.example.llya.data.ApiResponse
import com.example.llya.data.DeviceModel
import com.example.llya.data.LanguageModel
import com.example.llya.data.UserModel
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST

/**
 * 用户API服务接口
 */
interface ApiService {
    /**
     * 修改用户信息
     */
    @FormUrlEncoded
    @POST("/api/user/update")
    suspend fun updateUserInfo(
        @Field("username") username: String,
        @Field("email") email: String,
        @Field("userType") userType: Int,
        @Field("status") status: Int? = null,
        @Field("vipExpireTime") vipExpireTime: String? = null
    ): ApiResponse<Any>

    /**
     * 用户账号注销
     */
    @FormUrlEncoded
    @POST("/api/user/accountCancellation")
    suspend fun accountCancellation(
        @Field("password") password: String
    ): ApiResponse<Any>

    /**
     * 查看用户详情
     */
    @POST("/api/user/detail")
    suspend fun getUserDetail(): ApiResponse<UserModel>

    /**
     * 发送手机验证码
     */
    @FormUrlEncoded
    @POST("/api/user/sendSms")
    suspend fun sendSms(
        @Field("mobile") mobile: String
    ): ApiResponse<Any>

    /**
     * 发送邮箱验证码
     */
    @FormUrlEncoded
    @POST("/api/user/sendEmailCode")
    suspend fun sendEmailCode(
        @Field("email") email: String
    ): ApiResponse<Any>

    /**
     * 验证邮箱验证码
     */
    @FormUrlEncoded
    @POST("/api/user/verifyEmailCode")
    suspend fun verifyEmailCode(
        @Field("email") email: String,
        @Field("code") code: String,
        @Field("key") key: String
    ): ApiResponse<Any>

    /**
     * 用户登录(手机)
     */
    @FormUrlEncoded
    @POST("/api/user/login")
    suspend fun login(
        @Field("mobile") mobile: String,
        @Field("verify_id") verifyId: String,
        @Field("verify") verify: String
    ): ApiResponse<UserModel>

    /**
     * 邮箱登录
     */
    @FormUrlEncoded
    @POST("/api/user/email_login")
    suspend fun emailLogin(
        @Field("email") email: String,
        @Field("verify_id") verifyId: String,
        @Field("verify") verify: String
    ): ApiResponse<UserModel>

    /**
     * 获取用户绑定设备
     */
    @POST("/api/user/getUserDevices")
    suspend fun getUserDevices(): ApiResponse<List<DeviceModel>>

    /**
     * 获取语言列表
     * 由于API可能返回不同结构的数据，这里使用Any作为泛型参数
     * 实际返回格式可能是：
     * 1. 分页数据对象: {"total": N, "per_page": M, "current_page": 1, "data": [语言列表]}
     * 2. 直接包含data的对象: {"data": [语言列表]}
     * 3. 直接返回语言列表数组: [{语言1}, {语言2}, ...]
     */
    @GET("/api/language/index")
    suspend fun getLanguageList(): ApiResponse<Any>
} 