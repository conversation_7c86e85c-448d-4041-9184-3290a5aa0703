package com.example.llya.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.example.llya.data.UserModel
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 用户信息缓存工具类
 * 
 * 用于在应用内全局缓存用户信息和token，并提供持久化存储
 */
object UserCache {
    private const val PREF_NAME = "user_cache"
    private const val KEY_TOKEN = "user_token"
    private const val KEY_USER_JSON = "user_json"
    private const val KEY_USER_ID = "user_id" 
    private const val KEY_USERNAME = "username"
    private const val KEY_EMAIL = "email"
    private const val KEY_LOGIN_TIME = "login_time"
    private const val KEY_IS_LOGGED_IN = "is_logged_in"
    
    private lateinit var sharedPreferences: SharedPreferences
    private val gson = Gson()
    private val mainScope = MainScope()
    
    // 当前登录用户信息（内存缓存）
    private val _currentUser = MutableLiveData<UserModel?>(null)
    val currentUser: LiveData<UserModel?> = _currentUser
    
    // 当前token（内存缓存）
    private var _token: String = ""
    
    // 登录时间
    private var _loginTime: Long = 0
    
    /**
     * 初始化缓存
     */
    fun initialize(context: Context) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        loadFromPreferences()
    }
    
    /**
     * 从SharedPreferences加载数据
     */
    private fun loadFromPreferences() {
        val isLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
        Log.d("UserCache", "从SharedPreferences加载登录状态: $isLoggedIn")
        
        if (isLoggedIn) {
            _token = sharedPreferences.getString(KEY_TOKEN, "") ?: ""
            _loginTime = sharedPreferences.getLong(KEY_LOGIN_TIME, 0)
            Log.d("UserCache", "加载的Token长度: ${_token.length}, 登录时间: $_loginTime")
            
            // 验证token是否过期
            if (_loginTime > 0 && TokenManager.isTokenExpired(_loginTime)) {
                Log.d("UserCache", "检测到Token已过期，清除登录状态")
                clearUserInfo()
                return
            }
            
            // 尝试从JSON加载完整用户信息
            val userJson = sharedPreferences.getString(KEY_USER_JSON, null)
            if (userJson != null) {
                try {
                    val user = gson.fromJson(userJson, UserModel::class.java)
                    Log.d("UserCache", "从JSON成功加载用户信息: ${user.username}")
                    // 使用主线程更新 LiveData
                    mainScope.launch(Dispatchers.Main) {
                        _currentUser.value = user
                    }
                } catch (e: Exception) {
                    Log.e("UserCache", "JSON解析失败: ${e.message}")
                    // JSON解析失败，尝试从单独字段构建用户信息
                    loadUserFromBasicFields()
                }
            } else {
                Log.d("UserCache", "没有找到用户JSON，尝试从基本字段加载")
                // 没有JSON，尝试从单独字段构建用户信息
                loadUserFromBasicFields()
            }
        } else {
            Log.d("UserCache", "用户未登录")
        }
    }
    
    /**
     * 从基本字段构建用户信息
     */
    private fun loadUserFromBasicFields() {
        val userId = sharedPreferences.getInt(KEY_USER_ID, 0)
        val username = sharedPreferences.getString(KEY_USERNAME, "") ?: ""
        val email = sharedPreferences.getString(KEY_EMAIL, "") ?: ""
        
        Log.d("UserCache", "从基本字段加载: userId=$userId, username=$username, email=$email")
        
        if (userId > 0) {
            val user = UserModel(
                userId = userId,
                username = username,
                email = email
            )
            // 使用主线程更新 LiveData
            mainScope.launch(Dispatchers.Main) {
                _currentUser.value = user
                Log.d("UserCache", "用户基本信息加载完成")
            }
        } else {
            Log.d("UserCache", "userId无效，无法构建用户模型")
        }
    }
    
    /**
     * 保存登录信息
     */
    fun saveLoginInfo(user: UserModel?, token: String) {
        Log.d("UserCache", "开始保存登录信息: user=${user != null}, token长度=${token.length}")
        
        // 使用同步块确保线程安全
        synchronized(this) {
            // 更新内存中的数据
            mainScope.launch(Dispatchers.Main) {
                _currentUser.value = user
            }
            _token = token
            _loginTime = System.currentTimeMillis()
            
            val editor = sharedPreferences.edit()
            
            // 始终保存token相关信息，无论用户是否为null
            editor.putString(KEY_TOKEN, token)
            editor.putLong(KEY_LOGIN_TIME, _loginTime)
            editor.putBoolean(KEY_IS_LOGGED_IN, token.isNotEmpty())
            
            if (user != null) {
                try {
                    // 保存完整的用户JSON
                    val userJson = gson.toJson(user)
                    editor.putString(KEY_USER_JSON, userJson)
                    
                    // 同时保存关键字段，便于快速访问或恢复
                    editor.putInt(KEY_USER_ID, user.userId)
                    editor.putString(KEY_USERNAME, user.username)
                    editor.putString(KEY_EMAIL, user.email)
                    
                    Log.d("UserCache", "保存用户信息: userId=${user.userId}, username=${user.username}, loginTime=$_loginTime")
                } catch (e: Exception) {
                    Log.e("UserCache", "保存用户JSON失败: ${e.message}")
                    // JSON序列化失败，只保存基本字段
                    editor.putInt(KEY_USER_ID, user.userId)
                    editor.putString(KEY_USERNAME, user.username)
                    editor.putString(KEY_EMAIL, user.email)
                }
            } else {
                // 用户为null时清除用户相关字段，但保留token
                Log.d("UserCache", "用户为空，清除用户字段，保留token")
                editor.remove(KEY_USER_JSON)
                editor.remove(KEY_USER_ID)
                editor.remove(KEY_USERNAME)
                editor.remove(KEY_EMAIL)
            }
            
            // 使用commit()而不是apply()，确保同步写入
            val success = editor.commit()
            Log.d("UserCache", "登录信息保存${if(success) "成功" else "失败"}")
        }
    }
    
    /**
     * 清除用户信息
     */
    fun clearUserInfo() {
        Log.d("UserCache", "清除用户信息")
        
        // 使用主线程更新 LiveData
        mainScope.launch(Dispatchers.Main) {
            _currentUser.value = null
        }
        _token = ""
        _loginTime = 0
        
        val editor = sharedPreferences.edit()
        editor.clear()
        editor.putBoolean(KEY_IS_LOGGED_IN, false)
        editor.apply()
        
        Log.d("UserCache", "用户信息已从内存和SharedPreferences中清除")
    }
    
    /**
     * 获取登录token
     */
    fun getToken(): String {
        return _token
    }
    
    /**
     * 获取登录时间
     */
    fun getLoginTime(): Long {
        return _loginTime
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(): Boolean {
        // 先检查内存中的状态
        val memoryLoggedIn = _currentUser.value != null && _token.isNotEmpty()
        
        // 如果内存中显示未登录，再检查SharedPreferences
        if (!memoryLoggedIn && ::sharedPreferences.isInitialized) {
            val storedLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
            val storedToken = sharedPreferences.getString(KEY_TOKEN, "") ?: ""
            
            Log.d("UserCache", "内存中未登录，检查SharedPreferences: 登录状态=$storedLoggedIn, Token=${if (storedToken.isEmpty()) "空" else "非空"}")
            
            // 如果SharedPreferences中显示已登录但内存中未加载，尝试重新加载
            if (storedLoggedIn && storedToken.isNotEmpty()) {
                // 检查token是否已过期
                val loginTime = sharedPreferences.getLong(KEY_LOGIN_TIME, 0)
                if (loginTime > 0 && !TokenManager.isTokenExpired(loginTime)) {
                    Log.d("UserCache", "检测到有效的持久化登录状态，重新加载")
                    loadFromPreferences()
                    // 重新检查内存中的状态
                    return _currentUser.value != null && _token.isNotEmpty()
                } else {
                    Log.d("UserCache", "持久化的Token已过期，清除登录状态")
                    clearUserInfo()
                    return false
                }
            }
        }
        
        Log.d("UserCache", "最终登录状态检查结果: $memoryLoggedIn")
        return memoryLoggedIn
    }
    
    /**
     * 检查token是否有效（未过期）
     */
    fun isTokenValid(customTtl: Long? = null): Boolean {
        if (!isLoggedIn()) {
            Log.d("UserCache", "未登录，token无效")
            return false
        }
        
        // 如果没有登录时间记录，视为无效
        if (_loginTime <= 0) {
            Log.d("UserCache", "登录时间无效: $_loginTime")
            return false
        }
        
        val ttl = customTtl ?: TokenManager.DEFAULT_TOKEN_EXPIRE_TIME
        val isExpired = TokenManager.isTokenExpired(_loginTime, ttl)
        
        Log.d("UserCache", "检查token有效性: 登录时间=$_loginTime, TTL=$ttl, 是否过期=$isExpired")
        return !isExpired
    }
    
    /**
     * 获取当前用户ID
     */
    fun getUserId(): Int {
        return _currentUser.value?.userId ?: 0
    }
    
    /**
     * 获取当前用户名
     */
    fun getUsername(): String {
        return _currentUser.value?.username ?: ""
    }
    
    /**
     * 获取当前用户邮箱
     */
    fun getEmail(): String {
        return _currentUser.value?.email ?: ""
    }
} 