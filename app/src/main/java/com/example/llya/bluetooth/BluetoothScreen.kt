package com.example.llya.bluetooth

import android.Manifest
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothGattService
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.outlined.Info
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.MultiplePermissionsState
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.LifecycleOwner
import android.util.Log
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.MainScope
import com.example.llya.bluetooth.ConnectionState
import com.example.llya.bluetooth.BluetoothUiState
import com.example.llya.R
import androidx.compose.animation.core.*
import com.example.llya.utils.LocalLifecycleOwner

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun BluetoothScreen(
    viewModel: BluetoothViewModel = viewModel(),
    fromUnbind: Boolean = false,  // 新增参数，表示是否从解绑页面跳转而来
    onBluetoothConnected: (() -> Unit)? = null,
    onNoDeviceDetected: (() -> Unit)? = null,
    onNavigateToMain: (() -> Unit)? = null,
    onNavigateToEarbudsDetail: (() -> Unit)? = null
) {
    val context = LocalContext.current
    
    // 获取LifecycleOwner
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // 应用状态
    val state by viewModel.state.collectAsStateWithLifecycle()
    var showDeviceDetails by remember { mutableStateOf(false) }
    var checkCount by remember { mutableStateOf(0) }
    
    // 获取状态消息
    val statusMessage by viewModel.statusMessage.collectAsStateWithLifecycle()
    // 获取设备绑定状态
    val isDeviceBinding by viewModel.isDeviceBinding.collectAsStateWithLifecycle()
    
    // 当蓝牙连接成功时自动跳转或未找到设备时跳转到NoDeviceScreen
    LaunchedEffect(state.connectionState, state.connectedDevice) {
        // 先检查蓝牙连接状态
        if (state.connectionState == ConnectionState.CONNECTED && state.connectedDevice != null) {
            // 蓝牙设备已连接，记录日志
            Log.d("BluetoothScreen", "检测到蓝牙设备已连接: ${state.connectedDevice?.name ?: "未知设备"}")
            
            // 确认设备是确实连接的，不是状态不一致
            viewModel.verifyDeviceConnection { isReallyConnected ->
                if (isReallyConnected) {
                    // 延迟一点时间，让用户看到连接成功状态
                    MainScope().launch {
                        delay(500)
                        // 回调函数不为空时执行跳转
                        onBluetoothConnected?.invoke()
                        // 重置检查计数
                        checkCount = 0
                    }
                } else {
                    Log.d("BluetoothScreen", "⚠️警告: 设备显示为已连接，但验证失败")
                    // 刷新状态
                    viewModel.refreshOnResume()
                }
            }
        } else if (state.connectionState == ConnectionState.DISCONNECTED && state.connectedDevice == null && !fromUnbind) {
            // 蓝牙设备未连接
            Log.d("BluetoothScreen", "蓝牙设备未连接或未找到设备")
            
            // 只有在不是从解绑页面跳转来时，才执行未找到设备后跳回NoDeviceScreen的逻辑
            // 设备断开连接，且未找到设备时，先重试几次
            if (checkCount < 2) {
                checkCount++ // 增加检查计数
                Log.d("BluetoothScreen", "未发现连接设备，第${checkCount}次重试")
                viewModel.refreshOnResume() // 重新检查连接状态
                delay(1500) // 延迟等待检查完成
            } else {
                // 多次检查后仍未发现设备，跳转到NoDeviceScreen
                Log.d("BluetoothScreen", "多次检查后未发现连接设备，跳转到NoDeviceScreen")
                onNoDeviceDetected?.invoke()
            }
        } else if (state.connectionState == ConnectionState.CONNECTING) {
            // 正在连接中，仅记录日志
            Log.d("BluetoothScreen", "正在连接蓝牙设备...")
        }
    }
    
    // 应用启动时直接触发刷新
    LaunchedEffect(Unit) {
        Log.d("BluetoothScreen", "页面初始化，先检测蓝牙连接状态")
        // 首先刷新并检测蓝牙连接状态
        viewModel.refreshOnResume()
    }
    
    // 设备绑定状态检测
    // 添加LaunchedEffect监听状态消息变化，当绑定成功时才跳转
    LaunchedEffect(statusMessage) {
        val message = statusMessage
        if (message != null && message.contains("成功")) {
            // 绑定成功时，检查蓝牙连接状态
            Log.d("BluetoothScreen", "设备绑定成功，检查蓝牙连接状态")
            
            // 设备绑定成功，确认蓝牙设备是否连接
            var isReallyConnected = false
            // 只进行两次检测，减少延迟
            for (i in 1..2) {
                // 短时间内刷新蓝牙状态
                viewModel.refreshOnResume()
                delay(150) // 减少延迟时间
                
                val currentState = state.connectionState
                val currentDevice = state.connectedDevice
                
                Log.d("BluetoothScreen", "检查 #$i - 蓝牙状态: $currentState, 设备: ${currentDevice?.name ?: "无"}")
                
                if (currentState == ConnectionState.CONNECTED && currentDevice != null) {
                    isReallyConnected = true
                    break
                }
            }
            
            if (isReallyConnected) {
                Log.d("BluetoothScreen", "确认蓝牙设备已连接，准备跳转到耳机详情页面")
                
                // 等待一小段时间确保UI更新
                delay(300)
                
                // 再次检查蓝牙状态，确保设备仍然连接
                viewModel.refreshOnResume() 
                delay(200)
                
                // 最终确认连接状态
                val finalState = state.connectionState
                val finalDevice = state.connectedDevice
                
                if (finalState == ConnectionState.CONNECTED && finalDevice != null) {
                    Log.d("BluetoothScreen", "最终确认: 蓝牙确实已连接，跳转到耳机详情页面")
                    // 跳转到耳机详情页面
                    onNavigateToEarbudsDetail?.invoke()
                    // 清除状态消息，防止重复跳转
                    viewModel.clearStatusMessage()
                } else {
                    // 蓝牙状态异常
                    Log.d("BluetoothScreen", "⚠️警告: 最终确认发现蓝牙未连接，取消跳转")
                    viewModel.updateStatusMessage("蓝牙设备已断开连接，请重新连接")
                }
            } else {
                // 设备已绑定但蓝牙未连接，提示用户
                Log.d("BluetoothScreen", "确认蓝牙设备未连接，不跳转")
                viewModel.updateStatusMessage("设备绑定成功，但蓝牙设备未连接，请先连接蓝牙设备")
            }
        }
    }
    
    // 如果是从解绑页面跳转过来的，显示适当的提示
    LaunchedEffect(fromUnbind) {
        if (fromUnbind) {
            Log.d("BluetoothScreen", "从设备解绑页面跳转而来，不会自动跳回NoDeviceScreen")
        }
    }
    
    // 跳转系统设置状态
    val shouldOpenSystemSettings by viewModel.shouldOpenSystemSettings.collectAsStateWithLifecycle()
    
    // 监听是否应该打开系统设置
    LaunchedEffect(shouldOpenSystemSettings) {
        if (shouldOpenSystemSettings) {
            // 跳转到系统蓝牙设置
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
            context.startActivity(intent)
            // 重置状态
            viewModel.systemSettingsOpened()
        }
    }
    
    // 应用恢复到前台时刷新状态
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 当应用恢复到前台时刷新状态
                Log.d("BluetoothScreen", "应用回到前台，刷新蓝牙状态")
                viewModel.refreshOnResume()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    
    // 提供ViewModel给CompositionLocal
    CompositionLocalProvider(LocalViewModelProvider provides viewModel) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFF1E1D2B)),
            verticalArrangement = Arrangement.Top
        ) {
            // 主体内容
            Box(
                modifier = Modifier
                    .fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                // 已连接设备详情
                if (state.connectedDevice != null && state.connectionState == ConnectionState.CONNECTED && showDeviceDetails) {
                    DeviceDetailView(
                        connectedDevice = state.connectedDevice!!,
                        deviceInfo = state.deviceInfo,
                        services = state.discoveredServices,
                        onBackClick = { showDeviceDetails = false },
                        onDisconnect = {
                            // 断开连接时引导用户去系统设置
                            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                            context.startActivity(intent)
                        }
                    )
                }
                // 连接状态页面
                else {
                    SimplifiedBluetoothStatusScreen(
                        state = state,
                        onViewDetails = { 
                            if (state.connectedDevice != null) {
                                showDeviceDetails = true 
                            }
                        },
                        onNavigateToMain = onNavigateToMain,
                        onNavigateToEarbudsDetail = onNavigateToEarbudsDetail
                    )
                }
            }
        }
    }
}

// 简化的蓝牙状态屏幕，只显示当前连接的设备信息
@Composable
fun SimplifiedBluetoothStatusScreen(
    state: BluetoothUiState,
    onViewDetails: () -> Unit,
    onNavigateToMain: (() -> Unit)? = null,
    onNavigateToEarbudsDetail: (() -> Unit)? = null
) {
    val context = LocalContext.current
    val viewModel = LocalViewModelProvider.current
    
    // 记录UI状态日志
    LaunchedEffect(state.connectionState, state.connectedDevice) {
        Log.d("BluetoothScreen", "UI状态更新: connectionState=${state.connectionState}, " +
              "connectedDevice=${state.connectedDevice?.name ?: "无"}, " +
              "deviceAddress=${state.connectedDevice?.address ?: "无"}")
    }
    
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 空白顶部空间
        Spacer(modifier = Modifier.height(24.dp))
        
        // 中间部分 - 扫描动画
        Box(
            modifier = Modifier.weight(1f),
            contentAlignment = Alignment.Center
        ) {
            // 同心圆扫描动画
            Box(
                modifier = Modifier.size(250.dp),
                contentAlignment = Alignment.Center
            ) {
                // 创建无限循环的动画
                val infiniteTransition = rememberInfiniteTransition()
                
                // 第一层动画 - 最外圈
                val outerCircleScale by infiniteTransition.animateFloat(
                    initialValue = 0.95f,
                    targetValue = 1.05f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(2000, easing = LinearEasing),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                val outerCircleAlpha by infiniteTransition.animateFloat(
                    initialValue = 0.7f,
                    targetValue = 1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(2000, easing = LinearEasing),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                // 第二层动画 - 中间圈
                val middleCircleScale by infiniteTransition.animateFloat(
                    initialValue = 0.9f,
                    targetValue = 1.1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1800, easing = LinearEasing, delayMillis = 200),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                val middleCircleAlpha by infiniteTransition.animateFloat(
                    initialValue = 0.6f,
                    targetValue = 0.9f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1800, easing = LinearEasing, delayMillis = 200),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                // 第三层动画 - 内层圈
                val innerCircleScale by infiniteTransition.animateFloat(
                    initialValue = 0.85f,
                    targetValue = 1.15f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1600, easing = LinearEasing, delayMillis = 400),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                val innerCircleAlpha by infiniteTransition.animateFloat(
                    initialValue = 0.5f,
                    targetValue = 0.8f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1600, easing = LinearEasing, delayMillis = 400),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                // 最外层圆
                Box(
                    modifier = Modifier
                        .size(250.dp * outerCircleScale)
                        .clip(CircleShape)
                        .background(Color(0xFF2A2940).copy(alpha = outerCircleAlpha))
                )
                
                // 中层圆
                Box(
                    modifier = Modifier
                        .size(180.dp * middleCircleScale)
                        .clip(CircleShape)
                        .background(Color(0xFF3A3962).copy(alpha = middleCircleAlpha))
                )
                
                // 内层圆
                Box(
                    modifier = Modifier
                        .size(110.dp * innerCircleScale)
                        .clip(CircleShape)
                        .background(Color(0xFF4C4A88).copy(alpha = innerCircleAlpha))
                )
                
                // 最内层（蓝牙图标）- 脉动效果
                val centerPulse by infiniteTransition.animateFloat(
                    initialValue = 0.95f,
                    targetValue = 1.05f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(800, easing = FastOutSlowInEasing),
                        repeatMode = RepeatMode.Reverse
                    )
                )
                
                Box(
                    modifier = Modifier
                        .size(70.dp * centerPulse)
                        .clip(CircleShape)
                        .background(Color(0xFF8771FF)),
                    contentAlignment = Alignment.Center
                ) {
                    // 蓝牙图标
                    Icon(
                        painter = painterResource(id = R.drawable.bluetooth_icon),
                        contentDescription = "蓝牙图标",
                        tint = Color.White,
                        modifier = Modifier.size(30.dp)
                    )
                }
            }
        }
        
        // 底部按钮区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 设备信息卡片
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF33324A)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "设备状态",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    if (state.connectedDevice != null) {
                        val device = state.connectedDevice
                        val deviceName = device.name ?: "OWS1002" 
                        val deviceAddress = formatMacAddress(device.address)
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 设备图标
                            Box(
                                modifier = Modifier
                                    .size(50.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFFFF9800)),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = deviceName.first().toString(),
                                    color = Color.White,
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                            
                            // 设备信息
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 16.dp)
                            ) {
                                Text(
                                    text = deviceName,
                                    color = Color.White,
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold
                                )
                                
                                Text(
                                    text = deviceAddress,
                                    color = Color.White.copy(alpha = 0.7f),
                                    fontSize = 14.sp
                                )
                                
                                // 连接状态
                                Text(
                                    text = when(state.connectionState) {
                                        ConnectionState.CONNECTED -> "已连接"
                                        ConnectionState.CONNECTING -> "连接中..."
                                        ConnectionState.DISCONNECTED -> "未连接"
                                    },
                                    color = when(state.connectionState) {
                                        ConnectionState.CONNECTED -> Color(0xFF4CAF50)
                                        ConnectionState.CONNECTING -> Color(0xFFFFC107)
                                        ConnectionState.DISCONNECTED -> Color(0xFFFF5722)
                                    },
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Bold
                                )
                                
                                // 设备绑定状态
                                val isDeviceBinding by viewModel.isDeviceBinding.collectAsStateWithLifecycle()
                                if (isDeviceBinding) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.padding(top = 4.dp)
                                    ) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(14.dp),
                                            color = Color(0xFF8771FF),
                                            strokeWidth = 2.dp
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text(
                                            text = "正在绑定设备...",
                                            color = Color(0xFF8771FF),
                                            fontSize = 12.sp
                                        )
                                    }
                                }
                                
                                // 状态消息
                                val statusMessage by viewModel.statusMessage.collectAsStateWithLifecycle()
                                statusMessage?.let { message ->
                                    if (message.contains("成功")) {
                                        Text(
                                            text = message,
                                            color = Color(0xFF4CAF50),
                                            fontSize = 12.sp,
                                            modifier = Modifier.padding(top = 4.dp)
                                        )
                                    } else if (message.isNotEmpty()) {
                                        Text(
                                            text = message,
                                            color = Color(0xFFFF5722),
                                            fontSize = 12.sp,
                                            modifier = Modifier.padding(top = 4.dp)
                                        )
                                    }
                                }
                            }
                            
                            // 连接按钮
                            Button(
                                onClick = { 
                                    // 检查蓝牙是否处于连接状态
                                    if (state.connectionState == ConnectionState.CONNECTED) {
                                        // 蓝牙已连接，绑定当前设备到服务器
                                        viewModel.bindCurrentDevice()
                                    } else {
                                        // 蓝牙未连接，提示用户
                                        viewModel.updateStatusMessage("请确保蓝牙设备已连接后再尝试")
                                        // 提示打开系统设置
                                        viewModel.openSystemBluetoothSettings()
                                    }
                                },
                                shape = RoundedCornerShape(50),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF8771FF)
                                ),
                                modifier = Modifier.height(40.dp)
                            ) {
                                // 获取设备绑定状态
                                val isDeviceBinding by viewModel.isDeviceBinding.collectAsStateWithLifecycle()
                                if (isDeviceBinding) {
                                    // 显示加载指示器
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                } else {
                                    Text(
                                        text = if (state.connectionState == ConnectionState.CONNECTED) "连接" else "连接设置",
                                        color = Color.White,
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                        
                        // 添加设备详细信息
                        Divider(
                            color = Color.White.copy(alpha = 0.1f),
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                        
                        // 设备类型信息
                        if (device.bluetoothClass != null) {
                            val deviceClass = when (device.bluetoothClass.majorDeviceClass) {
                                BluetoothClass.Device.Major.AUDIO_VIDEO -> "音频设备"
                                BluetoothClass.Device.Major.COMPUTER -> "电脑设备"
                                BluetoothClass.Device.Major.HEALTH -> "健康设备"
                                BluetoothClass.Device.Major.IMAGING -> "成像设备"
                                BluetoothClass.Device.Major.MISC -> "其他设备"
                                BluetoothClass.Device.Major.NETWORKING -> "网络设备"
                                BluetoothClass.Device.Major.PERIPHERAL -> "外围设备"
                                BluetoothClass.Device.Major.PHONE -> "电话设备"
                                BluetoothClass.Device.Major.TOY -> "玩具设备"
                                BluetoothClass.Device.Major.UNCATEGORIZED -> "未分类设备"
                                BluetoothClass.Device.Major.WEARABLE -> "可穿戴设备"
                                else -> "未知设备类型"
                            }
                            
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp)
                            ) {
                                Text(
                                    text = "设备类型：",
                                    color = Color.White.copy(alpha = 0.7f),
                                    fontSize = 14.sp
                                )
                                Text(
                                    text = deviceClass,
                                    color = Color.White,
                                    fontSize = 14.sp
                                )
                            }
                        }
                        
                        // 添加更多设备信息
                        if (state.deviceInfo.isNotEmpty()) {
                            state.deviceInfo.forEach { (key, value) ->
                                if (key != "name" && key != "address") {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = "$key：",
                                            color = Color.White.copy(alpha = 0.7f),
                                            fontSize = 14.sp
                                        )
                                        Text(
                                            text = value,
                                            color = Color.White,
                                            fontSize = 14.sp
                                        )
                                    }
                                }
                            }
                        }
                    } else {
                        // 没有连接设备时显示提示
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(120.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.bluetooth_icon),
                                    contentDescription = "蓝牙图标",
                                    tint = Color.White.copy(alpha = 0.7f),
                                    modifier = Modifier.size(48.dp)
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = "未检测到连接的设备",
                                    color = Color.White.copy(alpha = 0.7f),
                                    fontSize = 16.sp
                                )
                                
                                Spacer(modifier = Modifier.height(4.dp))
                                
                                Text(
                                    text = "请点击下方按钮刷新或进入系统设置连接",
                                    color = Color.White.copy(alpha = 0.5f),
                                    fontSize = 14.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
            }
            
            // 刷新按钮 - 始终显示
            Button(
                onClick = { viewModel.refreshOnResume() },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF8771FF)
                ),
                shape = RoundedCornerShape(50)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "刷新设备信息",
                        color = Color.White,
                        fontSize = 16.sp,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                    
                    // 显示加载指示器
                    if (state.connectionState == ConnectionState.CONNECTING) {
                        Spacer(modifier = Modifier.width(8.dp))
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    }
                }
            }
            
            // 添加打开系统蓝牙设置按钮
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = { 
                    val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                    context.startActivity(intent)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF33324A)
                ),
                shape = RoundedCornerShape(50)
            ) {
                Text(
                    text = "打开系统蓝牙设置",
                    color = Color.White,
                    fontSize = 16.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

// 辅助函数：格式化MAC地址显示
private fun formatMacAddress(address: String): String {
    // 检查地址是否已经包含":"
    if (address.contains(":")) return address
    
    // 如果是纯字母数字格式，添加冒号
    val parts = address.chunked(2)
    return parts.joinToString(":")
}

@Composable
fun PermissionRequestCard(
    onRequestPermission: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Filled.Info,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "需要权限",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "扫描音频设备需要蓝牙和位置权限。Android系统要求应用在扫描蓝牙音频设备时必须拥有位置权限。",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onRequestPermission,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("授予权限")
            }
        }
    }
}

@Composable
fun BluetoothDisabledCard(
    onEnableBluetooth: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Filled.Info,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.error
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "蓝牙已禁用",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "请开启蓝牙以扫描和连接音频设备",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onEnableBluetooth,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) {
                Text("开启蓝牙")
            }
        }
    }
}

@Composable
fun ConnectionStatusIndicator(
    connectionState: ConnectionState,
    connectedDevice: BluetoothDevice?,
    onClick: () -> Unit
) {
    val icon: ImageVector
    val text: String
    val color: androidx.compose.ui.graphics.Color

    when (connectionState) {
        ConnectionState.CONNECTED -> {
            icon = Icons.Filled.Info
            text = "已连接: ${connectedDevice?.name ?: "未知设备"}"
            color = MaterialTheme.colorScheme.primary
        }
        ConnectionState.CONNECTING -> {
            icon = Icons.Filled.Info
            text = "正在连接..."
            color = MaterialTheme.colorScheme.tertiary
        }
        ConnectionState.DISCONNECTED -> {
            icon = Icons.Filled.Info
            text = "未连接"
            color = MaterialTheme.colorScheme.outline
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                enabled = connectionState == ConnectionState.CONNECTED,
                onClick = onClick
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )

            Text(
                text = text,
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 16.dp)
            )

            if (connectionState == ConnectionState.CONNECTED) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "查看详情",
                    tint = MaterialTheme.colorScheme.primary
                )
            } else if (connectionState == ConnectionState.CONNECTING) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp
                )
            }
        }
    }
}

@Composable
fun ScanButton(
    isScanning: Boolean,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit
) {
    Button(
        onClick = { if (isScanning) onStopScan() else onStartScan() },
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Filled.Search,
                contentDescription = null
            )

            Spacer(modifier = Modifier.size(8.dp))

            Text(text = if (isScanning) "停止扫描" else "扫描音频设备")

            if (isScanning) {
                Spacer(modifier = Modifier.size(8.dp))
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp
                )
            }
        }
    }
}

@Composable
fun DevicesList(
    scanResults: List<BluetoothDevice>,
    connectionState: ConnectionState,
    connectedDevice: BluetoothDevice?,
    onDeviceClick: (BluetoothDevice) -> Unit
) {
    Column {
        Text(
            text = "可用音频设备 (${scanResults.size})",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        if (scanResults.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "未发现音频设备，请点击扫描按钮",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.outline
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp)
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(RoundedCornerShape(8.dp))
            ) {
                items(scanResults) { device ->
                    // 排除已连接的设备（已经在上面显示了）
                    if (connectedDevice == null || device.address != connectedDevice.address) {
                        DeviceItem(
                            device = device,
                            onClick = { onDeviceClick(device) },
                            enabled = connectionState == ConnectionState.DISCONNECTED
                        )
                        
                        Divider(modifier = Modifier.padding(horizontal = 16.dp))
                    }
                }
            }
        }
    }
}

@Composable
fun DeviceItem(
    device: BluetoothDevice,
    onClick: () -> Unit,
    enabled: Boolean = true
) {
    // 使用本地变量存储名称，避免每次调用可能为空的属性
    val deviceName = device.name ?: "未知设备 (${device.address.takeLast(5)})"
    val deviceAddress = device.address
    val context = LocalContext.current
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { 
                // 点击设备时直接跳转到系统蓝牙设置
                val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 蓝牙图标
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.primaryContainer)
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Filled.Info,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
        
        // 设备信息
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        ) {
            Text(
                text = deviceName,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = deviceAddress,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.outline
            )
            // 添加提示文本
            Text(
                text = "点击跳转到系统设置连接",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        // 系统设置图标
        Icon(
            imageVector = Icons.Filled.Info,
            contentDescription = "系统设置",
            tint = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
fun DeviceDetailView(
    connectedDevice: BluetoothDevice,
    deviceInfo: Map<String, String>,
    services: List<BluetoothGattService>,
    onBackClick: () -> Unit,
    onDisconnect: () -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回"
                )
            }
            
            Text(
                text = "设备详情",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        }
        
        // 使用我们新创建的设备详情屏幕
        DeviceDetailScreen(
            device = connectedDevice,
            connectionState = ConnectionState.CONNECTED,
            deviceInfo = deviceInfo,
            services = services,
            onDisconnect = onDisconnect
        )
    }
}

@Composable
fun InfoItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.outline,
            modifier = Modifier.weight(0.3f)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(0.7f)
        )
    }
}

@Composable
fun ServiceItem(service: BluetoothGattService) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = "服务：${service.uuid}",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "特征数量：${service.characteristics.size}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.outline
        )
    }
}

// 显示当前连接的设备
@Composable
fun CurrentConnectedDevice(
    connectedDevice: BluetoothDevice?,
    connectionState: ConnectionState,
    onDisconnectClick: () -> Unit,
    onViewDetailsClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        elevation = CardDefaults.cardElevation(4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前连接设备",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            when {
                connectionState == ConnectionState.CONNECTING -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text("正在连接...")
                            
                            // 耳机设备连接提示信息
                            if (connectedDevice != null && deviceIsAudio(connectedDevice)) {
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = "蓝牙耳机需要通过系统设置连接",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.error
                                )
                                
                                // 为AAC耳机添加特殊提示
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = "AAC耳机使用高级音频编码，请先在系统蓝牙设置中配对",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.error
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                OpenBluetoothSettingsButton()
                            }
                        }
                    }
                }
                connectionState == ConnectionState.CONNECTED && connectedDevice != null -> {
                    val device = connectedDevice
                    // 使用基本设备信息
                    val deviceName = device.name ?: "未知设备"
                    val deviceAddress = device.address
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = deviceName,
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            
                            Text(
                                text = deviceAddress,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            
                            Text(
                                text = "已连接",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                        
                        Row {
                            Button(
                                onClick = onViewDetailsClick,
                                modifier = Modifier.padding(end = 8.dp)
                            ) {
                                Text("查看详情")
                            }
                            
                            Button(
                                onClick = onDisconnectClick,
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.error
                                )
                            ) {
                                Text("断开")
                            }
                        }
                    }
                }
                else -> {
                    Text(
                        text = "未连接设备",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 显示状态消息（如果有）
            val uiState = LocalUiState.current
            uiState.statusMessage?.let { message ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
fun OpenBluetoothSettingsButton() {
    val context = LocalContext.current
    Button(
        onClick = {
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
            context.startActivity(intent)
        },
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.secondary
        )
    ) {
        Text("打开系统蓝牙设置")
    }
}

// 判断设备是否为音频设备
private fun isAudioDevice(device: BluetoothDevice): Boolean {
    val deviceClass = device.bluetoothClass?.majorDeviceClass
    val deviceSubClass = device.bluetoothClass?.deviceClass
    
    return deviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO ||
            deviceSubClass == BluetoothClass.Device.AUDIO_VIDEO_HEADPHONES ||
            deviceSubClass == BluetoothClass.Device.AUDIO_VIDEO_WEARABLE_HEADSET ||
            deviceSubClass == BluetoothClass.Device.AUDIO_VIDEO_HANDSFREE
}

// 安全调用isAudioDevice函数的包装函数
private fun deviceIsAudio(device: BluetoothDevice?): Boolean {
    if (device == null) return false
    return isAudioDevice(device)
}

// UI状态提供者
private val LocalUiState = compositionLocalOf<BluetoothUiState> { BluetoothUiState() }

@Composable
fun BluetoothScreenContent(
    state: BluetoothUiState,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit,
    onDeviceClick: (BluetoothDevice) -> Unit,
    onDisconnectClick: () -> Unit,
    showDeviceDetails: Boolean,
    onCloseDetails: () -> Unit
) {
    CompositionLocalProvider(LocalUiState provides state) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // 添加一个提示卡片告知用户
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "蓝牙设备连接指南",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "• 本应用只提供蓝牙设备搜索功能",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    Text(
                        text = "• 需要通过系统蓝牙设置来连接设备",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    Text(
                        text = "• 点击设备或下方按钮可直接跳转到系统设置",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 跳转到系统设置的按钮
                    val context = LocalContext.current
                    Button(
                        onClick = {
                            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            context.startActivity(intent)
                        },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text("打开系统蓝牙设置")
                    }
                }
            }
            
            // 当前连接设备状态
            Text(
                text = "当前连接设备",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            // 当前连接设备显示
            CurrentConnectedDevice(
                connectedDevice = state.connectedDevice,
                connectionState = state.connectionState,
                onDisconnectClick = onDisconnectClick,
                onViewDetailsClick = onCloseDetails
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 扫描按钮
            ScanButton(
                isScanning = state.isScanning,
                onStartScan = onStartScan,
                onStopScan = onStopScan
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 扫描结果列表
            DevicesList(
                scanResults = state.scanResults,
                connectionState = state.connectionState,
                connectedDevice = state.connectedDevice,
                onDeviceClick = onDeviceClick
            )
        }
    }
}

@Composable
fun DeviceDetailScreen(
    device: BluetoothDevice,
    connectionState: ConnectionState,
    deviceInfo: Map<String, String>,
    services: List<BluetoothGattService>,
    onDisconnect: () -> Unit
) {
    // 如果设备名称为空，显示友好的未知设备名称
    val deviceName = device.name ?: "未知设备 (${device.address.takeLast(5)})"
    val deviceAddress = device.address
    val context = LocalContext.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 设备基本信息
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "设备信息",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "名称: $deviceName",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
                
                Text(
                    text = "地址: $deviceAddress",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
                
                // 显示其他设备信息
                deviceInfo.forEach { (key, value) ->
                    if (key != "name" && key != "address") {
                        Text(
                            text = "$key: $value",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }
        
        // 断开连接按钮
        Button(
            onClick = onDisconnect,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.errorContainer)
        ) {
            Text(
                text = "断开连接",
                color = MaterialTheme.colorScheme.onErrorContainer
            )
        }

        // 断开连接指南
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "断开连接指南",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "对于某些蓝牙音频设备，可能需要通过以下方式断开连接：",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "1. 尝试点击上方的\"断开连接\"按钮",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
                
                Text(
                    text = "2. 如果无法断开，请前往系统设置中的蓝牙选项",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
                
                Text(
                    text = "3. 在已配对设备列表中找到该设备",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
                
                Text(
                    text = "4. 点击设备右侧的设置图标",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
                
                Text(
                    text = "5. 选择\"取消保存\"或\"断开连接\"选项",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
        
        // 服务列表
        if (services.isNotEmpty()) {
            Text(
                text = "设备服务",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(services) { service ->
                    ServiceItem(service = service)
                }
            }
        }
    }
}

// 为BluetoothViewModel提供CompositionLocal
private val LocalViewModelProvider = compositionLocalOf<BluetoothViewModel> { 
    error("No BluetoothViewModel provided") 
} 