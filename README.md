# LLya - 实时语音翻译应用

LLya是一款基于Android平台的实时语音翻译应用，支持多种语言之间的即时翻译和语音播放功能。本应用采用Kotlin语言和Jetpack Compose开发，遵循Material Design 3设计规范。

## 主要功能

1. **实时语音识别**：支持通过麦克风采集用户语音，实时转换为文字。
2. **多语言翻译**：支持中文、英语、日语、韩语、法语等多种语言之间的文本翻译。
3. **文本到语音**：将翻译结果通过语音合成技术转换为目标语言的语音输出。
4. **连续翻译模式**：支持连续语音识别和翻译，无需手动开始/停止。
5. **语音识别历史**：保存翻译历史记录，方便用户回顾。
6. **多语言界面**：支持多种语言的应用界面，用户可以在语言设置中切换。
7. **用户反馈**：提供意见反馈功能，用户可以提交问题或建议。

## 技术架构

### 前端技术

- **Jetpack Compose**：现代化的Android UI开发工具包
- **Material Design 3**：遵循Google最新的设计规范
- **Kotlin Flow**：处理异步数据流
- **Kotlin Coroutines**：管理异步操作

### 后端服务

- **Google Cloud API适配器**：连接Google Cloud语音和翻译服务
- **WebSocket通信**：实现实时语音识别
- **OkHttp**：网络请求库

## 项目结构

```
app/
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       ├── example/
│       │       │   └── llya/
│       │       │       ├── audio/ - 音频处理相关类
│       │       │       ├── network/ - 网络请求相关类
│       │       │       ├── ui/ - 用户界面组件
│       │       │       ├── viewmodel/ - ViewModel类
│       │       │       └── LlyaApplication.kt - 应用入口类
│       │       └── speech/
│       │           └── service/ - 语音服务适配器
│       └── res/ - 资源文件
└── build.gradle.kts - 构建配置
```

## 核心组件详解

### 1. SpeechServiceAdapter

位于`com.speech.service`包，是应用与语音API之间的桥梁，主要负责：

- WebSocket连接管理
- 音频数据采集与传输
- 语音识别结果处理
- 文本到语音转换请求
- 翻译请求

### 2. GoogleCloudApiClient

位于`com.example.llya.network`包，封装了与Google Cloud API的交互，包括：

- 语音识别
- 文本翻译
- 文本到语音转换
- 语言列表获取

### 3. SimultaneousTranslationViewModel

位于`com.example.llya.viewmodel`包，管理应用核心业务逻辑：

- 状态管理
- 录音控制
- 翻译流程协调
- 错误处理

### 4. AudioPlayer和AudioRecorder

位于`com.example.llya.audio`包，负责：

- 音频录制
- 静音检测
- 音频播放

## 使用指南

### 基本使用流程

1. 启动应用后，选择源语言（你要说的语言）和目标语言（翻译成的语言）
2. 点击麦克风按钮开始录音
3. 说话，应用会实时将你的语音转换为文字
4. 转换完成后，应用会自动翻译并播放翻译后的语音
5. 在连续模式下，检测到语句结束（静默）会自动停止当前录音并开始翻译

### 高级功能

- **切换语言**：点击语言选择器可以切换源语言和目标语言
- **连续模式**：可以开启或关闭连续翻译模式
- **历史记录**：查看之前的翻译历史
- **清空历史**：清除所有翻译记录

### 应用设置

- **语言设置**：在"我的"页面中点击"语言设置"，可以切换应用界面语言
- **意见反馈**：在"我的"页面中点击"意见反馈"，可以提交使用问题或建议
- **关于我们**：在"我的"页面中点击"关于我们"，了解应用开发团队信息

## 播放模式

在同声传译功能中，应用支持四种不同的播放模式，满足不同使用场景的需求：

1. **静音模式**：不播放翻译声音，只显示翻译结果。适合在安静的环境下使用，或者不需要语音输出的场景。

2. **耳机模式**：对手机讲话，从耳机收听翻译声音。这是默认模式，适合个人使用，提供最佳的私密性体验。

3. **外放模式**：点击开始讲话，停止讲话时从手机播放翻译的声音。适合在没有耳机的情况下使用，或者需要与周围人共享翻译结果的场景。

4. **双耳模式**：一人带一只耳机，对着手机讲话，可以从耳机收听翻译语音。适合两人面对面交流时使用，提供相互理解的桥梁。

### 如何切换播放模式

1. 在同声传译界面，点击右上角的音频图标（耳机或音量图标）
2. 在弹出的对话框中选择所需的播放模式
3. 点击"取消"关闭对话框

系统会根据所选模式自动调整音频设置，包括音量大小和扬声器状态。

## 常见问题

1. **无法连接服务器**：
   - 检查网络连接
   - 确认服务器URL是否正确（当前设置为 https://gogoapi.sdltws.com/）

2. **语音识别不准确**：
   - 确保在安静的环境中使用
   - 靠近麦克风说话
   - 确认选择了正确的源语言

3. **翻译结果有误**：
   - 确认源语言和目标语言选择正确
   - 尝试使用更规范的表达方式

4. **应用崩溃**：
   - 检查是否授予了录音权限
   - 如果问题持续存在，可以尝试重启应用或联系开发者

## 未来计划

- 支持更多的语言
- 添加离线模式支持
- 提升语音识别准确度
- 优化用户界面体验
- 添加语音辨识功能

## 隐私声明

本应用需要以下权限：
- 麦克风权限：用于录制语音
- 网络权限：用于连接服务器进行语音识别和翻译

所有音频数据仅用于实时翻译处理，不会在本地长期存储或用于其他用途。

# 智能翻译耳机应用

## 功能介绍

本应用是一款智能翻译耳机的控制应用，提供以下功能：

1. **语音识别**：使用Google Cloud Speech-to-Text API，支持40多种语言的实时语音转写
2. **AI对话**：集成火山引擎豆包大语言模型，提供智能对话功能
3. **多语言支持**：支持中文、英语、日语、韩语等多种语言
4. **会议记录**：可将语音识别结果保存为会议记录，方便后续查阅

## 语音转写配置

本应用使用Google Cloud Speech-to-Text API进行语音转写。应用中已经集成了相关接口，无需额外配置。

如果需要修改转写服务器地址，可以在`GoogleCloudServiceAdapter.kt`文件中修改以下配置：

```kotlin
// 基础URL和WebSocket URL
private val BASE_URL = "https://gogoapi.sdltws.com"
private val WS_URL = "wss://gogoapi.sdltws.com/ws/speech-to-text"
```

## 火山引擎豆包AI配置

要使用火山引擎豆包AI功能，需要进行以下配置：

1. 注册火山引擎账号并开通豆包大语言模型服务
2. 获取API密钥（API Key）和密钥（Secret Key）
3. 在应用中配置密钥

### 获取火山引擎豆包AI密钥

1. 访问[火山引擎官网](https://www.volcengine.com/)并注册账号
2. 开通豆包大语言模型服务
3. 在控制台创建应用并获取API密钥
4. 复制生成的API Key和Secret Key

### 在应用中配置密钥

打开`SpeechRecognitionViewModel.kt`文件，找到以下代码段：

```kotlin
// 火山引擎豆包AI配置
private const val VOLC_ENGINE_API_KEY = "替换为火山引擎API密钥"
private const val VOLC_ENGINE_SECRET_KEY = "替换为火山引擎Secret密钥"
```

将获取到的API Key和Secret Key分别替换对应的占位符。

### 修改豆包API地址

如果需要修改豆包API服务器地址，可以在`VolcEngineAIClient.kt`文件中修改以下配置：

```kotlin
private const val API_URL = "https://zhengzhoudianbiancheng.cn/api/chat" // 替换为实际的API地址
```

## 使用说明

### 语音转写

1. 点击麦克风按钮开始录音
2. 说话内容将实时显示在输入框中
3. 再次点击麦克风按钮结束录音
4. 录音结果将自动发送给AI助手并获取回复

### 切换识别语言

1. 点击顶部的语言图标(🌐)
2. 在弹出的菜单中选择需要的语言
3. 系统将自动切换为所选语言进行语音识别

### AI对话

1. 在输入框中输入文字或使用语音输入
2. 点击发送按钮或使用语音输入后自动发送
3. AI助手将处理您的问题并给出回复
4. 对话历史将以聊天形式展示在界面上

## 支持的语言

应用支持以下语言的语音识别：

- 中文普通话 (zh-CN)
- 英语 (en-US)
- 粤语 (yue-Hant-HK)
- 韩语 (ko-KR)
- 日语 (ja-JP)
- 泰语 (th-TH)
- 越南语 (vi-VN)
- 马来语 (ms-MY)
- 印尼语 (id-ID)
- 法语 (fr-FR)
- 德语 (de-DE)
- 西班牙语 (es-ES)
- 意大利语 (it-IT)
- 俄语 (ru-RU)
- 阿拉伯语 (ar-SA)

## 依赖项

- Android Jetpack Compose
- Kotlin Coroutines & Flow
- OkHttp3 & WebSockets
- Google Cloud Speech-to-Text API
- 火山引擎豆包大语言模型API

## 多语言支持

LLya应用支持多种语言界面，让全球用户都能以他们熟悉的语言使用应用。

### 支持的界面语言

应用通过实时连接API动态获取支持的语言列表，同时也内置了以下默认语言支持：

- 简体中文 (zh-CN)
- 繁体中文 (zh-TW)
- 英语 (en)
- 法语 (fr)
- 俄语 (ru)
- 西班牙语 (es)
- 阿拉伯语 (ar)
- 日语 (ja)
- 韩语 (ko)
- 德语 (de)
- 意大利语 (it)
- 葡萄牙语 (pt)
- 越南语 (vi)
- 印尼语 (id)
- 泰语 (th)
- 马来语 (ms)
- 土耳其语 (tr)
- 荷兰语 (nl)

### 切换语言方法

用户可以通过以下两种方式切换应用界面语言：

1. **从个人中心页面**：
   - 进入"个人中心"页面
   - 在语言显示卡片中点击"编辑"按钮
   - 在语言设置页面选择所需语言

2. **从设置菜单**：
   - 进入"个人中心"页面
   - 点击"语言设置"选项
   - 在语言设置页面选择所需语言

### 技术实现

应用使用了以下技术来实现多语言支持：

- **API动态获取语言列表**：应用启动时从`/api/language/index`接口获取最新的支持语言列表
- **资源文件本地化**：根据不同语言创建`strings.xml`资源文件
- **语言工具类**：`LanguageUtils`提供语言切换、获取语言代码和显示名称等功能
- **语言管理器**：`LanguageManager`处理API交互，保存和缓存语言数据
- **动态应用语言设置**：在运行时根据用户选择切换应用界面语言
- **语言状态管理**：使用`MutableStateFlow`实时更新应用的语言状态
- **备用语言机制**：当API获取失败时，使用内置默认语言列表

### 开发者扩展

如需添加新的语言支持，开发者需要：

1. 在`app/src/main/res/`下创建对应语言的资源文件夹，如`values-fr`(法语)
2. 在新创建的文件夹中添加`strings.xml`文件，包含所有界面文本的翻译
3. API系统中添加相应的语言记录，确保`/api/language/index`返回新添加的语言

### API接口格式

支持获取语言列表的API接口格式如下：

```json
{
  "status": 1,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "language_code": "zh-CN",
      "language_name": "简体中文",
      "native_name": "中文",
      "region": "China",
      "is_default": 1,
      "sort": 1,
      "status": 1,
      "create_time": 1625097600,
      "update_time": 1625097600
    },
    // ... 更多语言
  ]
}
```

# 会议记录功能改进说明

## 问题背景

会议记录功能使用Google Cloud和腾讯云两种语音识别服务，之前在使用谷歌云方案时存在以下问题：

1. 当用户说话较长时，谷歌云的文本处理方式与腾讯云不同，导致实时文字展示体验不一致
2. 技术术语（如HTTP、HTTPS、URL等）容易在文本中重复出现
3. 缺少智能的文本合并机制，造成文本叠加问题

## 主要改进内容

1. **谷歌云文本累积功能**：
   - 添加`_googleCloudAccumulatedText`累积变量，模拟腾讯云的累积文本机制
   - 实现`updateGoogleCloudAccumulatedText`方法，智能处理文本合并

2. **技术术语处理优化**：
   - 改进技术术语识别功能，避免专有名词重复
   - 添加`findNonOverlappingText`方法识别文本中的非重叠部分
   - 优化两段文本之间的合并策略

3. **文本质量评估**：
   - 根据文本长度、内容、句子结构评估质量
   - 仅当文本质量合格时才添加到会议记录

4. **智能文本分隔符**：
   - 根据句子结束符自动选择合适的分隔符（换行或空格）
   - 创建`getAppropriateTextSeparator`辅助函数

5. **停止录音优化**：
   - 改进停止录音时的文本处理逻辑
   - 确保最后的识别结果也能被正确处理

这些改进使谷歌云语音识别方案的文本处理更加接近腾讯云的实现，提供了更一致的用户体验，避免了技术术语重复和文本叠加问题。同时保留了两种识别服务各自的优势，用户可以根据不同语言选择最适合的服务。 