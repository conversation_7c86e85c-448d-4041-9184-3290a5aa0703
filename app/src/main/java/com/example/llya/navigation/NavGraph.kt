package com.example.llya.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.llya.ui.screens.TencentCloudAsrScreen

sealed class Screen(val route: String) {
    object Main : Screen("main")
    object TencentCloudAsr : Screen("tencent_cloud_asr")
}

@Composable
fun NavGraph(
    navController: NavHostController = rememberNavController(),
    startDestination: String = Screen.Main.route
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        composable(Screen.TencentCloudAsr.route) {
            TencentCloudAsrScreen(navController)
        }
    }
} 