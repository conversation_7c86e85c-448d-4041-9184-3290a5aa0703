package com.example.llya.viewmodel

import android.content.Context
import android.media.MediaRecorder
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.asr.TencentCloudSpeechToTextClient
import com.example.llya.utils.AudioRecorder
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileInputStream

class TencentCloudAsrViewModel : ViewModel() {
    companion object {
        private const val TAG = "TencentCloudAsrViewModel"
        private const val APP_ID = "1251118525"  // 替换为你的AppId
        private const val SECRET_ID = "AKIDoTt53sTpwoW2KQIuHPIvJSM2TeaHkyyk"
        private const val SECRET_KEY = "aw4wZy83iBQP9fx1qQmunRq1yf7KJ3Yp"
    }

    // 录音器
    private var audioRecorder: AudioRecorder? = null
    
    // 腾讯云语音识别客户端
    private var speechClient: TencentCloudSpeechToTextClient? = null

    // 录音状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    // 识别结果
    private val _recognitionResult = MutableStateFlow("")
    val recognitionResult: StateFlow<String> = _recognitionResult.asStateFlow()

    // 累积文本
    private val _accumulatedText = MutableStateFlow("")
    val accumulatedText: StateFlow<String> = _accumulatedText.asStateFlow()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 连接状态
    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    init {
        initializeSpeechClient()
    }

    private fun initializeSpeechClient() {
        try {
            speechClient = TencentCloudSpeechToTextClient(
                appId = APP_ID,
                secretId = SECRET_ID,
                secretKey = SECRET_KEY,
                coroutineScope = viewModelScope
            )
            
            // 收集识别结果
            viewModelScope.launch {
                speechClient?.recognitionResult?.collect { result ->
                    _recognitionResult.value = result
                }
            }
            
            // 收集累积文本
            viewModelScope.launch {
                speechClient?.accumulatedText?.collect { text ->
                    _accumulatedText.value = text
                }
            }
        } catch (e: Exception) {
            _errorMessage.value = "初始化语音识别客户端失败: ${e.message}"
            Log.e(TAG, "初始化语音识别客户端失败", e)
        }
    }

    /**
     * 开始录音
     */
    fun startRecording(engineModelType: String = "16k_zh") {
        if (_isRecording.value) return

        viewModelScope.launch {
            try {
                // 先连接WebSocket
                speechClient?.connect(engineModelType)
                
                // 初始化录音器
                audioRecorder = AudioRecorder(
                    onAudioBufferReady = { audioData ->
                        // 发送音频数据到WebSocket
                        speechClient?.sendAudioData(audioData)
                    },
                    onError = { error ->
                        _errorMessage.value = error
                    }
                )
                
                // 开始录音
                audioRecorder?.startRecording()
                _isRecording.value = true
                _errorMessage.value = null
            } catch (e: Exception) {
                Log.e(TAG, "开始录音失败", e)
                _errorMessage.value = "开始录音失败: ${e.message}"
            }
        }
    }

    /**
     * 停止录音
     */
    fun stopRecording() {
        if (!_isRecording.value) return

        viewModelScope.launch {
            try {
                // 停止录音
                audioRecorder?.stopRecording()
                _isRecording.value = false
                
                // 结束识别
                speechClient?.endRecognition()
            } catch (e: Exception) {
                Log.e(TAG, "停止录音失败", e)
                _errorMessage.value = "停止录音失败: ${e.message}"
            }
        }
    }

    fun clearResults() {
        speechClient?.clearAll()
        _recognitionResult.value = ""
        _accumulatedText.value = ""
        _errorMessage.value = null
    }

    override fun onCleared() {
        super.onCleared()
        audioRecorder?.release()
        speechClient?.release()
    }
} 