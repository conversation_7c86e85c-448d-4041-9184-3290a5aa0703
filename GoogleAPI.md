# 语音识别转文字App

## 项目介绍

这是一款基于腾讯云和阿里云实时语音识别API开发的Android应用，可以实现实时语音转文字和多语言互译功能。应用使用Kotlin语言和Jetpack Compose开发，采用MVVM架构，UI遵循Material Design 3设计规范。应用还提供蓝牙音频设备（如耳机、扬声器）扫描、连接和管理功能，让您可以方便地使用蓝牙音频设备进行语音识别。

## 功能特点

- 实时语音识别：边说边出文字
- 多语言互译：支持中文、英文、日语等多种语言的互相翻译
- 简洁美观的用户界面
- 实时显示识别和翻译结果
- 支持两种语音识别服务提供商：腾讯云和阿里云
- 支持两种翻译服务提供商：腾讯云和阿里云
- 蓝牙音频设备功能：
  - 扫描附近音频设备（耳机、扬声器等）
  - 自动识别已连接的音频设备
  - 连接设备、查看设备信息和服务
  - 专注于音频设备，过滤其他类型的蓝牙设备
- 个人中心功能：
  - 用户信息与ID显示
  - 设备管理与绑定
  - 系统设置与偏好管理

## 技术架构

- **编程语言**：Kotlin
- **UI框架**：Jetpack Compose
- **架构模式**：MVVM（Model-View-ViewModel）
- **网络通信**：OkHttp WebSocket和REST API
- **音频处理**：Android AudioRecord
- **蓝牙通信**：Android Bluetooth LE API
- **语音识别**：
  - 腾讯云实时语音识别API
  - 阿里云实时语音识别API
- **机器翻译**：
  - 腾讯云机器翻译API
  - 阿里云机器翻译API（REST）

## 运行说明

1. 克隆本项目到本地
2. 在Android Studio中打开项目
3. 在`SpeechRecognitionViewModel.kt`文件中填入你的腾讯云API密钥（已预设）
   ```kotlin
   private const val APP_ID = "你的APP_ID" 
   private const val SECRET_ID = "你的SECRET_ID"
   private const val SECRET_KEY = "你的SECRET_KEY"
   ```
4. 在`MainActivity.kt`文件中填入你的阿里云API密钥：
   ```kotlin
   private val aliyunAccessKeyId = "你的阿里云AccessKeyId"
   private val aliyunAccessKeySecret = "你的阿里云AccessKeySecret"
   private val aliyunAppKey = "你的阿里云AppKey"
   ```
5. 构建并运行应用

## 使用方法

### 腾讯云语音识别（主界面）

1. 启动应用，默认进入主界面（腾讯云语音识别）
2. 从下拉菜单中选择想要识别的语言
3. 点击麦克风按钮开始录音（首次使用会请求麦克风权限）
4. 开始说话，识别结果会实时显示在屏幕上
5. 选择目标翻译语言，翻译结果会自动显示
6. 再次点击按钮停止录音

### 阿里云语音识别

1. 在主界面点击右上角的设置图标或底部的"阿里云语音识别"按钮
2. 进入阿里云语音识别界面
3. 点击麦克风按钮开始录音
4. 开始说话，识别结果会实时显示
5. 再次点击按钮停止录音
6. 点击左上角返回按钮回到主界面

### 阿里云翻译

1. 在主界面点击底部的"阿里云翻译"按钮
2. 进入阿里云翻译界面
3. 从下拉菜单中选择源语言和目标语言
4. 在输入框中输入待翻译文本
5. 点击"翻译"按钮或按下完成键进行翻译
6. 翻译结果会显示在下方的结果区域
7. 可以使用底部的交换按钮快速切换源语言和目标语言
8. 点击左上角返回按钮回到主界面

### 蓝牙功能

1. 在主界面点击顶部导航栏的蓝牙图标或底部的"蓝牙管理"按钮
2. 进入蓝牙管理界面
3. 如果是首次使用，系统会请求蓝牙和位置权限
4. 如果蓝牙未开启，应用会提示你开启蓝牙
5. 点击"扫描音频设备"按钮开始扫描附近的蓝牙音频设备（如耳机、扬声器）
6. 应用会自动检测并显示已连接的音频设备
7. 在设备列表中点击一个设备进行连接
8. 连接成功后，点击连接状态栏可以查看设备详情
9. 在设备详情页面可以查看设备的基本信息和可用服务
10. 点击"断开连接"按钮可以断开与设备的连接
11. 点击左上角返回按钮回到主界面

### 个人中心

1. 在主界面点击底部导航栏的"我的"选项进入个人中心
2. 个人中心页面顶部显示用户头像和ID信息
3. 设备卡片区域显示已绑定的设备（如耳机）和添加新设备的选项
4. 设置项列表提供以下功能：
   - 分享给朋友：分享应用给其他用户
   - 语言设置：修改应用界面语言
   - 关于我们：查看应用开发团队信息
   - 意见反馈：提交使用反馈和建议
   - 权限设置：管理应用所需权限
   - 更多设置：其他系统设置选项
5. 点击底部导航栏的"首页"选项可返回主界面

## 权限说明

应用需要以下权限：
- `RECORD_AUDIO`：用于录制语音
- `INTERNET`：连接云语音识别和翻译服务
- `ACCESS_NETWORK_STATE`：检测网络状态
- `BLUETOOTH`/`BLUETOOTH_ADMIN`：用于控制蓝牙
- `BLUETOOTH_SCAN`/`BLUETOOTH_CONNECT`：用于扫描和连接蓝牙设备
- `ACCESS_FINE_LOCATION`/`ACCESS_COARSE_LOCATION`：在Android上扫描蓝牙设备需要位置权限

## 代码结构

- `MainActivity.kt`：应用入口和导航管理
- `MainScreen.kt`：腾讯云语音识别和翻译主界面
- `SpeechRecognitionViewModel.kt`：腾讯云语音识别和翻译状态管理
- `audio/AudioRecorder.kt`：音频录制工具类
- `asr/TencentAsrClient.kt`：腾讯云实时语音识别客户端
- `translation/TencentTranslationClient.kt`：腾讯云机器翻译客户端
- `translation/AliyunTranslationClient.kt`：阿里云机器翻译客户端
- `viewmodel/AliyunTranslationViewModel.kt`：阿里云翻译状态管理
- `ui/AliyunTranslationScreen.kt`：阿里云翻译界面
- `ui/ProfileScreen.kt`：个人中心界面
- `utils/SignatureUtil.kt`：腾讯云API签名工具类
- `aliyunasi/AliyunAsrClient.kt`：阿里云实时语音识别客户端
- `aliyunasi/AliyunTokenUtil.kt`：阿里云Token获取工具类
- `aliyunasi/AliyunAsrViewModel.kt`：阿里云语音识别状态管理
- `aliyunasi/AliyunAsrScreen.kt`：阿里云语音识别界面
- `bluetooth/BluetoothManager.kt`：蓝牙音频设备管理
- `bluetooth/BluetoothViewModel.kt`：蓝牙状态管理
- `bluetooth/BluetoothScreen.kt`：蓝牙界面

## 注意事项

- 需要稳定的网络连接以确保识别和翻译效果
- 语音识别和翻译需要消耗流量，建议在WiFi环境下使用
- 应用默认使用16kHz采样率，确保较好的识别效果
- 使用阿里云服务需要有效的阿里云账号并开通相关服务
- 阿里云翻译API支持自动语言检测功能，可选择"自动检测"作为源语言
- 蓝牙功能专注于音频设备（如耳机、扬声器），过滤其他类型的蓝牙设备
- 某些蓝牙设备可能需要配对后才能连接
- 支持自动检测系统已连接的音频设备

## 后续改进计划

- 添加识别和翻译结果历史记录功能
- 支持识别结果编辑和导出
- 优化识别效果和响应速度
- 增加更多语言支持
- 添加语音播放翻译结果功能
- 支持离线识别模式
- 支持长句翻译和文档翻译
- 增强蓝牙功能，支持音频输入设备智能切换
- 添加蓝牙音频设备配对功能
- 支持保存常用蓝牙音频设备
- 添加音频设备的声音均衡器功能

## API文档参考

详细API文档请参考：
- [腾讯云实时语音识别（websocket）](https://cloud.tencent.cn/document/product/1093/48982)
- [腾讯云机器翻译](https://cloud.tencent.com/document/product/551/15619)
- [阿里云实时语音识别（WebSocket）](https://help.aliyun.com/zh/isi/developer-reference/websocket)
- [阿里云获取Token](https://help.aliyun.com/zh/isi/getting-started/use-http-or-https-to-obtain-an-access-token)
- [阿里云机器翻译（REST API）](https://help.aliyun.com/zh/machine-translation/developer-reference/using-rest-api)
- [Android蓝牙API](https://developer.android.com/guide/topics/connectivity/bluetooth/ble-overview)