package com.example.llya

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.microsoft.cognitiveservices.speech.translation.SpeechTranslationConfig
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognizer
import kotlinx.coroutines.*
import com.example.llya.ui.theme.LlyaTheme
import com.example.llya.viewmodel.HistoryViewModel
import com.example.llya.data.HistoryRecord
import com.example.llya.ui.HistoryItemCard
import com.example.llya.ui.PlaybackModeSelectionDialog
import androidx.lifecycle.viewmodel.compose.viewModel
import android.media.AudioManager
import android.media.AudioDeviceInfo
import android.content.Context
import com.example.llya.viewmodel.GoogleCloudServiceViewModel
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.Executors
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class MicrosoftSpeechActivity : ComponentActivity() {
    private val TAG = "MicrosoftSpeechActivity"

    // 微软语音服务的订阅密钥和区域
    private val speechSubscriptionKey = "7lJ1e4p4LppCs62hMBvzyzxYv5vCOauJTzIKqnY3U0NVw7hFXiGaJQQJ99BEACYeBjFXJ3w3AAAYACOGuXCg"
    private val speechRegion = "eastus"

    // 双向翻译模式开关
    private var enableBidirectionalTranslation = true

    // 语音识别器
    private var translationRecognizer: TranslationRecognizer? = null
    private var synthesizer: SpeechSynthesizer? = null

    // TTS播放队列
    private val ttsQueue = ConcurrentLinkedQueue<Pair<String, String>>()
    private val ttsExecutor = Executors.newSingleThreadExecutor()
    private var isProcessingTts = false

    // 识别历史记录 - 修复累积文本消失问题
    private val recognitionHistory = mutableListOf<String>()

    // 添加停止状态标志，防止频繁点击
    private var isStopping = false

    // 累积文本状态 - 确保不会丢失
    private var accumulatedText = ""

    // 音频反馈控制
    private var isTtsPlaying = false
    private var lastTtsText = ""
    private var ttsStartTime = 0L
    private var recognitionPausedForTts = false

    // 播放模式相关状态
    private var playbackMode = mutableStateOf(GoogleCloudServiceViewModel.PlaybackMode.EARBUDS) // 默认为耳机模式
    private var showPlaybackModeDialog = mutableStateOf(false)
    private lateinit var audioManager: AudioManager
    private var audioFocusRequest: Any? = null

    // 请求麦克风权限
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            Log.d(TAG, "麦克风权限已授予")
        } else {
            Log.e(TAG, "麦克风权限被拒绝")
            Toast.makeText(this, "需要麦克风权限才能进行语音识别", Toast.LENGTH_LONG).show()
        }
    }

    // 支持的语音识别语言 - 根据微软官方文档更新
    private val supportedSpeechLanguages = mapOf(
        // 中文
        "zh-CN" to "中文(简体)",
        "zh-TW" to "中文(台湾)",
        "zh-HK" to "中文(香港)",
        "yue-CN" to "粤语(简体)",

        // 英语
        "en-US" to "英语(美国)",
        "en-GB" to "英语(英国)",
        "en-AU" to "英语(澳大利亚)",
        "en-CA" to "英语(加拿大)",
        "en-IN" to "英语(印度)",
        "en-IE" to "英语(爱尔兰)",
        "en-NZ" to "英语(新西兰)",
        "en-SG" to "英语(新加坡)",
        "en-ZA" to "英语(南非)",

        // 日语和韩语
        "ja-JP" to "日语",
        "ko-KR" to "韩语",

        // 法语
        "fr-FR" to "法语(法国)",
        "fr-CA" to "法语(加拿大)",
        "fr-BE" to "法语(比利时)",
        "fr-CH" to "法语(瑞士)",

        // 德语
        "de-DE" to "德语(德国)",
        "de-AT" to "德语(奥地利)",
        "de-CH" to "德语(瑞士)",

        // 西班牙语
        "es-ES" to "西班牙语(西班牙)",
        "es-MX" to "西班牙语(墨西哥)",
        "es-AR" to "西班牙语(阿根廷)",
        "es-CO" to "西班牙语(哥伦比亚)",
        "es-CL" to "西班牙语(智利)",
        "es-PE" to "西班牙语(秘鲁)",
        "es-VE" to "西班牙语(委内瑞拉)",
        "es-US" to "西班牙语(美国)",

        // 意大利语
        "it-IT" to "意大利语",

        // 俄语
        "ru-RU" to "俄语",

        // 葡萄牙语
        "pt-PT" to "葡萄牙语(葡萄牙)",
        "pt-BR" to "葡萄牙语(巴西)",

        // 阿拉伯语
        "ar-SA" to "阿拉伯语(沙特阿拉伯)",
        "ar-AE" to "阿拉伯语(阿联酋)",
        "ar-EG" to "阿拉伯语(埃及)",
        "ar-JO" to "阿拉伯语(约旦)",
        "ar-KW" to "阿拉伯语(科威特)",
        "ar-LB" to "阿拉伯语(黎巴嫩)",
        "ar-MA" to "阿拉伯语(摩洛哥)",
        "ar-QA" to "阿拉伯语(卡塔尔)",

        // 荷兰语
        "nl-NL" to "荷兰语(荷兰)",
        "nl-BE" to "荷兰语(比利时)",

        // 北欧语言
        "sv-SE" to "瑞典语",
        "da-DK" to "丹麦语",
        "no-NO" to "挪威语",
        "fi-FI" to "芬兰语",
        "is-IS" to "冰岛语",

        // 东欧语言
        "pl-PL" to "波兰语",
        "cs-CZ" to "捷克语",
        "sk-SK" to "斯洛伐克语",
        "hu-HU" to "匈牙利语",
        "ro-RO" to "罗马尼亚语",
        "bg-BG" to "保加利亚语",
        "hr-HR" to "克罗地亚语",
        "sl-SI" to "斯洛文尼亚语",
        "et-EE" to "爱沙尼亚语",
        "lv-LV" to "拉脱维亚语",
        "lt-LT" to "立陶宛语",

        // 土耳其语
        "tr-TR" to "土耳其语",

        // 希腊语
        "el-GR" to "希腊语",

        // 希伯来语
        "he-IL" to "希伯来语",

        // 印地语和其他印度语言
        "hi-IN" to "印地语",
        "bn-IN" to "孟加拉语(印度)",
        "gu-IN" to "古吉拉特语",
        "kn-IN" to "卡纳达语",
        "ml-IN" to "马拉雅拉姆语",
        "mr-IN" to "马拉地语",
        "pa-IN" to "旁遮普语",
        "ta-IN" to "泰米尔语",
        "te-IN" to "泰卢固语",
        "ur-IN" to "乌尔都语",

        // 东南亚语言
        "th-TH" to "泰语",
        "vi-VN" to "越南语",
        "id-ID" to "印尼语",
        "ms-MY" to "马来语",
        "fil-PH" to "菲律宾语",

        // 其他亚洲语言
        "fa-IR" to "波斯语",
        "uz-UZ" to "乌兹别克语",
        "kk-KZ" to "哈萨克语",
        "ky-KG" to "吉尔吉斯语",
        "mn-MN" to "蒙古语",

        // 非洲语言
        "af-ZA" to "南非荷兰语",
        "am-ET" to "阿姆哈拉语",
        "sw-KE" to "斯瓦希里语",

        // 其他欧洲语言
        "ca-ES" to "加泰罗尼亚语",
        "eu-ES" to "巴斯克语",
        "gl-ES" to "加利西亚语",
        "cy-GB" to "威尔士语",
        "ga-IE" to "爱尔兰语",
        "mt-MT" to "马耳他语"
    )

    // 支持的翻译语言 - 根据微软翻译API更新
    private val supportedTranslationLanguages = mapOf(
        // 中文
        "zh-Hans" to "中文(简体)",
        "zh-Hant" to "中文(繁体)",
        "yue" to "粤语",

        // 英语
        "en" to "英语",

        // 日语和韩语
        "ja" to "日语",
        "ko" to "韩语",

        // 法语
        "fr" to "法语",

        // 德语
        "de" to "德语",

        // 西班牙语
        "es" to "西班牙语",

        // 意大利语
        "it" to "意大利语",

        // 俄语
        "ru" to "俄语",

        // 葡萄牙语
        "pt" to "葡萄牙语",

        // 阿拉伯语
        "ar" to "阿拉伯语",

        // 荷兰语
        "nl" to "荷兰语",

        // 北欧语言
        "sv" to "瑞典语",
        "da" to "丹麦语",
        "no" to "挪威语",
        "fi" to "芬兰语",
        "is" to "冰岛语",

        // 东欧语言
        "pl" to "波兰语",
        "cs" to "捷克语",
        "sk" to "斯洛伐克语",
        "hu" to "匈牙利语",
        "ro" to "罗马尼亚语",
        "bg" to "保加利亚语",
        "hr" to "克罗地亚语",
        "sl" to "斯洛文尼亚语",
        "et" to "爱沙尼亚语",
        "lv" to "拉脱维亚语",
        "lt" to "立陶宛语",

        // 土耳其语
        "tr" to "土耳其语",

        // 希腊语
        "el" to "希腊语",

        // 希伯来语
        "he" to "希伯来语",

        // 印地语和其他印度语言
        "hi" to "印地语",
        "bn" to "孟加拉语",
        "gu" to "古吉拉特语",
        "kn" to "卡纳达语",
        "ml" to "马拉雅拉姆语",
        "mr" to "马拉地语",
        "pa" to "旁遮普语",
        "ta" to "泰米尔语",
        "te" to "泰卢固语",
        "ur" to "乌尔都语",

        // 东南亚语言
        "th" to "泰语",
        "vi" to "越南语",
        "id" to "印尼语",
        "ms" to "马来语",
        "fil" to "菲律宾语",

        // 其他亚洲语言
        "fa" to "波斯语",
        "uz" to "乌兹别克语",
        "kk" to "哈萨克语",
        "ky" to "吉尔吉斯语",
        "mn" to "蒙古语",

        // 非洲语言
        "af" to "南非荷兰语",
        "am" to "阿姆哈拉语",
        "sw" to "斯瓦希里语",

        // 其他欧洲语言
        "ca" to "加泰罗尼亚语",
        "eu" to "巴斯克语",
        "gl" to "加利西亚语",
        "cy" to "威尔士语",
        "ga" to "爱尔兰语",
        "mt" to "马耳他语"
    )

    // TTS语音偏好 - 根据微软官方文档更新
    private val ttsVoicePreferences = mapOf(
        // 中文语音 - 根据微软官方文档修正
        "zh-CN" to "zh-CN-XiaoxiaoNeural", // 中文(普通话，简体) - 女声
        "zh-TW" to "zh-TW-HsiaoChenNeural", // 中文(台湾普通话，繁体) - 女声
        "zh-HK" to "zh-HK-HiuMaanNeural", // 中文(粤语，繁体) - 女声，官方文档确认
        "yue-CN" to "yue-CN-XiaoMinNeural", // 中文(粤语，简体) - 女声，官方文档确认
        "wuu-CN" to "wuu-CN-XiaotongNeural", // 中文(吴语，简体) - 女声

        // 英语语音
        "en-US" to "en-US-JennyNeural", // 英语(美国) - 女声
        "en-GB" to "en-GB-SoniaNeural", // 英语(英国) - 女声
        "en-AU" to "en-AU-NatashaNeural", // 英语(澳大利亚) - 女声
        "en-CA" to "en-CA-ClaraNeural", // 英语(加拿大) - 女声
        "en-IN" to "en-IN-NeerjaNeural", // 英语(印度) - 女声
        "en-IE" to "en-IE-EmilyNeural", // 英语(爱尔兰) - 女声
        "en-NZ" to "en-NZ-MollyNeural", // 英语(新西兰) - 女声
        "en-ZA" to "en-ZA-LeahNeural", // 英语(南非) - 女声
        "en-SG" to "en-SG-LunaNeural", // 英语(新加坡) - 女声
        "en-HK" to "en-HK-YanNeural", // 英语(香港) - 女声
        "en-PH" to "en-PH-RosaNeural", // 英语(菲律宾) - 女声
        "en-NG" to "en-NG-EzinneNeural", // 英语(尼日利亚) - 女声
        "en-KE" to "en-KE-AsiliaNeural", // 英语(肯尼亚) - 女声
        "en-GH" to "en-GH-AsiliaNeural", // 英语(加纳) - 女声
        "en-TZ" to "en-TZ-ImaniNeural", // 英语(坦桑尼亚) - 女声

        // 西班牙语语音
        "es-ES" to "es-ES-ElviraNeural", // 西班牙语(西班牙) - 女声
        "es-MX" to "es-MX-DaliaNeural", // 西班牙语(墨西哥) - 女声
        "es-US" to "es-US-PalomaNeural", // 西班牙语(美国) - 女声
        "es-AR" to "es-AR-ElenaNeural", // 西班牙语(阿根廷) - 女声
        "es-CO" to "es-CO-SalomeNeural", // 西班牙语(哥伦比亚) - 女声
        "es-CL" to "es-CL-CatalinaNeural", // 西班牙语(智利) - 女声
        "es-PE" to "es-PE-CamilaNeural", // 西班牙语(秘鲁) - 女声
        "es-VE" to "es-VE-PaolaNeural", // 西班牙语(委内瑞拉) - 女声
        "es-EC" to "es-EC-AndreaNeural", // 西班牙语(厄瓜多尔) - 女声
        "es-GT" to "es-GT-MartaNeural", // 西班牙语(危地马拉) - 女声
        "es-CR" to "es-CR-MariaNeural", // 西班牙语(哥斯达黎加) - 女声
        "es-PA" to "es-PA-MargaritaNeural", // 西班牙语(巴拿马) - 女声
        "es-DO" to "es-DO-RamonaNeural", // 西班牙语(多米尼加) - 女声
        "es-BO" to "es-BO-SofiaNeural", // 西班牙语(玻利维亚) - 女声
        "es-HN" to "es-HN-KarlaNeural", // 西班牙语(洪都拉斯) - 女声
        "es-PY" to "es-PY-TaniaNeural", // 西班牙语(巴拉圭) - 女声
        "es-SV" to "es-SV-LorenaNeural", // 西班牙语(萨尔瓦多) - 女声
        "es-NI" to "es-NI-YolandaNeural", // 西班牙语(尼加拉瓜) - 女声
        "es-CU" to "es-CU-BelkysNeural", // 西班牙语(古巴) - 女声
        "es-PR" to "es-PR-KarinaNeural", // 西班牙语(波多黎各) - 女声
        "es-UY" to "es-UY-ValentinaNeural", // 西班牙语(乌拉圭) - 女声
        "es-GQ" to "es-GQ-TeresaNeural", // 西班牙语(赤道几内亚) - 女声

        // 法语语音
        "fr-FR" to "fr-FR-DeniseNeural", // 法语(法国) - 女声
        "fr-CA" to "fr-CA-SylvieNeural", // 法语(加拿大) - 女声
        "fr-BE" to "fr-BE-CharlineNeural", // 法语(比利时) - 女声
        "fr-CH" to "fr-CH-ArianeNeural", // 法语(瑞士) - 女声

        // 德语语音
        "de-DE" to "de-DE-KatjaNeural", // 德语(德国) - 女声
        "de-AT" to "de-AT-IngridNeural", // 德语(奥地利) - 女声
        "de-CH" to "de-CH-LeniNeural", // 德语(瑞士) - 女声

        // 意大利语语音
        "it-IT" to "it-IT-ElsaNeural", // 意大利语(意大利) - 女声
        "it-CH" to "it-IT-ElsaNeural", // 意大利语(瑞士) - 使用意大利语音

        // 葡萄牙语语音
        "pt-BR" to "pt-BR-FranciscaNeural", // 葡萄牙语(巴西) - 女声
        "pt-PT" to "pt-PT-RaquelNeural", // 葡萄牙语(葡萄牙) - 女声

        // 俄语语音
        "ru-RU" to "ru-RU-SvetlanaNeural", // 俄语(俄罗斯) - 女声

        // 日语语音
        "ja-JP" to "ja-JP-NanamiNeural", // 日语(日本) - 女声

        // 韩语语音
        "ko-KR" to "ko-KR-SunHiNeural", // 韩语(韩国) - 女声

        // 阿拉伯语语音
        "ar-SA" to "ar-SA-ZariyahNeural", // 阿拉伯语(沙特阿拉伯) - 女声
        "ar-EG" to "ar-EG-SalmaNeural", // 阿拉伯语(埃及) - 女声
        "ar-AE" to "ar-AE-FatimaNeural", // 阿拉伯语(阿联酋) - 女声
        "ar-BH" to "ar-BH-LailaNeural", // 阿拉伯语(巴林) - 女声
        "ar-DZ" to "ar-DZ-AminaNeural", // 阿拉伯语(阿尔及利亚) - 女声
        "ar-IQ" to "ar-IQ-RanaNeural", // 阿拉伯语(伊拉克) - 女声
        "ar-JO" to "ar-JO-SanaNeural", // 阿拉伯语(约旦) - 女声
        "ar-KW" to "ar-KW-NouraNeural", // 阿拉伯语(科威特) - 女声
        "ar-LB" to "ar-LB-LaylaNeural", // 阿拉伯语(黎巴嫩) - 女声
        "ar-LY" to "ar-LY-ImanNeural", // 阿拉伯语(利比亚) - 女声
        "ar-MA" to "ar-MA-MounaNeural", // 阿拉伯语(摩洛哥) - 女声
        "ar-OM" to "ar-OM-AyshaNeural", // 阿拉伯语(阿曼) - 女声
        "ar-QA" to "ar-QA-AmalNeural", // 阿拉伯语(卡塔尔) - 女声
        "ar-SY" to "ar-SY-AmanyNeural", // 阿拉伯语(叙利亚) - 女声
        "ar-TN" to "ar-TN-ReemNeural", // 阿拉伯语(突尼斯) - 女声
        "ar-YE" to "ar-YE-MaryamNeural", // 阿拉伯语(也门) - 女声
        "ar-IL" to "ar-SA-ZariyahNeural", // 阿拉伯语(以色列) - 使用沙特语音
        "ar-PS" to "ar-SA-ZariyahNeural", // 阿拉伯语(巴勒斯坦) - 使用沙特语音

        // 印地语语音
        "hi-IN" to "hi-IN-SwaraNeural", // 印地语(印度) - 女声

        // 泰语语音
        "th-TH" to "th-TH-PremwadeeNeural", // 泰语(泰国) - 女声

        // 越南语语音
        "vi-VN" to "vi-VN-HoaiMyNeural", // 越南语(越南) - 女声

        // 印度尼西亚语语音
        "id-ID" to "id-ID-GadisNeural", // 印度尼西亚语(印度尼西亚) - 女声

        // 马来语语音
        "ms-MY" to "ms-MY-YasminNeural", // 马来语(马来西亚) - 女声

        // 菲律宾语语音
        "fil-PH" to "fil-PH-BlessicaNeural", // 菲律宾语(菲律宾) - 女声

        // 荷兰语语音
        "nl-NL" to "nl-NL-FennaNeural", // 荷兰语(荷兰) - 女声
        "nl-BE" to "nl-BE-DenaNeural", // 荷兰语(比利时) - 女声

        // 瑞典语语音
        "sv-SE" to "sv-SE-SofieNeural", // 瑞典语(瑞典) - 女声

        // 挪威语语音
        "nb-NO" to "nb-NO-PernilleNeural", // 挪威语(挪威) - 女声

        // 丹麦语语音
        "da-DK" to "da-DK-ChristelNeural", // 丹麦语(丹麦) - 女声

        // 芬兰语语音
        "fi-FI" to "fi-FI-SelmaNeural", // 芬兰语(芬兰) - 女声

        // 波兰语语音
        "pl-PL" to "pl-PL-AgnieszkaNeural", // 波兰语(波兰) - 女声

        // 捷克语语音
        "cs-CZ" to "cs-CZ-VlastaNeural", // 捷克语(捷克) - 女声

        // 匈牙利语语音
        "hu-HU" to "hu-HU-NoemiNeural", // 匈牙利语(匈牙利) - 女声

        // 罗马尼亚语语音
        "ro-RO" to "ro-RO-AlinaNeural", // 罗马尼亚语(罗马尼亚) - 女声

        // 保加利亚语语音
        "bg-BG" to "bg-BG-KalinaNeural", // 保加利亚语(保加利亚) - 女声

        // 克罗地亚语语音
        "hr-HR" to "hr-HR-GabrijelaNeural", // 克罗地亚语(克罗地亚) - 女声

        // 塞尔维亚语语音
        "sr-RS" to "sr-RS-SophieNeural", // 塞尔维亚语(塞尔维亚) - 女声

        // 斯洛伐克语语音
        "sk-SK" to "sk-SK-ViktoriaNeural", // 斯洛伐克语(斯洛伐克) - 女声

        // 斯洛文尼亚语语音
        "sl-SI" to "sl-SI-PetraNeural", // 斯洛文尼亚语(斯洛文尼亚) - 女声

        // 立陶宛语语音
        "lt-LT" to "lt-LT-OnaNeural", // 立陶宛语(立陶宛) - 女声

        // 拉脱维亚语语音
        "lv-LV" to "lv-LV-EveritaNeural", // 拉脱维亚语(拉脱维亚) - 女声

        // 爱沙尼亚语语音
        "et-EE" to "et-EE-AnuNeural", // 爱沙尼亚语(爱沙尼亚) - 女声

        // 希腊语语音
        "el-GR" to "el-GR-AthinaNeural", // 希腊语(希腊) - 女声

        // 土耳其语语音
        "tr-TR" to "tr-TR-EmelNeural", // 土耳其语(土耳其) - 女声

        // 希伯来语语音
        "he-IL" to "he-IL-HilaNeural", // 希伯来语(以色列) - 女声

        // 乌克兰语语音
        "uk-UA" to "uk-UA-PolinaNeural" // 乌克兰语(乌克兰) - 女声
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化 AudioManager
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager

        // 请求麦克风权限
        requestMicrophonePermission()

        Log.d(TAG, "微软语音识别应用启动")

        // 测试粤语语音可用性
        testCantoneseVoiceAvailability()

        setContent {
            LlyaTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SpeechRecognitionScreen()
                }
            }
        }
    }

    private fun requestMicrophonePermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED -> {
                Log.d(TAG, "麦克风权限已预先授予")
            }
            else -> {
                Log.d(TAG, "请求麦克风权限")
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun SpeechRecognitionScreen() {
        val context = LocalContext.current
        val primaryLanguage = remember { mutableStateOf("zh-CN") }
        val targetLanguage = remember { mutableStateOf("en") }
        val recognizedText = remember { mutableStateOf("") }
        val translatedText = remember { mutableStateOf("") }
        val accumulatedTextState = remember { mutableStateOf("") }
        val isRecognizing = remember { mutableStateOf(false) }
        val statusMessage = remember { mutableStateOf("准备就绪") }
        val bidirectionalMode = remember { mutableStateOf(enableBidirectionalTranslation) }

        // TTS播放状态
        val isSpeaking = remember { mutableStateOf(false) }

        // 历史记录ViewModel及状态
        val historyViewModel: HistoryViewModel = viewModel()
        val historyRecords by historyViewModel.historyRecords.collectAsState()
        // 筛选只有翻译文本的记录
        val translationRecords = remember(historyRecords) {
            historyRecords.filter { it.translatedText.isNotEmpty() }.take(5)
        }
        val isHistoryPlaying by historyViewModel.isPlaying.collectAsState()
        val isHistoryLoading by historyViewModel.isLoading.collectAsState()

        // 下拉菜单状态
        var sourceLanguageMenuExpanded by remember { mutableStateOf(false) }
        var targetLanguageMenuExpanded by remember { mutableStateOf(false) }

        // 深色背景色和强调色
        val darkBgColor = Color(0xFF1E1D2B)
        val purpleColor = Color(0xFF8364FD)
        val cardBgColor = Color(0xFF33324A)

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(darkBgColor)
        ) {
            // 顶部标题栏
            TopAppBar(
                title = {
                    Text(
                        text = "同声传译",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { finish() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 添加播放模式选择按钮
                    IconButton(onClick = { showPlaybackModeDialog.value = true }) {
                        Icon(
                            imageVector = when (playbackMode.value) {
                                GoogleCloudServiceViewModel.PlaybackMode.SILENT -> Icons.Default.VolumeOff
                                GoogleCloudServiceViewModel.PlaybackMode.EARBUDS -> Icons.Default.Headset
                                GoogleCloudServiceViewModel.PlaybackMode.SPEAKER -> Icons.Default.VolumeUp
                                GoogleCloudServiceViewModel.PlaybackMode.DUAL_EARBUDS -> Icons.Default.HeadsetMic
                            },
                            contentDescription = "播放模式",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = darkBgColor,
                    titleContentColor = Color.White
                )
            )

            // 语言选择面板
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                color = cardBgColor
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 源语言
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { sourceLanguageMenuExpanded = true },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = supportedSpeechLanguages[primaryLanguage.value] ?: "选择源语言",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "源语言",
                                color = Color.Gray,
                                fontSize = 12.sp
                            )

                            DropdownMenu(
                                expanded = sourceLanguageMenuExpanded,
                                onDismissRequest = { sourceLanguageMenuExpanded = false },
                                modifier = Modifier.background(cardBgColor)
                            ) {
                                supportedSpeechLanguages.forEach { (code, name) ->
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                name,
                                                color = Color.White
                                            )
                                        },
                                        onClick = {
                                            primaryLanguage.value = code
                                            sourceLanguageMenuExpanded = false
                                        },
                                        colors = MenuDefaults.itemColors(
                                            textColor = Color.White
                                        )
                                    )
                                }
                            }
                        }

                        // 交换按钮
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(purpleColor)
                                .clickable {
                                    val sourceLang = primaryLanguage.value
                                    val targetLang = targetLanguage.value
                                    // 交换语言
                                    primaryLanguage.value = mapTranslationToSpeechLanguage(targetLang) ?: "en-US"
                                    targetLanguage.value = getTranslationLanguageCode(sourceLang)
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.SwapHoriz,
                                contentDescription = "交换语言",
                                tint = Color.White,
                                modifier = Modifier.size(24.dp)
                            )
                        }

                        // 目标语言
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { targetLanguageMenuExpanded = true },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = supportedTranslationLanguages[targetLanguage.value] ?: "选择目标语言",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "目标语言",
                                color = Color.Gray,
                                fontSize = 12.sp
                            )

                            DropdownMenu(
                                expanded = targetLanguageMenuExpanded,
                                onDismissRequest = { targetLanguageMenuExpanded = false },
                                modifier = Modifier.background(cardBgColor)
                            ) {
                                supportedTranslationLanguages.forEach { (code, name) ->
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                name,
                                                color = Color.White
                                            )
                                        },
                                        onClick = {
                                            targetLanguage.value = code
                                            targetLanguageMenuExpanded = false
                                        },
                                        colors = MenuDefaults.itemColors(
                                            textColor = Color.White
                                        )
                                    )
                                }
                            }
                        }
                    }

                    // 双向翻译默认开启，不显示开关
                    // 确保双向翻译始终开启
                    LaunchedEffect(Unit) {
                        bidirectionalMode.value = true
                        enableBidirectionalTranslation = true
                    }
                }
            }

            // 今天日期
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                val currentTime = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault()).format(java.util.Date())
                Text(
                    text = "今天 $currentTime",
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }

            // 整个内容区域改为可滚动
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                // 状态信息
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(12.dp))
                            .background(cardBgColor.copy(alpha = 0.7f))
                            .padding(12.dp)
                    ) {
                        Text(
                            text = "状态: ${statusMessage.value}",
                            color = Color.White,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                    Spacer(modifier = Modifier.height(12.dp))
                }

                // 当前识别的文本
                if (recognizedText.value.isNotEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(16.dp))
                                .background(cardBgColor)
                                .padding(16.dp)
                        ) {
                            Column {
                                Text(
                                    text = "识别结果",
                                    color = Color.Gray,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = recognizedText.value,
                                    color = Color.White,
                                    fontSize = 16.sp
                                )

                                if (isRecognizing.value) {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "正在识别",
                                            color = Color.White.copy(alpha = 0.6f),
                                            fontSize = 12.sp,
                                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                        )

                                        // 添加动态省略号
                                        val infiniteTransition = rememberInfiniteTransition(label = "typingDots")
                                        val dotsAlpha by infiniteTransition.animateFloat(
                                            initialValue = 0.4f,
                                            targetValue = 1.0f,
                                            animationSpec = infiniteRepeatable(
                                                animation = tween(500, easing = LinearEasing),
                                                repeatMode = RepeatMode.Reverse
                                            ),
                                            label = "dotAlpha"
                                        )

                                        Text(
                                            text = "...",
                                            color = Color.White.copy(alpha = dotsAlpha),
                                            fontSize = 12.sp,
                                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                        )
                                    }
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }

                // 当前翻译的文本
                if (translatedText.value.isNotEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(16.dp))
                                .background(purpleColor.copy(alpha = 0.7f))
                                .padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.Top
                            ) {
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = "翻译结果",
                                        color = Color.White.copy(alpha = 0.7f),
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = translatedText.value,
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }

                                // 播放按钮
                                if (translatedText.value.isNotEmpty() && !translatedText.value.startsWith("翻译中:")) {
                                    IconButton(
                                        onClick = {
                                            if (isSpeaking.value) {
                                                stopCurrentTts()
                                                isSpeaking.value = false
                                            } else {
                                                // 播放翻译文本
                                                val textToSpeak = translatedText.value.removePrefix("[").substringAfter("] ")
                                                if (textToSpeak.isNotEmpty()) {
                                                    isSpeaking.value = true
                                                    playTranslatedText(textToSpeak, targetLanguage.value) {
                                                        isSpeaking.value = false
                                                    }
                                                }
                                            }
                                        },
                                        modifier = Modifier.size(32.dp)
                                    ) {
                                        Icon(
                                            imageVector = if (isSpeaking.value) Icons.Default.Stop else Icons.Default.PlayArrow,
                                            contentDescription = if (isSpeaking.value) "停止播放" else "播放翻译",
                                            tint = Color.White,
                                            modifier = Modifier.size(20.dp)
                                        )
                                    }
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }

                // 累积识别的文本
                if (accumulatedTextState.value.isNotEmpty()) {
                    item {
                        Column {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "识别与翻译历史:",
                                    color = Color.White,
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    modifier = Modifier.weight(1f)
                                )


                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            // 显示历史记录列表，每条记录都有播放按钮（最新记录在上面）
                            val historyItems = remember(accumulatedTextState.value) {
                                accumulatedTextState.value.split("\n\n").filter { it.isNotEmpty() }
                            }

                            val listState = rememberLazyListState()

                            // 当历史记录更新时，自动滚动到顶部（最新记录）
                            LaunchedEffect(historyItems.size) {
                                if (historyItems.isNotEmpty()) {
                                    listState.animateScrollToItem(0)
                                }
                            }

                            LazyColumn(
                                state = listState,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(max = 400.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                reverseLayout = true // 反转布局，最新记录在上面
                            ) {
                                items(historyItems.size) { index ->
                                    val historyItem = historyItems[index]
                                    HistoryRecordCard(
                                        historyText = historyItem,
                                        onPlayOriginal = { originalText, language ->
                                            playTranslatedText(originalText, language) {}
                                        },
                                        onPlayTranslation = { translationText, language ->
                                            playTranslatedText(translationText, language) {}
                                        },
                                        cardBgColor = cardBgColor,
                                        purpleColor = purpleColor
                                    )
                                }
                            }
                        }
                    }
                }


            }

            // 底部控制栏 - 确保它始终可见
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                color = cardBgColor
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 录音状态提示
                    if (isRecognizing.value) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.Red.copy(alpha = 0.1f))
                                .padding(vertical = 4.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "正在录音",
                                color = Color.White,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 左侧按钮 - 播放功能
                        if (translatedText.value.isNotEmpty() && !translatedText.value.startsWith("翻译中:")) {
                            IconButton(
                                onClick = {
                                    if (isSpeaking.value) {
                                        stopCurrentTts()
                                        isSpeaking.value = false
                                    } else {
                                        // 播放翻译文本
                                        val textToSpeak = translatedText.value.removePrefix("[").substringAfter("] ")
                                        if (textToSpeak.isNotEmpty()) {
                                            isSpeaking.value = true
                                            playTranslatedText(textToSpeak, targetLanguage.value) {
                                                isSpeaking.value = false
                                            }
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .size(50.dp)
                                    .clip(CircleShape)
                                    .background(cardBgColor)
                            ) {
                                Icon(
                                    imageVector = if (isSpeaking.value) Icons.Default.Stop else Icons.Default.PlayArrow,
                                    contentDescription = if (isSpeaking.value) "停止播放" else "播放翻译",
                                    tint = Color.White
                                )
                            }
                        } else {
                            // 占位
                            Spacer(modifier = Modifier.size(50.dp))
                        }

                        // 语音输入按钮
                        Box(
                            modifier = Modifier
                                .size(70.dp)
                                .clip(CircleShape)
                                .background(if (isRecognizing.value) Color.Red else purpleColor)
                                .clickable {
                                    if (isRecognizing.value) {
                                        // 防止频繁点击
                                        if (isStopping) {
                                            Toast.makeText(context, "正在停止中，请稍候...", Toast.LENGTH_SHORT).show()
                                            return@clickable
                                        }

                                        statusMessage.value = "正在停止识别..."
                                        isStopping = true

                                        // 异步停止识别，避免阻塞UI
                                        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                                            try {
                                                stopRecognitionAsync()

                                                // 在主线程更新UI
                                                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                                                    isRecognizing.value = false
                                                    statusMessage.value = "识别已停止"
                                                    isStopping = false
                                                    Toast.makeText(context, "识别已停止", Toast.LENGTH_SHORT).show()
                                                }
                                            } catch (e: Exception) {
                                                Log.e(TAG, "停止识别失败: ${e.message}", e)
                                                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                                                    statusMessage.value = "停止失败: ${e.message}"
                                                    isStopping = false
                                                    Toast.makeText(context, "停止识别失败", Toast.LENGTH_SHORT).show()
                                                }
                                            }
                                        }
                                    } else {
                                        statusMessage.value = "初始化语音识别..."
                                        try {
                                            startContinuousRecognitionWithTranslation(
                                                sourceLanguage = primaryLanguage.value,
                                                targetLanguage = targetLanguage.value,
                                                onRecognized = { text ->
                                                    recognizedText.value = text
                                                    Log.d(TAG, "识别结果: $text")
                                                },
                                                onTranslated = { translated ->
                                                    translatedText.value = translated
                                                    Log.d(TAG, "翻译结果: $translated")
                                                },
                                                onAccumulated = { accumulated ->
                                                    accumulatedTextState.value = accumulated
                                                }
                                            )
                                            isRecognizing.value = true
                                            statusMessage.value = "识别中..."
                                            Toast.makeText(context, "开始识别，请说话", Toast.LENGTH_SHORT).show()
                                        } catch (e: Exception) {
                                            Log.e(TAG, "启动识别失败: ${e.message}", e)
                                            statusMessage.value = "启动失败: ${e.message}"
                                            Toast.makeText(context, "启动识别失败: ${e.message}", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            // 录音中的波纹动画
                            if (isRecognizing.value) {
                                val animatedSize = remember { Animatable(1f) }

                                LaunchedEffect(Unit) {
                                    animatedSize.animateTo(
                                        targetValue = 1.2f,
                                        animationSpec = infiniteRepeatable(
                                            animation = tween(800, easing = FastOutSlowInEasing),
                                            repeatMode = RepeatMode.Reverse
                                        )
                                    )
                                }

                                Box(
                                    modifier = Modifier
                                        .size(70.dp)
                                        .scale(animatedSize.value)
                                        .clip(CircleShape)
                                        .background(Color.Red.copy(alpha = 0.3f))
                                )
                            }

                            Icon(
                                imageVector = Icons.Default.Mic,
                                contentDescription = if (isRecognizing.value) "停止录音" else "开始录音",
                                tint = Color.White,
                                modifier = Modifier.size(32.dp)
                            )
                        }

                        // 右侧按钮 - 清除文本
                        IconButton(
                            onClick = {
                                recognizedText.value = ""
                                translatedText.value = ""
                                accumulatedTextState.value = ""
                                recognitionHistory.clear()
                                accumulatedText = ""
                            },
                            modifier = Modifier
                                .size(50.dp)
                                .clip(CircleShape)
                                .background(cardBgColor)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "清除",
                                tint = Color.White
                            )
                        }
                    }
                }
            }
        }

        // 显示播放模式选择对话框
        if (showPlaybackModeDialog.value) {
            PlaybackModeSelectionDialog(
                currentMode = playbackMode.value,
                onModeSelected = { mode ->
                    setPlaybackMode(mode)
                    showPlaybackModeDialog.value = false
                },
                onDismiss = { showPlaybackModeDialog.value = false }
            )
        }
    }


    // 修复后的连续识别和翻译方法 - 解决翻译丢失和累积文本消失问题
    private fun startContinuousRecognitionWithTranslation(
        sourceLanguage: String,
        targetLanguage: String,
        onRecognized: (String) -> Unit,
        onTranslated: (String) -> Unit,
        onAccumulated: (String) -> Unit
    ) {
        try {
            Log.d(TAG, "开始创建翻译配置，源语言: $sourceLanguage, 目标语言: $targetLanguage")

            // 使用语音密钥创建翻译配置
            val translationConfig = SpeechTranslationConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            Log.d(TAG, "翻译配置创建成功")

            // 设置源语言和目标语言
            translationConfig.speechRecognitionLanguage = sourceLanguage
            Log.d(TAG, "已设置识别语言: $sourceLanguage")

            // 启用自动语言检测（支持双向翻译）
            val candidateLanguages = if (enableBidirectionalTranslation) {
                listOf(sourceLanguage, mapTranslationToSpeechLanguage(targetLanguage) ?: "en-US")
            } else {
                listOf(sourceLanguage)
            }

            val autoDetectConfig = AutoDetectSourceLanguageConfig.fromLanguages(candidateLanguages)
            Log.d(TAG, "已创建自动语言检测配置，候选语言: $candidateLanguages")

            // 指定翻译目标语言
            val targetLangCode = getTranslationLanguageCode(targetLanguage)
            translationConfig.addTargetLanguage(targetLangCode)
            Log.d(TAG, "添加目标语言: $targetLangCode")

            // 双向翻译：同时添加源语言作为翻译目标
            if (enableBidirectionalTranslation) {
                val sourceLangCode = getTranslationLanguageCode(sourceLanguage)
                if (sourceLangCode != targetLangCode) {
                    translationConfig.addTargetLanguage(sourceLangCode)
                    Log.d(TAG, "添加源语言作为翻译目标: $sourceLangCode")
                }
            }

            // 启用连续语言检测
            translationConfig.setProperty(PropertyId.SpeechServiceConnection_LanguageIdMode, "Continuous")
            translationConfig.setProperty(PropertyId.SpeechServiceResponse_PostProcessingOption, "TrueText")
            translationConfig.setProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "1000")
            Log.d(TAG, "已设置连续检测属性")

            // 创建音频配置
            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()
            Log.d(TAG, "音频配置创建成功")

            // 创建识别器 - 使用自动语言检测配置
            translationRecognizer = TranslationRecognizer(translationConfig, autoDetectConfig, audioConfig)
            Log.d(TAG, "翻译识别器创建成功")

            // 创建语音合成器
            val synthesizerConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            synthesizerConfig.speechSynthesisVoiceName = getVoiceNameForLanguage(targetLanguage)
            synthesizer = SpeechSynthesizer(synthesizerConfig)
            Log.d(TAG, "语音合成器创建成功")

            // 注册识别事件
            translationRecognizer?.let { recognizer ->
                Log.d(TAG, "开始注册事件处理器")

                // 识别中事件
                recognizer.recognizing.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    if (recognizedText.isNotEmpty()) {
                        Log.d(TAG, "识别中: $recognizedText")
                        onRecognized("识别中: $recognizedText")

                        // 获取实时翻译结果
                        val detectedLanguage = getDetectedLanguageFromEvent(event)
                        val effectiveTargetLanguage = getEffectiveTargetLanguage(detectedLanguage, targetLanguage, sourceLanguage)

                        val translatedText = event.result.translations[effectiveTargetLanguage] ?: ""
                        if (translatedText.isNotEmpty()) {
                            Log.d(TAG, "实时翻译: $translatedText")
                            onTranslated("翻译中: $translatedText")
                        }
                    }
                }

                // 语言检测和翻译完成事件 - 修复翻译丢失问题的关键代码
                recognizer.recognized.addEventListener { _, event ->
                    Log.d(TAG, "识别结果: ${event.result.reason} - ${event.result.text}")

                    if (event.result.reason == ResultReason.TranslatedSpeech || event.result.reason == ResultReason.RecognizedSpeech) {
                        val detectedLanguage = getDetectedLanguageFromEvent(event)
                        val recognizedText = event.result.text

                        Log.d(TAG, "检测到语言: $detectedLanguage, 文本: $recognizedText")

                        // 过滤掉无用内容
                        if (shouldProcessText(recognizedText)) {
                            // 判断检测到的语言与目标语言是否匹配
                            val effectiveTargetLanguage = getEffectiveTargetLanguage(detectedLanguage, targetLanguage, sourceLanguage)

                            // 获取翻译文本 - 修复翻译丢失的关键代码
                            val translatedText = event.result.translations[effectiveTargetLanguage] ?: ""
                            Log.d(TAG, "最终翻译结果 ($effectiveTargetLanguage): $translatedText")

                            // 构建显示文本
                            val sourceLangName = getLanguageDisplayName(detectedLanguage ?: sourceLanguage)
                            val targetLangName = getLanguageDisplayName(effectiveTargetLanguage)

                            val displayText = "[$sourceLangName] $recognizedText"
                            val translationDisplayText = "[$targetLangName] $translatedText"

                            // 更新UI
                            onRecognized(displayText)
                            onTranslated(translationDisplayText)

                            // 添加到累积文本和历史记录 - 修复累积文本消失问题
                            synchronized(recognitionHistory) {
                                val fullDisplayText = "$displayText\n⟹ $translationDisplayText"
                                recognitionHistory.add(fullDisplayText)

                                // 确保累积文本不会丢失 - 保持所有历史记录
                                accumulatedText = recognitionHistory.joinToString("\n\n")
                                onAccumulated(accumulatedText)

                                Log.d(TAG, "已添加到历史记录，当前历史记录数量: ${recognitionHistory.size}")
                                Log.d(TAG, "累积文本长度: ${accumulatedText.length}")
                            }

                            // 将翻译文本加入TTS队列
                            if (translatedText.isNotEmpty()) {
                                Log.d(TAG, "将文本加入TTS队列: $translatedText")
                                enqueueTts(translatedText, effectiveTargetLanguage)
                            }
                        } else {
                            Log.d(TAG, "过滤掉无用文本: $recognizedText")
                        }
                    } else {
                        Log.d(TAG, "识别结果不是语音或翻译: ${event.result.reason}")
                    }
                }

                // 取消事件
                recognizer.canceled.addEventListener { _, event ->
                    Log.e(TAG, "识别被取消: ${event.reason}, 详情: ${event.errorDetails}")
                    onRecognized("识别被取消: ${event.errorDetails}")
                }

                // 识别结束事件
                recognizer.sessionStopped.addEventListener { _, _ ->
                    Log.d(TAG, "识别会话结束")
                }

                // 启动连续识别
                try {
                    Log.d(TAG, "开始启动连续识别...")
                    recognizer.startContinuousRecognitionAsync().get()
                    Log.d(TAG, "连续识别已启动")
                    onRecognized("请开始说话...")
                } catch (e: Exception) {
                    Log.e(TAG, "连续识别启动失败: ${e.message}")
                    throw e
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "识别初始化失败: ${ex.message}")
            ex.printStackTrace()
            throw ex
        }
    }

    // 获取检测到的语言
    private fun getDetectedLanguageFromEvent(event: com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionEventArgs): String? {
        try {
            val properties = event.result.properties
            if (properties != null) {
                val autoDetectResult = properties.getProperty(PropertyId.SpeechServiceConnection_AutoDetectSourceLanguageResult)
                if (autoDetectResult != null && autoDetectResult.isNotEmpty()) {
                    Log.d(TAG, "检测到的语言: $autoDetectResult")
                    return autoDetectResult
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "获取检测到的语言时出错", ex)
        }
        return null
    }

    // 将翻译语言代码映射为语音识别语言代码
    private fun mapTranslationToSpeechLanguage(translationCode: String): String? {
        return when (translationCode) {
            "en" -> "en-US"
            "zh-Hans" -> "zh-CN"
            "zh-Hant" -> "zh-TW"
            "ja" -> "ja-JP"
            "ko" -> "ko-KR"
            "fr" -> "fr-FR"
            "es" -> "es-ES"
            "de" -> "de-DE"
            "it" -> "it-IT"
            "ru" -> "ru-RU"
            else -> null
        }
    }

    // 获取翻译语言代码
    private fun getTranslationLanguageCode(speechLanguageCode: String): String {
        return when {
            speechLanguageCode.startsWith("zh-CN") -> "zh-Hans"
            speechLanguageCode.startsWith("zh-TW") -> "zh-Hant"
            speechLanguageCode.startsWith("en") -> "en"
            speechLanguageCode.startsWith("ja") -> "ja"
            speechLanguageCode.startsWith("ko") -> "ko"
            speechLanguageCode.startsWith("fr") -> "fr"
            speechLanguageCode.startsWith("de") -> "de"
            speechLanguageCode.startsWith("es") -> "es"
            speechLanguageCode.startsWith("it") -> "it"
            speechLanguageCode.startsWith("ru") -> "ru"
            speechLanguageCode.contains("-") -> speechLanguageCode.split("-")[0]
            else -> speechLanguageCode
        }
    }

    // 根据语言选择合适的TTS语音 - 支持完整的语音库
    private fun getVoiceNameForLanguage(language: String): String {
        Log.d(TAG, "为语言 '$language' 选择TTS语音")

        // 特殊处理粤语
        if (language.contains("yue") || language.contains("cantonese", ignoreCase = true)) {
            Log.d(TAG, "检测到粤语，强制使用粤语语音")
            return "yue-CN-XiaoMinNeural"
        }

        if (language == "zh-HK") {
            Log.d(TAG, "检测到香港中文，使用香港粤语语音")
            return "zh-HK-HiuMaanNeural"
        }

        // 首先尝试精确匹配
        ttsVoicePreferences[language]?.let {
            Log.d(TAG, "精确匹配到语音: $it")
            return it
        }

        // 如果没有精确匹配，尝试语言族匹配
        val languageFamily = language.substringBefore("-")
        Log.d(TAG, "语言族: $languageFamily")

        val selectedVoice = when (languageFamily) {
            "zh" -> ttsVoicePreferences["zh-CN"] ?: "zh-CN-XiaoxiaoNeural"
            "yue" -> ttsVoicePreferences["yue-CN"] ?: "yue-CN-XiaoMinNeural" // 粤语特殊处理，使用简体粤语
            "en" -> ttsVoicePreferences["en-US"] ?: "en-US-JennyNeural"
            "es" -> ttsVoicePreferences["es-ES"] ?: "es-ES-ElviraNeural"
            "fr" -> ttsVoicePreferences["fr-FR"] ?: "fr-FR-DeniseNeural"
            "de" -> ttsVoicePreferences["de-DE"] ?: "de-DE-KatjaNeural"
            "it" -> ttsVoicePreferences["it-IT"] ?: "it-IT-ElsaNeural"
            "pt" -> ttsVoicePreferences["pt-BR"] ?: "pt-BR-FranciscaNeural"
            "ru" -> ttsVoicePreferences["ru-RU"] ?: "ru-RU-SvetlanaNeural"
            "ja" -> ttsVoicePreferences["ja-JP"] ?: "ja-JP-NanamiNeural"
            "ko" -> ttsVoicePreferences["ko-KR"] ?: "ko-KR-SunHiNeural"
            "ar" -> ttsVoicePreferences["ar-SA"] ?: "ar-SA-ZariyahNeural"
            "hi" -> ttsVoicePreferences["hi-IN"] ?: "hi-IN-SwaraNeural"
            "th" -> ttsVoicePreferences["th-TH"] ?: "th-TH-PremwadeeNeural"
            "vi" -> ttsVoicePreferences["vi-VN"] ?: "vi-VN-HoaiMyNeural"
            "id" -> ttsVoicePreferences["id-ID"] ?: "id-ID-GadisNeural"
            "ms" -> ttsVoicePreferences["ms-MY"] ?: "ms-MY-YasminNeural"
            "fil" -> ttsVoicePreferences["fil-PH"] ?: "fil-PH-BlessicaNeural"
            "nl" -> ttsVoicePreferences["nl-NL"] ?: "nl-NL-FennaNeural"
            "sv" -> ttsVoicePreferences["sv-SE"] ?: "sv-SE-SofieNeural"
            "nb" -> ttsVoicePreferences["nb-NO"] ?: "nb-NO-PernilleNeural"
            "da" -> ttsVoicePreferences["da-DK"] ?: "da-DK-ChristelNeural"
            "fi" -> ttsVoicePreferences["fi-FI"] ?: "fi-FI-SelmaNeural"
            "pl" -> ttsVoicePreferences["pl-PL"] ?: "pl-PL-AgnieszkaNeural"
            "cs" -> ttsVoicePreferences["cs-CZ"] ?: "cs-CZ-VlastaNeural"
            "hu" -> ttsVoicePreferences["hu-HU"] ?: "hu-HU-NoemiNeural"
            "ro" -> ttsVoicePreferences["ro-RO"] ?: "ro-RO-AlinaNeural"
            "bg" -> ttsVoicePreferences["bg-BG"] ?: "bg-BG-KalinaNeural"
            "hr" -> ttsVoicePreferences["hr-HR"] ?: "hr-HR-GabrijelaNeural"
            "sr" -> ttsVoicePreferences["sr-RS"] ?: "sr-RS-SophieNeural"
            "sk" -> ttsVoicePreferences["sk-SK"] ?: "sk-SK-ViktoriaNeural"
            "sl" -> ttsVoicePreferences["sl-SI"] ?: "sl-SI-PetraNeural"
            "lt" -> ttsVoicePreferences["lt-LT"] ?: "lt-LT-OnaNeural"
            "lv" -> ttsVoicePreferences["lv-LV"] ?: "lv-LV-EveritaNeural"
            "et" -> ttsVoicePreferences["et-EE"] ?: "et-EE-AnuNeural"
            "el" -> ttsVoicePreferences["el-GR"] ?: "el-GR-AthinaNeural"
            "tr" -> ttsVoicePreferences["tr-TR"] ?: "tr-TR-EmelNeural"
            "he" -> ttsVoicePreferences["he-IL"] ?: "he-IL-HilaNeural"
            "uk" -> ttsVoicePreferences["uk-UA"] ?: "uk-UA-PolinaNeural"
            else -> ttsVoicePreferences["en-US"] ?: "en-US-JennyNeural" // 默认使用美式英语
        }

        Log.d(TAG, "最终选择的语音: $selectedVoice")
        return selectedVoice
    }

    /**
     * 尝试粤语的备用语音
     */
    private fun tryAlternativeVoiceForCantonese(text: String, synthesizer: SpeechSynthesizer) {
        val alternativeVoices = listOf(
            "zh-HK-HiuGaaiNeural",  // 香港粤语备用女声
            "zh-HK-WanLungNeural",  // 香港粤语男声
            "zh-CN-XiaoxiaoNeural" // 如果粤语都不行，使用普通话
        )

        for (voice in alternativeVoices) {
            try {
                Log.d(TAG, "尝试备用语音: $voice")
                synthesizer.properties.setProperty(PropertyId.SpeechServiceConnection_SynthVoice, voice)
                val result = synthesizer.SpeakTextAsync(text).get()

                if (result?.reason == ResultReason.SynthesizingAudioCompleted) {
                    Log.d(TAG, "备用语音 $voice 成功播放")
                    return
                } else {
                    Log.w(TAG, "备用语音 $voice 失败: ${result?.reason}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "备用语音 $voice 异常: ${e.message}")
            }
        }

        Log.e(TAG, "所有粤语备用语音都失败")
    }

    /**
     * 测试粤语语音可用性
     */
    private fun testCantoneseVoiceAvailability() {
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始测试粤语语音可用性...")

                val testVoices = listOf(
                    "yue-CN-XiaoMinNeural",
                    "zh-HK-HiuMaanNeural",
                    "zh-HK-HiuGaaiNeural",
                    "zh-HK-WanLungNeural"
                )

                val testText = "你好"

                for (voice in testVoices) {
                    try {
                        Log.d(TAG, "测试语音: $voice")

                        val config = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
                        config.speechSynthesisVoiceName = voice
                        config.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3)

                        val testSynthesizer = SpeechSynthesizer(config)
                        val result = testSynthesizer.SpeakTextAsync(testText).get()

                        if (result?.reason == ResultReason.SynthesizingAudioCompleted) {
                            Log.d(TAG, "✅ 语音 $voice 可用，音频数据: ${result.audioData?.size ?: 0} 字节")
                        } else {
                            Log.w(TAG, "❌ 语音 $voice 不可用: ${result?.reason}")
                            if (result?.reason == ResultReason.Canceled) {
                                val cancellation = SpeechSynthesisCancellationDetails.fromResult(result)
                                Log.w(TAG, "   错误详情: ${cancellation.errorDetails}")
                            }
                        }

                        testSynthesizer.close()

                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 测试语音 $voice 异常: ${e.message}")
                    }
                }

                Log.d(TAG, "粤语语音可用性测试完成")

            } catch (e: Exception) {
                Log.e(TAG, "测试粤语语音可用性失败: ${e.message}")
            }
        }
    }

    /**
     * 尝试手动播放音频数据
     */
    private fun tryManualAudioPlayback(audioData: ByteArray, text: String) {
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始手动播放音频，数据大小: ${audioData.size} 字节")

                // 创建临时文件
                val tempFile = File.createTempFile("cantonese_tts", ".mp3", cacheDir)
                tempFile.writeBytes(audioData)

                Log.d(TAG, "音频文件已保存到: ${tempFile.absolutePath}")

                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                    try {
                        // 使用MediaPlayer播放
                        val mediaPlayer = android.media.MediaPlayer()

                        // 设置音频流类型
                        mediaPlayer.setAudioStreamType(android.media.AudioManager.STREAM_MUSIC)

                        // 设置数据源
                        mediaPlayer.setDataSource(tempFile.absolutePath)

                        // 准备播放
                        mediaPlayer.prepareAsync()

                        mediaPlayer.setOnPreparedListener { player ->
                            Log.d(TAG, "MediaPlayer准备完成，开始播放粤语音频")

                            // 确保音量设置正确
                            ensureAudioVolume()

                            // 强制设置为扬声器模式
                            if (playbackMode.value == GoogleCloudServiceViewModel.PlaybackMode.SPEAKER) {
                                forceAudioToSpeaker()
                            }

                            player.start()
                            Log.d(TAG, "粤语音频播放开始，时长: ${player.duration}ms")
                        }

                        mediaPlayer.setOnCompletionListener { player ->
                            Log.d(TAG, "粤语音频播放完成")
                            player.release()
                            tempFile.delete()
                        }

                        mediaPlayer.setOnErrorListener { player, what, extra ->
                            Log.e(TAG, "MediaPlayer播放错误: what=$what, extra=$extra")
                            player.release()
                            tempFile.delete()
                            true
                        }

                    } catch (e: Exception) {
                        Log.e(TAG, "MediaPlayer播放异常: ${e.message}")
                        tempFile.delete()
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "手动播放音频失败: ${e.message}")
            }
        }
    }

    /**
     * 确保音频音量设置正确
     */
    private fun ensureAudioVolume() {
        try {
            val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)

            Log.d(TAG, "当前音量: $currentVolume/$maxVolume")

            // 如果音量太低，设置为合适的音量
            if (currentVolume < maxVolume * 0.3) {
                val targetVolume = (maxVolume * 0.5).toInt()
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, targetVolume, 0)
                Log.d(TAG, "音量已调整为: $targetVolume/$maxVolume")
            }
        } catch (e: Exception) {
            Log.e(TAG, "调整音量失败: ${e.message}")
        }
    }

    /**
     * 强制音频输出到扬声器
     */
    private fun forceAudioToSpeaker() {
        try {
            val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
            audioManager.mode = AudioManager.MODE_NORMAL
            audioManager.isSpeakerphoneOn = true
            Log.d(TAG, "已强制设置为扬声器模式")
        } catch (e: Exception) {
            Log.e(TAG, "设置扬声器模式失败: ${e.message}")
        }
    }

    private fun shouldProcessText(text: String): Boolean {
        if (text.length <= 2) return false
        if (text.matches(Regex("^[\\p{Punct}\\s]+$"))) return false

        // 检查是否是TTS反馈
        if (isTtsFeedback(text)) {
            Log.d(TAG, "检测到TTS反馈，忽略: '$text'")
            return false
        }

        return true
    }

    // 检测是否是TTS反馈
    private fun isTtsFeedback(recognizedText: String): Boolean {
        // 如果当前没有播放TTS，不是反馈
        if (!isTtsPlaying) return false

        // 如果是扬声器模式，需要更严格的检测
        if (playbackMode.value == GoogleCloudServiceViewModel.PlaybackMode.SPEAKER) {
            val currentTime = System.currentTimeMillis()

            // 如果TTS刚开始播放（3秒内），很可能是反馈
            if (currentTime - ttsStartTime < 3000) {
                Log.d(TAG, "TTS播放中，时间差: ${currentTime - ttsStartTime}ms")
                return true
            }

            // 检查识别文本是否与最后播放的TTS文本相似
            if (lastTtsText.isNotEmpty()) {
                val similarity = calculateTextSimilarity(recognizedText.lowercase(), lastTtsText.lowercase())
                if (similarity > 0.5) { // 50%相似度阈值
                    Log.d(TAG, "检测到TTS反馈，相似度: $similarity, 识别: '$recognizedText', TTS: '$lastTtsText'")
                    return true
                }
            }
        }

        return false
    }

    // 计算文本相似度（简单的Levenshtein距离）
    private fun calculateTextSimilarity(text1: String, text2: String): Double {
        if (text1.isEmpty() && text2.isEmpty()) return 1.0
        if (text1.isEmpty() || text2.isEmpty()) return 0.0

        val maxLength = maxOf(text1.length, text2.length)
        val distance = levenshteinDistance(text1, text2)
        return 1.0 - (distance.toDouble() / maxLength)
    }

    // Levenshtein距离算法
    private fun levenshteinDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }

        for (i in 0..s1.length) dp[i][0] = i
        for (j in 0..s2.length) dp[0][j] = j

        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return dp[s1.length][s2.length]
    }

    private fun getLanguageDisplayName(languageCode: String): String {
        val translationName = supportedTranslationLanguages[languageCode]
        if (translationName != null) return translationName

        val speechName = supportedSpeechLanguages[languageCode]
        if (speechName != null) return speechName

        return languageCode
    }

    // 根据双向翻译逻辑确定实际的目标语言
    private fun getEffectiveTargetLanguage(detectedLanguage: String?, targetLanguage: String, sourceLanguage: String): String {
        if (!enableBidirectionalTranslation || detectedLanguage.isNullOrEmpty()) {
            return getTranslationLanguageCode(targetLanguage)
        }

        val detectedShortCode = if (detectedLanguage.contains("-")) {
            detectedLanguage.split("-")[0]
        } else detectedLanguage

        val targetShortCode = getTranslationLanguageCode(targetLanguage)

        // 如果检测到的语言与目标语言匹配，则使用源语言作为翻译目标
        if (detectedShortCode.equals(targetShortCode, ignoreCase = true)) {
            Log.d(TAG, "检测到的语言($detectedLanguage)与目标语言($targetLanguage)匹配，切换为源语言翻译")
            return getTranslationLanguageCode(sourceLanguage)
        }

        return getTranslationLanguageCode(targetLanguage)
    }

    private fun enqueueTts(text: String, language: String) {
        val ttsLanguage = getTtsLanguageFromTranslation(language)
        Log.d(TAG, "加入TTS队列: '$text', 语言: $language -> TTS语言: $ttsLanguage")

        ttsQueue.add(Pair(text, ttsLanguage))

        if (!isProcessingTts) {
            processTtsQueue()
        }
    }

    private fun processTtsQueue() {
        ttsExecutor.execute {
            isProcessingTts = true
            while (ttsQueue.isNotEmpty()) {
                val (text, language) = ttsQueue.poll() ?: break

                // 检查播放模式，静音模式下不播放
                if (playbackMode.value == GoogleCloudServiceViewModel.PlaybackMode.SILENT) {
                    Log.d(TAG, "静音模式，跳过语音合成: '$text'")
                    continue
                }

                try {
                    Log.d(TAG, "准备合成语音: '$text', 语言: $language, 播放模式: ${playbackMode.value}")

                    val voiceName = getVoiceNameForLanguage(language)
                    Log.d(TAG, "使用语音: $voiceName")

                    synchronized(this) {
                        try {
                            synthesizer?.close()
                        } catch (e: Exception) {
                            Log.e(TAG, "关闭现有语音合成器异常: ${e.message}")
                        }

                        val synthesizerConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
                        synthesizerConfig.speechSynthesisVoiceName = voiceName

                        // 根据播放模式配置音频输出
                        configureAudioOutputForSynthesizer(synthesizerConfig)

                        // 强制设置音频输出格式以确保兼容性
                        synthesizerConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3)

                        synthesizer = SpeechSynthesizer(synthesizerConfig)

                        val synth = synthesizer
                        if (synth != null) {
                            // 在扬声器模式下，暂停语音识别以避免反馈
                            pauseRecognitionForTts()

                            // 设置TTS播放状态，用于反馈检测
                            isTtsPlaying = true
                            lastTtsText = text
                            ttsStartTime = System.currentTimeMillis()

                            Log.d(TAG, "开始语音合成: '$text'")
                            Log.d(TAG, "使用语音: ${synth.properties.getProperty(PropertyId.SpeechServiceConnection_SynthVoice)}")

                            val result = synth.SpeakTextAsync(text).get()
                            if (result?.reason != ResultReason.SynthesizingAudioCompleted) {
                                Log.e(TAG, "语音合成失败: ${result?.reason}")
                                // 输出详细的错误信息
                                result?.let { r ->
                                    Log.e(TAG, "错误详情: ${r.reason}")
                                    if (r.reason == ResultReason.Canceled) {
                                        val cancellation = SpeechSynthesisCancellationDetails.fromResult(r)
                                        Log.e(TAG, "取消原因: ${cancellation.reason}")
                                        Log.e(TAG, "错误代码: ${cancellation.errorCode}")
                                        Log.e(TAG, "错误详情: ${cancellation.errorDetails}")

                                        // 如果是粤语语音失败，尝试备用语音
                                        if (voiceName.contains("yue") || voiceName.contains("HK")) {
                                            Log.w(TAG, "粤语语音失败，尝试使用备用语音")
                                            tryAlternativeVoiceForCantonese(text, synth)
                                        }
                                    }
                                }
                            } else {
                                Log.d(TAG, "语音合成成功完成: '$text'")
                                Log.d(TAG, "音频数据长度: ${result.audioData?.size ?: 0} 字节")

                                // 检查音频输出设备状态
                                val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
                                Log.d(TAG, "当前音频模式: ${audioManager.mode}")
                                Log.d(TAG, "扬声器状态: ${audioManager.isSpeakerphoneOn}")
                                Log.d(TAG, "音量: ${audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)}/${audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)}")
                                Log.d(TAG, "播放模式: ${playbackMode.value}")

                                // 特别针对粤语的音频播放诊断
                                if (voiceName.contains("yue") || voiceName.contains("HK")) {
                                    Log.w(TAG, "🔍 粤语音频播放诊断:")
                                    Log.w(TAG, "   语音名称: $voiceName")
                                    Log.w(TAG, "   音频数据: ${result.audioData?.size ?: 0} 字节")
                                    Log.w(TAG, "   播放模式: ${playbackMode.value}")
                                    Log.w(TAG, "   音频格式: ${synth.properties.getProperty(PropertyId.SpeechServiceConnection_SynthOutputFormat)}")

                                    // 尝试手动播放音频数据
                                    result.audioData?.let { audioData ->
                                        Log.w(TAG, "   尝试手动播放粤语音频...")
                                        tryManualAudioPlayback(audioData, text)
                                    }
                                }
                            }

                            // TTS播放完成，重置状态并恢复识别
                            isTtsPlaying = false
                            if (recognitionPausedForTts) {
                                resumeRecognitionAfterTts()
                            }
                        }
                    }
                } catch (ex: Exception) {
                    Log.e(TAG, "语音合成异常: ${ex.message}", ex)
                }

                try { Thread.sleep(300) } catch (e: InterruptedException) { }
            }
            isProcessingTts = false
        }
    }

    private fun getTtsLanguageFromTranslation(translationLanguage: String): String {
        return when {
            translationLanguage == "zh-Hans" || translationLanguage == "zh" -> "zh-CN"
            translationLanguage == "zh-Hant" -> "zh-TW"
            translationLanguage == "en" -> "en-US"
            translationLanguage == "ja" -> "ja-JP"
            translationLanguage == "ko" -> "ko-KR"
            translationLanguage == "fr" -> "fr-FR"
            translationLanguage == "de" -> "de-DE"
            translationLanguage == "es" -> "es-ES"
            translationLanguage == "it" -> "it-IT"
            translationLanguage == "ru" -> "ru-RU"
            translationLanguage.contains("-") -> translationLanguage
            else -> "$translationLanguage-$translationLanguage".uppercase()
        }
    }

    // 优化后的异步停止方法，解决卡顿问题
    private suspend fun stopRecognitionAsync() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始异步停止语音识别...")

            // 1. 首先停止TTS队列，避免新的合成任务
            ttsQueue.clear()
            isProcessingTts = false
            Log.d(TAG, "已清空TTS队列")

            // 2. 异步停止翻译识别器，设置超时
            translationRecognizer?.let { recognizer ->
                try {
                    Log.d(TAG, "正在停止翻译识别器...")

                    // 使用withTimeout设置超时，避免无限等待
                    withTimeout(3000) { // 3秒超时
                        // 异步停止，不使用.get()阻塞
                        val stopFuture = recognizer.stopContinuousRecognitionAsync()

                        // 在协程中等待完成
                        try {
                            // 使用Java Future的get方法，但在IO线程中执行
                            stopFuture.get()
                            Log.d(TAG, "翻译识别器已成功停止")
                        } catch (e: Exception) {
                            Log.w(TAG, "停止识别器时出现异常: ${e.message}")
                        }
                    }
                } catch (e: kotlinx.coroutines.TimeoutCancellationException) {
                    Log.w(TAG, "停止识别器超时，将强制关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "停止识别器失败: ${e.message}")
                }

                // 3. 关闭识别器资源
                try {
                    recognizer.close()
                    Log.d(TAG, "翻译识别器资源已关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "关闭翻译识别器资源异常: ${e.message}")
                }
            }

            translationRecognizer = null

            // 4. 异步关闭语音合成器
            launch {
                try {
                    synchronized(this@MicrosoftSpeechActivity) {
                        synthesizer?.let { synth ->
                            try {
                                synth.close()
                                Log.d(TAG, "语音合成器已关闭")
                            } catch (e: Exception) {
                                Log.e(TAG, "关闭语音合成器异常: ${e.message}")
                            }
                        }
                        synthesizer = null
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "关闭语音合成器时出错: ${e.message}")
                }
            }

            Log.d(TAG, "异步停止语音识别完成")

        } catch (ex: Exception) {
            Log.e(TAG, "异步停止识别时发生错误: ${ex.message}")
            ex.printStackTrace()
            throw ex
        }
    }

    // 保留原来的同步方法，用于onDestroy调用
    private fun stopRecognition() {
        try {
            Log.d(TAG, "开始停止语音识别...")

            // 快速清理，不等待网络响应
            ttsQueue.clear()
            isProcessingTts = false

            translationRecognizer?.let { recognizer ->
                try {
                    // 不使用.get()，避免阻塞
                    recognizer.stopContinuousRecognitionAsync()
                    Log.d(TAG, "已发送停止识别请求")
                } catch (e: Exception) {
                    Log.e(TAG, "发送停止请求失败: ${e.message}")
                }

                try {
                    recognizer.close()
                    Log.d(TAG, "翻译识别器已关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "关闭翻译识别器异常: ${e.message}")
                }
            }

            translationRecognizer = null

            synchronized(this) {
                try {
                    synthesizer?.close()
                } catch (e: Exception) {
                    Log.e(TAG, "关闭语音合成器异常: ${e.message}")
                }
                synthesizer = null
            }

            Log.d(TAG, "所有资源已关闭")
        } catch (ex: Exception) {
            Log.e(TAG, "停止识别时发生错误: ${ex.message}")
            ex.printStackTrace()
        }
    }

    // 播放翻译文本的方法
    private fun playTranslatedText(text: String, language: String, onComplete: () -> Unit) {
        try {
            // 检查播放模式，静音模式下不播放
            if (playbackMode.value == GoogleCloudServiceViewModel.PlaybackMode.SILENT) {
                Log.d(TAG, "静音模式，跳过播放翻译文本: '$text'")
                onComplete()
                return
            }

            val ttsLanguage = getTtsLanguageFromTranslation(language)
            Log.d(TAG, "播放翻译文本: '$text', 语言: $language -> TTS语言: $ttsLanguage, 播放模式: ${playbackMode.value}")

            // 停止当前播放
            stopCurrentTts()

            // 创建新的语音合成器
            val synthesizerConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            synthesizerConfig.speechSynthesisVoiceName = getVoiceNameForLanguage(ttsLanguage)

            // 根据播放模式配置音频输出
            configureAudioOutputForSynthesizer(synthesizerConfig)

            // 强制设置音频输出格式以确保兼容性
            synthesizerConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3)

            val currentSynthesizer = SpeechSynthesizer(synthesizerConfig)

            // 在扬声器模式下，暂停语音识别以避免反馈
            pauseRecognitionForTts()

            // 在后台线程执行TTS
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                try {
                    // 设置TTS播放状态，用于反馈检测
                    isTtsPlaying = true
                    lastTtsText = text
                    ttsStartTime = System.currentTimeMillis()

                    Log.d(TAG, "开始语音合成播放: '$text'")
                    val result = currentSynthesizer.SpeakTextAsync(text).get()

                    kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                        if (result?.reason == ResultReason.SynthesizingAudioCompleted) {
                            Log.d(TAG, "语音合成播放成功完成: '$text'")
                        } else {
                            Log.e(TAG, "语音合成播放失败: ${result?.reason}")
                        }

                        // TTS播放完成，重置状态并恢复识别
                        isTtsPlaying = false
                        if (recognitionPausedForTts) {
                            resumeRecognitionAfterTts()
                        }
                        onComplete()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "语音合成播放异常: ${e.message}", e)
                    kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                        // TTS播放异常，重置状态并恢复识别
                        isTtsPlaying = false
                        if (recognitionPausedForTts) {
                            resumeRecognitionAfterTts()
                        }
                        onComplete()
                    }
                } finally {
                    try {
                        currentSynthesizer.close()
                    } catch (e: Exception) {
                        Log.e(TAG, "关闭语音合成器异常: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "播放翻译文本失败: ${e.message}", e)
            onComplete()
        }
    }

    // 停止当前TTS播放
    private fun stopCurrentTts() {
        try {
            // 清空TTS队列
            ttsQueue.clear()
            isProcessingTts = false

            // 重置TTS反馈控制状态
            isTtsPlaying = false
            lastTtsText = ""
            ttsStartTime = 0L

            // 如果因为TTS暂停了识别，现在恢复
            if (recognitionPausedForTts) {
                resumeRecognitionAfterTts()
            }

            // 停止当前合成器
            synchronized(this) {
                try {
                    synthesizer?.let { synth ->
                        // 尝试停止当前播放
                        synth.close()
                        Log.d(TAG, "已停止当前TTS播放")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止TTS播放异常: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止当前TTS失败: ${e.message}")
        }
    }

    /**
     * 为TTS播放暂停语音识别（仅在扬声器模式下）
     */
    private fun pauseRecognitionForTts() {
        if (playbackMode.value == GoogleCloudServiceViewModel.PlaybackMode.SPEAKER && !recognitionPausedForTts) {
            try {
                translationRecognizer?.let { recognizer ->
                    Log.d(TAG, "暂停语音识别以避免TTS反馈")
                    recognizer.stopContinuousRecognitionAsync()
                    recognitionPausedForTts = true
                }
            } catch (e: Exception) {
                Log.e(TAG, "暂停语音识别失败: ${e.message}")
            }
        }
    }

    /**
     * TTS播放完成后恢复语音识别
     */
    private fun resumeRecognitionAfterTts() {
        if (recognitionPausedForTts) {
            try {
                translationRecognizer?.let { recognizer ->
                    Log.d(TAG, "TTS播放完成，恢复语音识别")
                    // 延迟500ms再恢复，确保TTS音频完全结束
                    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                        kotlinx.coroutines.delay(500)
                        try {
                            recognizer.startContinuousRecognitionAsync()
                            Log.d(TAG, "语音识别已恢复")
                        } catch (e: Exception) {
                            Log.e(TAG, "恢复语音识别失败: ${e.message}")
                        }
                    }
                    recognitionPausedForTts = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "恢复语音识别失败: ${e.message}")
                recognitionPausedForTts = false
            }
        }
    }

    /**
     * 设置播放模式
     */
    private fun setPlaybackMode(mode: GoogleCloudServiceViewModel.PlaybackMode) {
        playbackMode.value = mode
        configureAudioOutput()
        Log.d(TAG, "播放模式已设置为: $mode")
    }

    /**
     * 配置音频输出设置，根据播放模式调整
     */
    private fun configureAudioOutput() {
        try {
            when (playbackMode.value) {
                GoogleCloudServiceViewModel.PlaybackMode.SILENT -> {
                    // 静音模式 - 不播放任何声音
                    audioManager.isSpeakerphoneOn = false
                    audioManager.mode = AudioManager.MODE_NORMAL
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        audioManager.clearCommunicationDevice()
                    }
                    Log.d(TAG, "已设置为静音模式")
                }
                GoogleCloudServiceViewModel.PlaybackMode.EARBUDS -> {
                    // 耳机模式 - 音频输出到耳机
                    val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.9).toInt(), 0)
                    audioManager.isSpeakerphoneOn = false
                    audioManager.mode = AudioManager.MODE_NORMAL

                    // 清除通信设备设置，让系统自动选择耳机
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        audioManager.clearCommunicationDevice()
                    }
                    Log.d(TAG, "已设置为耳机模式")
                }
                GoogleCloudServiceViewModel.PlaybackMode.SPEAKER -> {
                    // 外放模式 - 强制使用扬声器
                    val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, maxVolume, 0)

                    // 强制使用扬声器的多种方法
                    audioManager.isSpeakerphoneOn = true
                    audioManager.mode = AudioManager.MODE_IN_COMMUNICATION

                    // 对于较新的Android版本，使用更直接的方法
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        try {
                            val devices = audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS)
                            val speakerDevice = devices.find {
                                it.type == android.media.AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
                            }
                            speakerDevice?.let { device ->
                                audioManager.setCommunicationDevice(device)
                                Log.d(TAG, "已设置通信设备为内置扬声器")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "设置通信设备失败: ${e.message}")
                        }
                    }

                    Log.d(TAG, "已设置为外放模式，扬声器状态: ${audioManager.isSpeakerphoneOn}")
                }
                GoogleCloudServiceViewModel.PlaybackMode.DUAL_EARBUDS -> {
                    // 双耳模式 - 优化双声道输出
                    val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.8).toInt(), 0)
                    audioManager.isSpeakerphoneOn = false
                    audioManager.mode = AudioManager.MODE_NORMAL

                    // 清除通信设备设置，让系统自动选择耳机
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        audioManager.clearCommunicationDevice()
                    }
                    Log.d(TAG, "已设置为双耳模式")
                }
            }

            // 在配置音频输出后请求音频焦点
            requestAudioFocus()

        } catch (e: Exception) {
            Log.e(TAG, "配置音频输出失败: ${e.message}", e)
        }
    }

    /**
     * 请求音频焦点 - 简化版本，避免冲突
     */
    private fun requestAudioFocus() {
        try {
            // 只在播放时请求音频焦点，避免与语音识别冲突
            if (playbackMode.value != GoogleCloudServiceViewModel.PlaybackMode.SILENT) {
                @Suppress("DEPRECATION")
                val result = audioManager.requestAudioFocus(
                    null, // 不设置监听器，避免冲突
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
                )
                Log.d(TAG, "请求音频焦点结果: $result")
            }
        } catch (e: Exception) {
            Log.e(TAG, "请求音频焦点失败: ${e.message}", e)
        }
    }

    /**
     * 释放音频焦点
     */
    private fun abandonAudioFocus() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                audioFocusRequest?.let { request ->
                    audioManager.abandonAudioFocusRequest(request as android.media.AudioFocusRequest)
                }
            } else {
                @Suppress("DEPRECATION")
                audioManager.abandonAudioFocus(null)
            }
            Log.d(TAG, "已释放音频焦点")
        } catch (e: Exception) {
            Log.e(TAG, "释放音频焦点失败: ${e.message}", e)
        }
    }

    /**
     * 为语音合成器配置音频输出
     */
    private fun configureAudioOutputForSynthesizer(synthesizerConfig: SpeechConfig) {
        try {
            // 强制设置音频输出到默认设备，确保能够播放
            Log.d(TAG, "配置语音合成器音频输出，播放模式: ${playbackMode.value}")

            // 在播放前再次确保音频路由正确
            when (playbackMode.value) {
                GoogleCloudServiceViewModel.PlaybackMode.SILENT -> {
                    // 静音模式 - 不进行任何音频配置，因为不会播放
                    Log.d(TAG, "静音模式，跳过音频配置")
                    return
                }
                GoogleCloudServiceViewModel.PlaybackMode.SPEAKER -> {
                    // 外放模式 - 强制音频路由到扬声器
                    forceAudioToSpeaker()
                    Log.d(TAG, "外放模式音频配置")
                }
                GoogleCloudServiceViewModel.PlaybackMode.EARBUDS -> {
                    // 耳机模式 - 确保音频路由到耳机
                    forceAudioToEarbuds()
                    Log.d(TAG, "耳机模式音频配置")
                }
                GoogleCloudServiceViewModel.PlaybackMode.DUAL_EARBUDS -> {
                    // 双耳模式 - 确保音频路由到耳机
                    forceAudioToEarbuds()
                    Log.d(TAG, "双耳模式音频配置")
                }
            }

            // 确保音量设置正确
            ensureAudioVolume()

            // 请求音频焦点
            requestAudioFocus()

            Log.d(TAG, "语音合成器音频配置完成")

        } catch (e: Exception) {
            Log.e(TAG, "配置语音合成器音频输出失败: ${e.message}", e)
        }
    }

    /**
     * 确保音频音量设置正确
     */
    private fun ensureAudioVolume() {
        try {
            val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)

            Log.d(TAG, "当前音量: $currentVolume/$maxVolume")

            // 如果音量太低，设置为合适的音量
            if (currentVolume < maxVolume * 0.3) {
                val targetVolume = (maxVolume * 0.7).toInt()
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, targetVolume, 0)
                Log.d(TAG, "音量已调整为: $targetVolume/$maxVolume")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置音量失败: ${e.message}")
        }
    }

    /**
     * 强制音频输出到扬声器
     */
    private fun forceAudioToSpeaker() {
        try {
            audioManager.isSpeakerphoneOn = true
            audioManager.mode = AudioManager.MODE_IN_COMMUNICATION

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                val devices = audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS)
                val speakerDevice = devices.find {
                    it.type == android.media.AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
                }
                speakerDevice?.let { device ->
                    audioManager.setCommunicationDevice(device)
                    Log.d(TAG, "强制设置音频输出到扬声器")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "强制音频到扬声器失败: ${e.message}")
        }
    }

    /**
     * 强制音频输出到耳机
     */
    private fun forceAudioToEarbuds() {
        try {
            audioManager.isSpeakerphoneOn = false
            audioManager.mode = AudioManager.MODE_NORMAL

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                audioManager.clearCommunicationDevice()
                Log.d(TAG, "强制设置音频输出到耳机")
            }
        } catch (e: Exception) {
            Log.e(TAG, "强制音频到耳机失败: ${e.message}")
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "活动销毁，清理资源")

        // 设置停止标志，防止新的操作
        isStopping = true

        // 停止TTS播放
        stopCurrentTts()

        // 释放音频焦点
        abandonAudioFocus()

        // 快速停止识别，不等待网络响应
        stopRecognition()

        // 强制关闭TTS执行器
        try {
            ttsExecutor.shutdownNow()
            Log.d(TAG, "TTS执行器已强制关闭")
        } catch (e: Exception) {
            Log.e(TAG, "关闭TTS执行器异常: ${e.message}")
        }

        super.onDestroy()
    }
}

/**
 * 历史记录卡片组件，显示单条翻译记录并提供播放按钮
 */
@Composable
fun HistoryRecordCard(
    historyText: String,
    onPlayOriginal: (String, String) -> Unit,
    onPlayTranslation: (String, String) -> Unit,
    cardBgColor: Color,
    purpleColor: Color
) {
    // 解析历史记录文本
    val lines = historyText.split("\n").filter { it.isNotEmpty() }
    if (lines.size < 2) return

    val originalLine = lines[0] // [语言] 原文
    val translationLine = lines.getOrNull(1)?.removePrefix("⟹ ") ?: "" // [语言] 翻译

    // 解析原文
    val originalLanguage = originalLine.substringAfter("[").substringBefore("]")
    val originalText = originalLine.substringAfter("] ")

    // 解析翻译
    val translationLanguage = translationLine.substringAfter("[").substringBefore("]")
    val translationText = translationLine.substringAfter("] ")

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp)),
        colors = CardDefaults.cardColors(containerColor = cardBgColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            // 原文行
            if (originalText.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = originalLanguage,
                            color = purpleColor,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = originalText,
                            color = Color.White,
                            fontSize = 14.sp,
                            lineHeight = 18.sp
                        )
                    }

                    IconButton(
                        onClick = {
                            val languageCode = getLanguageCodeFromDisplayName(originalLanguage)
                            onPlayOriginal(originalText, languageCode)
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "播放原文",
                            tint = purpleColor,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            // 分隔线
            if (originalText.isNotEmpty() && translationText.isNotEmpty()) {
                HorizontalDivider(
                    color = Color.Gray.copy(alpha = 0.3f),
                    thickness = 1.dp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // 翻译行
            if (translationText.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = translationLanguage,
                            color = Color.Cyan,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = translationText,
                            color = Color.White,
                            fontSize = 14.sp,
                            lineHeight = 18.sp
                        )
                    }

                    IconButton(
                        onClick = {
                            val languageCode = getLanguageCodeFromDisplayName(translationLanguage)
                            onPlayTranslation(translationText, languageCode)
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "播放翻译",
                            tint = Color.Cyan,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 将显示名称转换为语言代码
 */
private fun getLanguageCodeFromDisplayName(displayName: String): String {
    return when (displayName) {
        // 中文
        "中文(普通话，简体)" -> "zh-CN"
        "中文(台湾普通话，繁体)" -> "zh-TW"
        "中文(粤语，繁体)" -> "zh-HK"
        "中文(粤语，简体)" -> "yue-CN"
        "中文(吴语，简体)" -> "wuu-CN"

        // 英语
        "英语(美国)" -> "en-US"
        "英语(英国)" -> "en-GB"
        "英语(澳大利亚)" -> "en-AU"
        "英语(加拿大)" -> "en-CA"
        "英语(印度)" -> "en-IN"
        "英语(爱尔兰)" -> "en-IE"
        "英语(新西兰)" -> "en-NZ"
        "英语(南非)" -> "en-ZA"
        "英语(新加坡)" -> "en-SG"
        "英语(香港)" -> "en-HK"
        "英语(菲律宾)" -> "en-PH"
        "英语(尼日利亚)" -> "en-NG"
        "英语(肯尼亚)" -> "en-KE"
        "英语(加纳)" -> "en-GH"
        "英语(坦桑尼亚)" -> "en-TZ"

        // 西班牙语
        "西班牙语(西班牙)" -> "es-ES"
        "西班牙语(墨西哥)" -> "es-MX"
        "西班牙语(美国)" -> "es-US"
        "西班牙语(阿根廷)" -> "es-AR"
        "西班牙语(哥伦比亚)" -> "es-CO"
        "西班牙语(智利)" -> "es-CL"
        "西班牙语(秘鲁)" -> "es-PE"
        "西班牙语(委内瑞拉)" -> "es-VE"
        "西班牙语(厄瓜多尔)" -> "es-EC"
        "西班牙语(危地马拉)" -> "es-GT"
        "西班牙语(哥斯达黎加)" -> "es-CR"
        "西班牙语(巴拿马)" -> "es-PA"
        "西班牙语(多米尼加)" -> "es-DO"
        "西班牙语(玻利维亚)" -> "es-BO"
        "西班牙语(洪都拉斯)" -> "es-HN"
        "西班牙语(巴拉圭)" -> "es-PY"
        "西班牙语(萨尔瓦多)" -> "es-SV"
        "西班牙语(尼加拉瓜)" -> "es-NI"
        "西班牙语(古巴)" -> "es-CU"
        "西班牙语(波多黎各)" -> "es-PR"
        "西班牙语(乌拉圭)" -> "es-UY"
        "西班牙语(赤道几内亚)" -> "es-GQ"

        // 法语
        "法语(法国)" -> "fr-FR"
        "法语(加拿大)" -> "fr-CA"
        "法语(比利时)" -> "fr-BE"
        "法语(瑞士)" -> "fr-CH"

        // 德语
        "德语(德国)" -> "de-DE"
        "德语(奥地利)" -> "de-AT"
        "德语(瑞士)" -> "de-CH"

        // 意大利语
        "意大利语(意大利)" -> "it-IT"
        "意大利语(瑞士)" -> "it-CH"

        // 葡萄牙语
        "葡萄牙语(巴西)" -> "pt-BR"
        "葡萄牙语(葡萄牙)" -> "pt-PT"

        // 俄语
        "俄语(俄罗斯)" -> "ru-RU"

        // 日语
        "日语(日本)" -> "ja-JP"

        // 韩语
        "韩语(韩国)" -> "ko-KR"

        // 阿拉伯语
        "阿拉伯语(沙特阿拉伯)" -> "ar-SA"
        "阿拉伯语(埃及)" -> "ar-EG"
        "阿拉伯语(阿联酋)" -> "ar-AE"
        "阿拉伯语(巴林)" -> "ar-BH"
        "阿拉伯语(阿尔及利亚)" -> "ar-DZ"
        "阿拉伯语(伊拉克)" -> "ar-IQ"
        "阿拉伯语(约旦)" -> "ar-JO"
        "阿拉伯语(科威特)" -> "ar-KW"
        "阿拉伯语(黎巴嫩)" -> "ar-LB"
        "阿拉伯语(利比亚)" -> "ar-LY"
        "阿拉伯语(摩洛哥)" -> "ar-MA"
        "阿拉伯语(阿曼)" -> "ar-OM"
        "阿拉伯语(卡塔尔)" -> "ar-QA"
        "阿拉伯语(叙利亚)" -> "ar-SY"
        "阿拉伯语(突尼斯)" -> "ar-TN"
        "阿拉伯语(也门)" -> "ar-YE"
        "阿拉伯语(以色列)" -> "ar-IL"
        "阿拉伯语(巴勒斯坦)" -> "ar-PS"

        // 印地语
        "印地语(印度)" -> "hi-IN"

        // 泰语
        "泰语(泰国)" -> "th-TH"

        // 越南语
        "越南语(越南)" -> "vi-VN"

        // 印度尼西亚语
        "印度尼西亚语(印度尼西亚)" -> "id-ID"

        // 马来语
        "马来语(马来西亚)" -> "ms-MY"

        // 菲律宾语
        "菲律宾语(菲律宾)" -> "fil-PH"

        // 荷兰语
        "荷兰语(荷兰)" -> "nl-NL"
        "荷兰语(比利时)" -> "nl-BE"

        // 瑞典语
        "瑞典语(瑞典)" -> "sv-SE"

        // 挪威语
        "挪威语(挪威)" -> "nb-NO"

        // 丹麦语
        "丹麦语(丹麦)" -> "da-DK"

        // 芬兰语
        "芬兰语(芬兰)" -> "fi-FI"

        // 波兰语
        "波兰语(波兰)" -> "pl-PL"

        // 捷克语
        "捷克语(捷克)" -> "cs-CZ"

        // 匈牙利语
        "匈牙利语(匈牙利)" -> "hu-HU"

        // 罗马尼亚语
        "罗马尼亚语(罗马尼亚)" -> "ro-RO"

        // 保加利亚语
        "保加利亚语(保加利亚)" -> "bg-BG"

        // 克罗地亚语
        "克罗地亚语(克罗地亚)" -> "hr-HR"

        // 塞尔维亚语
        "塞尔维亚语(塞尔维亚)" -> "sr-RS"

        // 斯洛伐克语
        "斯洛伐克语(斯洛伐克)" -> "sk-SK"

        // 斯洛文尼亚语
        "斯洛文尼亚语(斯洛文尼亚)" -> "sl-SI"

        // 立陶宛语
        "立陶宛语(立陶宛)" -> "lt-LT"

        // 拉脱维亚语
        "拉脱维亚语(拉脱维亚)" -> "lv-LV"

        // 爱沙尼亚语
        "爱沙尼亚语(爱沙尼亚)" -> "et-EE"

        // 希腊语
        "希腊语(希腊)" -> "el-GR"

        // 土耳其语
        "土耳其语(土耳其)" -> "tr-TR"

        // 希伯来语
        "希伯来语(以色列)" -> "he-IL"

        // 乌克兰语
        "乌克兰语(乌克兰)" -> "uk-UA"

        // 兼容旧版本的简化名称
        "中文(简体)" -> "zh-CN"
        "中文(繁体)" -> "zh-TW"
        "英语" -> "en-US"
        "日语" -> "ja-JP"
        "韩语" -> "ko-KR"
        "法语" -> "fr-FR"
        "德语" -> "de-DE"
        "西班牙语" -> "es-ES"
        "意大利语" -> "it-IT"
        "俄语" -> "ru-RU"

        else -> "en-US" // 默认英语
    }
}

@Preview(showBackground = true)
@Composable
fun MicrosoftSpeechPreview() {
    LlyaTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            // Preview content
        }
    }
}