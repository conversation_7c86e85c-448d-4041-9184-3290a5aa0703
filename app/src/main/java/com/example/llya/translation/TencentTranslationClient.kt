package com.example.llya.translation

import android.util.Log
import com.example.llya.utils.SignatureUtil
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit

class TencentTranslationClient(
    private val secretId: String,
    private val secretKey: String,
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "TranslationClient"
        private const val API_URL = "https://tmt.tencentcloudapi.com"
    }

    private val _translationResult = MutableStateFlow<String>("")
    val translationResult: StateFlow<String> = _translationResult

    private val client = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(10, TimeUnit.SECONDS)
        .build()

    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()

    private val responseAdapter: JsonAdapter<TranslationResponse> = 
        moshi.adapter(TranslationResponse::class.java)

    // 添加一个独立的签名生成器类
    class TC3Signer {
        companion object {
            private const val ALGORITHM = "TC3-HMAC-SHA256"
            private const val CHARSET = "UTF-8"
            
            fun generateSignature(
                secretId: String,
                secretKey: String,
                service: String,
                host: String,
                region: String,
                action: String,
                version: String,
                timestamp: Long,
                requestBody: String
            ): Map<String, String> {
                // 获取UTC时间并格式化
                val utcTime = java.util.Calendar.getInstance(java.util.TimeZone.getTimeZone("UTC"))
                utcTime.timeInMillis = timestamp * 1000
                val date = "${utcTime.get(java.util.Calendar.YEAR)}-" +
                        "${String.format("%02d", utcTime.get(java.util.Calendar.MONTH) + 1)}-" +
                        String.format("%02d", utcTime.get(java.util.Calendar.DAY_OF_MONTH))
                
                val timestampStr = timestamp.toString()
                
                // 步骤1：构建规范请求
                val httpRequestMethod = "POST"
                val canonicalUri = "/"
                val canonicalQueryString = ""
                val contentType = "application/json; charset=utf-8"
                
                // 重要：header名称必须小写，且冒号后不能有空格
                val canonicalHeaders = "content-type:$contentType\nhost:$host\nx-tc-action:${action.lowercase()}\n"
                val signedHeaders = "content-type;host;x-tc-action"
                
                val hashedRequestPayload = sha256Hex(requestBody.toByteArray(charset(CHARSET))).lowercase()
                
                val canonicalRequest = "$httpRequestMethod\n$canonicalUri\n$canonicalQueryString\n$canonicalHeaders\n$signedHeaders\n$hashedRequestPayload"
                
                // 步骤2：构建待签名字符串
                val credentialScope = "$date/$service/tc3_request"
                val hashedCanonicalRequest = sha256Hex(canonicalRequest.toByteArray(charset(CHARSET))).lowercase()
                
                // 使用官方示例中的方式构建
                val stringToSign = "$ALGORITHM\n$timestampStr\n$credentialScope\n$hashedCanonicalRequest"
                
                // 步骤3：计算签名
                val secretKeyBin = "TC3$secretKey".toByteArray(charset(CHARSET))
                val dateBin = date.toByteArray(charset(CHARSET))
                
                val secretDate = hmacSHA256(secretKeyBin, dateBin)
                val secretService = hmacSHA256(secretDate, service.toByteArray(charset(CHARSET)))
                val secretSigning = hmacSHA256(secretService, "tc3_request".toByteArray(charset(CHARSET)))
                val signature = bytesToHex(hmacSHA256(secretSigning, stringToSign.toByteArray(charset(CHARSET)))).lowercase()
                
                // 步骤4：构建授权信息
                val authorization = "$ALGORITHM Credential=$secretId/$credentialScope, SignedHeaders=$signedHeaders, Signature=$signature"
                
                // 输出详细的调试信息
                android.util.Log.d("TC3Signer", "签名生成详情:")
                android.util.Log.d("TC3Signer", "日期: $date")
                android.util.Log.d("TC3Signer", "时间戳: $timestampStr")
                android.util.Log.d("TC3Signer", "规范请求串:\n$canonicalRequest")
                android.util.Log.d("TC3Signer", "规范请求哈希: $hashedCanonicalRequest")
                android.util.Log.d("TC3Signer", "凭证范围: $credentialScope")
                android.util.Log.d("TC3Signer", "待签名字符串:\n$stringToSign")
                android.util.Log.d("TC3Signer", "最终授权头: $authorization")
                
                // 返回所有需要的HTTP头
                return mapOf(
                    "Authorization" to authorization,
                    "Content-Type" to contentType,
                    "Host" to host,
                    "X-TC-Action" to action,
                    "X-TC-Timestamp" to timestampStr,
                    "X-TC-Version" to version,
                    "X-TC-Region" to region
                )
            }
            
            private fun sha256Hex(data: ByteArray): String {
                val md = java.security.MessageDigest.getInstance("SHA-256")
                val digest = md.digest(data)
                return bytesToHex(digest).lowercase()
            }
            
            private fun hmacSHA256(key: ByteArray, data: ByteArray): ByteArray {
                val mac = javax.crypto.Mac.getInstance("HmacSHA256")
                val secretKeySpec = javax.crypto.spec.SecretKeySpec(key, "HmacSHA256")
                mac.init(secretKeySpec)
                return mac.doFinal(data)
            }
            
            private fun bytesToHex(bytes: ByteArray): String {
                val hexArray = "0123456789abcdef".toCharArray()
                val hexChars = CharArray(bytes.size * 2)
                for (j in bytes.indices) {
                    val v = bytes[j].toInt() and 0xFF
                    hexChars[j * 2] = hexArray[v ushr 4]
                    hexChars[j * 2 + 1] = hexArray[v and 0x0F]
                }
                return String(hexChars)
            }
        }
    }

    // 执行翻译请求
    fun translate(text: String, sourceLanguage: String, targetLanguage: String) {
        if (text.isEmpty()) {
            _translationResult.value = ""
            return
        }

        coroutineScope.launch(Dispatchers.IO) {
            try {
                // 定义API参数
                val service = "tmt"
                val host = "tmt.tencentcloudapi.com"
                val region = "ap-guangzhou"
                val action = "TextTranslate"
                val version = "2018-03-21"
                
                // 获取当前UTC时间戳
                val timestamp = System.currentTimeMillis() / 1000
                
                // 将源语言和目标语言代码转换
                val sourceCode = mapLanguageCode(sourceLanguage)
                val targetCode = mapLanguageCode(targetLanguage)
                
                // 构造请求体
                val requestBody = """{"ProjectId":0,"Source":"$sourceCode","Target":"$targetCode","SourceText":"$text"}"""
                
                // 生成签名和请求头
                val headers = TC3Signer.generateSignature(
                    secretId,
                    secretKey,
                    service,
                    host,
                    region,
                    action,
                    version,
                    timestamp,
                    requestBody
                )
                
                // 输出调试信息
                Log.d(TAG, "===== 翻译请求详情 =====")
                Log.d(TAG, "时间戳: ${timestamp}")
                Log.d(TAG, "请求体: $requestBody")
                headers.forEach { (key, value) ->
                    Log.d(TAG, "$key: $value")
                }
                
                // 构建HTTP请求
                val mediaType = "application/json".toMediaType()
                val okHttpRequestBody = requestBody.toRequestBody(mediaType)
                
                val requestBuilder = Request.Builder().url(API_URL)
                
                // 添加所有头部
                headers.forEach { (key, value) ->
                    requestBuilder.addHeader(key, value)
                }
                
                val request = requestBuilder.post(okHttpRequestBody).build()
                
                // 输出完整请求信息
                Log.d(TAG, "请求URL: ${request.url}")
                Log.d(TAG, "请求头:")
                request.headers.forEach { 
                    Log.d(TAG, "  ${it.first}: ${it.second}") 
                }
                Log.d(TAG, "===== 翻译请求结束 =====")
                
                // 发送请求并处理响应
                client.newCall(request).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "翻译请求失败: ${e.message}", e)
                        coroutineScope.launch {
                            _translationResult.value = "翻译失败: ${e.message}"
                        }
                    }
                    
                    override fun onResponse(call: Call, response: okhttp3.Response) {
                        try {
                            val responseString = response.body?.string()
                            Log.d(TAG, "翻译API响应: $responseString")
                            
                            if (responseString != null) {
                                val translationResponse = responseAdapter.fromJson(responseString)
                                if (translationResponse != null) {
                                    if (translationResponse.Response?.Error != null) {
                                        // API返回了错误信息
                                        val errorCode = translationResponse.Response.Error.Code ?: "未知"
                                        val errorMsg = translationResponse.Response.Error.Message ?: "未知错误"
                                        val requestId = translationResponse.Response.RequestId ?: "无请求ID"
                                        
                                        Log.e(TAG, "翻译API错误: $errorCode - $errorMsg")
                                        Log.e(TAG, "请求ID: $requestId")
                                        
                                        // 检查特定错误类型
                                        if (errorCode.contains("AuthFailure")) {
                                            // 认证失败，打印更详细的信息
                                            Log.e(TAG, "认证错误: $errorMsg")
                                            Log.e(TAG, "==检查以下可能原因==")
                                            Log.e(TAG, "1. 签名算法错误")
                                            Log.e(TAG, "2. 日期格式不正确")
                                            Log.e(TAG, "3. 密钥ID或Key错误")
                                            Log.e(TAG, "4. 请求格式不符合要求")
                                        }
                                        
                                        coroutineScope.launch {
                                            _translationResult.value = "翻译失败: $errorMsg"
                                        }
                                    } else if (translationResponse.Response?.TargetText != null) {
                                        // 成功获取翻译结果
                                        coroutineScope.launch {
                                            _translationResult.value = translationResponse.Response.TargetText
                                        }
                                    } else {
                                        // Response存在但没有翻译结果
                                        coroutineScope.launch {
                                            _translationResult.value = "翻译失败: 返回数据格式异常"
                                        }
                                    }
                                } else {
                                    coroutineScope.launch {
                                        _translationResult.value = "翻译失败: 解析响应数据失败"
                                    }
                                }
                            } else {
                                coroutineScope.launch {
                                    _translationResult.value = "翻译失败: 响应为空"
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析翻译响应失败: ${e.message}", e)
                            coroutineScope.launch {
                                _translationResult.value = "解析翻译失败: ${e.message}"
                            }
                        } finally {
                            response.body?.close()
                        }
                    }
                })
                
            } catch (e: Exception) {
                Log.e(TAG, "准备翻译请求失败: ${e.message}", e)
                _translationResult.value = "翻译失败: ${e.message}"
            }
        }
    }
    
    // 将ASR语言代码映射到翻译API语言代码
    private fun mapLanguageCode(asrLanguage: String): String {
        return when(asrLanguage) {
            "16k_zh" -> "zh"
            "16k_en" -> "en"
            "16k_ca" -> "zh-TW"
            "16k_ja" -> "ja"
            "16k_ko" -> "ko"
            "16k_th" -> "th"
            "16k_vi" -> "vi"
            "16k_ms" -> "ms"
            "16k_id" -> "id"
            else -> "zh"
        }
    }
    
    // 响应数据类
    data class TranslationResponse(
        val Response: TranslationData? = null
    )
    
    data class TranslationData(
        val RequestId: String? = null,
        val Source: String? = null,
        val Target: String? = null,
        val TargetText: String? = null,
        val Error: ErrorInfo? = null
    )
    
    data class ErrorInfo(
        val Code: String? = null,
        val Message: String? = null
    )
} 