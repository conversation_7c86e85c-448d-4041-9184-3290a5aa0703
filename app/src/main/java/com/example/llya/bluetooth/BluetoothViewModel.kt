package com.example.llya.bluetooth

import android.app.Application
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.cancel
import com.example.llya.bluetooth.ConnectionState
import com.example.llya.bluetooth.BluetoothUiState
import com.example.llya.network.DeviceBindManager
import com.example.llya.utils.UserCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.annotation.SuppressLint
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

// 权限状态枚举
enum class PermissionState {
    GRANTED,
    DENIED
}

class BluetoothViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "BluetoothViewModel"

    // 蓝牙管理器
    private val bluetoothManager = BluetoothManager(application.applicationContext)

    // 设备绑定管理器
    private val deviceBindManager = DeviceBindManager.getInstance(application.applicationContext)

    // 统一状态
    private val _state = MutableStateFlow(BluetoothUiState())
    val state: StateFlow<BluetoothUiState> = _state.asStateFlow()

    // 添加消息处理
    private val _statusMessage = MutableStateFlow<String?>(null)
    val statusMessage: StateFlow<String?> = _statusMessage.asStateFlow()

    // 添加新的状态，表示是否应该打开系统设置
    private val _shouldOpenSystemSettings = MutableStateFlow(false)
    val shouldOpenSystemSettings: StateFlow<Boolean> = _shouldOpenSystemSettings.asStateFlow()

    // 设备绑定状态
    private val _isDeviceBinding = MutableStateFlow(false)
    val isDeviceBinding: StateFlow<Boolean> = _isDeviceBinding.asStateFlow()

    // 添加新的状态，表示是否正在刷新
    private var _isRefreshing = false

    // 添加新的状态，表示是否正在更新设备信息
    private var _isUpdatingDevice = false

    // 初始化
    init {
        // 注册广播接收器
        registerDeviceConnectedReceiver()

        // 初始化状态流监听
        initializeStateFlow()

        // 延迟执行设备连接检查
        viewModelScope.launch {
            delay(500) // 延迟500ms再执行初始化检查
            bluetoothManager.checkSystemConnectedDevices()
        }

        // 记录token状态便于调试
        Log.d(TAG, "token状态: " + (if (UserCache.getToken().isNotEmpty()) "已存在" else "不存在"))
    }

    // 初始化状态流 - 按需调用，不在init中立即执行全部监听
    private fun initializeStateFlow() {
        // 监听连接状态变化
        viewModelScope.launch {
            bluetoothManager.connectionState.collect { connectionState ->
                // 更新连接状态
                val mappedState = when(connectionState) {
                    BluetoothManager.ConnectionState.DISCONNECTED -> ConnectionState.DISCONNECTED
                    BluetoothManager.ConnectionState.CONNECTING -> ConnectionState.CONNECTING
                    BluetoothManager.ConnectionState.CONNECTED -> ConnectionState.CONNECTED
                }

                // 仅当状态变化时才更新，减少不必要的状态更新
                if (mappedState != _state.value.connectionState) {
                    _state.value = _state.value.copy(connectionState = mappedState)

                    // 只有在连接状态变为CONNECTED且没有设备信息时才强制更新
                    if (connectionState == BluetoothManager.ConnectionState.CONNECTED && _state.value.connectedDevice == null) {
                        forceDeviceUpdate()
                    } else if (connectionState == BluetoothManager.ConnectionState.DISCONNECTED) {
                        _state.value = _state.value.copy(connectedDevice = null)
                    }
                }
            }
        }
    }

    // 刷新蓝牙状态
    private fun refreshBluetoothState() {
        // 避免重复启动协程
        viewModelScope.launch {
            if (_isRefreshing) {
                return@launch
            }

            try {
                _isRefreshing = true
                Log.d(TAG, "开始刷新蓝牙状态")

                // 设置连接中状态
                _state.value = _state.value.copy(connectionState = ConnectionState.CONNECTING)

                // 使用IO调度器执行蓝牙操作
                withContext(Dispatchers.IO) {
                    // 检查系统已连接设备，使用强制刷新模式
                    bluetoothManager.checkSystemConnectedDevices(forceRefresh = true)

                    // 等待状态更新
                    delay(500)

                    // 验证连接状态
                    val isConnected = withContext(Dispatchers.IO) {
                        var result = false
                        val latch = java.util.concurrent.CountDownLatch(1)

                        // 在IO线程中执行验证
                        verifyDeviceConnection { connected ->
                            result = connected
                            latch.countDown()
                        }

                        // 等待验证完成
                        latch.await(1000, java.util.concurrent.TimeUnit.MILLISECONDS)
                        result
                    }

                    if (isConnected) {
                        // 如果验证成功，确保状态为已连接
                        withContext(Dispatchers.Main) {
                            _state.value = _state.value.copy(connectionState = ConnectionState.CONNECTED)
                        }
                    } else {
                        // 如果验证失败，重试一次
                        delay(500)
                        bluetoothManager.checkSystemConnectedDevices(forceRefresh = true)
                        delay(500)

                        // 再次验证
                        val isStillConnected = withContext(Dispatchers.IO) {
                            var result = false
                            val latch = java.util.concurrent.CountDownLatch(1)

                            verifyDeviceConnection { connected ->
                                result = connected
                                latch.countDown()
                            }

                            latch.await(1000, java.util.concurrent.TimeUnit.MILLISECONDS)
                            result
                        }

                        withContext(Dispatchers.Main) {
                            if (isStillConnected) {
                                _state.value = _state.value.copy(connectionState = ConnectionState.CONNECTED)
                            } else {
                                _state.value = _state.value.copy(
                                    connectionState = ConnectionState.DISCONNECTED,
                                    connectedDevice = null
                                )
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "刷新蓝牙状态失败: ${e.message}")
                _state.value = _state.value.copy(
                    connectionState = ConnectionState.DISCONNECTED,
                    connectedDevice = null
                )
            } finally {
                _isRefreshing = false
            }
        }
    }

    // 更新已连接设备信息
    fun updateConnectedDeviceInfo(device: BluetoothDevice) {
        viewModelScope.launch {
            Log.d(TAG, "开始更新设备信息: 名称=${device.name ?: "未知设备"}, 地址=${device.address}")

            // 强制设置连接状态为已连接
            _state.value = _state.value.copy(connectionState = ConnectionState.CONNECTED)
            Log.d(TAG, "UI状态已更新: connectionState=CONNECTED")

            // 设置已连接设备
            _state.value = _state.value.copy(connectedDevice = device)
            Log.d(TAG, "UI状态已更新: connectedDevice=${device.name ?: "未知设备"}")

            // 获取设备信息
            val deviceInfo = bluetoothManager.getDeviceInfoAsMap(device)
            _state.value = _state.value.copy(deviceInfo = deviceInfo)
            Log.d(TAG, "UI状态已更新: 设备信息=${deviceInfo}")

            // 清除状态消息
            _statusMessage.value = null

            // 发送通知强制刷新UI
            val newState = _state.value.copy(
                connectionState = ConnectionState.CONNECTED,
                connectedDevice = device,
                deviceInfo = deviceInfo
            )
            _state.value = newState

            Log.d(TAG, "设备信息更新完成，最终UI状态: connectionState=${_state.value.connectionState}, " +
                   "connectedDevice=${_state.value.connectedDevice?.name}, " +
                   "deviceInfo=${_state.value.deviceInfo}")
        }
    }

    // 权限状态更新
    fun updatePermissionState(permissions: Map<String, Boolean>) {
        // 检查简化的权限状态
        val allGranted = permissions["hasPermissions"] ?: false

        if (allGranted) {
            Log.d(TAG, "所有音频设备扫描权限已授予，检查连接设备")
            bluetoothManager.checkSystemConnectedDevices()
        } else {
            Log.d(TAG, "音频设备扫描权限被拒绝")
        }
    }

    // 打开系统蓝牙设置
    fun openSystemBluetoothSettings() {
        _shouldOpenSystemSettings.value = true
    }

    // 系统设置已打开
    fun systemSettingsOpened() {
        _shouldOpenSystemSettings.value = false
    }

    // 注册生命周期观察者
    fun registerLifecycleObserver(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        // 恢复时触发设备刷新
                        onResume()
                    }
                    Lifecycle.Event.ON_PAUSE -> {
                        // 暂停时可以做一些清理工作
                    }
                    else -> {}
                }
            }
        })
    }

    // 当应用从后台恢复时触发 - 优化版
    fun onResume() {
        if (_isRefreshing) return

        viewModelScope.launch {
            try {
                Log.d(TAG, "刷新蓝牙状态")
                _isRefreshing = true

                // 使用IO调度器执行蓝牙操作
                withContext(Dispatchers.IO) {
                    // 检查系统已连接设备，使用强制刷新模式
                    bluetoothManager.checkSystemConnectedDevices(forceRefresh = true)

                    // 减少延迟时间
                    delay(200)

                    // 如果此时仍未找到设备，尝试从适配器获取
                    if (_state.value.connectedDevice == null) {
                        val device = getConnectedDeviceFromAdapter()
                        if (device != null) {
                            val deviceInfo = bluetoothManager.getDeviceInfoAsMap(device)

                            // 在主线程中更新UI状态，确保安全更新
                            withContext(Dispatchers.Main) {
                                _state.value = _state.value.copy(
                                    connectionState = ConnectionState.CONNECTED,
                                    connectedDevice = device,
                                    deviceInfo = deviceInfo
                                )
                            }
                        } else {
                            // 未找到设备，确保UI状态为断开
                            withContext(Dispatchers.Main) {
                                _state.value = _state.value.copy(
                                    connectionState = ConnectionState.DISCONNECTED,
                                    connectedDevice = null
                                )
                            }
                        }
                    }
                }
            } finally {
                _isRefreshing = false
            }
        }
    }

    // 对外提供的刷新方法，供UI层调用 - 优化版
    fun refreshOnResume() {
        // 直接调用优化后的onResume方法
        onResume()
    }

    // 强制更新设备信息 - 优化版
    fun forceDeviceUpdate() {
        // 如果已在更新中，避免重复执行
        if (_isUpdatingDevice) return

        viewModelScope.launch {
            _isUpdatingDevice = true
            try {
                // 只有在没有连接设备时才将状态设为CONNECTING
                if (_state.value.connectedDevice == null) {
                    _state.value = _state.value.copy(connectionState = ConnectionState.CONNECTING)
                }

                // 使用IO调度器执行蓝牙操作
                withContext(Dispatchers.IO) {
                    // 直接检查蓝牙配置文件中的已连接设备
                    // 这个调用会更新bluetoothManager的连接状态
                    bluetoothManager.forceUpdateConnectedDeviceStatus()

                    // 等待状态更新
                    delay(200)

                    // 如果还没有连接设备，尝试从蓝牙适配器获取
                    if (_state.value.connectedDevice == null) {
                        val device = getConnectedDeviceFromAdapter()
                        if (device != null) {
                            val deviceInfo = bluetoothManager.getDeviceInfoAsMap(device)
                            // 更新UI状态
                            _state.value = _state.value.copy(
                                connectionState = ConnectionState.CONNECTED,
                                connectedDevice = device,
                                deviceInfo = deviceInfo
                            )
                        } else {
                            // 未找到设备，设置为断开状态
                            _state.value = _state.value.copy(
                                connectionState = ConnectionState.DISCONNECTED,
                                connectedDevice = null
                            )
                        }
                    } else if (_state.value.connectionState != ConnectionState.CONNECTED) {
                        // 如果已有设备但状态不是CONNECTED，将状态更新为CONNECTED
                        _state.value = _state.value.copy(connectionState = ConnectionState.CONNECTED)
                    }
                }
            } catch (e: Exception) {
                // 发生错误时设置为断开状态
                _state.value = _state.value.copy(
                    connectionState = ConnectionState.DISCONNECTED,
                    connectedDevice = null
                )
            } finally {
                _isUpdatingDevice = false
            }
        }
    }

    // 辅助方法：从蓝牙适配器获取已连接设备
    @SuppressLint("MissingPermission")
    private fun getConnectedDeviceFromAdapter(): BluetoothDevice? {
        try {
            Log.d(TAG, "开始从配置文件中查找已连接设备")

            // 优先从蓝牙配置文件中查找已连接设备
            // 1. 尝试从HEADSET配置文件获取
            val headsetProfile = getBluetoothProfile(BluetoothProfile.HEADSET)
            if (headsetProfile != null) {
                val connectedHeadsetDevices = headsetProfile.connectedDevices
                if (connectedHeadsetDevices.isNotEmpty()) {
                    val device = connectedHeadsetDevices[0]
                    Log.d(TAG, "从HEADSET配置文件中找到连接的设备: ${device.name ?: "未知设备"}, ${device.address}")
                    return device
                }
            }

            // 2. 尝试从A2DP配置文件获取
            val a2dpProfile = getBluetoothProfile(BluetoothProfile.A2DP)
            if (a2dpProfile != null) {
                val connectedA2dpDevices = a2dpProfile.connectedDevices
                if (connectedA2dpDevices.isNotEmpty()) {
                    val device = connectedA2dpDevices[0]
                    Log.d(TAG, "从A2DP配置文件中找到连接的设备: ${device.name ?: "未知设备"}, ${device.address}")
                    return device
                }
            }

            Log.d(TAG, "未从蓝牙配置文件中找到已连接设备")
            return null
        } catch (e: Exception) {
            Log.e(TAG, "从适配器获取已连接设备出错: ${e.message}")
            return null
        }
    }

    // 辅助方法：获取蓝牙配置文件
    @SuppressLint("MissingPermission")
    private fun getBluetoothProfile(profileId: Int): BluetoothProfile? {
        val bluetoothAdapter = android.bluetooth.BluetoothAdapter.getDefaultAdapter() ?: return null

        var profileProxy: BluetoothProfile? = null
        val latch = java.util.concurrent.CountDownLatch(1)

        val listener = object : BluetoothProfile.ServiceListener {
            override fun onServiceConnected(profileType: Int, proxy: BluetoothProfile) {
                profileProxy = proxy
                latch.countDown()
            }

            override fun onServiceDisconnected(profileType: Int) {
                profileProxy = null
            }
        }

        val success = bluetoothAdapter.getProfileProxy(getApplication(), listener, profileId)
        if (!success) {
            Log.e(TAG, "获取蓝牙配置文件代理失败: profileId=$profileId")
            return null
        }

        try {
            latch.await(500, java.util.concurrent.TimeUnit.MILLISECONDS)
        } catch (e: InterruptedException) {
            Log.e(TAG, "等待蓝牙配置文件代理超时: ${e.message}")
        }

        return profileProxy
    }

    // 设备连接广播接收器
    private val deviceConnectionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.example.llya.DEVICE_CONNECTED") {
                // 无论当前状态如何，都应该强制更新设备信息
                viewModelScope.launch {
                    Log.d(TAG, "收到设备连接广播，强制更新设备信息")
                    // 清除现有设备信息，避免显示旧的设备
                    _state.value = _state.value.copy(
                        connectionState = ConnectionState.CONNECTING,
                        connectedDevice = null
                    )
                    delay(100)
                    forceDeviceUpdate()
                }
            }
        }
    }

    // 设备断开连接广播接收器
    private val deviceDisconnectReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.example.llya.DEVICE_DISCONNECTED") {
                Log.d(TAG, "收到设备断开连接广播")

                // 更新UI状态为断开连接
                _state.value = _state.value.copy(
                    connectionState = ConnectionState.DISCONNECTED,
                    connectedDevice = null
                )

                // 添加状态消息
                _statusMessage.value = "蓝牙设备已断开连接，请重新连接"
            }
        }
    }

    // 注册广播接收器
    private var broadcastReceiver: BroadcastReceiver? = null
    private fun registerDeviceConnectedReceiver() {
        val appContext = getApplication<Application>().applicationContext
        try {
            // 设备连接广播
            val deviceConnectedFilter = IntentFilter("com.example.llya.DEVICE_CONNECTED")
            appContext.registerReceiver(deviceConnectionReceiver, deviceConnectedFilter)

            // 设备断开连接广播
            val deviceDisconnectedFilter = IntentFilter("com.example.llya.DEVICE_DISCONNECTED")
            appContext.registerReceiver(deviceDisconnectReceiver, deviceDisconnectedFilter)

            // 注册蓝牙权限缺失广播接收器
            val permissionMissingFilter = IntentFilter("com.example.llya.BLUETOOTH_PERMISSION_MISSING")
            appContext.registerReceiver(permissionMissingReceiver, permissionMissingFilter)

            Log.d(TAG, "成功注册设备连接广播接收器")
        } catch (e: Exception) {
            Log.e(TAG, "注册设备连接广播接收器失败: ${e.message}")
        }
    }

    // 蓝牙权限缺失广播接收器
    private val permissionMissingReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Log.d(TAG, "接收到蓝牙权限缺失广播")
            // 更新UI状态，提示用户开启权限
            _statusMessage.value = "蓝牙权限不足，请在权限设置中授予蓝牙权限"

            // 提示打开系统设置
            _shouldOpenSystemSettings.value = true
        }
    }

    // 清理资源
    override fun onCleared() {
        super.onCleared()

        try {
            // 释放蓝牙管理器资源
            bluetoothManager.release()

            // 注销广播接收器
            val appContext = getApplication<Application>().applicationContext
            appContext.unregisterReceiver(deviceConnectionReceiver)
            appContext.unregisterReceiver(deviceDisconnectReceiver)
            appContext.unregisterReceiver(permissionMissingReceiver) // 注销权限广播接收器

            // 取消所有协程
            viewModelScope.cancel()

        } catch (e: Exception) {
            Log.e(TAG, "清理资源出错: ${e.message}")
        }
    }

    /**
     * 绑定当前连接的蓝牙设备到服务器
     */
    @SuppressLint("MissingPermission")
    fun bindCurrentDevice() {
        // 如果已经在绑定中，避免重复操作
        if (_isDeviceBinding.value) return

        viewModelScope.launch {
            try {
                // 更新绑定状态
                _isDeviceBinding.value = true

                // 获取当前连接的设备
                val device = _state.value.connectedDevice

                if (device != null) {
                    // 获取设备名称和MAC地址
                    val deviceName = device.name ?: "未知设备"
                    val macAddress = device.address

                    Log.d(TAG, "开始绑定设备: $deviceName ($macAddress)")
                    Log.d(TAG, "当前token状态: " + (if (UserCache.getToken().isNotEmpty()) "已存在(${UserCache.getToken().length}字符)" else "不存在"))

                    // 获取位置信息可能需要权限，此处简化为不包含位置信息
                    deviceBindManager.bindDevice(deviceName, macAddress, false)
                        .collect { response ->
                            Log.d(TAG, "设备绑定API响应: status=${response.status}, message=${response.msg}, 数据=${response.data}")

                            // 检查是否是token过期
                            if (response.status == 401 || response.status == 101 || response.msg?.contains("token过期") == true) {
                                // Token过期
                                Log.e(TAG, "设备绑定失败: token过期 (状态码: ${response.status})")

                                // 显示错误提示
                                Toast.makeText(
                                    getApplication(),
                                    "登录已过期，请重新登录",
                                    Toast.LENGTH_LONG
                                ).show()

                                // 更新状态消息
                                _statusMessage.value = "登录已过期，请重新登录"

                                // 处理token过期
                                val appContext = getApplication<Application>().applicationContext
                                com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                            }
                            // 更新判断条件，根据消息判断是否成功
                            else if (response.status == 0 || (response.msg?.contains("成功") == true)) {
                                // 绑定成功
                                val deviceId = response.data?.deviceId ?: "未知ID"
                                val bindTime = response.data?.bindTime ?: "未知时间"

                                Log.d(TAG, "设备绑定成功: ID=$deviceId, 时间=$bindTime")

                                // 显示成功提示
                                Toast.makeText(
                                    getApplication(),
                                    "设备\"$deviceName\"绑定成功",
                                    Toast.LENGTH_SHORT
                                ).show()

                                // 更新状态消息
                                _statusMessage.value = "设备绑定成功"
                            } else {
                                // 绑定失败
                                Log.e(TAG, "设备绑定失败: ${response.msg}")

                                // 显示错误提示
                                Toast.makeText(
                                    getApplication(),
                                    "设备绑定失败: ${response.msg}",
                                    Toast.LENGTH_SHORT
                                ).show()

                                // 更新状态消息
                                _statusMessage.value = "设备绑定失败: ${response.msg}"
                            }
                        }
                } else {
                    // 未找到连接的设备
                    Log.e(TAG, "未找到已连接的设备，无法绑定")

                    // 显示错误提示
                    Toast.makeText(
                        getApplication(),
                        "未找到已连接的设备，无法绑定",
                        Toast.LENGTH_SHORT
                    ).show()

                    // 更新状态消息
                    _statusMessage.value = "未找到已连接的设备，无法绑定"
                }
            } catch (e: Exception) {
                // 处理异常
                Log.e(TAG, "设备绑定过程中发生异常: ${e.message}", e)

                // 显示错误提示
                Toast.makeText(
                    getApplication(),
                    "设备绑定异常: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()

                // 更新状态消息
                _statusMessage.value = "设备绑定异常: ${e.message}"
            } finally {
                // 无论成功失败，都更新绑定状态为已完成
                _isDeviceBinding.value = false
            }
        }
    }

    /**
     * 清除状态消息
     */
    fun clearStatusMessage() {
        _statusMessage.value = null
    }

    /**
     * 更新状态消息
     */
    fun updateStatusMessage(message: String) {
        _statusMessage.value = message
    }

    /**
     * 验证蓝牙设备是否真正连接
     * 使用多种方法进行更严格的连接检测
     */
    fun verifyDeviceConnection(callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始严格验证蓝牙设备连接状态")

                // 1. 首先检查当前状态
                val currentState = _state.value.connectionState
                val currentDevice = _state.value.connectedDevice

                // 如果当前状态明显为未连接，直接返回false
                if (currentState != ConnectionState.CONNECTED || currentDevice == null) {
                    Log.d(TAG, "状态验证失败: 当前状态=$currentState，设备=${currentDevice?.name ?: "无"}")
                    callback(false)
                    return@launch
                }

                // 进行真正的连接验证 - 使用IO调度器
                val isReallyConnected = withContext(Dispatchers.IO) {
                    // 2. 检查蓝牙配置文件状态 (最可靠的方法)
                    val isA2dpConnected = checkProfileConnected(BluetoothProfile.A2DP)
                    val isHeadsetConnected = checkProfileConnected(BluetoothProfile.HEADSET)

                    // 3. 检查设备类型和连接状态
                    var deviceCheck = false
                    try {
                        val device = currentDevice
                        if (device != null) {
                            // 检查设备类型
                            val isAudioDevice = device.bluetoothClass.majorDeviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO
                            // 尝试使用反射调用isConnected方法（如果设备支持）
                            var deviceConnected = false
                            try {
                                val isConnectedMethod = device.javaClass.getMethod("isConnected")
                                deviceConnected = isConnectedMethod.invoke(device) as Boolean
                            } catch (e: Exception) {
                                Log.d(TAG, "设备不支持isConnected方法: ${e.message}")
                            }

                            deviceCheck = isAudioDevice && deviceConnected

                            Log.d(TAG, "设备验证结果: 音频设备=$isAudioDevice, 连接状态=$deviceConnected")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "设备状态检查失败: ${e.message}")
                    }

                    // 综合判断
                    val result = isA2dpConnected || isHeadsetConnected || deviceCheck
                    Log.d(TAG, "蓝牙连接验证结果: $result (A2DP=$isA2dpConnected, HEADSET=$isHeadsetConnected, 设备=$deviceCheck)")

                    result
                }

                // 返回结果
                callback(isReallyConnected)

            } catch (e: Exception) {
                Log.e(TAG, "蓝牙连接验证出错: ${e.message}")
                callback(false)
            }
        }
    }

    /**
     * 检查特定蓝牙配置文件是否有连接的设备
     */
    @SuppressLint("MissingPermission")
    private fun checkProfileConnected(profileType: Int): Boolean {
        try {
            val bluetoothAdapter = android.bluetooth.BluetoothAdapter.getDefaultAdapter() ?: return false

            // 检查配置文件连接状态 - 最直接的方法
            val profileState = bluetoothAdapter.getProfileConnectionState(profileType)
            if (profileState == BluetoothProfile.STATE_CONNECTED) {
                Log.d(TAG, "配置文件${profileType}处于已连接状态")
                return true
            }

            // 如果状态检查未显示连接，尝试获取配置文件代理并检查连接设备
            val latch = java.util.concurrent.CountDownLatch(1)
            var hasConnectedDevices = false

            try {
                bluetoothAdapter.getProfileProxy(getApplication(), object : BluetoothProfile.ServiceListener {
                    override fun onServiceConnected(profile: Int, proxy: BluetoothProfile) {
                        try {
                            val devices = proxy.connectedDevices
                            hasConnectedDevices = devices.isNotEmpty()

                            if (hasConnectedDevices) {
                                Log.d(TAG, "配置文件${profile}代理发现${devices.size}个已连接设备")
                            }

                            // 关闭代理
                            bluetoothAdapter.closeProfileProxy(profile, proxy)
                        } catch (e: Exception) {
                            Log.e(TAG, "获取配置文件连接设备失败: ${e.message}")
                        }
                        latch.countDown()
                    }

                    override fun onServiceDisconnected(profile: Int) {
                        latch.countDown()
                    }
                }, profileType)

                // 等待结果
                latch.await(1000, java.util.concurrent.TimeUnit.MILLISECONDS)
            } catch (e: Exception) {
                Log.e(TAG, "获取配置文件代理失败: ${e.message}")
            }

            return hasConnectedDevices

        } catch (e: Exception) {
            Log.e(TAG, "检查配置文件连接状态失败: ${e.message}")
            return false
        }
    }
}