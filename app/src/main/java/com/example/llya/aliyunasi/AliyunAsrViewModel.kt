package com.example.llya.aliyunasi

import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.example.llya.audio.AudioRecorder
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 阿里云语音识别ViewModel
 */
class AliyunAsrViewModel(
    private val context: Context,
    private val accessKeyId: String,
    private val accessKeySecret: String,
    private val appKey: String
) : ViewModel() {

    companion object {
        private const val TAG = "AliyunAsrViewModel"
    }

    // 录音器
    private var audioRecorder: AudioRecorder? = null
    
    // 阿里云ASR客户端
    private var asrClient: AliyunAsrClient? = null
    
    // 识别结果
    private val _recognitionResult = MutableStateFlow<String>("")
    val recognitionResult: StateFlow<String> = _recognitionResult.asStateFlow()
    
    // 连接状态
    private val _connectionStatus = MutableStateFlow<String>("未连接")
    val connectionStatus: StateFlow<String> = _connectionStatus.asStateFlow()
    
    // 录音状态
    private val _isRecording = MutableStateFlow<Boolean>(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String>("")
    val errorMessage: StateFlow<String> = _errorMessage.asStateFlow()
    
    // 当前语言
    private var currentLanguage = "zh"

    // 初始化
    init {
        audioRecorder = AudioRecorder(
            context = context,
            onAudioBufferReady = { audioData ->
                asrClient?.sendAudioData(audioData)
            },
            onError = { error ->
                _errorMessage.value = "录音错误: $error"
                stopRecognition()
            }
        )
    }

    // 设置语音识别语言
    fun setLanguage(languageCode: String) {
        // 根据阿里云API限制，目前仅支持中文和英文
        val supportedLanguages = arrayOf("zh", "en")
        val isSupported = supportedLanguages.contains(languageCode)
        
        if (currentLanguage != languageCode) {
            Log.d(TAG, "语言设置变更: $currentLanguage -> $languageCode ${if (!isSupported) "(不支持，将使用中文)" else ""}")
            currentLanguage = languageCode
            
            // 如果当前有活跃的客户端，通知变更
            asrClient?.let {
                // 记录日志但不立即更改语言，需要重新连接才会生效
                Log.d(TAG, "语言变更需要重新连接ASR客户端才能生效")
            }
            
            // 如果选择了不支持的语言，显示提示
            if (!isSupported && languageCode != "zh") {
                _errorMessage.value = "提示：阿里云语音识别目前仅支持中文和英文"
                // 2秒后自动清除错误消息
                viewModelScope.launch {
                    delay(2000)
                    if (_errorMessage.value.contains("提示：阿里云语音识别")) {
                        _errorMessage.value = ""
                    }
                }
            }
        } else {
            Log.d(TAG, "语言设置未变化: $languageCode")
        }
    }

    // 开始语音识别
    fun startRecognition() {
        viewModelScope.launch {
            try {
                // 记录当前要使用的语言
                Log.d(TAG, "准备开始语音识别，当前设置的语言: $currentLanguage")
                
                // 如果已经在录音，则先停止
                if (_isRecording.value) {
                    Log.d(TAG, "已在录音状态，先停止当前录音")
                    stopRecognition()
                    delay(500) // 等待停止完成
                }

                // 重置识别结果
                _recognitionResult.value = ""
                _errorMessage.value = ""
                
                // 获取Token
                _connectionStatus.value = "正在获取Token..."
                Log.d(TAG, "使用AccessKeyId: $accessKeyId 获取Token，设定使用语言: $currentLanguage")
                
                val token = AliyunTokenUtil.getToken(accessKeyId, accessKeySecret)
                if (token == null) {
                    _errorMessage.value = "获取Token失败，请检查AccessKey是否正确"
                    _connectionStatus.value = "未连接"
                    return@launch
                }
                
                Log.d(TAG, "Token获取成功，开始连接WebSocket")
                _connectionStatus.value = "Token获取成功，正在连接WebSocket..."
                
                // 先关闭旧的客户端（如果存在）
                Log.d(TAG, "关闭可能存在的旧ASR客户端")
                asrClient?.close()
                
                // 创建ASR客户端
                Log.d(TAG, "创建新的ASR客户端, 语言: $currentLanguage")
                asrClient = AliyunAsrClient(token, appKey).apply {
                    // 设置语音识别语言 - 明确打印当前设置的语言
                    Log.d(TAG, "正在显式设置ASR客户端的语言: $currentLanguage")
                    
                    when (currentLanguage) {
                        "zh" -> {
                            Log.d(TAG, "设置中文识别模式: zh_cn")
                            setLanguage("zh_cn")
                        }
                        "zh-tw" -> {
                            Log.d(TAG, "设置繁体中文识别模式: zh_tw")
                            setLanguage("zh_tw")
                        }
                        "en" -> {
                            Log.d(TAG, "设置英语识别模式: en_us")
                            setLanguage("en_us")
                        }
                        "ja" -> {
                            Log.d(TAG, "设置日语识别模式: ja_jp")
                            setLanguage("ja_jp")
                        }
                        "ko" -> {
                            Log.d(TAG, "设置韩语识别模式: ko_kr")
                            setLanguage("ko_kr")
                        }
                        "fr" -> {
                            Log.d(TAG, "设置法语识别模式: fr_fr")
                            setLanguage("fr_fr")
                        }
                        "es" -> {
                            Log.d(TAG, "设置西班牙语识别模式: es_es")
                            setLanguage("es_es")
                        }
                        "it" -> {
                            Log.d(TAG, "设置意大利语识别模式: it_it")
                            setLanguage("it_it")
                        }
                        "de" -> {
                            Log.d(TAG, "设置德语识别模式: de_de")
                            setLanguage("de_de")
                        }
                        "ru" -> {
                            Log.d(TAG, "设置俄语识别模式: ru_ru")
                            setLanguage("ru_ru")
                        }
                        else -> {
                            Log.d(TAG, "未知语言 '$currentLanguage'，默认使用中文")
                            setLanguage("zh_cn") // 默认中文
                        }
                    }
                    
                    Log.d(TAG, "ASR客户端已设置语言，准备连接")
                    
                    // 监听识别结果
                    viewModelScope.launch {
                        recognitionResultFlow.collect { result ->
                            _recognitionResult.value = result
                        }
                    }
                    
                    // 监听连接状态
                    viewModelScope.launch {
                        connectionStatusFlow.collect { status ->
                            _connectionStatus.value = status
                            
                            Log.d(TAG, "ASR连接状态更新: $status")
                            
                            // 检测转写已开始事件
                            if (status.contains("转写已开始") || status.contains("正在识别语音")) {
                                if (!_isRecording.value) {
                                    Log.d(TAG, "收到转写开始事件，立即开始录音")
                                    _isRecording.value = true
                                    audioRecorder?.startRecording()
                                }
                            }
                        }
                    }
                    
                    // 监听错误信息
                    viewModelScope.launch {
                        errorMessageFlow.collect { error ->
                            if (error.isNotEmpty()) {
                                _errorMessage.value = error
                                Log.e(TAG, "ASR错误: $error")
                                // 如果是连接失败错误，停止录音
                                if (error.contains("连接失败") || error.contains("任务失败")) {
                                    Log.d(TAG, "由于错误停止录音: $error")
                                    stopRecognition()
                                }
                            }
                        }
                    }
                    
                    // 连接到ASR服务
                    Log.d(TAG, "开始连接ASR服务")
                    connect()
                    
                    // 等待连接状态，最多等待5秒
                    var waitTime = 0
                    val checkInterval = 500L // 500毫秒检查一次
                    val maxWaitTime = 5000L // 最多等待5秒
                    
                    // 创建变量记录是否已收到TranscriptionStarted事件
                    var receivedTranscriptionStarted = false
                    
                    // 监听连接状态特别关注转写开始事件
                    val transcriptionStartedJob = viewModelScope.launch {
                        connectionStatusFlow.collect { status ->
                            _connectionStatus.value = status
                            
                            Log.d(TAG, "ASR连接状态更新: $status")
                            
                            // 检测转写已开始事件
                            if (status.contains("转写已开始") || status.contains("正在识别语音")) {
                                receivedTranscriptionStarted = true
                                if (!_isRecording.value) {
                                    Log.d(TAG, "收到转写开始事件，立即开始录音")
                                    _isRecording.value = true
                                    audioRecorder?.startRecording()
                                }
                            }
                        }
                    }
                    
                    // 等待WebSocket连接建立，最多等待5秒
                    while (waitTime < maxWaitTime && !receivedTranscriptionStarted) {
                        delay(checkInterval)
                        waitTime += checkInterval.toInt()
                        Log.d(TAG, "等待WebSocket连接和TranscriptionStarted事件... ${waitTime}ms/${maxWaitTime}ms")
                        
                        // 如果已经收到了"转写已开始"状态，提前退出等待
                        if (receivedTranscriptionStarted) {
                            Log.d(TAG, "已检测到TranscriptionStarted事件，不再等待")
                            break
                        }
                    }
                    
                    // 取消状态监听
                    transcriptionStartedJob.cancel()
                    
                    // 检查是否收到转写开始事件
                    if (!receivedTranscriptionStarted) {
                        Log.e(TAG, "等待超时：未收到TranscriptionStarted事件")
                        _errorMessage.value = "未能成功启动语音识别，请重试"
                        _connectionStatus.value = "连接失败"
                        return@launch
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "启动识别失败", e)
                _errorMessage.value = "启动识别失败: ${e.message}"
                _isRecording.value = false
                _connectionStatus.value = "未连接"
            }
        }
    }

    // 停止语音识别
    fun stopRecognition() {
        viewModelScope.launch {
            try {
                // 停止录音
                audioRecorder?.stopRecording()
                _isRecording.value = false
                
                // 停止ASR
                asrClient?.sendStopTranscriptionCommand()
                asrClient?.close()
                asrClient = null
                
                _connectionStatus.value = "未连接"
            } catch (e: Exception) {
                Log.e(TAG, "停止识别失败", e)
                _errorMessage.value = "停止识别失败: ${e.message}"
            }
        }
    }
    
    // 检查客户端是否连接
    private fun isClientConnected(): Boolean {
        return asrClient?.isConnected() ?: false
    }

    // 清除错误信息
    fun clearError() {
        _errorMessage.value = ""
    }

    // 释放资源
    override fun onCleared() {
        super.onCleared()
        
        // 直接释放资源，不使用suspend函数
        audioRecorder?.release()
        asrClient?.close()
    }

    // ViewModel工厂
    class Factory(
        private val context: Context,
        private val accessKeyId: String,
        private val accessKeySecret: String,
        private val appKey: String
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(AliyunAsrViewModel::class.java)) {
                return AliyunAsrViewModel(context, accessKeyId, accessKeySecret, appKey) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    // 重试逻辑 - 添加一个新方法，用于处理重试
    fun retryConnection() {
        if (_isRecording.value) {
            stopRecognition()
            // 等待一秒后重试
            viewModelScope.launch {
                delay(1000)
                startRecognition()
            }
        } else {
            startRecognition()
        }
    }
} 