package com.example.llya.asr

import android.util.Log
import com.example.llya.network.GoogleCloudSpeechService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Google Cloud语音转写客户端
 * 基于GoogleCloudSpeechService进行封装，提供更简单的接口
 * 专门用于普通语音转写功能，与会议记录功能分离
 */
class GoogleCloudSpeechToTextClient(
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "GoogleSpeechClient"
    }

    // 获取已存在的GoogleCloudSpeechService单例
    private val speechService = GoogleCloudSpeechService.getInstance()

    // 语音识别结果
    private val _recognitionResult = MutableStateFlow<String>("")
    val recognitionResult: StateFlow<String> = _recognitionResult.asStateFlow()

    // 是否为最终结果
    private val _isFinalResult = MutableStateFlow<Boolean>(false)
    val isFinalResult: StateFlow<Boolean> = _isFinalResult.asStateFlow()

    // 累积的文本
    private val _accumulatedText = MutableStateFlow<String>("")
    val accumulatedText: StateFlow<String> = _accumulatedText.asStateFlow()

    // 连接状态
    private val _connectionStatus = MutableStateFlow<String>("未连接")
    val connectionStatus: StateFlow<String> = _connectionStatus.asStateFlow()

    // 错误状态
    private val _errorMessage = MutableStateFlow<String>("")
    val errorMessage: StateFlow<String> = _errorMessage.asStateFlow()

    // 当前使用的语言
    private var currentLanguage = "zh-CN"

    // 用于判断最近处理过的文本，避免重复
    private var lastProcessedTexts = mutableListOf<String>()
    private var hasProcessedSliceType2 = false
    private var lastProcessedText = ""

    // 实现语音识别监听器
    private val recognitionListener = object : GoogleCloudSpeechService.SpeechRecognitionListener {
        override fun onConnectionEstablished() {
            Log.d(TAG, "Google语音识别连接已建立")
            _connectionStatus.value = "已连接"
        }

        override fun onRecognitionResult(text: String, isFinal: Boolean) {
            if (text.isNotEmpty()) {
                // 检查是否是新的识别会话的开始
                val isNewSession = _recognitionResult.value.isEmpty() ||
                                  !text.startsWith(_recognitionResult.value)

                if (isNewSession) {
                    Log.d(TAG, "检测到新的识别会话，清除之前的结果")
                    // 如果是新会话，清除之前的识别结果
                    clearCurrentResult()
                }

                // 更新识别结果和最终标志
                _recognitionResult.value = text
                _isFinalResult.value = isFinal

                // 如果是最终结果，并且不是重复文本，添加到累积文本
                if (isFinal && !isDuplicateText(text)) {
                    // 添加到已处理文本列表，避免重复
                    addToProcessedTexts(text)

                    // 将最终文本添加到累积文本
                    if (_accumulatedText.value.isEmpty()) {
                        _accumulatedText.value = text
                    } else {
                        _accumulatedText.value += " " + text
                    }

                    // 标记为已处理
                    hasProcessedSliceType2 = true
                    lastProcessedText = text

                    // 当收到最终结果后，清空当前识别结果，避免下次开始说话时累积
                    // 这样做不会影响累积文本，因为累积文本已经保存了最终结果
                    Log.d(TAG, "收到最终结果，清空当前识别结果缓冲区，避免下次开始说话时累积")
                    clearCurrentResult()

                    // 通知服务器重置识别状态
                    try {
                        speechService.resetRecognitionState()
                    } catch (e: Exception) {
                        Log.e(TAG, "重置识别状态失败", e)
                    }
                }
            }
        }

        override fun onError(errorMessage: String) {
            Log.e(TAG, "Google语音识别错误: $errorMessage")
            _errorMessage.value = errorMessage
        }

        override fun onConnectionClosed() {
            Log.d(TAG, "Google语音识别连接已关闭")
            _connectionStatus.value = "连接已关闭"
        }

        override val lastLanguageCode: String?
            get() = currentLanguage
    }

    /**
     * 连接并开始语音识别
     * @param languageCode 语言代码，例如zh-CN、en-US等
     */
    fun connect(languageCode: String) {
        try {
            // 保存当前语言设置
            currentLanguage = languageCode

            // 开始实时语音识别
            Log.d(TAG, "开始Google Cloud语音识别，语言: $languageCode")
            speechService.startRealTimeSpeechRecognition(
                languageCode = languageCode,
                model = "default",
                listener = recognitionListener
            )
        } catch (e: Exception) {
            Log.e(TAG, "连接Google Cloud语音识别失败", e)
            _errorMessage.value = "连接失败: ${e.message}"
        }
    }

    /**
     * 发送音频数据进行识别
     * @param audioData 音频数据字节数组
     */
    fun sendAudioData(audioData: ByteArray) {
        try {
            speechService.sendAudioData(audioData)
        } catch (e: Exception) {
            Log.e(TAG, "发送音频数据失败", e)
            _errorMessage.value = "发送音频数据失败: ${e.message}"
        }
    }

    /**
     * 结束语音识别
     */
    fun endRecognition() {
        try {
            Log.d(TAG, "结束Google Cloud语音识别")
            speechService.stopSpeechRecognition()
        } catch (e: Exception) {
            Log.e(TAG, "结束语音识别失败", e)
            _errorMessage.value = "结束语音识别失败: ${e.message}"
        }
    }

    /**
     * 清空当前识别结果
     */
    fun clearCurrentResult() {
        _recognitionResult.value = ""
        _isFinalResult.value = false
    }

    /**
     * 清空所有结果
     */
    fun clearAll() {
        _recognitionResult.value = ""
        _isFinalResult.value = false
        _accumulatedText.value = ""
        lastProcessedTexts.clear()
        hasProcessedSliceType2 = false
        lastProcessedText = ""
    }

    /**
     * 设置累积文本
     */
    fun setAccumulatedText(text: String) {
        _accumulatedText.value = text
    }

    /**
     * 关闭连接并释放资源
     */
    fun close() {
        try {
            speechService.stopSpeechRecognition()
        } catch (e: Exception) {
            Log.e(TAG, "关闭连接失败", e)
        }
    }

    /**
     * 判断文本是否已经被处理过
     */
    fun hasProcessedSliceType2Text(): Boolean {
        return hasProcessedSliceType2
    }

    /**
     * 获取最后处理的文本
     */
    fun getLastProcessedText(): String {
        return lastProcessedText
    }

    /**
     * 检查文本是否重复
     */
    private fun isDuplicateText(text: String): Boolean {
        return lastProcessedTexts.contains(text)
    }

    /**
     * 添加到已处理文本列表
     */
    private fun addToProcessedTexts(text: String) {
        // 如果列表过长，移除最早的元素
        if (lastProcessedTexts.size >= 10) {
            lastProcessedTexts.removeAt(0)
        }
        lastProcessedTexts.add(text)
    }
}