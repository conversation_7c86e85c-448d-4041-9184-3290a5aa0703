package com.example.llya.utils

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

/**
 * 语言工具类
 * 用于管理应用的多语言支持，包括语言切换、获取语言代码和显示名称等功能
 */
object LanguageUtils {
    private const val TAG = "LanguageUtils"
    
    // 公开常量，方便在其他类中一致地访问
    const val LANGUAGE_PREFERENCE = "app_settings"
    const val SELECTED_LANGUAGE_KEY = "selected_language"
    const val LANGUAGE_SOURCE_KEY = "language_source" // 语言设置来源：0=系统默认，1=用户选择
    
    // 状态管理
    private val _currentLanguage = MutableStateFlow<Locale>(Locale.getDefault())
    val currentLanguage: StateFlow<Locale> = _currentLanguage.asStateFlow()
    
    // 使用线程安全的Map存储语言映射，支持动态更新
    private val _supportedLanguagesMap = ConcurrentHashMap<String, String>()
    
    // 默认备用语言列表，当API获取失败时使用
    private val defaultLanguages = mapOf(
        "zh-CN" to "简体中文",
        "zh-TW" to "繁體中文",
        "en" to "English",
        "fr" to "Français",
        "ru" to "Русский",
        "es" to "Español",
        "ar" to "العربية",
        "ja" to "日本語",
        "ko" to "한국어",
        "de" to "Deutsch",
        "it" to "Italiano",
        "pt" to "Português",
        "vi" to "Tiếng Việt",
        "id" to "Bahasa Indonesia",
        "th" to "ไทย",
        "ms" to "Bahasa Melayu",
        "tr" to "Türkçe",
        "nl" to "Nederlands"
    )
    
    // 语言代码到语言显示名称的映射
    private val _displayNameToCodeMap = ConcurrentHashMap<String, String>()
    
    // 导出的只读语言映射，从_supportedLanguagesMap和默认语言列表合并生成
    val supportedLanguages: Map<String, String>
        get() {
            return if (_supportedLanguagesMap.isNotEmpty()) {
                _supportedLanguagesMap.toMap()
            } else {
                defaultLanguages
            }
        }
    
    init {
        // 初始化默认语言列表
        _supportedLanguagesMap.putAll(defaultLanguages)
        
        // 初始化反向映射
        defaultLanguages.forEach { (code, name) ->
            _displayNameToCodeMap[name] = code
        }
    }
    
    /**
     * 初始化语言工具类
     * 应在应用启动时调用，以确保应用使用正确的语言
     * 优先级: 用户设置的语言 > 系统语言
     */
    fun initialize(context: Context) {
        val sharedPrefs = context.getSharedPreferences(LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
        val savedLanguageName = sharedPrefs.getString(SELECTED_LANGUAGE_KEY, null)
        val languageSource = sharedPrefs.getInt(LANGUAGE_SOURCE_KEY, 0) // 0=系统默认，1=用户选择
        
        if (savedLanguageName != null && languageSource == 1) {
            // 用户显式设置了语言，使用用户设置
            val languageCode = getLanguageCodeFromDisplayName(savedLanguageName)
            if (languageCode != null) {
                val locale = createLocaleFromCode(languageCode)
                setLocale(context, locale)
                _currentLanguage.value = locale
                Log.d(TAG, "应用用户设置的语言: $savedLanguageName ($languageCode)")
            } else {
                applySystemLanguage(context, sharedPrefs)
            }
        } else {
            // 未设置语言或使用系统默认设置
            applySystemLanguage(context, sharedPrefs)
        }
    }
    
    /**
     * 应用系统语言
     * 读取系统语言设置并应用到应用
     */
    private fun applySystemLanguage(context: Context, sharedPrefs: SharedPreferences) {
        try {
            // 获取系统语言
            val systemLocale = Locale.getDefault()
            val systemLanguage = systemLocale.language
            val systemCountry = systemLocale.country
            
            // 标准化语言代码
            val languageCode = if (systemCountry.isNotEmpty()) {
                standardizeLanguageCode("$systemLanguage-$systemCountry")
            } else {
                standardizeLanguageCode(systemLanguage)
            }
            
            // 获取语言显示名称
            val languageName = getLanguageDisplayName(languageCode)
            
            // 保存系统语言设置
            sharedPrefs.edit()
                .putString(SELECTED_LANGUAGE_KEY, languageName)
                .putInt(LANGUAGE_SOURCE_KEY, 0) // 标记为系统默认
                .apply()
            
            // 应用语言
            val locale = createLocaleFromCode(languageCode)
            setLocale(context, locale)
            _currentLanguage.value = locale
            
            Log.d(TAG, "应用系统语言: $languageName ($languageCode)")
        } catch (e: Exception) {
            Log.e(TAG, "应用系统语言时出错", e)
            _currentLanguage.value = Locale.getDefault()
        }
    }
    
    /**
     * 更新支持的语言列表
     * 由LanguageManager调用，使用API获取的语言列表更新
     */
    fun updateSupportedLanguages(apiLanguages: Map<String, String>) {
        if (apiLanguages.isNotEmpty()) {
            Log.d(TAG, "使用API获取的语言列表更新LanguageUtils: ${apiLanguages.size}个语言")
            
            _supportedLanguagesMap.clear()
            _displayNameToCodeMap.clear()
            
            // 添加API获取的语言
            _supportedLanguagesMap.putAll(apiLanguages)
            
            // 更新反向映射
            apiLanguages.forEach { (code, name) ->
                _displayNameToCodeMap[name] = code
            }
        } else {
            Log.w(TAG, "API返回的语言列表为空，使用默认语言列表")
            resetToDefaultLanguages()
        }
    }
    
    /**
     * 重置为默认语言列表
     */
    fun resetToDefaultLanguages() {
        _supportedLanguagesMap.clear()
        _displayNameToCodeMap.clear()
        
        _supportedLanguagesMap.putAll(defaultLanguages)
        defaultLanguages.forEach { (code, name) ->
            _displayNameToCodeMap[name] = code
        }
        
        Log.d(TAG, "已重置为默认语言列表: ${defaultLanguages.size}个语言")
    }
    
    /**
     * 切换应用语言
     * @param context 上下文
     * @param languageName 语言显示名称，如"简体中文"、"English"等
     * @param isUserSelected 是否是用户手动选择的语言（true=用户选择，false=系统默认）
     * @return 是否成功切换语言
     */
    fun setLanguage(context: Context, languageName: String, isUserSelected: Boolean = true): Boolean {
        val languageCode = getLanguageCodeFromDisplayName(languageName) ?: return false
        
        val locale = createLocaleFromCode(languageCode)
        val result = setLocale(context, locale)
        
        if (result) {
            // 保存所选语言到SharedPreferences
            val sharedPrefs = context.getSharedPreferences(LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putString(SELECTED_LANGUAGE_KEY, languageName)
                .putInt(LANGUAGE_SOURCE_KEY, if (isUserSelected) 1 else 0) // 1=用户选择，0=系统默认
                .apply()
            
            // 更新当前语言状态
            _currentLanguage.value = locale
            Log.d(TAG, "语言已切换为: $languageName ($languageCode), 来源: ${if (isUserSelected) "用户选择" else "系统默认"}")
        }
        
        return result
    }
    
    /**
     * 设置指定的Locale
     */
    private fun setLocale(context: Context, locale: Locale): Boolean {
        return try {
            // 设置默认语言环境
            Locale.setDefault(locale)
            
            val resources = context.resources
            val configuration = Configuration(resources.configuration)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                configuration.setLocale(locale)
                
                // 创建一个新的配置上下文并应用它
                val configContext = context.createConfigurationContext(configuration)
                
                // 将配置应用到资源上
                context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
            } else {
                @Suppress("DEPRECATION")
                configuration.locale = locale
                resources.updateConfiguration(configuration, resources.displayMetrics)
            }
            
            // 记录设置了新的语言环境
            Log.d(TAG, "设置语言环境成功: ${locale.language}-${locale.country}")
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置语言时出错", e)
            false
        }
    }
    
    /**
     * 获取指定语言代码对应的语言名称
     * @param languageCode 语言代码，如"zh-CN"、"en"等
     * @return 语言显示名称，如"简体中文"、"English"等
     */
    fun getLanguageDisplayName(languageCode: String): String {
        val standardCode = standardizeLanguageCode(languageCode)
        return supportedLanguages[standardCode] ?: languageCode
    }
    
    /**
     * 从语言显示名称获取对应的语言代码
     * @param displayName 语言显示名称，如"简体中文"、"English"等
     * @return 语言代码，如"zh-CN"、"en"等
     */
    fun getLanguageCodeFromDisplayName(displayName: String): String? {
        return _displayNameToCodeMap[displayName]
    }
    
    /**
     * 标准化语言代码
     * 将各种格式的语言代码转换为标准格式
     */
    fun standardizeLanguageCode(languageCode: String): String {
        return when {
            languageCode.startsWith("zh_CN") || languageCode.startsWith("zh-CN") || 
                    languageCode == "cmn-CN" || languageCode == "zh" -> "zh-CN"
            languageCode.startsWith("zh_TW") || languageCode.startsWith("zh-TW") || 
                    languageCode == "cmn-TW" || languageCode == "yue" || languageCode == "zh_HK" -> "zh-TW"
            languageCode.startsWith("en") -> "en"
            languageCode.startsWith("ja") -> "ja"
            languageCode.startsWith("ko") -> "ko"
            languageCode.startsWith("fr") -> "fr"
            languageCode.startsWith("ru") -> "ru"
            languageCode.startsWith("es") -> "es"
            languageCode.startsWith("ar") -> "ar"
            languageCode.startsWith("de") -> "de"
            languageCode.startsWith("it") -> "it"
            languageCode.startsWith("pt") -> "pt"
            languageCode.startsWith("vi") -> "vi"
            languageCode.startsWith("id") -> "id"
            languageCode.startsWith("th") -> "th"
            languageCode.startsWith("ms") -> "ms"
            languageCode.startsWith("tr") -> "tr"
            languageCode.startsWith("nl") -> "nl"
            else -> languageCode // 其他语言保持不变
        }
    }
    
    /**
     * 根据语言代码创建Locale对象
     */
    private fun createLocaleFromCode(languageCode: String): Locale {
        val parts = languageCode.split("-")
        return when {
            parts.size > 1 -> Locale(parts[0], parts[1])
            else -> Locale(parts[0])
        }
    }
    
    /**
     * 获取当前应用语言的代码
     */
    fun getCurrentLanguageCode(context: Context): String {
        // 首先尝试从SharedPreferences读取保存的语言设置
        val sharedPrefs = context.getSharedPreferences(LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
        val savedLanguageName = sharedPrefs.getString(SELECTED_LANGUAGE_KEY, null)
        
        if (savedLanguageName != null) {
            // 如果有保存的语言设置，从显示名称获取语言代码
            val languageCode = getLanguageCodeFromDisplayName(savedLanguageName)
            if (languageCode != null) {
                Log.d(TAG, "从SharedPreferences读取到保存的语言代码: $languageCode")
                return languageCode
            }
        }
        
        // 如果没有找到保存的语言设置，则使用当前资源配置中的语言
        val locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.resources.configuration.locales.get(0)
        } else {
            @Suppress("DEPRECATION")
            context.resources.configuration.locale
        }
        
        val language = locale.language
        val country = locale.country
        
        val result = if (country.isNotEmpty()) {
            standardizeLanguageCode("$language-$country")
        } else {
            standardizeLanguageCode(language)
        }
        
        Log.d(TAG, "未找到保存的语言设置，使用当前资源配置中的语言: $result")
        return result
    }
    
    /**
     * 获取当前应用语言的显示名称
     */
    fun getCurrentLanguageDisplayName(context: Context): String {
        val languageCode = getCurrentLanguageCode(context)
        return getLanguageDisplayName(languageCode)
    }
    
    /**
     * 获取语音识别支持的标准格式语言代码
     */
    fun getSpeechRecognitionLanguageCode(languageCode: String): String {
        return when (standardizeLanguageCode(languageCode)) {
            "zh-CN" -> "zh-CN"
            "zh-TW" -> "zh-TW"
            "en" -> "en-US"
            "ja" -> "ja-JP"
            "ko" -> "ko-KR"
            "fr" -> "fr-FR"
            "ru" -> "ru-RU"
            "es" -> "es-ES"
            "de" -> "de-DE"
            "it" -> "it-IT"
            "pt" -> "pt-BR"
            "vi" -> "vi-VN"
            "id" -> "id-ID"
            "th" -> "th-TH"
            "ar" -> "ar-SA"
            "ms" -> "ms-MY"
            "tr" -> "tr-TR"
            "nl" -> "nl-NL"
            else -> "en-US" // 默认使用英语
        }
    }
    
    /**
     * 获取文本到语音(TTS)支持的标准格式语言代码
     */
    fun getTtsLanguageCode(languageCode: String): String {
        return when (standardizeLanguageCode(languageCode)) {
            "zh-CN" -> "zh-CN"
            "zh-TW" -> "zh-TW"
            "en" -> "en-US"
            "ja" -> "ja-JP"
            "ko" -> "ko-KR"
            "fr" -> "fr-FR"
            "ru" -> "ru-RU"
            "es" -> "es-ES"
            "de" -> "de-DE"
            "it" -> "it-IT"
            "pt" -> "pt-BR"
            "vi" -> "vi-VN"
            "id" -> "id-ID"
            "th" -> "th-TH"
            "ar" -> "ar-SA"
            "ms" -> "ms-MY"
            "tr" -> "tr-TR"
            "nl" -> "nl-NL"
            else -> "en-US" // 默认使用英语
        }
    }
    
    /**
     * Compose可组合函数，用于获取当前应用语言
     * 可在Compose组件中直接使用
     */
    @Composable
    fun getCurrentLanguage(): Locale {
        val languageState by currentLanguage.collectAsState()
        return languageState
    }
    
    /**
     * Compose可组合函数，用于获取当前应用语言代码
     */
    @Composable
    fun getCurrentLanguageCode(): String {
        val context = LocalContext.current
        return getCurrentLanguageCode(context)
    }
    
    /**
     * 获取设备支持的所有语言列表
     */
    fun getSystemAvailableLanguages(): List<Locale> {
        return Locale.getAvailableLocales().toList()
    }
    
    /**
     * 应用所有语言相关的设置到给定的Context
     * 这是一个一站式解决方案，用于处理语言设置、保存和应用
     * 特别适用于Activity的onCreate方法
     * 
     * @param context 要应用语言设置的Context
     * @param languageName 可选的语言显示名称，如果提供则会设置为新的语言
     * @param isUserSelected 如果提供了languageName，指定是否是用户手动选择的语言
     */
    fun applyAllLanguageSettings(context: Context, languageName: String? = null, isUserSelected: Boolean = true) {
        try {
            val sharedPrefs = context.getSharedPreferences(LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
            
            // 如果提供了新的语言名称，则保存并应用它
            if (languageName != null) {
                setLanguage(context, languageName, isUserSelected)
                Log.d(TAG, "已设置新的语言: $languageName, 来源: ${if (isUserSelected) "用户选择" else "系统默认"}")
                return // setLanguage已经完成了所有工作
            }
            
            // 否则，尝试读取已保存的设置
            val savedLanguageName = sharedPrefs.getString(SELECTED_LANGUAGE_KEY, null)
            val languageSource = sharedPrefs.getInt(LANGUAGE_SOURCE_KEY, 0) // 0=系统默认，1=用户选择
            
            if (savedLanguageName != null) {
                // 如果语言来源是系统默认，检查系统语言是否变更
                if (languageSource == 0) {
                    // 获取当前系统语言
                    val systemLocale = Locale.getDefault()
                    val systemLanguage = systemLocale.language
                    val systemCountry = systemLocale.country
                    
                    // 标准化语言代码
                    val systemLanguageCode = if (systemCountry.isNotEmpty()) {
                        standardizeLanguageCode("$systemLanguage-$systemCountry")
                    } else {
                        standardizeLanguageCode(systemLanguage)
                    }
                    
                    // 获取系统语言的显示名称
                    val systemLanguageName = getLanguageDisplayName(systemLanguageCode)
                    
                    // 如果系统语言已变更，则更新设置
                    if (systemLanguageName != savedLanguageName) {
                        Log.d(TAG, "检测到系统语言变更: $savedLanguageName -> $systemLanguageName")
                        setLanguage(context, systemLanguageName, false)
                        return
                    }
                }
                
                // 如果是用户设置的语言，或者系统语言未变更，直接应用保存的设置
                val languageCode = getLanguageCodeFromDisplayName(savedLanguageName)
                if (languageCode != null) {
                    // 创建Locale并设置
                    val locale = createLocaleFromCode(languageCode)
                    Locale.setDefault(locale)
                    
                    // 创建新的配置
                    val resources = context.resources
                    val configuration = Configuration(resources.configuration)
                    
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                        // 对于API 17+
                        configuration.setLocale(locale)
                        context.createConfigurationContext(configuration)
                    } else {
                        // 对于API 16及以下
                        @Suppress("DEPRECATION")
                        configuration.locale = locale
                    }
                    
                    // 更新资源配置
                    resources.updateConfiguration(configuration, resources.displayMetrics)
                    
                    // 更新当前语言状态
                    _currentLanguage.value = locale
                    
                    Log.d(TAG, "已应用保存的语言设置: $savedLanguageName ($languageCode), 来源: ${if (languageSource == 1) "用户选择" else "系统默认"}")
                } else {
                    Log.w(TAG, "无法解析保存的语言名称: $savedLanguageName，使用系统默认语言")
                    applySystemLanguage(context, sharedPrefs)
                }
            } else {
                Log.d(TAG, "没有保存的语言设置，使用系统默认语言")
                applySystemLanguage(context, sharedPrefs)
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用语言设置时出错", e)
        }
    }
} 