package com.example.llya.translation

/**
 * 微软语音服务配置类
 * 用于存储和管理微软认知服务的API密钥和区域设置
 */
data class MicrosoftSpeechConfig(
    val subscriptionKey: String = DEFAULT_SUBSCRIPTION_KEY,
    val region: String = DEFAULT_REGION,
    val translationKey: String = DEFAULT_TRANSLATION_KEY, // 使用专门的翻译密钥
    val translationRegion: String = DEFAULT_TRANSLATION_REGION, // 使用专门的翻译区域
    val continuousLanguageDetection: Boolean = true // 是否启用连续语言检测
) {
    companion object {
        // 默认密钥和区域设置
        const val DEFAULT_SUBSCRIPTION_KEY = "7lJ1e4p4LppCs62hMBvzyzxYv5vCOauJTzIKqnY3U0NVw7hFXiGaJQQJ99BEACYeBjFXJ3w3AAAYACOGuXCg"
        const val DEFAULT_REGION = "eastus"
        const val DEFAULT_TRANSLATION_KEY = "FAl1A2Eh06dVSf4HXfUhZuZphKpIMqt6jVL9i7cg1OlJ8XhZmwxAJQQJ99BEACULyCpXJ3w3AAAbACOGgW5l"
        const val DEFAULT_TRANSLATION_REGION = "global"
        
        // 语言检测敏感度级别
        const val LANGUAGE_DETECTION_SENSITIVITY_HIGH = "0.3" // 高敏感度，快速检测语言变化
        const val LANGUAGE_DETECTION_SENSITIVITY_MEDIUM = "0.5" // 中等敏感度
        const val LANGUAGE_DETECTION_SENSITIVITY_LOW = "0.7" // 低敏感度，减少误判
        
        // 连续语言检测相关配置
        val CONTINUOUS_LANGUAGE_DETECTION_PROPS = mapOf(
            "SpeechServiceConnection_EnableLanguageDetection" to "true",
            "SpeechServiceConnection_ContinuousLanguageIdPriority" to "Latency",
            "SpeechServiceConnection_LanguageIdMode" to "Continuous",
            "SpeechServiceConnection_LanguageIdOnSuccess" to "Recognize",
            "SpeechServiceConnection_LanguageIdOnUnknown" to "Recognize",
            "SpeechServiceConnection_ContinuousLanguageIdThreshold" to LANGUAGE_DETECTION_SENSITIVITY_HIGH,
            "SpeechServiceConnection_SegmentationSilenceTimeoutMs" to "200",
            "SpeechServiceConnection_InitialSilenceTimeoutMs" to "500",
            "SpeechServiceConnection_EndSilenceTimeoutMs" to "500",
            // 增强的语言检测配置
            "SpeechServiceConnection_LanguageIdMinimumDuration" to "0",
            "SpeechServiceConnection_LanguageIdMaximumDuration" to "100",
            "SpeechServiceConnection_LanguageIdConfidenceThreshold" to "0.05",
            "SpeechServiceConnection_LanguageDetectionTaskPriority" to "High",
            "SpeechServiceConnection_AutoDetectSourceLanguageResult" to "true"
        )
        
        // 常用语言组合
        val COMMON_LANGUAGE_GROUPS = mapOf(
            "中英互译" to listOf("zh-CN", "en-US"),
            "中日互译" to listOf("zh-CN", "ja-JP"),
            "中韩互译" to listOf("zh-CN", "ko-KR"),
            "中法互译" to listOf("zh-CN", "fr-FR"),
            "中德互译" to listOf("zh-CN", "de-DE"),
            "英日互译" to listOf("en-US", "ja-JP"),
            "英韩互译" to listOf("en-US", "ko-KR"),
            "英法互译" to listOf("en-US", "fr-FR"),
            "多语言模式" to listOf("zh-CN", "en-US", "ja-JP", "ko-KR", "fr-FR"),
            "亚洲语言" to listOf("zh-CN", "ja-JP", "ko-KR"),
            "欧洲语言" to listOf("en-US", "fr-FR", "de-DE", "es-ES", "it-IT")
        )
        
        // 支持的语言列表
        val SUPPORTED_LANGUAGES = listOf(
            "zh-CN" to "中文(简体)",
            "zh-TW" to "中文(繁体)",
            "zh-HK" to "中文(香港)",
            "en-US" to "英语(美国)",
            "en-GB" to "英语(英国)",
            "en-AU" to "英语(澳大利亚)",
            "ja-JP" to "日语",
            "ko-KR" to "韩语",
            "fr-FR" to "法语",
            "fr-CA" to "法语(加拿大)",
            "de-DE" to "德语",
            "es-ES" to "西班牙语",
            "es-MX" to "西班牙语(墨西哥)",
            "it-IT" to "意大利语",
            "ru-RU" to "俄语",
            "pt-PT" to "葡萄牙语",
            "pt-BR" to "葡萄牙语(巴西)",
            "ar-SA" to "阿拉伯语",
            "nl-NL" to "荷兰语",
            "pl-PL" to "波兰语",
            "tr-TR" to "土耳其语",
            "th-TH" to "泰语",
            "vi-VN" to "越南语",
            "hi-IN" to "印地语",
            "sv-SE" to "瑞典语",
            "da-DK" to "丹麦语",
            "no-NO" to "挪威语",
            "fi-FI" to "芬兰语"
        )
        
        // 语音人映射表
        val VOICE_MAPPING = mapOf(
            "zh-CN" to "zh-CN-XiaoxiaoNeural",
            "zh-TW" to "zh-TW-HsiaoChenNeural",
            "zh-HK" to "zh-HK-HiuGaaiNeural",
            "en-US" to "en-US-JennyNeural",
            "en-GB" to "en-GB-SoniaNeural",
            "en-AU" to "en-AU-NatashaNeural",
            "ja-JP" to "ja-JP-NanamiNeural",
            "ko-KR" to "ko-KR-SunHiNeural",
            "fr-FR" to "fr-FR-DeniseNeural",
            "fr-CA" to "fr-CA-SylvieNeural",
            "de-DE" to "de-DE-KatjaNeural",
            "es-ES" to "es-ES-ElviraNeural",
            "es-MX" to "es-MX-DaliaNeural",
            "it-IT" to "it-IT-ElsaNeural",
            "ru-RU" to "ru-RU-SvetlanaNeural",
            "pt-PT" to "pt-PT-RaquelNeural",
            "pt-BR" to "pt-BR-FranciscaNeural",
            "ar-SA" to "ar-SA-ZariyahNeural",
            "nl-NL" to "nl-NL-ColetteNeural",
            "pl-PL" to "pl-PL-AgnieszkaNeural",
            "tr-TR" to "tr-TR-EmelNeural",
            "th-TH" to "th-TH-PremwadeeNeural",
            "vi-VN" to "vi-VN-HoaiMyNeural",
            "hi-IN" to "hi-IN-SwaraNeural",
            "sv-SE" to "sv-SE-SofieNeural",
            "da-DK" to "da-DK-ChristelNeural",
            "no-NO" to "no-NO-PernilleNeural",
            "fi-FI" to "fi-FI-SelmaNeural"
        )
        
        // 语言检测优化配置
        val LANGUAGE_DETECTION_OPTIMIZATION = mapOf(
            // 快速响应配置
            "fast_response" to mapOf(
                "SpeechServiceConnection_ContinuousLanguageIdThreshold" to "0.2",
                "SpeechServiceConnection_LanguageIdMinimumDuration" to "0",
                "SpeechServiceConnection_LanguageIdMaximumDuration" to "50"
            ),
            // 平衡配置
            "balanced" to mapOf(
                "SpeechServiceConnection_ContinuousLanguageIdThreshold" to "0.5",
                "SpeechServiceConnection_LanguageIdMinimumDuration" to "100",
                "SpeechServiceConnection_LanguageIdMaximumDuration" to "200"
            ),
            // 准确性优先配置
            "accuracy" to mapOf(
                "SpeechServiceConnection_ContinuousLanguageIdThreshold" to "0.8",
                "SpeechServiceConnection_LanguageIdMinimumDuration" to "300",
                "SpeechServiceConnection_LanguageIdMaximumDuration" to "500"
            )
        )
        
        /**
         * 获取语言的显示名称
         */
        fun getLanguageDisplayName(languageCode: String): String {
            return SUPPORTED_LANGUAGES.find { it.first == languageCode }?.second ?: languageCode
        }
        
        /**
         * 获取语言的TTS语音名称
         */
        fun getVoiceNameForLanguage(languageCode: String): String {
            return VOICE_MAPPING[languageCode] ?: "en-US-JennyNeural"
        }
        
        /**
         * 检查语言是否支持
         */
        fun isLanguageSupported(languageCode: String): Boolean {
            return SUPPORTED_LANGUAGES.any { it.first == languageCode }
        }
        
        /**
         * 获取语言检测优化配置
         */
        fun getLanguageDetectionConfig(mode: String): Map<String, String> {
            return LANGUAGE_DETECTION_OPTIMIZATION[mode] ?: LANGUAGE_DETECTION_OPTIMIZATION["balanced"]!!
        }
    }
} 