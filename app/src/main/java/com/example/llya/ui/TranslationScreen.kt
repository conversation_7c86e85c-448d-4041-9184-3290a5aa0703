package com.example.llya.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Translate
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.data.HistoryRecord
import com.example.llya.data.RecordType
import com.example.llya.ui.components.BottomNavItem
import com.example.llya.ui.components.CommonBottomNavBar
import com.example.llya.utils.UserCache
import com.example.llya.viewmodel.HistoryViewModel
import com.example.llya.viewmodel.UserViewModel
import android.app.Application
import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun TranslationScreen(
    navController: NavController? = null
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8364FD)
    val cardBgColor = Color(0xFF33324A)
    val context = LocalContext.current

    // 获取UserViewModel以便刷新用户数据
    val userViewModel: UserViewModel = viewModel(factory = ViewModelProvider.AndroidViewModelFactory.getInstance(context.applicationContext as Application))

    // 获取HistoryViewModel以显示历史记录
    val historyViewModel: HistoryViewModel = viewModel()
    val historyRecords by historyViewModel.historyRecords.collectAsState()
    val isPlaying by historyViewModel.isPlaying.collectAsState()
    val isLoading by historyViewModel.isLoading.collectAsState()

    // 最近的5条历史记录
    val recentRecords = remember(historyRecords) {
        historyRecords.take(5)
    }

    // 控制是否显示历史记录列表
    var showHistory by remember { mutableStateOf(false) }

    // VIP过期对话框状态
    var showVipExpiredDialog by remember { mutableStateOf(false) }
    var isCheckingVip by remember { mutableStateOf(true) } // 添加检查状态

    // 刷新并检查VIP状态
    LaunchedEffect(Unit) {
        Log.d("TranslationScreen", "开始检查VIP状态...")
        isCheckingVip = true

        // 强制刷新用户数据
        userViewModel.getUserDetail()

        // 延迟100ms等待数据刷新
        delay(100)

        // 获取最新用户数据
        val currentUser = UserCache.currentUser.value
        Log.d("TranslationScreen", "刷新后的用户数据: ${currentUser != null}, VIP到期时间: ${currentUser?.vipExpireTime}")

        val isExpired = if (currentUser != null) {
            val expired = isVipExpired(currentUser.vipExpireTime)
            Log.d("TranslationScreen", "VIP状态检查完成: 是否过期=$expired")
            expired
        } else {
            Log.d("TranslationScreen", "用户数据为空，视为非VIP用户")
            true
        }

        if (isExpired) {
            Log.d("TranslationScreen", "VIP已过期，显示提醒对话框")
            showVipExpiredDialog = true
        }

        isCheckingVip = false
    }

    // 显示加载状态
    if (isCheckingVip) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(color = purpleColor)
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = stringResource(R.string.checking_vip_status),
                    color = Color.White
                )
            }
        }
        return
    }

    // VIP过期提醒对话框
    if (showVipExpiredDialog) {
        AlertDialog(
            onDismissRequest = {
                showVipExpiredDialog = false
                // 返回耳机详情页面
                navController?.navigate("earbuds_detail")
            },
            title = { Text(stringResource(R.string.vip_expired)) },
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        stringResource(R.string.vip_expired_translation_message),
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 显示VIP到期时间
                    val expireTimeDisplay = formatVipExpireDate(UserCache.currentUser.value?.vipExpireTime)
                    Text(
                        stringResource(R.string.expire_time_display, expireTimeDisplay),
                        textAlign = TextAlign.Center,
                        fontSize = 14.sp,
                        color = Color.LightGray
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        stringResource(R.string.contact_customer_service),
                        textAlign = TextAlign.Center
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        showVipExpiredDialog = false
                        // 返回耳机详情页面
                        navController?.navigate("earbuds_detail")
                    },
                    colors = ButtonDefaults.buttonColors(containerColor = purpleColor)
                ) {
                    Text(stringResource(R.string.return_to_earbuds_detail))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showVipExpiredDialog = false
                        // 可以添加跳转到联系客服页面的逻辑
                    }
                ) {
                    Text(stringResource(R.string.contact_customer_service_button))
                }
            },
            containerColor = Color(0xFF272636),
            titleContentColor = Color.White,
            textContentColor = Color.White
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏和历史记录切换
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, top = 16.dp, end = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = stringResource(R.string.translation),
                color = Color.White,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold
            )

            // 历史记录按钮
            IconButton(
                onClick = { showHistory = !showHistory }
            ) {
                Icon(
                    imageVector = if (showHistory) Icons.Filled.KeyboardArrowUp else Icons.Filled.History,
                    contentDescription = stringResource(R.string.history_title),
                    tint = Color.White
                )
            }
        }

        // 最近历史记录区域
        if (showHistory && recentRecords.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = cardBgColor)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "最近翻译记录",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // 显示最近5条记录
                    LazyColumn(
                        modifier = Modifier.height(240.dp)
                    ) {
                        items(recentRecords) { record ->
                            HistoryRecordListItem(
                                record = record,
                                onPlayTranslation = {
                                    historyViewModel.playTranslatedText(record)
                                },
                                isPlaying = isPlaying && historyViewModel.selectedRecord.value?.id == record.id,
                                isLoading = isLoading && historyViewModel.selectedRecord.value?.id == record.id,
                                purpleColor = purpleColor
                            )

                            if (recentRecords.indexOf(record) < recentRecords.size - 1) {
                                Divider(
                                    color = Color.Gray.copy(alpha = 0.3f),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }

        // 翻译模式卡片区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp) // 增加卡片间距
        ) {
            // AI对话模式卡片 - 浅绿色
            TranslationModeCard(
                title = stringResource(R.string.ai_chat_mode),
                backgroundColor = Color(0xFFE6F4F1),
                iconRes = R.drawable.aichat,
                onClick = {
                    val currentUser = UserCache.currentUser.value
                    Log.d("TranslationScreen", "点击AI对话模式: 当前用户=${currentUser != null}, VIP到期时间=${currentUser?.vipExpireTime}")

                    val expired = if (currentUser != null) {
                        isVipExpired(currentUser.vipExpireTime)
                    } else {
                        true // 用户为空，视为非VIP
                    }

                    if (!expired) {
                        Log.d("TranslationScreen", "VIP有效，导航到AI对话页面")
                        navController?.navigate("aichat")
                    } else {
                        Log.d("TranslationScreen", "VIP过期或不是VIP用户，显示提醒")
                        showVipExpiredDialog = true
                    }
                }
            )

            // 同声传译卡片 - 浅橙色
            TranslationModeCard(
                title = stringResource(R.string.simultaneous_interpretation),
                backgroundColor = Color(0xFFFFF1E6),
                iconRes = R.drawable.tel,
                onClick = {
                    val currentUser = UserCache.currentUser.value
                    Log.d("TranslationScreen", "点击同声传译: 当前用户=${currentUser != null}, VIP到期时间=${currentUser?.vipExpireTime}")

                    val expired = if (currentUser != null) {
                        isVipExpired(currentUser.vipExpireTime)
                    } else {
                        true // 用户为空，视为非VIP
                    }

                    if (!expired) {
                        Log.d("TranslationScreen", "VIP有效，导航到微软同声传译页面")
                        navController?.navigate("microsoft_translation")
                    } else {
                        Log.d("TranslationScreen", "VIP过期或不是VIP用户，显示提醒")
                        showVipExpiredDialog = true
                    }
                }
            )


            // 会议记录卡片 - 浅紫色
            TranslationModeCard(
                title = stringResource(R.string.meeting_record),
                backgroundColor = Color(0xFFF8E6FF),
                iconRes = R.drawable.note,
                onClick = {
                    val currentUser = UserCache.currentUser.value
                    Log.d("TranslationScreen", "点击会议记录: 当前用户=${currentUser != null}, VIP到期时间=${currentUser?.vipExpireTime}")

                    val expired = if (currentUser != null) {
                        isVipExpired(currentUser.vipExpireTime)
                    } else {
                        true // 用户为空，视为非VIP
                    }

                    if (!expired) {
                        Log.d("TranslationScreen", "VIP有效，导航到会议记录页面")
                        navController?.navigate("google_meeting_record")
                    } else {
                        Log.d("TranslationScreen", "VIP过期或不是VIP用户，显示提醒")
                        showVipExpiredDialog = true
                    }
                }
            )





        }

        Spacer(modifier = Modifier.height(16.dp))

        // 使用公共底部导航栏
        CommonBottomNavBar(
            navController = navController,
            currentRoute = "translation"
        )
    }
}

@Composable
fun TranslationModeCard(
    title: String,
    backgroundColor: Color,
    iconRes: Int,
    onClick: () -> Unit
) {
    // 获取上下文
    val context = LocalContext.current

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(160.dp) // 增大卡片高度
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp // 添加阴影
        ),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧标题
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 24.sp, // 增大文字大小
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 添加简短描述文字
                Text(
                    text = getCardDescription(title, context),
                    fontSize = 14.sp,
                    color = Color.DarkGray.copy(alpha = 0.7f)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 右侧图标
            Image(
                painter = painterResource(id = iconRes),
                contentDescription = title,
                contentScale = ContentScale.Fit,
                modifier = Modifier.size(100.dp) // 增大图标尺寸
            )
        }
    }
}

// 获取卡片描述文字
private fun getCardDescription(title: String, context: Context): String {
    return when (title) {
        context.getString(R.string.ai_chat_mode) -> context.getString(R.string.ai_chat_mode_description)
        context.getString(R.string.simultaneous_interpretation) -> context.getString(R.string.simultaneous_interpretation_description)
        context.getString(R.string.meeting_record) -> context.getString(R.string.meeting_record_description)
        else -> ""
    }
}

// 检查VIP是否过期
private fun isVipExpired(vipExpireTime: Long?): Boolean {
    // 添加调试日志，输出原始值
    Log.d("TranslationScreen", "VIP过期检查原始值: $vipExpireTime, 类型: ${vipExpireTime?.javaClass?.simpleName}")

    // 如果vipExpireTime为null或0，表示用户不是VIP
    if (vipExpireTime == null || vipExpireTime == 0L) {
        Log.d("TranslationScreen", "用户没有VIP权限（值为null或0）")
        return true // 视为已过期，无法使用VIP功能
    }

    try {
        // 判断是秒级还是毫秒级时间戳
        val timestamp = if (vipExpireTime < 10000000000L) {
            // 秒级时间戳，转换为毫秒
            vipExpireTime * 1000
        } else {
            // 已经是毫秒级时间戳
            vipExpireTime
        }

        val currentTime = System.currentTimeMillis()

        // 尝试将timestamp作为时间戳处理（毫秒）
        val expireDate = Date(timestamp)
        val currentDate = Date(currentTime)

        // 格式化日期以便于日志输出
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val formattedExpireTime = dateFormat.format(expireDate)
        val formattedCurrentTime = dateFormat.format(currentDate)

        // 比较日期
        val isExpired = expireDate.before(currentDate)

        // 详细日志输出
        Log.d("TranslationScreen", """
            VIP过期检查详情:
            - 原始时间戳: $vipExpireTime
            - 转换后时间戳: $timestamp
            - 过期时间: $formattedExpireTime
            - 当前时间戳: $currentTime
            - 当前时间: $formattedCurrentTime
            - 是否过期: $isExpired
        """.trimIndent())

        return isExpired
    } catch (e: Exception) {
        // 捕获任何可能的异常，确保安全处理
        Log.e("TranslationScreen", "VIP过期检查出错: ${e.message}", e)
        return false // 发生错误时，默认为未过期，避免错误地阻止用户访问
    }
}

// 格式化VIP到期时间为易读格式
private fun formatVipExpireDate(vipExpireTime: Long?): String {
    if (vipExpireTime == null || vipExpireTime == 0L) {
        return "未设置"
    }

    try {
        // 判断是秒级还是毫秒级时间戳
        val timestamp = if (vipExpireTime < 10000000000L) {
            // 秒级时间戳，转换为毫秒
            vipExpireTime * 1000
        } else {
            // 已经是毫秒级时间戳
            vipExpireTime
        }

        // 转换为日期并格式化（只显示到日）
        val date = Date(timestamp)
        val dateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())

        // 记录调试信息
        Log.d("TranslationScreen", "格式化VIP到期时间: 原始值=$vipExpireTime, 转换后时间戳=$timestamp, 格式化结果=${dateFormat.format(date)}")

        return dateFormat.format(date)
    } catch (e: Exception) {
        Log.e("TranslationScreen", "格式化VIP到期时间失败: ${e.message}")
        // 尝试直接返回原始值作为后备
        return vipExpireTime.toString()
    }
}

@Composable
fun TranslationContent(
    // ... existing code ...
) {
    // ... existing code ...
}

/**
 * 历史记录列表项组件
 */
@Composable
fun HistoryRecordListItem(
    record: HistoryRecord,
    onPlayTranslation: () -> Unit,
    isPlaying: Boolean,
    isLoading: Boolean,
    purpleColor: Color
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // 记录头部信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 记录类型和时间
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (record.type == RecordType.SPEECH_RECOGNITION)
                                    Icons.Filled.Translate
                                  else
                                    Icons.Filled.Translate,
                    contentDescription = null,
                    tint = purpleColor,
                    modifier = Modifier.size(16.dp)
                )

                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = record.getShortTime(),
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 播放按钮
            if (record.translatedText.isNotEmpty()) {
                IconButton(
                    onClick = onPlayTranslation,
                    modifier = Modifier.size(24.dp)
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = purpleColor,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Filled.PlayArrow,
                            contentDescription = "播放翻译",
                            tint = if (isPlaying) purpleColor else Color.White
                        )
                    }
                }
            }
        }

        // 原文
        Text(
            text = record.sourceText,
            fontSize = 14.sp,
            color = Color.White,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        // 翻译文本 (如果有)
        if (record.translatedText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = record.translatedText,
                fontSize = 14.sp,
                color = Color.White.copy(alpha = 0.7f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 语言信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = "${record.sourceLanguageName} → ${record.targetLanguageName}",
                fontSize = 11.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 翻译功能卡片
 */
@Composable
private fun TranslationCard(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit
) {
    val cardBgColor = Color(0xFF33324A)
    val purpleColor = Color(0xFF8364FD)

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = cardBgColor)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.6f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = purpleColor,
                modifier = Modifier
                    .size(24.dp)
                    .padding(start = 8.dp)
            )
        }
    }
}