<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.9.0)" variant="all" version="8.9.0">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            audioRecord = AudioRecord("
        errorLine2="                          ^">
        <location
            file="src/main/java/com/example/llya/audio/AudioRecorder.kt"
            line="58"
            column="27"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="              &quot;connectedDevice=${state.connectedDevice?.name ?: &quot;无&quot;}, &quot; +"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="219"
            column="34"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                val deviceName = device.name ?: &quot;OWS1002&quot; "
        errorLine2="                                 ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="363"
            column="34"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            text = &quot;已连接: ${connectedDevice?.name ?: &quot;未知设备&quot;}&quot;"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="602"
            column="28"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="    val deviceName = device.name ?: &quot;未知设备 (${device.address.takeLast(5)})&quot;"
        errorLine2="                     ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="760"
            column="22"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    val deviceName = device.name ?: &quot;未知设备&quot;"
        errorLine2="                                     ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="975"
            column="38"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="    val deviceClass = device.bluetoothClass?.majorDeviceClass"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="1063"
            column="23"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="    val deviceSubClass = device.bluetoothClass?.deviceClass"
        errorLine2="                         ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="1064"
            column="26"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="    val deviceName = device.name ?: &quot;未知设备 (${device.address.takeLast(5)})&quot;"
        errorLine2="                     ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="1202"
            column="22"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            Log.d(TAG, &quot;开始更新设备信息: 名称=${device.name ?: &quot;未知设备&quot;}, 地址=${device.address}&quot;)"
        errorLine2="                                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothViewModel.kt"
            line="120"
            column="40"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            Log.d(TAG, &quot;UI状态已更新: connectedDevice=${device.name ?: &quot;未知设备&quot;}&quot;)"
        errorLine2="                                                   ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothViewModel.kt"
            line="128"
            column="52"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                   &quot;connectedDevice=${_state.value.connectedDevice?.name}, &quot; +"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothViewModel.kt"
            line="147"
            column="39"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="    val deviceName = device.name ?: &quot;未知设备 (${device.address.takeLast(5)})&quot;"
        errorLine2="                     ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/DevicesList.kt"
            line="173"
            column="22"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        val a2dpProfile = bluetoothAdapter.getProfileConnectionState(android.bluetooth.BluetoothProfile.A2DP)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/ui/NoDeviceScreen.kt"
            line="299"
            column="27"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        val headsetProfile = bluetoothAdapter.getProfileConnectionState(android.bluetooth.BluetoothProfile.HEADSET)"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/ui/NoDeviceScreen.kt"
            line="301"
            column="30"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                        &quot;${String.format(&quot;%02d&quot;, utcTime.get(java.util.Calendar.MONTH) + 1)}-&quot; +"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/translation/TencentTranslationClient.kt"
            line="65"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                        String.format(&quot;%02d&quot;, utcTime.get(java.util.Calendar.DAY_OF_MONTH))"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/translation/TencentTranslationClient.kt"
            line="66"
            column="25"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="            val dateFormat = java.text.SimpleDateFormat(&quot;yyyyMMdd&quot;)"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/utils/SignatureUtil.kt"
            line="95"
            column="30"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        message="`broadcastReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for com.example.llya.DEVICE_CONNECTED"
        errorLine1="            getApplication&lt;Application>().registerReceiver(broadcastReceiver, filter)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothViewModel.kt"
            line="307"
            column="13"/>
    </issue>

    <issue
        id="VectorRaster"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more"
        errorLine1="    android:width=&quot;240dp&quot;"
        errorLine2="                   ~~~~~">
        <location
            file="src/main/res/drawable/earbuds_image.xml"
            line="2"
            column="20"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.9.0 is available: 8.9.1"
        errorLine1="agp = &quot;8.9.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.9.0 is available: 8.9.1"
        errorLine1="agp = &quot;8.9.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.9.0 is available: 8.9.1"
        errorLine1="agp = &quot;8.9.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose.material:material-icons-extended than 1.6.1 is available: 1.7.8"
        errorLine1="    implementation(&quot;androidx.compose.material:material-icons-extended:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="66"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.6.2 is available: 2.8.7"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-runtime-compose:2.6.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="69"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer than 1.2.1 is available: 1.6.1"
        errorLine1="    implementation(&quot;androidx.media3:media3-exoplayer:1.2.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="80"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of commons-codec:commons-codec than 1.16.0 is available: 1.17.1"
        errorLine1="    implementation(&quot;commons-codec:commons-codec:1.16.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="83"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.7 is available: 2.8.9"
        errorLine1="    implementation(&quot;androidx.navigation:navigation-compose:2.7.7&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="92"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.04.00"
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.04.00"
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.04.00"
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/Desktop/llya/gradle/libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/ui/AIChatScreen.kt"
            line="188"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="This folder configuration (`v26`) is unnecessary; `minSdkVersion` is 26. Merge all the resources in this folder into `mipmap-anydpi`.">
        <location
            file="src/main/res/mipmap-anydpi-v26"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="This field leaks a context object"
        errorLine1="    private val context: Context,"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/aliyunasi/AliyunAsrViewModel.kt"
            line="22"
            column="5"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        errorLine1="    var checkCount by remember { mutableStateOf(0) }"
        errorLine2="                                 ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/bluetooth/BluetoothScreen.kt"
            line="97"
            column="34"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        errorLine1="    var wordCount by remember { mutableStateOf(0) }"
        errorLine2="                                ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/llya/ui/MeetingRecordScreen.kt"
            line="70"
            column="33"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.aichat_vector` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/aichat_vector.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black` appears to be unused"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.earbuds_image` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/earbuds_image.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_ai_robot` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_ai_robot.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_battery` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_battery.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_chat_ai` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_chat_ai.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_earbuds` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_earbuds.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_earbuds_phone` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_earbuds_phone.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_language` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_language.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_meeting_notes` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_meeting_notes.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_swap` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_swap.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_translator` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_translator.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.note_vector` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/note_vector.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.tel_vector` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/tel_vector.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src/main/res/drawable/ic_launcher_foreground.xml, src/main/res/mipmap-hdpi/ic_launcher_foreground.webp">
        <location
            file="src/main/res/mipmap-xxxhdpi/ic_launcher_foreground.webp"/>
        <location
            file="src/main/res/mipmap-xxhdpi/ic_launcher_foreground.webp"/>
        <location
            file="src/main/res/mipmap-xhdpi/ic_launcher_foreground.webp"/>
        <location
            file="src/main/res/mipmap-mdpi/ic_launcher_foreground.webp"/>
        <location
            file="src/main/res/mipmap-hdpi/ic_launcher_foreground.webp"/>
        <location
            file="src/main/res/drawable/ic_launcher_foreground.xml"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive icon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive roundIcon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/aichat.png` in densityless folder">
        <location
            file="src/main/res/drawable/aichat.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/earbuds.png` in densityless folder">
        <location
            file="src/main/res/drawable/earbuds.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/note.png` in densityless folder">
        <location
            file="src/main/res/drawable/note.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/tel.png` in densityless folder">
        <location
            file="src/main/res/drawable/tel.png"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.compose.material:material-icons-extended:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="66"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-runtime-compose:2.6.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="69"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.retrofit2:retrofit:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="72"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.retrofit2:converter-gson:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="73"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.okhttp3:okhttp:4.12.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="77"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-exoplayer:1.2.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="80"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;commons-codec:commons-codec:1.16.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="83"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="86"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.moshi:moshi-kotlin:1.15.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="89"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.navigation:navigation-compose:2.7.7&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="92"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.accompanist:accompanist-permissions:0.33.2-alpha&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="95"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.accompanist:accompanist-systemuicontroller:0.32.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="98"
            column="20"/>
    </issue>

</issues>
