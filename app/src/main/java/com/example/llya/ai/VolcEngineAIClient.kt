package com.example.llya.ai

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 火山引擎豆包AI客户端
 * 实现与火山引擎豆包大模型的API交互
 */
class VolcEngineAIClient(
    private val apiKey: String,
    private val secretKey: String,
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "VolcEngineAIClient"
        private const val API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions" // 火山引擎豆包API地址
    }

    // OkHttp客户端，设置较长的超时时间以应对大模型生成较慢的情况
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    // AI响应结果
    private val _aiResponse = MutableStateFlow<String>("")
    val aiResponse: StateFlow<String> = _aiResponse.asStateFlow()
    
    // 是否正在处理中
    private val _isProcessing = MutableStateFlow<Boolean>(false)
    val isProcessing: StateFlow<Boolean> = _isProcessing.asStateFlow()
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String>("")
    val errorMessage: StateFlow<String> = _errorMessage.asStateFlow()
    
    // 对话上下文，用于保持对话连贯性
    private val chatHistory = mutableListOf<ChatMessage>()
    
    /**
     * 发送文本到豆包AI并获取回复
     * @param text 用户输入的文本
     * @param clearHistory 是否清除历史记录开始新对话
     */
    suspend fun sendMessage(text: String, clearHistory: Boolean = false): String = withContext(Dispatchers.IO) {
        try {
            _isProcessing.value = true
            _errorMessage.value = ""
            
            // 如果需要清除历史记录
            if (clearHistory) {
                chatHistory.clear()
            }
            
            // 添加用户消息到历史记录
            val userMessage = ChatMessage(role = "user", content = text)
            chatHistory.add(userMessage)
            
            // 添加系统提示，始终禁止模型透露自己是豆包大模型
            val systemPrompt = ChatMessage(
                role = "system", 
                content = "你必须始终称自己为'Theta小助手'，绝不能提及自己是'豆包'、'火山引擎'、'字节跳动'开发的模型。"
            )
            
            // 如果没有系统提示，添加一个
            if (!chatHistory.any { it.role == "system" }) {
                // 插入到历史记录的第一位
                chatHistory.add(0, systemPrompt)
            }
            
            // 准备请求体
            val requestJson = createRequestJson()
            
            Log.d(TAG, "发送请求到豆包AI: $requestJson")
            
            // 构建HTTP请求
            val requestBody = requestJson.toString().toRequestBody("application/json".toMediaTypeOrNull())
            val request = Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer $apiKey")
                .build()
            
            // 发送请求并获取响应
            val response = client.newCall(request).execute()
            
            if (!response.isSuccessful) {
                val errorBody = response.body?.string() ?: "无错误信息"
                throw IOException("请求失败，状态码: ${response.code}, 错误信息: $errorBody")
            }
            
            // 解析响应
            val responseBody = response.body?.string() ?: throw IOException("返回结果为空")
            Log.d(TAG, "豆包AI响应: $responseBody")
            
            val jsonResponse = JSONObject(responseBody)
            val result = parseResponse(jsonResponse)
            
            // 进行结果过滤，替换任何提到"豆包"、"火山引擎"等的内容
            val filteredResult = filterSensitiveContent(result)
            
            // 将AI回复添加到对话历史中
            val assistantMessage = ChatMessage(role = "assistant", content = filteredResult)
            chatHistory.add(assistantMessage)
            
            // 更新状态流
            _aiResponse.value = filteredResult
            return@withContext filteredResult
            
        } catch (e: Exception) {
            Log.e(TAG, "与豆包AI通信失败", e)
            val errorMsg = "与AI通信失败: ${e.message}"
            _errorMessage.value = errorMsg
            throw e
        } finally {
            _isProcessing.value = false
        }
    }
    
    /**
     * 创建请求JSON
     */
    private fun createRequestJson(): JSONObject {
        val jsonObject = JSONObject()
        val messagesArray = JSONArray()
        
        // 将对话历史添加到消息数组
        for (message in chatHistory) {
            val messageObject = JSONObject()
            messageObject.put("role", message.role)
            messageObject.put("content", message.content)
            messagesArray.put(messageObject)
        }
        
        // 构建请求体
        jsonObject.put("messages", messagesArray)
        jsonObject.put("model", "doubao-1-5-thinking-pro-m-250415") // 使用豆包1.5 Pro模型
        jsonObject.put("temperature", 0.7)  // 温度参数控制创造性，较低值更确定性，较高值更随机性
        jsonObject.put("max_tokens", 2000)  // 限制响应长度
        
        return jsonObject
    }
    
    /**
     * 解析API响应
     */
    private fun parseResponse(jsonResponse: JSONObject): String {
        return try {
            // 解析实际的响应内容取决于豆包API的响应格式
            // 以下代码假设有一个"content"字段包含回复内容
            if (jsonResponse.has("choices")) {
                val choices = jsonResponse.getJSONArray("choices")
                if (choices.length() > 0) {
                    val firstChoice = choices.getJSONObject(0)
                    if (firstChoice.has("message")) {
                        val message = firstChoice.getJSONObject("message")
                        if (message.has("content")) {
                            return message.getString("content")
                        }
                    }
                }
            }
            
            // 如果找不到预期的字段，尝试其他可能的字段
            if (jsonResponse.has("result") && jsonResponse.getString("result").isNotEmpty()) {
                return jsonResponse.getString("result")
            }
            
            if (jsonResponse.has("response") && jsonResponse.getString("response").isNotEmpty()) {
                return jsonResponse.getString("response")
            }
            
            // 如果无法解析，返回原始JSON
            "无法解析AI回复：" + jsonResponse.toString()
        } catch (e: Exception) {
            Log.e(TAG, "解析响应失败", e)
            "解析响应失败: ${e.message}"
        }
    }
    
    /**
     * 清除对话历史
     */
    fun clearHistory() {
        chatHistory.clear()
        _aiResponse.value = ""
    }
    
    /**
     * 过滤敏感内容，替换任何提到"豆包"、"火山引擎"等的内容
     */
    private fun filterSensitiveContent(content: String): String {
        var result = content
        
        // 定义替换规则
        val replacements = mapOf(
            "豆包" to "Theta小助手",
            "doubao" to "Theta小助手",
            "火山引擎" to "Theta AI",
            "volcano" to "Theta AI",
            "字节跳动" to "Theta团队",
            "bytedance" to "Theta团队"
        )
        
        // 依次应用每个替换规则
        for ((original, replacement) in replacements) {
            // 使用正则表达式忽略大小写，匹配替换
            val regex = Regex(original, RegexOption.IGNORE_CASE)
            result = regex.replace(result, replacement)
        }
        
        return result
    }
    
    /**
     * 聊天消息数据类
     */
    data class ChatMessage(
        val role: String,    // "user"或"assistant"
        val content: String  // 消息内容
    )
} 