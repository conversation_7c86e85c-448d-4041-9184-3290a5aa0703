package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.SmartToy
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import com.example.llya.utils.AIChatPersistenceManager
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIChatDetailScreen(
    navController: NavController,
    chatSessionId: String
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val userBubbleColor = Color(0xFF8A6BFF) // 浅紫色
    val aiBubbleColor = Color(0xFF33324A)   // 深灰色
    
    val speechViewModel: SpeechRecognitionViewModel = viewModel()
    val selectedChatSession by speechViewModel.selectedChatSession.collectAsState()
    val context = LocalContext.current
    
    // 加载聊天会话详情
    LaunchedEffect(chatSessionId) {
        speechViewModel.loadChatSessionDetail(chatSessionId)
    }
    
    // 当前聊天消息
    val messages = selectedChatSession?.messages ?: emptyList()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    text = selectedChatSession?.title ?: "聊天记录",
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor,
                titleContentColor = Color.White
            )
        )
        
        // 聊天消息列表
        val listState = rememberLazyListState()
        val coroutineScope = rememberCoroutineScope()
        
        // 初次加载时自动滚动到底部
        LaunchedEffect(messages.size) {
            if (messages.isNotEmpty()) {
                coroutineScope.launch {
                    listState.animateScrollToItem(messages.size - 1)
                }
            }
        }
        
        if (messages.isEmpty()) {
            // 显示空状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF8A6BFF)
                )
            }
        } else {
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 16.dp),
                contentPadding = PaddingValues(vertical = 16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(messages) { message ->
                    ChatMessageItem(
                        message = message,
                        userBubbleColor = userBubbleColor,
                        aiBubbleColor = aiBubbleColor
                    )
                }
            }
        }
    }
}

@Composable
private fun ChatMessageItem(
    message: AIChatPersistenceManager.ChatMessage,
    userBubbleColor: Color,
    aiBubbleColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = if (message.isFromUser) Arrangement.End else Arrangement.Start,
        verticalAlignment = Alignment.Top
    ) {
        // AI头像 - 仅在消息来自AI时显示
        if (!message.isFromUser) {
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF8A6BFF)),  // 紫色背景
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.SmartToy,
                    contentDescription = "AI",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
        }
        
        // 消息气泡和时间戳
        Column(
            horizontalAlignment = if (message.isFromUser) Alignment.End else Alignment.Start
        ) {
            // 气泡
            Box(
                modifier = Modifier
                    .widthIn(max = 260.dp)
                    .clip(
                        RoundedCornerShape(
                            topStart = 16.dp,
                            topEnd = 16.dp,
                            bottomStart = if (message.isFromUser) 16.dp else 4.dp,
                            bottomEnd = if (message.isFromUser) 4.dp else 16.dp
                        )
                    )
                    .background(if (message.isFromUser) userBubbleColor else aiBubbleColor)
                    .padding(12.dp)
            ) {
                Text(
                    text = message.content,
                    color = Color.White,
                    modifier = Modifier.widthIn(max = 240.dp)
                )
            }
            
            // 时间戳
            Text(
                text = message.timestamp,
                color = Color.Gray,
                fontSize = 12.sp,
                modifier = Modifier.padding(top = 4.dp),
                textAlign = if (message.isFromUser) TextAlign.End else TextAlign.Start
            )
        }
        
        // 用户头像 - 仅在消息来自用户时显示
        if (message.isFromUser) {
            Spacer(modifier = Modifier.width(8.dp))
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF4CAF50)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "ME",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
} 