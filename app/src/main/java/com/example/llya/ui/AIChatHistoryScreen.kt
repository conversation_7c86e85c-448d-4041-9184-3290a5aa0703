package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.SmartToy
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.utils.AIChatPersistenceManager
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import java.text.SimpleDateFormat
import java.util.*
import android.widget.Toast

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIChatHistoryScreen(
    navController: NavController
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val speechViewModel: SpeechRecognitionViewModel = viewModel()
    val context = LocalContext.current
    
    // 聊天会话列表
    val chatSessions by speechViewModel.chatSessions.collectAsState()
    
    // 确认删除对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    var sessionToDelete by remember { mutableStateOf<AIChatPersistenceManager.ChatSession?>(null) }
    
    // 确认删除所有对话框状态
    var showDeleteAllDialog by remember { mutableStateOf(false) }
    
    // 加载聊天会话 - 确保第一次进入页面时加载
    LaunchedEffect(Unit) {
        speechViewModel.loadChatSessions()
    }
    
    // 确认删除对话框
    if (showDeleteDialog && sessionToDelete != null) {
        AlertDialog(
            onDismissRequest = { 
                showDeleteDialog = false 
                sessionToDelete = null
            },
            title = { Text("删除聊天记录") },
            text = { Text("确定要删除\"${sessionToDelete?.title}\"聊天记录吗？此操作不可撤销。") },
            confirmButton = {
                Button(
                    onClick = {
                        sessionToDelete?.id?.let { id ->
                            speechViewModel.deleteChatSession(id)
                            Toast.makeText(context, "已删除聊天记录", Toast.LENGTH_SHORT).show()
                        }
                        showDeleteDialog = false
                        sessionToDelete = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red
                    )
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    showDeleteDialog = false 
                    sessionToDelete = null
                }) {
                    Text("取消")
                }
            }
        )
    }
    
    // 确认删除所有对话框
    if (showDeleteAllDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAllDialog = false },
            title = { Text("删除所有聊天记录") },
            text = { Text("确定要删除所有聊天记录吗？此操作不可撤销。") },
            confirmButton = {
                Button(
                    onClick = {
                        speechViewModel.clearAllChatSessions()
                        Toast.makeText(context, "已删除所有聊天记录", Toast.LENGTH_SHORT).show()
                        showDeleteAllDialog = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red
                    )
                ) {
                    Text("删除所有")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteAllDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    text = "AI聊天记录",
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            actions = {
                // 清空所有记录按钮
                if (chatSessions.isNotEmpty()) {
                    IconButton(onClick = { showDeleteAllDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除所有",
                            tint = Color.White
                        )
                    }
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor,
                titleContentColor = Color.White
            )
        )
        
        // 内容区域
        if (chatSessions.isEmpty()) {
            // 显示空状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.SmartToy,
                        contentDescription = null,
                        tint = Color.Gray,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        "暂无AI聊天记录",
                        color = Color.Gray,
                        fontSize = 18.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        "与AI助手对话后，点击保存按钮即可将对话保存在这里",
                        color = Color.Gray,
                        fontSize = 14.sp,
                        textAlign = androidx.compose.ui.text.style.TextAlign.Center
                    )
                }
            }
        } else {
            // 显示聊天记录列表
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp)
            ) {
                items(chatSessions) { session ->
                    ChatSessionItem(
                        session = session,
                        onItemClick = {
                            // 保存会话ID到ViewModel，然后导航到聊天详情页面
                            speechViewModel.loadChatSessionDetail(session.id)
                            navController.navigate("aichat_detail/${session.id}")
                        },
                        onDeleteClick = {
                            sessionToDelete = session
                            showDeleteDialog = true
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ChatSessionItem(
    session: AIChatPersistenceManager.ChatSession,
    onItemClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    val date = dateFormat.format(Date(session.timestamp))
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable { onItemClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF33324A)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // AI图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(androidx.compose.foundation.shape.CircleShape)
                    .background(Color(0xFF8A6BFF)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.SmartToy,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // 标题和日期
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp)
            ) {
                Text(
                    text = session.title,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = date,
                    color = Color.Gray,
                    fontSize = 12.sp
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 显示消息预览
                val preview = if (session.messages.isNotEmpty()) {
                    val lastMessage = session.messages.last()
                    val prefix = if (lastMessage.isFromUser) "你: " else "AI: "
                    prefix + lastMessage.content
                } else {
                    "无消息内容"
                }
                
                Text(
                    text = preview,
                    color = Color.LightGray,
                    fontSize = 14.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 删除按钮
            IconButton(onClick = onDeleteClick) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "删除",
                    tint = Color.Gray
                )
            }
        }
    }
} 