# LLya iOS版本核心功能实现示例

本文档提供了LLya iOS版本中几个核心功能的实现示例，包括Google Cloud语音识别、WebSocket实时通信和蓝牙设备管理。

## 1. Google Cloud语音识别实现

### GoogleCloudSpeechService.swift

```swift
import Foundation
import AVFoundation
import Starscream

/// Google Cloud语音识别服务
class GoogleCloudSpeechService {
    // MARK: - 类型定义
    
    /// 识别结果回调
    typealias RecognitionResultHandler = (String, Bool) -> Void
    /// 错误回调
    typealias ErrorHandler = (Error) -> Void
    
    // MARK: - 属性
    
    /// 单例
    static let shared = GoogleCloudSpeechService()
    
    /// 基础URL
    private let baseURL = "https://gogoapi.sdltws.com"
    /// WebSocket URL
    private let wsURL = "wss://gogoapi.sdltws.com/ws/speech-to-text"
    
    /// WebSocket客户端
    private var webSocket: WebSocket?
    
    /// 回调
    private var onRecognitionResult: RecognitionResultHandler?
    private var onError: ErrorHandler?
    
    /// 音频录制器
    private var audioRecorder: AudioRecorder?
    
    /// 是否正在录音
    private(set) var isRecording = false
    
    // MARK: - 初始化方法
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 开始实时语音识别
    /// - Parameters:
    ///   - languageCode: 语言代码
    ///   - onResult: 识别结果回调
    ///   - onError: 错误回调
    func startRecognition(
        languageCode: String = "zh-CN",
        onResult: @escaping RecognitionResultHandler,
        onError: @escaping ErrorHandler
    ) {
        self.onRecognitionResult = onResult
        self.onError = onError
        
        // 如果已经在录音，先停止
        if isRecording {
            stopRecognition()
        }
        
        // 创建WebSocket连接
        connectWebSocket(languageCode: languageCode)
        
        // 创建音频录制器
        audioRecorder = AudioRecorder { [weak self] audioData in
            self?.sendAudioData(audioData)
        } onError: { [weak self] error in
            self?.onError?(error)
            self?.stopRecognition()
        }
        
        // 开始录音
        audioRecorder?.startRecording()
        isRecording = true
    }
    
    /// 停止语音识别
    func stopRecognition() {
        // 停止音频录制
        audioRecorder?.stopRecording()
        audioRecorder = nil
        
        // 关闭WebSocket连接
        webSocket?.disconnect()
        webSocket = nil
        
        isRecording = false
    }
    
    /// 发送音频数据
    /// - Parameter data: 音频数据
    func sendAudioData(_ data: Data) {
        guard let webSocket = webSocket else { return }
        
        webSocket.write(data: data)
    }
    
    // MARK: - 私有方法
    
    /// 连接WebSocket
    /// - Parameter languageCode: 语言代码
    private func connectWebSocket(languageCode: String) {
        // 创建配置消息
        let configMessage = [
            "config": [
                "languageCode": languageCode,
                "sampleRateHertz": 16000,
                "encoding": "LINEAR16",
                "model": "default"
            ]
        ]
        
        // 将配置消息转换为JSON数据
        guard let configData = try? JSONSerialization.data(withJSONObject: configMessage),
              let configString = String(data: configData, encoding: .utf8) else {
            onError?(NSError(domain: "GoogleCloudSpeechService", code: 1, userInfo: [NSLocalizedDescriptionKey: "创建配置消息失败"]))
            return
        }
        
        // 创建WebSocket URL
        guard let url = URL(string: wsURL) else {
            onError?(NSError(domain: "GoogleCloudSpeechService", code: 2, userInfo: [NSLocalizedDescriptionKey: "无效的WebSocket URL"]))
            return
        }
        
        // 创建WebSocket请求
        var request = URLRequest(url: url)
        request.timeoutInterval = 30
        
        // 创建WebSocket
        webSocket = WebSocket(request: request)
        webSocket?.delegate = self
        
        // 连接WebSocket
        webSocket?.connect()
        
        // 发送配置消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [weak self] in
            self?.webSocket?.write(string: configString)
        }
    }
}

// MARK: - WebSocket委托
extension GoogleCloudSpeechService: WebSocketDelegate {
    func didReceive(event: WebSocketEvent, client: WebSocket) {
        switch event {
        case .connected:
            print("WebSocket已连接")
            
        case .disconnected(let reason, _):
            print("WebSocket已断开连接: \(reason)")
            isRecording = false
            
        case .text(let string):
            do {
                if let data = string.data(using: .utf8),
                   let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    
                    // 处理错误
                    if let error = json["error"] as? String {
                        onError?(NSError(domain: "GoogleCloudSpeechService", code: 3, userInfo: [NSLocalizedDescriptionKey: error]))
                        return
                    }
                    
                    // 处理识别结果
                    if let text = json["text"] as? String {
                        let isFinal = json["isFinal"] as? Bool ?? false
                        onRecognitionResult?(text, isFinal)
                    }
                }
            } catch {
                onError?(error)
            }
            
        case .binary(let data):
            print("收到二进制数据: \(data.count) 字节")
            
        case .error(let error):
            print("WebSocket错误: \(error?.localizedDescription ?? "未知错误")")
            onError?(error ?? NSError(domain: "GoogleCloudSpeechService", code: 4, userInfo: [NSLocalizedDescriptionKey: "WebSocket错误"]))
            isRecording = false
            
        default:
            break
        }
    }
}
```

### AudioRecorder.swift

```swift
import Foundation
import AVFoundation

/// 音频录制器
class AudioRecorder {
    // MARK: - 类型定义
    
    /// 音频数据回调
    typealias AudioBufferHandler = (Data) -> Void
    /// 错误回调
    typealias ErrorHandler = (Error) -> Void
    
    // MARK: - 属性
    
    /// 音频引擎
    private let audioEngine = AVAudioEngine()
    /// 音频格式转换器
    private let formatConverter = AVAudioConverter(from: AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 44100, channels: 1, interleaved: false)!,
                                                   to: AVAudioFormat(commonFormat: .pcmFormatInt16, sampleRate: 16000, channels: 1, interleaved: false)!)
    
    /// 音频数据回调
    private let onAudioBuffer: AudioBufferHandler
    /// 错误回调
    private let onError: ErrorHandler
    
    /// 是否正在录音
    private(set) var isRecording = false
    
    // MARK: - 初始化方法
    
    /// 初始化音频录制器
    /// - Parameters:
    ///   - onAudioBuffer: 音频数据回调
    ///   - onError: 错误回调
    init(onAudioBuffer: @escaping AudioBufferHandler, onError: @escaping ErrorHandler) {
        self.onAudioBuffer = onAudioBuffer
        self.onError = onError
    }
    
    // MARK: - 公共方法
    
    /// 开始录音
    func startRecording() {
        // 如果已经在录音，先停止
        if isRecording {
            stopRecording()
        }
        
        do {
            // 配置音频会话
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            
            // 获取输入节点
            let inputNode = audioEngine.inputNode
            let inputFormat = inputNode.outputFormat(forBus: 0)
            
            // 安装音频输入回调
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: inputFormat) { [weak self] buffer, _ in
                guard let self = self else { return }
                
                // 转换音频格式
                guard let convertedBuffer = self.convertBuffer(buffer) else { return }
                
                // 将音频数据发送给回调
                let pcmBuffer = AVAudioPCMBuffer(pcmFormat: AVAudioFormat(commonFormat: .pcmFormatInt16, sampleRate: 16000, channels: 1, interleaved: false)!, frameCapacity: convertedBuffer.frameLength)!
                pcmBuffer.frameLength = convertedBuffer.frameLength
                
                // 复制转换后的数据
                memcpy(pcmBuffer.int16ChannelData?[0], convertedBuffer.int16ChannelData?[0], Int(convertedBuffer.frameLength) * 2)
                
                // 创建Data对象
                let channelData = pcmBuffer.int16ChannelData![0]
                let data = Data(bytes: channelData, count: Int(pcmBuffer.frameLength) * 2)
                
                // 发送数据
                self.onAudioBuffer(data)
            }
            
            // 准备并启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()
            
            isRecording = true
        } catch {
            onError(error)
        }
    }
    
    /// 停止录音
    func stopRecording() {
        if isRecording {
            // 停止音频引擎
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
            
            // 停止音频会话
            do {
                try AVAudioSession.sharedInstance().setActive(false)
            } catch {
                print("停止音频会话失败: \(error)")
            }
            
            isRecording = false
        }
    }
    
    // MARK: - 私有方法
    
    /// 转换音频缓冲区
    /// - Parameter buffer: 输入缓冲区
    /// - Returns: 转换后的缓冲区
    private func convertBuffer(_ buffer: AVAudioPCMBuffer) -> AVAudioPCMBuffer? {
        guard let formatConverter = formatConverter else { return nil }
        
        // 创建输出缓冲区
        let ratio = 16000 / 44100
        let frameCount = UInt32(Float(buffer.frameLength) * Float(ratio))
        
        guard let outputBuffer = AVAudioPCMBuffer(pcmFormat: AVAudioFormat(commonFormat: .pcmFormatInt16, sampleRate: 16000, channels: 1, interleaved: false)!, frameCapacity: frameCount) else {
            return nil
        }
        
        // 配置转换完成标志
        var error: NSError?
        let inputBlock: AVAudioConverterInputBlock = { _, outStatus in
            outStatus.pointee = .haveData
            return buffer
        }
        
        // 执行转换
        let status = formatConverter.convert(to: outputBuffer, error: &error, withInputFrom: inputBlock)
        
        // 检查转换状态
        if status == .error || error != nil {
            print("音频格式转换失败: \(error?.localizedDescription ?? "未知错误")")
            return nil
        }
        
        return outputBuffer
    }
}
```

## 2. 蓝牙设备管理

### BluetoothManager.swift

```swift
import Foundation
import CoreBluetooth

/// 蓝牙管理器
class BluetoothManager: NSObject {
    // MARK: - 类型定义
    
    /// 蓝牙状态变化回调
    typealias StateChangeHandler = (CBManagerState) -> Void
    /// 设备发现回调
    typealias DeviceDiscoveryHandler = (CBPeripheral, [String: Any], NSNumber) -> Void
    /// 连接状态变化回调
    typealias ConnectionHandler = (CBPeripheral, Bool) -> Void
    
    // MARK: - 属性
    
    /// 单例
    static let shared = BluetoothManager()
    
    /// 中央管理器
    private var centralManager: CBCentralManager!
    /// 已发现的设备
    private var discoveredDevices = [UUID: CBPeripheral]()
    /// 当前连接的设备
    private var connectedDevice: CBPeripheral?
    
    /// 状态变化回调
    private var onStateChange: StateChangeHandler?
    /// 设备发现回调
    private var onDeviceDiscovered: DeviceDiscoveryHandler?
    /// 连接状态变化回调
    private var onConnectionChanged: ConnectionHandler?
    
    /// 蓝牙可用状态
    var isAvailable: Bool {
        return centralManager.state == .poweredOn
    }
    
    // MARK: - 初始化方法
    
    private override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    // MARK: - 公共方法
    
    /// 设置回调
    /// - Parameters:
    ///   - stateChange: 状态变化回调
    ///   - deviceDiscovered: 设备发现回调
    ///   - connectionChanged: 连接状态变化回调
    func setCallbacks(stateChange: StateChangeHandler? = nil,
                      deviceDiscovered: DeviceDiscoveryHandler? = nil,
                      connectionChanged: ConnectionHandler? = nil) {
        self.onStateChange = stateChange
        self.onDeviceDiscovered = deviceDiscovered
        self.onConnectionChanged = connectionChanged
    }
    
    /// 开始扫描设备
    func startScan() {
        guard isAvailable else {
            print("蓝牙未开启")
            return
        }
        
        // 清空已发现设备列表
        discoveredDevices.removeAll()
        
        // 开始扫描
        centralManager.scanForPeripherals(withServices: nil, options: [CBCentralManagerScanOptionAllowDuplicatesKey: false])
        
        print("开始扫描蓝牙设备")
    }
    
    /// 停止扫描
    func stopScan() {
        centralManager.stopScan()
        print("停止扫描蓝牙设备")
    }
    
    /// 连接设备
    /// - Parameter peripheral: 外设
    func connect(to peripheral: CBPeripheral) {
        centralManager.connect(peripheral, options: nil)
        print("正在连接到设备: \(peripheral.name ?? "未知设备")")
    }
    
    /// 断开当前连接
    func disconnect() {
        guard let peripheral = connectedDevice else { return }
        
        centralManager.cancelPeripheralConnection(peripheral)
        print("断开设备连接: \(peripheral.name ?? "未知设备")")
    }
    
    /// 获取已发现的设备
    /// - Returns: 设备列表
    func getDiscoveredDevices() -> [CBPeripheral] {
        return Array(discoveredDevices.values)
    }
    
    /// 获取当前连接的设备
    /// - Returns: 当前连接的设备
    func getConnectedDevice() -> CBPeripheral? {
        return connectedDevice
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothManager: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        print("蓝牙状态更新: \(central.state.rawValue)")
        onStateChange?(central.state)
        
        if central.state == .poweredOn {
            // 蓝牙已开启
        } else {
            // 蓝牙未开启
            discoveredDevices.removeAll()
            connectedDevice = nil
        }
    }
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        // 过滤没有名称的设备
        guard let name = peripheral.name, !name.isEmpty else { return }
        
        // 保存发现的设备
        discoveredDevices[peripheral.identifier] = peripheral
        
        print("发现设备: \(name), RSSI: \(RSSI)")
        onDeviceDiscovered?(peripheral, advertisementData, RSSI)
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        connectedDevice = peripheral
        print("已连接到设备: \(peripheral.name ?? "未知设备")")
        onConnectionChanged?(peripheral, true)
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print("连接设备失败: \(peripheral.name ?? "未知设备"), 错误: \(error?.localizedDescription ?? "未知错误")")
        onConnectionChanged?(peripheral, false)
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        if connectedDevice?.identifier == peripheral.identifier {
            connectedDevice = nil
        }
        
        print("设备已断开连接: \(peripheral.name ?? "未知设备"), 错误: \(error?.localizedDescription ?? "无")")
        onConnectionChanged?(peripheral, false)
    }
}
```

## 3. 使用示例

### 在ViewModel中集成语音识别

```swift
import Foundation
import Combine
import AVFoundation

class SpeechViewModel: ObservableObject {
    // 发布的状态
    @Published var recognizedText: String = ""
    @Published var translatedText: String = ""
    @Published var isRecording: Bool = false
    @Published var errorMessage: String = ""
    
    // 语言设置
    @Published var sourceLanguage: String = "zh-CN"
    @Published var targetLanguage: String = "en-US"
    
    // 取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    // 初始化
    init() {
        setupPermissions()
    }
    
    // 设置权限
    private func setupPermissions() {
        // 检查麦克风权限
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                if !granted {
                    self?.errorMessage = "需要麦克风权限才能使用语音识别功能"
                }
            }
        }
    }
    
    // 开始/停止录音
    func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    // 开始录音
    private func startRecording() {
        // 确保麦克风权限已授权
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                if granted {
                    // 开始语音识别
                    GoogleCloudSpeechService.shared.startRecognition(
                        languageCode: self.sourceLanguage,
                        onResult: { [weak self] text, isFinal in
                            DispatchQueue.main.async {
                                self?.recognizedText = text
                                
                                // 如果是最终结果，执行翻译
                                if isFinal {
                                    self?.translate(text)
                                }
                            }
                        },
                        onError: { [weak self] error in
                            DispatchQueue.main.async {
                                self?.errorMessage = "语音识别错误: \(error.localizedDescription)"
                                self?.isRecording = false
                            }
                        }
                    )
                    
                    self.isRecording = true
                } else {
                    self.errorMessage = "需要麦克风权限才能使用语音识别功能"
                }
            }
        }
    }
    
    // 停止录音
    private func stopRecording() {
        GoogleCloudSpeechService.shared.stopRecognition()
        isRecording = false
    }
    
    // 翻译文本
    private func translate(_ text: String) {
        if text.isEmpty { return }
        
        // 调用翻译服务
        TranslationService.shared.translate(
            text: text,
            sourceLanguage: sourceLanguage,
            targetLanguage: targetLanguage
        ) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let translatedText):
                    self?.translatedText = translatedText
                    
                    // 播放翻译结果
                    self?.speakTranslatedText(translatedText)
                    
                case .failure(let error):
                    self?.errorMessage = "翻译错误: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // 语音播放翻译结果
    private func speakTranslatedText(_ text: String) {
        let speechSynthesizer = AVSpeechSynthesizer()
        let utterance = AVSpeechUtterance(string: text)
        
        // 设置语音语言
        utterance.voice = AVSpeechSynthesisVoice(language: targetLanguage)
        
        // 设置语速和音调
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        
        // 播放
        speechSynthesizer.speak(utterance)
    }
    
    // 交换语言
    func swapLanguages() {
        let temp = sourceLanguage
        sourceLanguage = targetLanguage
        targetLanguage = temp
        
        // 清空结果
        recognizedText = ""
        translatedText = ""
    }
}
```

## 4. 重要说明

1. 上面的示例代码仅展示了核心功能的基本实现，实际应用中需要根据项目需求进行调整和完善。

2. 在实现iOS版本时，需要注意以下几点：
   - iOS的权限处理与Android不同，必须在Info.plist中添加相应的描述
   - iOS的音频处理使用AVFoundation框架，与Android的AudioRecord有显著差异
   - 蓝牙功能使用CoreBluetooth框架，API与Android的蓝牙API完全不同
   - 界面开发使用SwiftUI，与Android的Jetpack Compose虽有相似之处，但语法和组件库不同

3. 网络服务器部分可以复用，但客户端适配器需要重新实现：
   - WebSocket通信库使用Starscream或NWWebSocket
   - 网络请求使用URLSession或Alamofire
   - JSON处理使用Codable协议或SwiftyJSON

4. 性能优化考虑：
   - 音频数据处理应在后台线程进行，避免阻塞主线程
   - 网络请求和大数据处理应使用异步API
   - 注意内存管理，及时释放不需要的资源

这些示例提供了iOS版本开发的起点，您可以在此基础上继续开发完整的应用程序。 