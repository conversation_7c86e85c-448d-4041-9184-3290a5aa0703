package com.example.llya.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.llya.data.HistoryRecord
import java.text.SimpleDateFormat
import java.util.*

/**
 * 历史记录项卡片
 */
@Composable
fun HistoryItemCard(
    record: HistoryRecord,
    onPlay: () -> Unit,
    isPlaying: Boolean,
    isLoading: Boolean,
    purpleColor: Color
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A293B)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 时间和类型
                Text(
                    text = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(Date(record.timestamp)),
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                // 播放按钮（仅当有翻译文本时显示）
                if (record.translatedText.isNotEmpty()) {
                    IconButton(
                        onClick = onPlay,
                        modifier = Modifier.size(28.dp)
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(14.dp),
                                color = purpleColor,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                imageVector = if (isPlaying) Icons.Default.Stop else Icons.Default.PlayArrow,
                                contentDescription = if (isPlaying) "停止播放" else "播放翻译",
                                tint = if (isPlaying) Color.Red else purpleColor
                            )
                        }
                    }
                }
            }

            // 源文本
            Text(
                text = record.sourceText,
                fontSize = 14.sp,
                color = Color.White,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(vertical = 4.dp)
            )

            // 翻译文本
            if (record.translatedText.isNotEmpty()) {
                Text(
                    text = record.translatedText,
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.7f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 语言对
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                Text(
                    text = "${record.sourceLanguageName} → ${record.targetLanguageName}",
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            }
        }
    }
}
