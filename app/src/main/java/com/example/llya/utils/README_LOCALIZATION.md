# 多语言支持文档

本文档说明了应用中多语言支持的实现和使用方法。

## 支持的语言

目前应用支持以下语言:

1. 简体中文 (zh-CN)
2. 繁体中文 (zh-TW)  
3. 英语 (en)
4. 法语 (fr)
5. 俄语 (ru)
6. 西班牙语 (es)
7. 阿拉伯语 (ar)
8. 日语 (ja)
9. 韩语 (ko)

## 资源文件结构

语言资源文件按照Android标准结构分布在以下目录:

- `values/` - 默认语言资源 (简体中文)
- `values-zh-rCN/` - 简体中文
- `values-zh-rTW/` - 繁体中文
- `values-en/` - 英语
- `values-fr/` - 法语
- `values-ru/` - 俄语
- `values-es/` - 西班牙语
- `values-ar/` - 阿拉伯语
- `values-ja/` - 日语
- `values-ko/` - 韩语

## 语言切换实现

语言切换主要通过以下几个类实现:

1. `LanguageUtils.kt` - 核心语言工具类，负责管理语言映射和切换语言
2. `LanguageManager.kt` - 从API获取语言列表，确保包含所有必要语言
3. `LanguageSettingsScreen.kt` - 语言选择界面
4. `LanguageHelper.kt` - 提供应用重启和语言环境包装功能
5. `ActivityHelper.kt` - 为Activity应用语言设置的辅助类
6. `BaseActivity.kt` - 基础Activity类，所有Activity都应该继承它以确保正确应用语言设置

## 确保所有页面应用正确的语言设置

应用采用了多种机制确保所有页面都能应用正确的语言设置:

1. **BaseActivity类**：
   - 所有Activity都应该继承BaseActivity类
   - BaseActivity在attachBaseContext、onCreate和onConfigurationChanged方法中应用语言设置
   - 这确保在Activity的整个生命周期中都使用正确的语言

2. **ActivityHelper类**：
   - 提供applyLanguageSettings方法，可以在任何Activity中调用
   - 从SharedPreferences读取保存的语言设置并应用到Activity

3. **ActivityLifecycleCallbacks**：
   - 在LlyaApplication中注册了ActivityLifecycleCallbacks
   - 在每个Activity创建时自动应用语言设置
   - 这是一个全局机制，即使Activity没有继承BaseActivity也能应用语言设置

4. **LanguageHelper类**：
   - 提供wrapContext方法，在attachBaseContext中使用
   - 确保在Activity的最早阶段就应用正确的语言设置

## 默认语言列表

当无法从服务器获取语言列表时，应用会使用默认语言列表。这个列表定义在`LanguageUtils.kt`中:

```kotlin
private val defaultLanguages = mapOf(
    "zh-CN" to "简体中文",
    "zh-TW" to "繁體中文",
    "en" to "English",
    "fr" to "Français",
    "ru" to "Русский",
    "es" to "Español",
    "ar" to "العربية",
    "ja" to "日本語",
    "ko" to "한국어",
    // 其他语言...
)
```

## 多语言与深色主题

在应用切换语言时，会保持深色主题的UI体验。系统实现了以下机制确保语言切换不会影响UI主题：

1. 在`LanguageHelper.kt`中，语言切换前会保存当前系统栏颜色设置
2. 在`MainActivity.kt`中，应用启动时会通过`restoreSystemBarsColor()`方法恢复系统栏颜色
3. 深色主题颜色值(`#1E1D2B`)在`themes.xml`中配置并在运行时应用

注意：语言切换会导致应用重启，此时会先调用`enableEdgeToEdge()`然后再调用`restoreSystemBarsColor()`，确保系统栏颜色设置不会被覆盖。

## 使用方法

### 添加新的Activity

当添加新的Activity时，应确保它能正确应用语言设置。有两种方式:

1. **推荐方式：继承BaseActivity**
   ```kotlin
   class YourActivity : BaseActivity() {
       override fun onCreate(savedInstanceState: Bundle?) {
           super.onCreate(savedInstanceState) // 这会自动应用语言设置
           // 其他代码...
       }
   }
   ```

2. **替代方式：手动应用语言设置**
   ```kotlin
   class YourActivity : ComponentActivity() {
       override fun attachBaseContext(newBase: Context) {
           val context = LanguageHelper.wrapContext(newBase)
           super.attachBaseContext(context)
       }
       
       override fun onCreate(savedInstanceState: Bundle?) {
           super.onCreate(savedInstanceState)
           ActivityHelper.applyLanguageSettings(this)
           // 其他代码...
       }
   }
   ```

### 添加新的语言

1. 在`res/`目录下创建对应的资源目录，如`values-xx/`
2. 添加`strings.xml`文件，包含所有需要翻译的字符串
3. 在`LanguageUtils.kt`的`defaultLanguages`映射中添加新语言的代码和显示名称
4. 在`LanguageManager.kt`的`ensureAllLanguagesIncluded`方法中的`essentialLanguageCodes`列表添加新的语言代码

### 切换语言

用户可以通过语言设置界面切换语言。应用会自动重启以应用新的语言设置，并保持深色主题UI。

## 注意事项

1. 确保所有字符串都使用资源引用，不要硬编码字符串
2. 如果添加新的字符串资源，确保在所有语言文件中都添加对应的翻译
3. 在代码中使用`context.getString(R.string.xxx)`或Compose中的`stringResource(R.string.xxx)`来获取当前语言的字符串

## 特殊情况处理

- 右到左语言(如阿拉伯语)：Android会自动处理RTL布局
- 日期和时间格式：使用`java.text.DateFormat`获取本地化的日期时间格式
- 数字格式：使用`java.text.NumberFormat`获取本地化的数字格式

## 系统栏颜色问题排查

如果在语言切换后发现系统栏颜色变白，请检查以下几点：

1. 确认`LanguageHelper.kt`中保存了正确的系统栏颜色设置
2. 确认`MainActivity.kt`的`restoreSystemBarsColor()`方法在`enableEdgeToEdge()`之后调用
3. 确认`themes.xml`中定义了正确的系统栏颜色
4. 检查SharedPreferences中的"ui_settings"是否包含正确的颜色值 