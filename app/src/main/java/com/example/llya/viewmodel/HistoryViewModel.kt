package com.example.llya.viewmodel

import android.app.Application
import android.media.MediaPlayer
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.data.HistoryManager
import com.example.llya.data.HistoryRecord
import com.example.llya.data.RecordType
import com.example.llya.network.GoogleCloudServiceAdapter
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.withContext

/**
 * 历史记录ViewModel
 * 负责历史记录的管理和播放功能
 */
class HistoryViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "HistoryViewModel"
    }

    // 历史记录管理器
    private val historyManager = HistoryManager.getInstance()

    // 谷歌云服务适配器，用于文本转语音
    private val cloudServiceAdapter = GoogleCloudServiceAdapter.getInstance()

    // 媒体播放器
    private var mediaPlayer: MediaPlayer? = null

    // 历史记录列表
    val historyRecords = historyManager.historyRecords

    // 当前选中的历史记录
    private val _selectedRecord = MutableStateFlow<HistoryRecord?>(null)
    val selectedRecord: StateFlow<HistoryRecord?> = _selectedRecord.asStateFlow()

    // 当前播放状态
    private val _isPlaying = MutableStateFlow(false)
    val isPlaying: StateFlow<Boolean> = _isPlaying.asStateFlow()

    // 当前正在播放的记录ID
    private val _currentPlayingId = MutableStateFlow("")
    val currentPlayingId: StateFlow<String> = _currentPlayingId.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 当前正在加载的记录ID
    private val _currentLoadingId = MutableStateFlow("")
    val currentLoadingId: StateFlow<String> = _currentLoadingId.asStateFlow()

    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 当前筛选模式
    private val _filterType = MutableStateFlow<RecordType?>(null)
    val filterType: StateFlow<RecordType?> = _filterType.asStateFlow()

    // 搜索关键词
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // 筛选后的历史记录
    private val _filteredRecords = MutableStateFlow<List<HistoryRecord>>(emptyList())
    val filteredRecords: StateFlow<List<HistoryRecord>> = _filteredRecords.asStateFlow()

    init {
        // 监听历史记录变化
        viewModelScope.launch {
            historyRecords.collectLatest { records ->
                updateFilteredRecords()
            }
        }
    }

    /**
     * 添加历史记录
     */
    fun addRecord(record: HistoryRecord) {
        historyManager.addRecord(record)
    }

    /**
     * 删除历史记录
     */
    fun deleteRecord(id: String) {
        historyManager.deleteRecord(id)
    }

    /**
     * 标记为收藏/取消收藏
     */
    fun toggleFavorite(id: String) {
        historyManager.toggleFavorite(id)
    }

    /**
     * 清空所有历史记录
     */
    fun clearAllRecords() {
        historyManager.clearAll()
    }

    /**
     * 设置筛选类型
     */
    fun setFilterType(type: RecordType?) {
        _filterType.value = type
        updateFilteredRecords()
    }

    /**
     * 设置搜索关键词
     */
    fun setSearchQuery(query: String) {
        _searchQuery.value = query
        updateFilteredRecords()
    }

    /**
     * 更新筛选后的记录
     */
    private fun updateFilteredRecords() {
        val allRecords = historyRecords.value
        val query = _searchQuery.value
        val type = _filterType.value

        _filteredRecords.value = allRecords
            .filter { record ->
                // 先按类型筛选
                (type == null || record.type == type) &&
                // 再按关键词搜索
                (query.isEmpty() ||
                 record.sourceText.contains(query, ignoreCase = true) ||
                 record.translatedText.contains(query, ignoreCase = true))
            }
    }

    /**
     * 选择历史记录
     */
    fun selectRecord(record: HistoryRecord) {
        _selectedRecord.value = record
    }

    /**
     * 播放历史记录的翻译文本
     */
    fun playTranslatedText(record: HistoryRecord? = _selectedRecord.value) {
        if (record == null || record.translatedText.isEmpty()) {
            _errorMessage.value = "没有可播放的翻译文本"
            return
        }

        if (_isPlaying.value) {
            stopPlaying()
        }

        _isLoading.value = true
        _currentLoadingId.value = record.id

        viewModelScope.launch {
            try {
                // 获取目标语言代码
                val languageCode = record.targetLanguage.ifEmpty { getDefaultLanguageCode(record.targetLanguageName) }

                // 合成语音
                val audioData = cloudServiceAdapter.textToSpeech(
                    text = record.translatedText,
                    languageCode = languageCode
                )

                if (audioData != null && audioData.isNotEmpty()) {
                    // 保存到临时文件
                    val tempFile = withContext(Dispatchers.IO) {
                        val file = File.createTempFile("history_tts_", ".mp3", getApplication<Application>().cacheDir)
                        FileOutputStream(file).use { it.write(audioData) }
                        file
                    }

                    // 播放音频
                    playAudioFile(tempFile)
                } else {
                    _errorMessage.value = "语音合成失败"
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "播放翻译文本失败", e)
                _errorMessage.value = "播放失败: ${e.message}"
                _isLoading.value = false
            }
        }
    }

    /**
     * 播放音频文件
     */
    private fun playAudioFile(file: File) {
        try {
            // 释放之前的媒体播放器
            mediaPlayer?.release()

            // 创建新的媒体播放器
            mediaPlayer = MediaPlayer().apply {
                setDataSource(file.absolutePath)
                setOnPreparedListener {
                    _isLoading.value = false
                    _currentLoadingId.value = ""
                    _isPlaying.value = true
                    _currentPlayingId.value = _selectedRecord.value?.id ?: ""
                    start()
                }
                setOnCompletionListener {
                    _isPlaying.value = false
                    _currentPlayingId.value = ""
                    release()
                    mediaPlayer = null
                    file.delete() // 删除临时文件
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "MediaPlayer错误: $what, $extra")
                    _errorMessage.value = "播放错误"
                    _isPlaying.value = false
                    _isLoading.value = false
                    true
                }
                prepareAsync()
            }
        } catch (e: Exception) {
            Log.e(TAG, "初始化媒体播放器失败", e)
            _errorMessage.value = "播放初始化失败: ${e.message}"
            _isPlaying.value = false
            _isLoading.value = false
            file.delete()
        }
    }

    /**
     * 停止播放
     */
    fun stopPlaying() {
        mediaPlayer?.let {
            if (it.isPlaying) {
                it.stop()
            }
            it.release()
            mediaPlayer = null
        }
        _isPlaying.value = false
        _currentPlayingId.value = ""
    }

    /**
     * 播放翻译
     */
    fun playTranslation(record: HistoryRecord) {
        _selectedRecord.value = record
        playTranslatedText(record)
    }

    /**
     * 获取默认语言代码
     */
    private fun getDefaultLanguageCode(languageName: String): String {
        // 根据语言名称推断语言代码
        return when {
            languageName.contains("中文") -> "cmn-CN"
            languageName.contains("英") -> "en-US"
            languageName.contains("日") -> "ja-JP"
            languageName.contains("韩") -> "ko-KR"
            languageName.contains("法") -> "fr-FR"
            languageName.contains("德") -> "de-DE"
            languageName.contains("俄") -> "ru-RU"
            languageName.contains("西班牙") -> "es-ES"
            else -> "cmn-CN" // 默认使用中文
        }
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 资源清理
     */
    override fun onCleared() {
        super.onCleared()
        stopPlaying()
    }
}