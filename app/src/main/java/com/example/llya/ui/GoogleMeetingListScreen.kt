package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.utils.TextPersistenceManager.MeetingRecord
import com.example.llya.viewmodel.MeetingRecordViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoogleMeetingListScreen(
    navController: NavController? = null,
    viewModel: MeetingRecordViewModel = viewModel()
) {
    val darkBgColor = Color(0xFF222131)
    val cardBgColor = Color(0xFF2D2C3E)
    val purpleColor = Color(0xFF8675E6)
    
    // 获取会议记录列表
    val meetingRecords by viewModel.meetingRecords.collectAsState()
    
    // 日期格式化工具
    val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    
    // 删除确认对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    var recordToDelete by remember { mutableStateOf<MeetingRecord?>(null) }
    
    // 清空所有记录的对话框状态
    var showClearAllDialog by remember { mutableStateOf(false) }
    
    // 加载会议记录列表
    LaunchedEffect(Unit) {
        viewModel.loadMeetingRecords()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.gml_title), color = Color.White) },
                navigationIcon = {
                    IconButton(onClick = { navController?.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back),
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 清空所有按钮
                    IconButton(onClick = { showClearAllDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = stringResource(R.string.meeting_delete_all),
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = darkBgColor
                )
            )
        },
        containerColor = darkBgColor
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (meetingRecords.isEmpty()) {
                EmptyListContent(navController = navController)
            } else {
                // 会议记录列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(meetingRecords) { record ->
                        // 格式化日期和时长
                        val dateStr = dateFormatter.format(Date(record.timestamp))
                        val durationStr = if (record.duration > 0) {
                            val duration = record.duration
                            val hours = duration / (3600 * 1000)
                            val minutes = (duration % (3600 * 1000)) / (60 * 1000)
                            val seconds = (duration % (60 * 1000)) / 1000
                            String.format("%02d:%02d:%02d", hours, minutes, seconds)
                        } else {
                            stringResource(R.string.gml_untitled)
                        }
                        
                        // 会议记录卡片
                        MeetingRecordCard(
                            record = record,
                            onClick = {
                                // 先加载会议记录内容，然后导航到编辑页面
                                println("点击会议记录卡片，加载记录并跳转到编辑页面")
                                println("会议记录ID: ${record.id}, 标题: ${record.title}")
                                
                                // 将所选记录内容加载到当前会议
                                viewModel.setCurrentMeetingContent(record.content)
                                
                                // 导航到会议记录编辑页面
                                if (navController != null) {
                                    navController.navigate("google_meeting_record")
                                } else {
                                    println("错误：导航控制器为空，无法导航")
                                }
                            },
                            onDelete = {
                                recordToDelete = record
                                showDeleteDialog = true
                            }
                        )
                    }
                }
            }
        }
        
        // 删除确认对话框
        if (showDeleteDialog && recordToDelete != null) {
            AlertDialog(
                onDismissRequest = { showDeleteDialog = false },
                title = { Text(stringResource(R.string.gml_delete_title)) },
                text = { Text(stringResource(R.string.gml_delete_confirm_message, recordToDelete?.title ?: "")) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showDeleteDialog = false
                            recordToDelete?.let { 
                                viewModel.deleteMeetingRecord(it.id)
                            }
                        }
                    ) {
                        Text(stringResource(R.string.gml_delete_button), color = Color.Red)
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showDeleteDialog = false }) {
                        Text(stringResource(R.string.gml_cancel_button))
                    }
                }
            )
        }
        
        // 清空所有记录确认对话框
        if (showClearAllDialog) {
            AlertDialog(
                onDismissRequest = { showClearAllDialog = false },
                title = { Text(stringResource(R.string.gml_clear_all_title)) },
                text = { Text(stringResource(R.string.gml_clear_all_message)) },
                confirmButton = {
                    TextButton(onClick = {
                        showClearAllDialog = false
                        viewModel.clearAllMeetingRecords()
                    }) {
                        Text(stringResource(R.string.gml_clear_button), color = Color.Red)
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showClearAllDialog = false }) {
                        Text(stringResource(R.string.gml_cancel_button))
                    }
                }
            )
        }
    }
}

@Composable
fun EmptyListContent(navController: NavController?) {
    // 定义颜色
    val purpleColor = Color(0xFF8675E6)
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.gml_no_records),
            style = MaterialTheme.typography.titleLarge,
            color = Color.White,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = { navController?.popBackStack() },
            colors = ButtonDefaults.buttonColors(containerColor = purpleColor)
        ) {
            Text(stringResource(R.string.gml_return_to_create))
        }
    }
}

@Composable
fun MeetingRecordCard(
    record: MeetingRecord,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    // 定义颜色
    val cardBgColor = Color(0xFF2D2C3E)
    
    // 日期格式化工具
    val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    
    val title = record.title.ifEmpty { stringResource(R.string.gml_untitled) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = cardBgColor
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 会议标题
                Text(
                    text = title,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                // 删除按钮
                IconButton(
                    onClick = { onDelete() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = stringResource(R.string.delete),
                        tint = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 日期和时长
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 格式化日期
                val dateStr = dateFormatter.format(Date(record.timestamp))
                Text(
                    text = stringResource(R.string.gml_date_prefix, dateStr),
                    color = Color.Gray,
                    fontSize = 12.sp
                )
                
                // 格式化时长
                val durationStr = if (record.duration > 0) {
                    val duration = record.duration
                    val hours = duration / (3600 * 1000)
                    val minutes = (duration % (3600 * 1000)) / (60 * 1000)
                    val seconds = (duration % (60 * 1000)) / 1000
                    String.format("%02d:%02d:%02d", hours, minutes, seconds)
                } else {
                    stringResource(R.string.gml_untitled)
                }
                
                Text(
                    text = stringResource(R.string.gml_duration_prefix, durationStr),
                    color = Color.Gray,
                    fontSize = 12.sp
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 会议内容预览
            Text(
                text = record.content,
                color = Color.LightGray,
                fontSize = 14.sp,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 字数统计
            Text(
                text = stringResource(R.string.gml_word_count, record.content.length),
                color = Color.Gray,
                fontSize = 12.sp,
                modifier = Modifier.align(Alignment.End)
            )
        }
    }
} 