package com.example.llya.utils

import android.Manifest
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

/**
 * 蓝牙权限修复工具类
 * 专门用于解决低版本Android上蓝牙连接显示但实际无法操作的问题
 */
object BluetoothPermissionFix {
    private const val TAG = "BluetoothPermissionFix"
    
    /**
     * 检查蓝牙权限并修复可能的问题
     * @return 是否所有权限都已授予
     */
    fun checkAndFixPermissions(context: Context): Boolean {
        val hasPermissions = BluetoothPermissionHelper.hasRequiredPermissions(context)
        
        if (!hasPermissions) {
            Log.d(TAG, "检测到蓝牙权限不足，设备可能显示已连接但实际无法操作")
            
            // 对于低版本Android，尝试发送广播通知应用有权限问题
            try {
                val intent = Intent("com.example.llya.BLUETOOTH_PERMISSION_MISSING")
                context.sendBroadcast(intent)
            } catch (e: Exception) {
                Log.e(TAG, "发送权限缺失广播失败: ${e.message}")
            }
            return false
        }
        
        return true
    }
    
    /**
     * 检查蓝牙设备连接状态
     * 包含所有必要的权限检查和错误处理
     */
    fun checkBluetoothConnection(context: Context): Boolean {
        try {
            // 先检查权限
            if (!checkAndFixPermissions(context)) {
                Log.d(TAG, "没有足够权限检查蓝牙连接状态")
                return false
            }
            
            // 获取蓝牙适配器
            val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as? BluetoothManager
            val bluetoothAdapter = bluetoothManager?.adapter ?: return false
            
            // 检查蓝牙是否开启
            if (!bluetoothAdapter.isEnabled) {
                Log.d(TAG, "蓝牙未开启")
                return false
            }
            
            // 检查连接状态 - 使用try-catch包装以防低版本系统崩溃
            try {
                val a2dpProfile = bluetoothAdapter.getProfileConnectionState(android.bluetooth.BluetoothProfile.A2DP)
                val headsetProfile = bluetoothAdapter.getProfileConnectionState(android.bluetooth.BluetoothProfile.HEADSET)
                
                val isConnected = (a2dpProfile == android.bluetooth.BluetoothProfile.STATE_CONNECTED || 
                               headsetProfile == android.bluetooth.BluetoothProfile.STATE_CONNECTED)
                
                Log.d(TAG, "蓝牙连接状态: A2DP=$a2dpProfile, HEADSET=$headsetProfile, 连接=$isConnected")
                return isConnected
            } catch (e: Exception) {
                Log.e(TAG, "检查蓝牙配置文件状态出错: ${e.message}")
                
                // 尝试备用方法 - 检查已配对设备
                try {
                    val bondedDevices = bluetoothAdapter.bondedDevices
                    for (device in bondedDevices) {
                        // 尝试检查设备连接状态
                        try {
                            val isConnectedMethod = device.javaClass.getMethod("isConnected")
                            val isConnected = isConnectedMethod.invoke(device) as Boolean
                            
                            if (isConnected) {
                                Log.d(TAG, "找到连接的设备: ${device.name}")
                                return true
                            }
                        } catch (e2: Exception) {
                            // 方法不可用，忽略
                        }
                    }
                } catch (e2: Exception) {
                    Log.e(TAG, "检查已配对设备失败: ${e2.message}")
                }
            }
            
            return false
        } catch (e: Exception) {
            Log.e(TAG, "检查蓝牙连接状态时出错: ${e.message}")
            return false
        }
    }
    
    /**
     * 显示蓝牙权限设置引导
     */
    fun showBluetoothPermissionSetting(context: Context) {
        try {
            // 打开应用设置页面
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri = android.net.Uri.fromParts("package", context.packageName, null)
            intent.data = uri
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开应用权限设置页面失败: ${e.message}")
            
            // 备用方式：打开蓝牙设置页面
            try {
                val bluetoothIntent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                bluetoothIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(bluetoothIntent)
            } catch (e2: Exception) {
                Log.e(TAG, "打开蓝牙设置页面也失败: ${e2.message}")
            }
        }
    }
    
    /**
     * 在检测到设备连接状态异常时自动修复
     * 适用于"蓝牙设备已断开连接，请重新连接"提示的情况
     */
    fun autoFixConnection(context: Context, scope: CoroutineScope) {
        scope.launch(Dispatchers.Main) {
            // 先检查权限
            if (!BluetoothPermissionHelper.hasRequiredPermissions(context)) {
                // 权限不足，延迟500ms后再重试
                delay(500)
                
                // 发送权限缺失广播
                try {
                    val intent = Intent("com.example.llya.BLUETOOTH_PERMISSION_MISSING")
                    context.sendBroadcast(intent)
                } catch (e: Exception) {
                    Log.e(TAG, "发送权限缺失广播失败: ${e.message}")
                }
                
                // 再次检查连接状态
                delay(1000)
                checkBluetoothConnection(context)
            }
        }
    }
} 