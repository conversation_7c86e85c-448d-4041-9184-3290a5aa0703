package com.example.llya.utils

import android.content.ContentResolver
import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaMetadataRetriever
import android.media.MediaMuxer
import android.net.Uri
import android.util.Log
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.channels.FileChannel

/**
 * 音频文件处理工具类
 */
class AudioFileProcessor(private val context: Context) {
    private val TAG = "AudioFileProcessor"

    /**
     * 将Uri转换为文件
     * @param uri 音频文件Uri
     * @return 临时文件
     */
    fun uriToFile(uri: Uri): File {
        val contentResolver = context.contentResolver
        val inputStream = contentResolver.openInputStream(uri)
            ?: throw IOException("无法打开输入流")

        // 获取MIME类型
        val mimeType = contentResolver.getType(uri) ?: "audio/wav"

        // 根据MIME类型确定文件扩展名
        val extension = when {
            mimeType.contains("wav") -> ".wav"
            mimeType.contains("mp3") -> ".mp3"
            mimeType.contains("aac") -> ".aac"
            mimeType.contains("m4a") -> ".m4a"
            mimeType.contains("ogg") -> ".ogg"
            mimeType.contains("flac") -> ".flac"
            else -> ".audio"
        }

        // 创建临时文件
        val tempFile = File.createTempFile("audio_", extension, context.cacheDir)

        // 将输入流写入临时文件
        inputStream.use { input ->
            FileOutputStream(tempFile).use { output ->
                input.copyTo(output)
            }
        }

        return tempFile
    }

    /**
     * 将任意音频文件转换为Microsoft Speech SDK支持的WAV格式
     * @param inputFile 输入音频文件
     * @return 转换后的WAV文件
     */
    fun convertToSupportedWav(inputFile: File): File {
        try {
            // 检查文件是否已经是有效的WAV文件
            if (isValidWavFile(inputFile)) {
                Log.d(TAG, "文件已经是有效的WAV格式，无需转换")
                return inputFile
            }

            Log.d(TAG, "开始转换音频文件为WAV格式: ${inputFile.absolutePath}")

            // 创建输出WAV文件
            val outputFile = File.createTempFile("converted_", ".wav", context.cacheDir)

            try {
                // 尝试使用MediaExtractor进行转换
                return convertUsingMediaExtractor(inputFile, outputFile)
            } catch (e: Exception) {
                Log.e(TAG, "使用MediaExtractor转换失败，尝试使用简单复制方法", e)

                // 如果MediaExtractor失败，尝试使用简单的文件复制并添加WAV头
                return convertUsingSimpleCopy(inputFile, outputFile)
            }
        } catch (e: Exception) {
            Log.e(TAG, "转换音频文件失败", e)
            // 如果转换失败，返回原始文件
            return inputFile
        }
    }

    /**
     * 使用MediaExtractor转换音频文件
     */
    private fun convertUsingMediaExtractor(inputFile: File, outputFile: File): File {
        // 使用MediaExtractor提取音频数据
        val extractor = MediaExtractor()
        extractor.setDataSource(inputFile.absolutePath)

        // 查找音频轨道
        var audioTrackIndex = -1
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            val mime = format.getString(MediaFormat.KEY_MIME)
            if (mime?.startsWith("audio/") == true) {
                audioTrackIndex = i
                break
            }
        }

        if (audioTrackIndex == -1) {
            throw IOException("找不到音频轨道")
        }

        // 选择音频轨道
        extractor.selectTrack(audioTrackIndex)
        val format = extractor.getTrackFormat(audioTrackIndex)

        // 获取音频参数
        val sampleRate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE)
        val channelCount = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT)
        val duration = if (format.containsKey(MediaFormat.KEY_DURATION)) format.getLong(MediaFormat.KEY_DURATION) else 0

        Log.d(TAG, "音频参数: 采样率=$sampleRate, 通道数=$channelCount, 时长=$duration")

        // 创建WAV文件头
        val dataOutputStream = FileOutputStream(outputFile)

        // 写入WAV文件头
        writeWavHeader(dataOutputStream, channelCount, sampleRate, 16)

        // 创建解码器
        val mime = format.getString(MediaFormat.KEY_MIME)
        val decoder = MediaCodec.createDecoderByType(mime!!)
        decoder.configure(format, null, null, 0)
        decoder.start()

        // 解码缓冲区
        val inputBuffers = decoder.inputBuffers
        val outputBuffers = decoder.outputBuffers
        val bufferInfo = MediaCodec.BufferInfo()
        var sawInputEOS = false
        var sawOutputEOS = false

        // 解码循环
        val TIMEOUT_US = 10000L
        val byteArray = ByteArray(1024 * 1024) // 1MB buffer

        while (!sawOutputEOS) {
            // 填充输入缓冲区
            if (!sawInputEOS) {
                val inputBufferIndex = decoder.dequeueInputBuffer(TIMEOUT_US)
                if (inputBufferIndex >= 0) {
                    val inputBuffer = inputBuffers[inputBufferIndex]
                    inputBuffer.clear()

                    val sampleSize = extractor.readSampleData(inputBuffer, 0)
                    if (sampleSize < 0) {
                        decoder.queueInputBuffer(inputBufferIndex, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
                        sawInputEOS = true
                    } else {
                        decoder.queueInputBuffer(inputBufferIndex, 0, sampleSize, extractor.sampleTime, 0)
                        extractor.advance()
                    }
                }
            }

            // 获取输出缓冲区
            val outputBufferIndex = decoder.dequeueOutputBuffer(bufferInfo, TIMEOUT_US)
            if (outputBufferIndex >= 0) {
                if ((bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                    sawOutputEOS = true
                }

                if (bufferInfo.size > 0) {
                    val outputBuffer = outputBuffers[outputBufferIndex]
                    outputBuffer.position(bufferInfo.offset)
                    outputBuffer.limit(bufferInfo.offset + bufferInfo.size)

                    // 将PCM数据写入WAV文件
                    val size = bufferInfo.size
                    if (size > byteArray.size) {
                        Log.w(TAG, "缓冲区大小不足，可能会丢失数据")
                    }
                    outputBuffer.get(byteArray, 0, Math.min(size, byteArray.size))
                    dataOutputStream.write(byteArray, 0, Math.min(size, byteArray.size))
                }

                decoder.releaseOutputBuffer(outputBufferIndex, false)
            }
        }

        // 关闭资源
        decoder.stop()
        decoder.release()
        extractor.release()

        // 更新WAV文件头中的数据大小
        updateWavHeader(outputFile)

        dataOutputStream.close()

        Log.d(TAG, "音频文件转换完成: ${outputFile.absolutePath}, 大小: ${outputFile.length()} 字节")

        return outputFile
    }

    /**
     * 使用简单复制方法转换音频文件
     * 这是一个备用方法，当MediaExtractor失败时使用
     */
    private fun convertUsingSimpleCopy(inputFile: File, outputFile: File): File {
        Log.d(TAG, "使用简单复制方法转换音频文件")

        // 默认音频参数
        val sampleRate = 16000
        val channelCount = 1

        // 创建WAV文件头
        val dataOutputStream = FileOutputStream(outputFile)

        // 写入WAV文件头
        writeWavHeader(dataOutputStream, channelCount, sampleRate, 16)

        // 复制原始文件数据
        val inputStream = FileInputStream(inputFile)
        val buffer = ByteArray(8192)
        var bytesRead: Int

        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
            dataOutputStream.write(buffer, 0, bytesRead)
        }

        inputStream.close()

        // 更新WAV文件头中的数据大小
        updateWavHeader(outputFile)

        dataOutputStream.close()

        Log.d(TAG, "使用简单复制方法转换完成: ${outputFile.absolutePath}, 大小: ${outputFile.length()} 字节")

        return outputFile
    }

    /**
     * 检查文件是否是有效的WAV文件
     * @param file 要检查的文件
     * @return 是否是有效的WAV文件
     */
    private fun isValidWavFile(file: File): Boolean {
        try {
            if (!file.exists() || file.length() < 44) { // WAV头至少44字节
                return false
            }

            val inputStream = FileInputStream(file)
            val buffer = ByteArray(12)
            inputStream.read(buffer, 0, 12)
            inputStream.close()

            // 检查WAV文件头
            val riffHeader = String(buffer, 0, 4)
            val waveHeader = String(buffer, 8, 4)

            return riffHeader == "RIFF" && waveHeader == "WAVE"
        } catch (e: Exception) {
            Log.e(TAG, "检查WAV文件失败", e)
            return false
        }
    }

    /**
     * 写入WAV文件头
     * @param outputStream 输出流
     * @param channels 通道数
     * @param sampleRate 采样率
     * @param bitsPerSample 每个样本的位数
     */
    private fun writeWavHeader(
        outputStream: FileOutputStream,
        channels: Int,
        sampleRate: Int,
        bitsPerSample: Int
    ) {
        try {
            val byteRate = sampleRate * channels * bitsPerSample / 8
            val blockAlign = channels * bitsPerSample / 8

            // RIFF头
            outputStream.write("RIFF".toByteArray())
            // 文件大小（暂时写0，后面更新）
            writeInt(outputStream, 0)
            // WAVE标识
            outputStream.write("WAVE".toByteArray())
            // fmt子块
            outputStream.write("fmt ".toByteArray())
            // fmt子块大小
            writeInt(outputStream, 16)
            // 音频格式（1表示PCM）
            writeShort(outputStream, 1)
            // 通道数
            writeShort(outputStream, channels)
            // 采样率
            writeInt(outputStream, sampleRate)
            // 字节率
            writeInt(outputStream, byteRate)
            // 块对齐
            writeShort(outputStream, blockAlign)
            // 每个样本的位数
            writeShort(outputStream, bitsPerSample)
            // data子块
            outputStream.write("data".toByteArray())
            // data子块大小（暂时写0，后面更新）
            writeInt(outputStream, 0)
        } catch (e: Exception) {
            Log.e(TAG, "写入WAV文件头失败", e)
        }
    }

    /**
     * 更新WAV文件头中的数据大小
     * @param wavFile WAV文件
     */
    private fun updateWavHeader(wavFile: File) {
        try {
            val fileSize = wavFile.length()
            if (fileSize <= 44) { // WAV头至少44字节
                return
            }

            val randomAccessFile = java.io.RandomAccessFile(wavFile, "rw")
            // 更新RIFF块大小
            randomAccessFile.seek(4)
            val riffSize = fileSize - 8
            randomAccessFile.write(intToByteArray(riffSize.toInt()))

            // 更新data块大小
            randomAccessFile.seek(40)
            val dataSize = fileSize - 44
            randomAccessFile.write(intToByteArray(dataSize.toInt()))

            randomAccessFile.close()
        } catch (e: Exception) {
            Log.e(TAG, "更新WAV文件头失败", e)
        }
    }

    /**
     * 写入16位整数（小端序）
     */
    private fun writeShort(outputStream: FileOutputStream, value: Int) {
        outputStream.write(value and 0xFF)
        outputStream.write((value shr 8) and 0xFF)
    }

    /**
     * 写入32位整数（小端序）
     */
    private fun writeInt(outputStream: FileOutputStream, value: Int) {
        outputStream.write(value and 0xFF)
        outputStream.write((value shr 8) and 0xFF)
        outputStream.write((value shr 16) and 0xFF)
        outputStream.write((value shr 24) and 0xFF)
    }

    /**
     * 将Int转换为字节数组（小端序）
     */
    private fun intToByteArray(value: Int): ByteArray {
        return byteArrayOf(
            (value and 0xFF).toByte(),
            ((value shr 8) and 0xFF).toByte(),
            ((value shr 16) and 0xFF).toByte(),
            ((value shr 24) and 0xFF).toByte()
        )
    }

    /**
     * 获取音频文件时长（毫秒）
     * @param uri 音频文件Uri
     * @return 时长（毫秒）
     */
    fun getAudioDuration(uri: Uri): Long {
        val retriever = MediaMetadataRetriever()
        try {
            retriever.setDataSource(context, uri)
            val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            return durationStr?.toLong() ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取音频时长失败", e)
            return 0
        } finally {
            retriever.release()
        }
    }

    /**
     * 获取音频文件采样率
     * @param uri 音频文件Uri
     * @return 采样率
     */
    fun getAudioSampleRate(uri: Uri): Int {
        val retriever = MediaMetadataRetriever()
        try {
            retriever.setDataSource(context, uri)
            val sampleRateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_SAMPLERATE)
            return sampleRateStr?.toInt() ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取音频采样率失败", e)
            return 0
        } finally {
            retriever.release()
        }
    }

    /**
     * 获取音频文件比特率
     * @param uri 音频文件Uri
     * @return 比特率
     */
    fun getAudioBitRate(uri: Uri): Int {
        val retriever = MediaMetadataRetriever()
        try {
            retriever.setDataSource(context, uri)
            val bitRateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)
            return bitRateStr?.toInt() ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取音频比特率失败", e)
            return 0
        } finally {
            retriever.release()
        }
    }

    /**
     * 获取音频文件MIME类型
     * @param uri 音频文件Uri
     * @return MIME类型
     */
    fun getAudioMimeType(uri: Uri): String {
        val contentResolver = context.contentResolver
        return contentResolver.getType(uri) ?: "unknown"
    }

    /**
     * 获取音频文件名
     * @param uri 音频文件Uri
     * @return 文件名
     */
    fun getAudioFileName(uri: Uri): String {
        val contentResolver = context.contentResolver
        val cursor = contentResolver.query(uri, null, null, null, null)

        return cursor?.use {
            if (it.moveToFirst()) {
                val displayNameIndex = it.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    it.getString(displayNameIndex)
                } else {
                    uri.lastPathSegment ?: "unknown"
                }
            } else {
                uri.lastPathSegment ?: "unknown"
            }
        } ?: (uri.lastPathSegment ?: "unknown")
    }

    /**
     * 获取音频文件大小
     * @param uri 音频文件Uri
     * @return 文件大小（字节）
     */
    fun getAudioFileSize(uri: Uri): Long {
        val contentResolver = context.contentResolver
        val cursor = contentResolver.query(uri, null, null, null, null)

        return cursor?.use {
            if (it.moveToFirst()) {
                val sizeIndex = it.getColumnIndex(android.provider.OpenableColumns.SIZE)
                if (sizeIndex != -1) {
                    it.getLong(sizeIndex)
                } else {
                    0
                }
            } else {
                0
            }
        } ?: 0
    }

    /**
     * 格式化时间（毫秒转为时:分:秒格式）
     * @param milliseconds 毫秒
     * @return 格式化的时间字符串
     */
    fun formatTime(milliseconds: Long): String {
        val seconds = milliseconds / 1000
        val minutes = seconds / 60
        val hours = minutes / 60

        return String.format(
            "%02d:%02d:%02d",
            hours,
            minutes % 60,
            seconds % 60
        )
    }
}
