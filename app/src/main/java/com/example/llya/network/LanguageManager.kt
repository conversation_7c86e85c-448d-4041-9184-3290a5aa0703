package com.example.llya.network

import android.content.Context
import android.util.Log
import com.example.llya.data.LanguageModel
import com.example.llya.utils.LanguageUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * 语言管理器
 * 用于从API获取语言列表并与LanguageUtils集成
 */
class LanguageManager private constructor(private val context: Context) {
    private val TAG = "LanguageManager"
    private val apiService = RetrofitClient.createApiService()
    
    // 语言列表状态
    private val _languageListState = MutableStateFlow<LanguageListState>(LanguageListState.Loading)
    val languageListState: StateFlow<LanguageListState> = _languageListState.asStateFlow()
    
    // 缓存的语言列表
    private var cachedLanguages: List<LanguageModel> = emptyList()
    
    companion object {
        @Volatile
        private var instance: LanguageManager? = null
        
        fun getInstance(context: Context): LanguageManager {
            return instance ?: synchronized(this) {
                instance ?: LanguageManager(context.applicationContext).also { instance = it }
            }
        }
    }
    
    /**
     * 从API获取语言列表
     * 成功后会更新LanguageUtils中的语言映射
     */
    suspend fun fetchLanguages(forceRefresh: Boolean = true): Boolean = withContext(Dispatchers.IO) {
        // 始终强制刷新，直接从API获取
        
        try {
            _languageListState.value = LanguageListState.Loading
            
            val response = apiService.getLanguageList()
            
            // 添加详细日志记录
            Log.d(TAG, "API响应: status=${response.status}, msg=${response.msg}, data类型=${response.data?.javaClass}")
            Log.d(TAG, "API响应data内容: ${response.data}")
            
            // 修改成功条件 - 接受状态码200或1，以兼容不同API格式
            if ((response.status == 200 || response.status == 1) && response.data != null) {
                try {
                    // 尝试从响应中提取语言列表
                    val languages = parseLanguageListFromResponse(response.data)
                    if (languages.isNotEmpty()) {
                        Log.d(TAG, "成功解析API返回的语言列表: ${languages.size}个语言")
                        cachedLanguages = languages.filter { it.status == 1 } // 只保留状态为启用的语言
                        
                        // 更新LanguageUtils中的语言映射
                        updateLanguageUtils(cachedLanguages)
                        
                        // 打印后端语言列表与默认列表差异
                        printLanguageDifference(cachedLanguages)
                        
                        _languageListState.value = LanguageListState.Success(cachedLanguages)
                        Log.d(TAG, "成功从API获取语言列表: ${cachedLanguages.size}个语言")
                        return@withContext true
                    } else {
                        Log.w(TAG, "API返回的语言列表为空或解析失败，使用默认列表")
                        useDefaultLanguageList()
                        _languageListState.value = LanguageListState.Success(cachedLanguages)
                        return@withContext true
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析数据失败: ${e.message}", e)
                    e.printStackTrace()
                    // 解析失败时使用默认语言列表
                    useDefaultLanguageList()
                    _languageListState.value = LanguageListState.Success(cachedLanguages)
                    return@withContext true
                }
            } else {
                Log.e(TAG, "API返回错误: ${response.msg}, 状态码: ${response.status}, 数据类型: ${response.data?.javaClass}")
                
                // API返回错误时使用默认语言列表
                useDefaultLanguageList()
                _languageListState.value = LanguageListState.Success(cachedLanguages)
                return@withContext true
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取语言列表失败: ${e.message}", e)
            e.printStackTrace() // 打印完整的堆栈信息
            
            // 异常情况下使用默认语言列表
            useDefaultLanguageList()
            _languageListState.value = LanguageListState.Success(cachedLanguages)
            return@withContext true
        }
    }
    
    /**
     * 解析API响应中的语言列表
     * 支持多种响应格式
     */
    @Suppress("UNCHECKED_CAST")
    private fun parseLanguageListFromResponse(data: Any): List<LanguageModel> {
        Log.d(TAG, "开始解析语言列表响应: $data")
        
        try {
            // 尝试按照分页结构解析
            if (data is Map<*, *> && data.containsKey("data")) {
                val innerData = data["data"]
                if (innerData is List<*>) {
                    // 处理标准格式：{data: [{language_id: 1, name: "语言名", sub_name: "语言代码"}, ...]}
                    val result = innerData.mapNotNull { item ->
                        if (item is Map<*, *>) {
                            val id = when (val idValue = item["language_id"]) {
                                is Number -> idValue.toInt()
                                is String -> idValue.toIntOrNull() ?: 0
                                else -> 0
                            }
                            
                            // 支持多种字段名称格式
                            val name = item["name"] as? String ?: item["language_name"] as? String ?: ""
                            val code = item["sub_name"] as? String ?: item["language_code"] as? String ?: ""
                            
                            if (name.isNotEmpty() && code.isNotEmpty()) {
                                LanguageModel(id, code, name)
                            } else null
                        } else null
                    }
                    
                    // 确保至少包含所有默认语言
                    return ensureAllLanguagesIncluded(result)
                }
            } else if (data is List<*>) {
                // 处理直接返回语言列表的情况
                val result = data.mapNotNull { item ->
                    if (item is Map<*, *>) {
                        val id = when (val idValue = item["id"] ?: item["language_id"]) {
                            is Number -> idValue.toInt()
                            is String -> idValue.toIntOrNull() ?: 0
                            else -> 0
                        }
                        
                        // 支持多种字段名称格式
                        val name = item["name"] as? String ?: item["language_name"] as? String ?: ""
                        val code = item["code"] as? String ?: item["sub_name"] as? String ?: 
                                   item["language_code"] as? String ?: ""
                        
                        if (name.isNotEmpty() && code.isNotEmpty()) {
                            LanguageModel(id, code, name)
                        } else null
                    } else null
                }
                
                // 确保至少包含所有默认语言
                return ensureAllLanguagesIncluded(result)
            }
            
            Log.w(TAG, "无法识别的响应格式，返回默认语言列表")
            return getDefaultLanguageList()
            
        } catch (e: Exception) {
            Log.e(TAG, "解析语言列表时出错", e)
            return getDefaultLanguageList()
        }
    }
    
    /**
     * 确保结果中包含所有需要支持的语言
     * 如果API返回的语言不完整，使用默认语言列表补充
     */
    private fun ensureAllLanguagesIncluded(apiLanguages: List<LanguageModel>): List<LanguageModel> {
        val result = apiLanguages.toMutableList()
        val existingCodes = result.map { it.languageCode }
        var nextId = (result.maxByOrNull { it.id }?.id ?: 0) + 1
        
        // 确保至少包含这些关键语言代码
        val essentialLanguageCodes = listOf("zh-CN", "zh-TW", "en", "fr", "ru", "es", "ar", "ja", "ko")
        
        for ((code, name) in LanguageUtils.supportedLanguages) {
            // 如果关键语言不存在，则添加它
            if (code in essentialLanguageCodes && !existingCodes.contains(code)) {
                result.add(LanguageModel(nextId++, code, name))
            }
        }
        
        return result
    }
    
    /**
     * 获取默认语言列表
     */
    private fun getDefaultLanguageList(): List<LanguageModel> {
        var id = 1
        return LanguageUtils.supportedLanguages.map { (code, name) ->
            LanguageModel(id++, code, name)
        }
    }
    
    /**
     * 使用默认语言列表
     */
    private fun useDefaultLanguageList() {
        cachedLanguages = LanguageUtils.supportedLanguages.map { (code, name) ->
            LanguageModel(
                id = 0,
                languageCode = code,
                languageName = name,
                nativeName = name,
                status = 1
            )
        }
        
        // 更新LanguageUtils中的语言映射
        updateLanguageUtils(cachedLanguages)
    }
    
    /**
     * 根据API返回的语言列表更新LanguageUtils中的语言映射
     */
    private fun updateLanguageUtils(languages: List<LanguageModel>) {
        val languageMap = languages.associate { 
            standardizeLanguageCode(it.languageCode) to it.languageName 
        }
        
        // 将API获取的语言映射添加到LanguageUtils
        LanguageUtils.updateSupportedLanguages(languageMap)
    }
    
    /**
     * 标准化语言代码
     * 确保API返回的语言代码与LanguageUtils中使用的格式一致
     */
    private fun standardizeLanguageCode(languageCode: String): String {
        return LanguageUtils.standardizeLanguageCode(languageCode)
    }
    
    /**
     * 获取语言列表
     * 如果本地有缓存则返回缓存，否则返回空列表
     */
    fun getLanguages(): List<LanguageModel> {
        return cachedLanguages
    }
    
    /**
     * 根据语言代码获取语言模型
     */
    fun getLanguageByCode(languageCode: String): LanguageModel? {
        val standardCode = standardizeLanguageCode(languageCode)
        return cachedLanguages.find { standardizeLanguageCode(it.languageCode) == standardCode }
    }
    
    /**
     * 根据语言名称获取语言模型
     */
    fun getLanguageByName(languageName: String): LanguageModel? {
        return cachedLanguages.find { it.languageName == languageName }
    }
    
    /**
     * 打印后端返回的语言列表与默认语言列表的差异
     */
    private fun printLanguageDifference(apiLanguages: List<LanguageModel>) {
        val defaultLanguages = LanguageUtils.supportedLanguages
        
        Log.d(TAG, "======== 语言列表比较开始 ========")
        Log.d(TAG, "API返回的语言数量: ${apiLanguages.size}, 默认语言数量: ${defaultLanguages.size}")
        
        // 后端有但默认列表没有的语言
        val onlyInApi = apiLanguages.filter { 
            val standardCode = standardizeLanguageCode(it.languageCode)
            !defaultLanguages.containsKey(standardCode)
        }
        if (onlyInApi.isNotEmpty()) {
            Log.d(TAG, "仅在API中存在的语言(${onlyInApi.size}):")
            onlyInApi.forEach { 
                Log.d(TAG, "  ${it.languageName} (${it.languageCode})")
            }
        }
        
        // 默认列表有但后端没有的语言
        val onlyInDefault = defaultLanguages.filter { (code, _) ->
            !apiLanguages.any { 
                standardizeLanguageCode(it.languageCode) == code
            }
        }
        if (onlyInDefault.isNotEmpty()) {
            Log.d(TAG, "仅在默认列表中存在的语言(${onlyInDefault.size}):")
            onlyInDefault.forEach { (code, name) ->
                Log.d(TAG, "  $name ($code)")
            }
        }
        
        // 名称不一致的语言
        val differentNames = apiLanguages.filter {
            val standardCode = standardizeLanguageCode(it.languageCode)
            defaultLanguages.containsKey(standardCode) && 
            defaultLanguages[standardCode] != it.languageName
        }
        if (differentNames.isNotEmpty()) {
            Log.d(TAG, "语言代码相同但名称不同的语言(${differentNames.size}):")
            differentNames.forEach {
                val standardCode = standardizeLanguageCode(it.languageCode)
                Log.d(TAG, "  代码: $standardCode, API名称: ${it.languageName}, 默认名称: ${defaultLanguages[standardCode]}")
            }
        }
        
        Log.d(TAG, "======== 语言列表比较结束 ========")
    }
}

/**
 * 语言列表状态
 */
sealed class LanguageListState {
    /**
     * 加载中
     */
    object Loading : LanguageListState()
    
    /**
     * 加载成功
     */
    data class Success(val languages: List<LanguageModel>) : LanguageListState()
    
    /**
     * 加载失败
     */
    data class Error(val message: String) : LanguageListState()
} 