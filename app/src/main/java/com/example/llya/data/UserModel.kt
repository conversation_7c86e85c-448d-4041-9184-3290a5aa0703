package com.example.llya.data

import com.google.gson.annotations.SerializedName

/**
 * 用户信息数据模型类
 */
data class UserModel(
    @SerializedName("user_id") val userId: Int = 0,
    @SerializedName("username") val username: String = "",
    @SerializedName("email") val email: String = "",
    @SerializedName("userType") val userType: Int = 0,
    @SerializedName("createtime") val createtime: Long = 0,
    @SerializedName("updatetime") val updatetime: Long = 0,
    @SerializedName("status") val status: Int = 1,
    @SerializedName("vipExpireTime") val vipExpireTime: Long = 0,
    @SerializedName("devices") val devices: List<DeviceModel> = emptyList()
)

/**
 * 用户绑定设备信息模型类
 */
data class DeviceModel(
    @SerializedName("devicename") val devicename: String = "",
    @SerializedName("macaddress") val macaddress: String = "",
    @SerializedName("location") val location: String = "",
    @SerializedName("bindtime") val bindtime: Long = 0,
    @SerializedName("bindtime_text") val bindtimeText: String = ""
)

/**
 * API响应基类
 */
data class ApiResponse<T>(
    @SerializedName("status") val status: Int = 0,
    @SerializedName("msg") val msg: String = "",
    @SerializedName("data") val data: T? = null,
    @SerializedName("token") val token: String? = null,
    @SerializedName("key") val key: String? = null
) 