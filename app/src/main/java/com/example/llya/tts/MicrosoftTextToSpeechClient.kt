package com.example.llya.tts

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit

/**
 * 微软文本转语音客户端
 * 使用REST API实现文本转语音
 */
class MicrosoftTextToSpeechClient(
    private val subscriptionKey: String,
    private val region: String = "eastasia",
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "MicrosoftTTS"
        private const val API_URL = "https://%s.tts.speech.microsoft.com/cognitiveservices/v1"
        
        // 语音映射表
        val VOICE_MAP = mapOf(
            "zh-<PERSON><PERSON>" to listOf(
                Voice("zh-<PERSON><PERSON>-<PERSON>xiaoNeural", "女", "<PERSON><PERSON><PERSON> (女)"),
                Voice("zh-C<PERSON>-YunxiNeural", "男", "<PERSON><PERSON> (男)"),
                Voice("zh-<PERSON><PERSON>-<PERSON><PERSON>an<PERSON>eural", "男", "<PERSON>jian (男)"),
                Voice("zh-<PERSON><PERSON>-<PERSON>chen<PERSON>eural", "女", "<PERSON>chen (女)")
            ),
            "en-US" to listOf(
                Voice("en-US-Aria<PERSON>eural", "女", "Aria (女)"),
                Voice("en-US-Guy<PERSON>eural", "男", "Guy (男)"),
                Voice("en-US-JennyNeural", "女", "Jenny (女)"),
                Voice("en-US-SaraNeural", "女", "Sara (女)")
            ),
            "ja-JP" to listOf(
                Voice("ja-JP-NanamiNeural", "女", "Nanami (女)"),
                Voice("ja-JP-KeitaNeural", "男", "Keita (男)")
            ),
            "ko-KR" to listOf(
                Voice("ko-KR-SunHiNeural", "女", "SunHi (女)"),
                Voice("ko-KR-InJoonNeural", "男", "InJoon (男)")
            ),
            "fr-FR" to listOf(
                Voice("fr-FR-DeniseNeural", "女", "Denise (女)"),
                Voice("fr-FR-HenriNeural", "男", "Henri (男)")
            ),
            "de-DE" to listOf(
                Voice("de-DE-KatjaNeural", "女", "Katja (女)"),
                Voice("de-DE-ConradNeural", "男", "Conrad (男)")
            ),
            "es-ES" to listOf(
                Voice("es-ES-ElviraNeural", "女", "Elvira (女)"),
                Voice("es-ES-AlvaroNeural", "男", "Alvaro (男)")
            ),
            "it-IT" to listOf(
                Voice("it-IT-ElsaNeural", "女", "Elsa (女)"),
                Voice("it-IT-DiegoNeural", "男", "Diego (男)")
            ),
            "ru-RU" to listOf(
                Voice("ru-RU-SvetlanaNeural", "女", "Svetlana (女)"),
                Voice("ru-RU-DmitryNeural", "男", "Dmitry (男)")
            )
        )
    }

    // 状态流
    private val _errorMessage = MutableStateFlow<String>("")
    val errorMessage: StateFlow<String> = _errorMessage

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()

    /**
     * 文本转语音
     * @param text 要转换的文本
     * @param languageCode 语言代码，如"zh-CN"、"en-US"等
     * @param voiceName 语音名称，如"zh-CN-XiaoxiaoNeural"
     * @return 音频数据字节数组
     */
    suspend fun textToSpeech(
        text: String,
        languageCode: String,
        voiceName: String
    ): ByteArray? = withContext(Dispatchers.IO) {
        try {
            if (text.isEmpty()) {
                Log.w(TAG, "文本为空，无法转换为语音")
                return@withContext null
            }

            // 获取适合的语音名称
            val adjustedVoiceName = getMatchingVoiceName(languageCode, voiceName)
            
            // 构建请求URL
            val url = String.format(API_URL, region)
            
            // 构建SSML
            val ssml = """
                <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="${languageCode}">
                    <voice name="${adjustedVoiceName}">
                        ${text}
                    </voice>
                </speak>
            """.trimIndent()
            
            // 构建请求体
            val requestBody = ssml.toRequestBody("application/ssml+xml".toMediaTypeOrNull())
            
            // 构建请求
            val request = Request.Builder()
                .url(url)
                .addHeader("Ocp-Apim-Subscription-Key", subscriptionKey)
                .addHeader("X-Microsoft-OutputFormat", "audio-16khz-128kbitrate-mono-mp3")
                .addHeader("Content-Type", "application/ssml+xml")
                .post(requestBody)
                .build()
            
            Log.d(TAG, "发送文本转语音请求: $url")
            Log.d(TAG, "SSML: $ssml")
            
            // 发送请求
            val response = client.newCall(request).execute()
            
            if (response.isSuccessful) {
                val audioData = response.body?.bytes()
                
                if (audioData != null && audioData.isNotEmpty()) {
                    Log.d(TAG, "文本转语音成功，音频数据大小: ${audioData.size} 字节")
                    return@withContext audioData
                } else {
                    Log.e(TAG, "文本转语音响应为空")
                    _errorMessage.value = "文本转语音响应为空"
                    return@withContext null
                }
            } else {
                val errorBody = response.body?.string() ?: "未知错误"
                Log.e(TAG, "文本转语音请求失败: ${response.code}, $errorBody")
                _errorMessage.value = "文本转语音请求失败: ${response.code}, $errorBody"
                return@withContext null
            }
        } catch (e: Exception) {
            Log.e(TAG, "文本转语音异常: ${e.message}", e)
            _errorMessage.value = "文本转语音异常: ${e.message}"
            return@withContext null
        }
    }

    /**
     * 获取匹配的语音名称
     */
    private fun getMatchingVoiceName(languageCode: String, voiceName: String): String {
        // 如果提供的语音名称不为空，且包含语言代码前缀，则直接使用
        if (voiceName.isNotEmpty() && voiceName.startsWith(languageCode.split("-")[0])) {
            return voiceName
        }
        
        // 否则，根据语言代码选择默认语音
        val normalizedLanguageCode = normalizeLanguageCode(languageCode)
        val voices = VOICE_MAP[normalizedLanguageCode] ?: VOICE_MAP["en-US"]
        
        // 默认选择第一个语音
        return voices?.firstOrNull()?.name ?: "en-US-AriaNeural"
    }

    /**
     * 标准化语言代码
     */
    private fun normalizeLanguageCode(languageCode: String): String {
        // 标准化语言代码为微软支持的格式
        return when {
            languageCode.startsWith("zh") -> "zh-CN"
            languageCode.startsWith("en") -> "en-US"
            languageCode.startsWith("ja") -> "ja-JP"
            languageCode.startsWith("ko") -> "ko-KR"
            languageCode.startsWith("fr") -> "fr-FR"
            languageCode.startsWith("de") -> "de-DE"
            languageCode.startsWith("es") -> "es-ES"
            languageCode.startsWith("it") -> "it-IT"
            languageCode.startsWith("ru") -> "ru-RU"
            else -> "en-US" // 默认使用英语
        }
    }

    /**
     * 获取指定语言的可用语音列表
     */
    fun getAvailableVoices(languageCode: String): List<Voice> {
        val normalizedLanguageCode = normalizeLanguageCode(languageCode)
        return VOICE_MAP[normalizedLanguageCode] ?: VOICE_MAP["en-US"] ?: emptyList()
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = ""
    }

    /**
     * 语音模型数据类
     */
    data class Voice(
        val name: String,
        val gender: String,
        val displayName: String
    )
}
