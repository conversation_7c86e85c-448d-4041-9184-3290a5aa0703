package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.SmartToy
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.Translate
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Lightbulb
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import android.Manifest
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.Animation
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.FastOutSlowInEasing
import android.widget.Toast
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.consumeAllChanges
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.content.Context
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.indication
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.foundation.Canvas
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.Animatable
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.res.stringResource
import com.example.llya.R
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import android.util.Log
import java.util.Locale

data class ChatMessage(
    val content: String,
    val isFromUser: Boolean,
    val timestamp: String = SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())
)

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class, ExperimentalFoundationApi::class)
@Composable
fun AIChatScreen(
    navController: NavController? = null
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val userBubbleColor = Color(0xFF8A6BFF) // 浅紫色
    val aiBubbleColor = Color(0xFF33324A)   // 深灰色

    // 语音识别ViewModel
    val speechViewModel: SpeechRecognitionViewModel = viewModel()

    // 在AIChatScreen中设置初始模式
    LaunchedEffect(Unit) {
        speechViewModel.toggleTranslation(false) // 关闭翻译功能
        speechViewModel.toggleMeetingMode(false) // 设置为非会议模式
        speechViewModel.toggleThinkMode(false)   // 默认关闭思考模式
    }

    val isRecording by speechViewModel.isRecording.collectAsState()
    val recognitionResult by speechViewModel.recognitionResult.collectAsState()
    val aiResponse by speechViewModel.aiResponse.collectAsState()
    val lastValidResult by speechViewModel.lastValidResult.collectAsState()
    val isTranslationEnabled by speechViewModel.isTranslationEnabled.collectAsState()
    val isAiProcessing by speechViewModel.isAiProcessing.collectAsState()

    // 麦克风权限
    val micPermissionState = rememberPermissionState(Manifest.permission.RECORD_AUDIO)
    val context = LocalContext.current

    // 获取本地化字符串资源
    val aiHelloMessage = stringResource(R.string.ai_hello_message)
    val aiEarphoneIntro = stringResource(R.string.ai_earphone_intro)
    val understandEarphoneMessage = stringResource(R.string.ai_chat_understand_earphone)
    val defaultGreeting = stringResource(R.string.ai_chat_default_greeting)
    val recognitionFailedText = stringResource(R.string.recognition_failed)
    val noTextRecognizedMessage = stringResource(R.string.ai_chat_no_text_recognized)

    // 聊天消息状态
    var messages by remember { mutableStateOf(listOf(
        ChatMessage(aiHelloMessage, false),
        ChatMessage(understandEarphoneMessage, true),
        ChatMessage(aiEarphoneIntro, false)
    )) }

    // 输入框状态
    var inputText by remember { mutableStateOf("") }

    // 语音录制状态
    var lastRecognitionResult by remember { mutableStateOf("") }

    // 音量波形动画值
    val waveAmplitude = remember { Animatable(0f) }

    // 记录原始识别文本
    var originalText by remember { mutableStateOf("") }

    // 微软语音识别相关状态
    var speechRecognizer by remember { mutableStateOf<SpeechRecognizer?>(null) }
    var isMicrosoftRecording by remember { mutableStateOf(false) }
    var microsoftRecognitionResult by remember { mutableStateOf("") }

    // 微软语音SDK配置
    val speechSubscriptionKey = "7lJ1e4p4LppCs62hMBvzyzxYv5vCOauJTzIKqnY3U0NVw7hFXiGaJQQJ99BEACYeBjFXJ3w3AAAYACOGuXCg" // 替换为实际的密钥
    val speechRegion = "eastasia" // 替换为实际的区域

    // 自动检测的语言（基于系统语言）
    val autoDetectedLanguage = remember {
        val systemLocale = Locale.getDefault()
        when (systemLocale.language) {
            "zh" -> if (systemLocale.country == "TW" || systemLocale.country == "HK") "zh-HK" else "zh-CN"
            "en" -> "en-US"
            "ja" -> "ja-JP"
            "ko" -> "ko-KR"
            "fr" -> "fr-FR"
            "de" -> "de-DE"
            "es" -> "es-ES"
            "it" -> "it-IT"
            "pt" -> "pt-BR"
            "ru" -> "ru-RU"
            "ar" -> "ar-SA"
            "th" -> "th-TH"
            "vi" -> "vi-VN"
            "id" -> "id-ID"
            "ms" -> "ms-MY"
            "hi" -> "hi-IN"
            "tr" -> "tr-TR"
            "pl" -> "pl-PL"
            "nl" -> "nl-NL"
            "sv" -> "sv-SE"
            "da" -> "da-DK"
            "no" -> "nb-NO"
            "fi" -> "fi-FI"
            "cs" -> "cs-CZ"
            "hu" -> "hu-HU"
            "ro" -> "ro-RO"
            "sk" -> "sk-SK"
            "bg" -> "bg-BG"
            "hr" -> "hr-HR"
            "sl" -> "sl-SI"
            "et" -> "et-EE"
            "lv" -> "lv-LV"
            "lt" -> "lt-LT"
            "uk" -> "uk-UA"
            "he" -> "he-IL"
            "fa" -> "fa-IR"
            else -> "en-US" // 默认英语
        }
    }

    // 保存对话框状态
    var showSaveDialog by remember { mutableStateOf(false) }
    var saveTitle by remember { mutableStateOf("") }

    // 更多选项菜单状态
    var showMoreMenu by remember { mutableStateOf(false) }

    // 当前思考模式状态
    val isThinkModeEnabled by speechViewModel.isThinkModeEnabled.collectAsState()

    // 身份设置对话框状态
    var showIdentityDialog by remember { mutableStateOf(false) }
    var customIdentityResponse by remember { mutableStateOf(speechViewModel.customIdentityResponse.value) }

    // 如果正在录音，启动音量波形动画
    LaunchedEffect(isMicrosoftRecording) {
        if (isMicrosoftRecording) {
            waveAmplitude.snapTo(0f)
            while (isMicrosoftRecording) {
                // 模拟音量变化的动画
                launch {
                    waveAmplitude.animateTo(
                        targetValue = 1f,
                        animationSpec = tween(300, easing = LinearEasing)
                    )
                    waveAmplitude.animateTo(
                        targetValue = 0.3f,
                        animationSpec = tween(300, easing = LinearEasing)
                    )
                }
                delay(600)
            }
        }
    }

    // 上滑取消状态
    var isCancelled by remember { mutableStateOf(false) }
    // 显示上滑取消的提示
    var showCancelHint by remember { mutableStateOf(false) }

    // 协程作用域用于发送消息
    val coroutineScope = rememberCoroutineScope()

    // 监听语音识别结果
    LaunchedEffect(recognitionResult) {
        // 只处理非空结果且不含"失败"字样的识别结果
        if (recognitionResult.isNotEmpty() &&
            !recognitionResult.contains("失败") &&
            !recognitionResult.contains("null") &&
            recognitionResult != lastRecognitionResult) {

            // 保存原始识别结果并更新输入框
            originalText = recognitionResult
            inputText = recognitionResult
            lastRecognitionResult = recognitionResult
        }
    }

    // 监听AI响应，自动添加到消息列表
    LaunchedEffect(aiResponse) {
        if (aiResponse.isNotEmpty() && messages.lastOrNull()?.content != aiResponse) {
            // 添加AI回复消息
            messages = messages + ChatMessage(aiResponse, false)
        }
    }

    // 获取振动器服务
    val vibrator = remember {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            @Suppress("DEPRECATION")
            context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }
    }

    // 触发振动反馈
    fun vibratePhone() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(50)
        }
    }

    // 是否已经振动过
    var hasVibrated by remember { mutableStateOf(false) }

    // 聊天消息列表状态 - 移到组件顶部以便在整个组件中访问
    val listState = rememberLazyListState()

    // 微软语音识别函数
    fun startMicrosoftRecognition() {
        try {
            Log.d("AIChatScreen", "开始微软语音识别，语言: $autoDetectedLanguage")

            val speechConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            speechConfig.speechRecognitionLanguage = autoDetectedLanguage

            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()
            speechRecognizer = SpeechRecognizer(speechConfig, audioConfig)

            speechRecognizer?.let { recognizer ->
                // 识别中事件
                recognizer.recognizing.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    if (recognizedText.isNotEmpty()) {
                        Log.d("AIChatScreen", "识别中: $recognizedText")
                        microsoftRecognitionResult = recognizedText
                        inputText = recognizedText
                    }
                }

                // 识别完成事件
                recognizer.recognized.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    if (recognizedText.isNotEmpty() && recognizedText.length > 2) {
                        Log.d("AIChatScreen", "识别完成: $recognizedText")
                        microsoftRecognitionResult = recognizedText
                        inputText = recognizedText
                    }
                }

                // 取消事件
                recognizer.canceled.addEventListener { _, event ->
                    Log.e("AIChatScreen", "识别被取消: ${event.reason}, 详情: ${event.errorDetails}")
                    isMicrosoftRecording = false
                }

                // 会话停止事件
                recognizer.sessionStopped.addEventListener { _, _ ->
                    Log.d("AIChatScreen", "识别会话结束")
                    isMicrosoftRecording = false
                }

                // 启动连续识别
                recognizer.startContinuousRecognitionAsync().get()
                isMicrosoftRecording = true

                Toast.makeText(context, "开始录音 (${getLanguageName(autoDetectedLanguage)})", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            Log.e("AIChatScreen", "启动微软语音识别失败: ${e.message}", e)
            Toast.makeText(context, "启动录音失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    fun stopMicrosoftRecognition() {
        try {
            Log.d("AIChatScreen", "停止微软语音识别")

            speechRecognizer?.let { recognizer ->
                recognizer.stopContinuousRecognitionAsync().get()
                recognizer.close()
            }
            speechRecognizer = null
            isMicrosoftRecording = false

            Toast.makeText(context, "录音已停止", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e("AIChatScreen", "停止微软语音识别失败: ${e.message}", e)
            isMicrosoftRecording = false
        }
    }

    // 获取语言显示名称
    fun getLanguageName(languageCode: String): String {
        return when (languageCode) {
            "zh-CN" -> "中文(简体)"
            "zh-HK" -> "中文(繁体)"
            "en-US" -> "English"
            "ja-JP" -> "日本語"
            "ko-KR" -> "한국어"
            "fr-FR" -> "Français"
            "de-DE" -> "Deutsch"
            "es-ES" -> "Español"
            "it-IT" -> "Italiano"
            "pt-BR" -> "Português"
            "ru-RU" -> "Русский"
            "ar-SA" -> "العربية"
            "th-TH" -> "ไทย"
            "vi-VN" -> "Tiếng Việt"
            "id-ID" -> "Bahasa Indonesia"
            "ms-MY" -> "Bahasa Melayu"
            "hi-IN" -> "हिन्दी"
            "tr-TR" -> "Türkçe"
            else -> languageCode
        }
    }

    // 显示保存对话框
    if (showSaveDialog) {
        AlertDialog(
            onDismissRequest = { showSaveDialog = false },
            title = { Text(stringResource(R.string.ai_chat_save_title)) },
            text = {
                Column {
                    Text(stringResource(R.string.ai_chat_enter_title))
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = saveTitle,
                        onValueChange = { saveTitle = it },
                        placeholder = { Text(stringResource(R.string.ai_chat_title_placeholder)) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 如果标题为空，使用默认值
                        val title = if (saveTitle.isBlank()) {
                            "AI聊天记录 ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())}"
                        } else {
                            saveTitle
                        }

                        // 将消息转换为AIChatPersistenceManager.ChatMessage格式
                        val chatMessages = messages.map { message ->
                            com.example.llya.utils.AIChatPersistenceManager.ChatMessage(
                                content = message.content,
                                isFromUser = message.isFromUser,
                                timestamp = message.timestamp
                            )
                        }

                        // 保存聊天记录
                        speechViewModel.saveCurrentChatSession(title, chatMessages)

                        // 关闭对话框
                        showSaveDialog = false
                        saveTitle = ""

                        // 显示保存成功提示
                        Toast.makeText(context, context.getString(R.string.ai_chat_save_success), Toast.LENGTH_SHORT).show()
                    }
                ) {
                    Text(stringResource(R.string.ai_chat_save_button))
                }
            },
            dismissButton = {
                TextButton(onClick = { showSaveDialog = false }) {
                    Text(stringResource(R.string.ai_chat_cancel_button))
                }
            }
        )
    }

    // 显示身份设置对话框
    if (showIdentityDialog) {
        AlertDialog(
            onDismissRequest = { showIdentityDialog = false },
            title = { Text(stringResource(R.string.ai_chat_identity_title)) },
            text = {
                Column {
                    Text(stringResource(R.string.ai_chat_identity_description))
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = customIdentityResponse,
                        onValueChange = { customIdentityResponse = it },
                        modifier = Modifier.fillMaxWidth(),
                        placeholder = { Text(stringResource(R.string.ai_chat_identity_input_placeholder)) },
                        maxLines = 5
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 保存自定义身份回答
                        if (customIdentityResponse.isNotEmpty()) {
                            speechViewModel.setCustomIdentityResponse(customIdentityResponse)
                            // 添加一条系统消息
                            messages = messages + ChatMessage("【系统提示】身份回答已更新，当用户询问AI是谁时，将回答为Theta小助手。", false)
                            // 自动滚动到底部
                            coroutineScope.launch {
                                delay(100)
                                listState.animateScrollToItem(messages.size - 1)
                            }
                        }
                        showIdentityDialog = false

                        // 显示设置成功提示
                        Toast.makeText(context, context.getString(R.string.ai_chat_identity_set_success), Toast.LENGTH_SHORT).show()
                    }
                ) {
                    Text(stringResource(R.string.ai_chat_save_button))
                }
            },
            dismissButton = {
                TextButton(onClick = { showIdentityDialog = false }) {
                    Text(stringResource(R.string.ai_chat_cancel_button))
                }
            }
        )
    }

    // 组件销毁时清理资源
    DisposableEffect(Unit) {
        onDispose {
            try {
                speechRecognizer?.let { recognizer ->
                    recognizer.stopContinuousRecognitionAsync()
                    recognizer.close()
                }
                speechRecognizer = null
                isMicrosoftRecording = false
                Log.d("AIChatScreen", "语音识别资源已清理")
            } catch (e: Exception) {
                Log.e("AIChatScreen", "清理语音识别资源失败: ${e.message}", e)
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    text = stringResource(R.string.ai_chat_title),
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = stringResource(R.string.ai_chat_back),
                        tint = Color.White
                    )
                }
            },
            actions = {
                // 显示当前自动检测的语言
                Text(
                    text = getLanguageName(autoDetectedLanguage),
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(end = 8.dp)
                )

                // 更多选项按钮
                IconButton(onClick = { showMoreMenu = true }) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = stringResource(R.string.ai_chat_more),
                        tint = Color.White
                    )
                }

                // 更多选项下拉菜单
                DropdownMenu(
                    expanded = showMoreMenu,
                    onDismissRequest = { showMoreMenu = false }
                ) {
                    DropdownMenuItem(
                        text = { Text(stringResource(R.string.ai_chat_save_record)) },
                        onClick = {
                            showMoreMenu = false
                            saveTitle = "AI聊天记录 ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())}"
                            showSaveDialog = true
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Save,
                                contentDescription = null
                            )
                        }
                    )
                    DropdownMenuItem(
                        text = { Text(stringResource(R.string.ai_chat_view_history)) },
                        onClick = {
                            showMoreMenu = false
                            navController?.navigate("aichat_history")
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.History,
                                contentDescription = null
                            )
                        }
                    )
                    DropdownMenuItem(
                        text = { Text(if (isThinkModeEnabled) stringResource(R.string.ai_chat_toggle_think_mode_on) else stringResource(R.string.ai_chat_toggle_think_mode_off)) },
                        onClick = {
                            showMoreMenu = false
                            val newMode = !isThinkModeEnabled
                            speechViewModel.toggleThinkMode(newMode)
                            if (newMode) {
                                // 添加一条系统消息，告知用户思考模式已开启
                                messages = messages + ChatMessage("【系统提示】思考模式已开启，AI将展示详细的思考步骤和结论。下一个问题会以思考模式回答。", false)
                                // 自动滚动到底部
                                coroutineScope.launch {
                                    delay(100)
                                    listState.animateScrollToItem(messages.size - 1)
                                }
                            }
                            Toast.makeText(
                                context,
                                if (newMode) context.getString(R.string.ai_chat_think_mode_on) else context.getString(R.string.ai_chat_think_mode_off),
                                Toast.LENGTH_SHORT
                            ).show()
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Lightbulb,
                                contentDescription = null
                            )
                        }
                    )

                    DropdownMenuItem(
                        text = { Text(stringResource(R.string.ai_chat_clear_history)) },
                        onClick = {
                            showMoreMenu = false
                            messages = listOf(
                                ChatMessage(defaultGreeting, false)
                            )
                            speechViewModel.clearAIHistory()
                            Toast.makeText(context, context.getString(R.string.ai_chat_history_cleared), Toast.LENGTH_SHORT).show()
                        }
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor,
                titleContentColor = Color.White
            )
        )

        // 聊天消息列表
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp),
            reverseLayout = false, // 最新消息在底部
            contentPadding = PaddingValues(vertical = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp) // 消息之间添加间距
        ) {
            items(messages) { message ->
                ChatMessageItem(
                    message = message,
                    userBubbleColor = userBubbleColor,
                    aiBubbleColor = aiBubbleColor
                )
            }

            // 如果AI正在处理，显示加载指示器
            if (isAiProcessing) {
                item {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        horizontalArrangement = Arrangement.Start,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(36.dp)
                                .clip(CircleShape)
                                .background(Color(0xFF8A6BFF)),  // 紫色背景
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.SmartToy,
                                contentDescription = "AI",
                                tint = Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                        Spacer(modifier = Modifier.width(8.dp))

                        // 加载动画
                        LinearProgressIndicator(
                            modifier = Modifier
                                .width(100.dp)
                                .height(2.dp),
                            color = Color(0xFF8A6BFF)
                        )
                    }
                }
            }
        }

        // 自动滚动到底部
        LaunchedEffect(messages.size, isAiProcessing) {
            if (messages.isNotEmpty() || isAiProcessing) {
                listState.animateScrollToItem(listState.layoutInfo.totalItemsCount - 1)
            }
        }

        // 底部输入区域
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color(0xFF33324A),
            tonalElevation = 4.dp
        ) {
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 上滑取消提示
                if (showCancelHint) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .background(
                                if (isCancelled) Color(0xFFE57373) else Color(0xFF555555).copy(alpha = 0.7f)
                            )
                            .align(Alignment.TopCenter),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = if (isCancelled) stringResource(R.string.ai_chat_release_cancel) else stringResource(R.string.ai_chat_swipe_cancel),
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = if (isCancelled) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 输入框
                    TextField(
                        value = inputText,
                        onValueChange = { inputText = it },
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 8.dp),
                        placeholder = { Text(stringResource(R.string.ai_chat_input_placeholder)) },
                        colors = TextFieldDefaults.colors(
                            focusedContainerColor = Color.Transparent,
                            unfocusedContainerColor = Color.Transparent,
                            focusedTextColor = Color.White,
                            unfocusedTextColor = Color.White,
                            cursorColor = Color.White,
                            focusedPlaceholderColor = Color.Gray,
                            unfocusedPlaceholderColor = Color.Gray,
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent
                        ),
                        maxLines = 1,
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                        keyboardActions = KeyboardActions(
                            onSend = {
                                if (inputText.isNotEmpty()) {
                                    // 添加用户消息
                                    messages = messages + ChatMessage(inputText, true)

                                    // 将用户输入发送给豆包AI
                                    val userInput = inputText
                                    speechViewModel.sendMessageToAI(userInput)

                                    // 清空输入框
                                    inputText = ""
                                }
                            }
                        )
                    )

                    // 发送按钮
                    IconButton(
                        onClick = {
                            if (inputText.isNotEmpty()) {
                                // 添加用户消息
                                messages = messages + ChatMessage(inputText, true)

                                // 将用户输入发送给豆包AI
                                val userInput = inputText
                                speechViewModel.sendMessageToAI(userInput)

                                // 清空输入框
                                inputText = ""
                            }
                        },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF8A6BFF))
                    ) {
                        Icon(
                            imageVector = Icons.Default.Send,
                            contentDescription = stringResource(R.string.ai_chat_send),
                            tint = Color.White
                        )
                    }

                    // 麦克风按钮 - 重新设计为点击切换模式
                    Spacer(modifier = Modifier.width(8.dp))
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        // 绘制波形动画背景
                        if (isMicrosoftRecording) {
                            Canvas(
                                modifier = Modifier
                                    .size(70.dp)
                                    .scale(waveAmplitude.value)
                            ) {
                                drawCircle(
                                    color = Color(0xFF4CAF50).copy(alpha = 0.3f),
                                    radius = size.minDimension / 2,
                                    style = Stroke(width = 2.dp.toPx(), cap = StrokeCap.Round)
                                )
                            }

                            Canvas(
                                modifier = Modifier
                                    .size(60.dp)
                                    .scale(waveAmplitude.value * 0.9f)
                            ) {
                                drawCircle(
                                    color = Color(0xFF4CAF50).copy(alpha = 0.5f),
                                    radius = size.minDimension / 2,
                                    style = Stroke(width = 2.dp.toPx(), cap = StrokeCap.Round)
                                )
                            }
                        }

                        // 主按钮
                        IconButton(
                            onClick = {
                                // 点击切换录音状态
                                if (!isMicrosoftRecording) {
                                    // 检查麦克风权限
                                    if (!micPermissionState.status.isGranted) {
                                        micPermissionState.launchPermissionRequest()
                                        return@IconButton
                                    }

                                    // 开始微软语音识别
                                    startMicrosoftRecognition()
                                    showCancelHint = false
                                } else {
                                    // 停止微软语音识别
                                    stopMicrosoftRecognition()
                                    showCancelHint = false

                                    // 短暂延迟处理结果
                                    coroutineScope.launch {
                                        delay(500)
                                        // 检查识别结果是否有效
                                        if (inputText.isNotEmpty() &&
                                           inputText.trim().length > 1) {

                                            // 添加用户消息
                                            messages = messages + ChatMessage(inputText, true)

                                            // 将识别结果发送给豆包AI
                                            val userInput = inputText
                                            speechViewModel.sendMessageToAI(userInput)

                                            // 清空输入框
                                            inputText = ""
                                            microsoftRecognitionResult = ""

                                        } else {
                                            // 没有识别到文字
                                            Toast.makeText(context, noTextRecognizedMessage, Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                }
                            },
                            modifier = Modifier
                                .size(48.dp)
                                .clip(CircleShape)
                                .background(
                                    if (isMicrosoftRecording) Color.Red else Color(0xFF4CAF50)
                                )
                        ) {
                            Icon(
                                imageVector = if (isMicrosoftRecording) Icons.Default.Stop else Icons.Default.Mic,
                                contentDescription = if (isMicrosoftRecording) stringResource(R.string.ai_chat_stop_recording) else stringResource(R.string.ai_chat_start_recording_desc),
                                tint = Color.White,
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ChatMessageItem(
    message: ChatMessage,
    userBubbleColor: Color,
    aiBubbleColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = if (message.isFromUser) Arrangement.End else Arrangement.Start,
        verticalAlignment = Alignment.Top
    ) {
        // AI头像 - 仅在消息来自AI时显示
        if (!message.isFromUser) {
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF8A6BFF)),  // 紫色背景
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.SmartToy,
                    contentDescription = "AI",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
        }

        // 消息气泡和时间戳
        Column(
            horizontalAlignment = if (message.isFromUser) Alignment.End else Alignment.Start
        ) {
            // 气泡
            Box(
                modifier = Modifier
                    .widthIn(max = 260.dp)
                    .clip(
                        RoundedCornerShape(
                            topStart = 16.dp,
                            topEnd = 16.dp,
                            bottomStart = if (message.isFromUser) 16.dp else 4.dp,
                            bottomEnd = if (message.isFromUser) 4.dp else 16.dp
                        )
                    )
                    .background(if (message.isFromUser) userBubbleColor else aiBubbleColor)
                    .padding(12.dp)
            ) {
                Text(
                    text = message.content,
                    color = Color.White,
                    modifier = Modifier.widthIn(max = 240.dp)
                )
            }

            // 时间戳
            Text(
                text = message.timestamp,
                color = Color.Gray,
                fontSize = 12.sp,
                modifier = Modifier.padding(top = 4.dp),
                textAlign = if (message.isFromUser) TextAlign.End else TextAlign.Start
            )
        }

        // 用户头像 - 仅在消息来自用户时显示
        if (message.isFromUser) {
            Spacer(modifier = Modifier.width(8.dp))
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF4CAF50)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "ME",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}