package com.example.llya.data

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

/**
 * 历史记录数据模型
 */
data class HistoryRecord(
    val id: String = UUID.randomUUID().toString(),
    val sourceText: String,                  // 原始文本
    val translatedText: String = "",         // 翻译文本
    val sourceLanguage: String,              // 源语言代码
    val targetLanguage: String = "",         // 目标语言代码
    val sourceLanguageName: String = "",     // 源语言名称
    val targetLanguageName: String = "",     // 目标语言名称
    val timestamp: Long = System.currentTimeMillis(),
    val type: RecordType = RecordType.SPEECH_RECOGNITION,  // 记录类型：语音识别或翻译
    val isFavorite: Boolean = false,         // 是否为收藏项
) {
    // 格式化时间的方法
    fun getFormattedTime(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }
    
    // 获取简短显示时间
    fun getShortTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            else -> {
                val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
                dateFormat.format(Date(timestamp))
            }
        }
    }
}

/**
 * 记录类型枚举
 */
enum class RecordType {
    SPEECH_RECOGNITION,  // 语音识别
    TRANSLATION          // 文本翻译
} 