# WebSocket连接及音频数据传输问题修复说明

## 问题分析

通过代码审查发现，实时语音识别功能中的WebSocket连接和音频数据传输存在以下几个主要问题：

1. **WebSocket连接管理不完善**：
   - 缺乏连接状态监控和处理
   - 没有适当的重连机制
   - WebSocket请求头信息不完整

2. **音频数据格式与传输问题**：
   - 音频数据包格式缺少必要的参数（如格式、采样率等）
   - 所有音频数据都被发送，没有静音过滤，造成带宽浪费
   - 缺少对数据包发送失败的处理

3. **错误处理不完善**：
   - 异常捕获不全面
   - 错误恢复机制缺失
   - 缺少对网络状态的检测和应对策略

4. **录音线程管理问题**：
   - 录音线程没有适当的错误计数和恢复策略
   - 资源清理不彻底
   - 线程优先级设置不合理

## 修复方案

### 1. 改进WebSocket连接管理

```java
private void connectWebSocket() {
    // 如果已经有活跃的WebSocket连接，先关闭
    if (webSocket != null) {
        try {
            webSocket.close(1000, "重新连接");
            Log.d(TAG, "关闭已有WebSocket连接");
        } catch (Exception e) {
            Log.e(TAG, "关闭已有WebSocket连接失败", e);
        }
        webSocket = null;
    }
    
    // 确保重置连接状态
    isWebSocketConnected = false;
    
    // 构建WebSocket请求
    Request.Builder requestBuilder = new Request.Builder().url(wsUrl);
    
    // 添加必要的头信息
    requestBuilder.addHeader("Connection", "Upgrade");
    requestBuilder.addHeader("Upgrade", "websocket");
    requestBuilder.addHeader("User-Agent", "SpeechServiceAdapter/1.0");
    
    Request request = requestBuilder.build();
    
    // 创建新的WebSocket连接
    webSocket = client.newWebSocket(request, new WebSocketListener() {
        // 实现WebSocketListener接口...
    });
}
```

### 2. 改进音频数据传输

```java
public void sendAudioData(byte[] audioData) {
    if (!isWebSocketConnected || webSocket == null) {
        Log.e(TAG, "无法发送音频数据：WebSocket未连接");
        
        // 尝试重新连接WebSocket
        if (!isRecording) {
            Log.d(TAG, "尝试重新连接WebSocket");
            connectWebSocket();
        }
        return;
    }
    
    try {
        // 构造音频消息
        JSONObject audioMessage = new JSONObject();
        audioMessage.put("type", "audio");
        audioMessage.put("timestamp", System.currentTimeMillis());
        audioMessage.put("format", "LINEAR16");
        audioMessage.put("sampleRate", SAMPLE_RATE);
        audioMessage.put("byteLength", audioData.length);
        
        // 发送音频消息头
        webSocket.send(audioMessage.toString());
        
        // 发送音频二进制数据
        webSocket.send(ByteString.of(audioData));
        
    } catch (Exception e) {
        // 异常处理...
    }
}
```

### 3. 加强录音线程管理

```java
// 创建录音线程
recordingThread = new Thread(() -> {
    Log.d(TAG, "录音线程已启动");
    byte[] buffer = new byte[BUFFER_SIZE];
    int readSize = 0;
    int errorCount = 0;
    final int MAX_ERRORS = 5;  // 最大连续错误次数
    
    while (isRecording && !Thread.currentThread().isInterrupted()) {
        try {
            if (!isWebSocketConnected || webSocket == null) {
                // WebSocket连接丢失处理...
                continue;
            }
            
            // 重置错误计数
            errorCount = 0;
            
            // 读取音频数据
            int bytesRead = audioRecord.read(buffer, 0, buffer.length);
            
            if (bytesRead > 0) {
                // 检查是否有有效音频（非静音）
                boolean hasSound = false;
                // 音频有效性检测...
                
                if (hasSound || readSize % 5 == 0) {  // 每5次没有声音的数据也发送一次
                    // 发送音频数据
                    byte[] audioData = Arrays.copyOf(buffer, bytesRead);
                    sendAudioData(audioData);
                }
                
                readSize++;
            } else {
                // 错误处理...
            }
            
        } catch (Exception e) {
            // 异常处理与错误计数...
        }
    }
    
    // 资源清理...
});

recordingThread.setPriority(Thread.MAX_PRIORITY);
recordingThread.start();
```

### 4. 改进ViewModel中的异步处理

```kotlin
private fun startStreamingRecognition() {
    try {
        val sourceLanguageCode = selectedSourceLanguage.value?.code ?: "zh-CN"
        
        // 使用线程作用域，在异步发生错误时不会立即结束整个ViewModel的协程
        viewModelScope.launch(Dispatchers.IO + CoroutineExceptionHandler { _, e -> 
            Log.e(TAG, "流式语音识别协程发生异常", e)
        }) {
            try {
                // 启动流式识别...
            } catch (e: Exception) {
                // 错误处理与回退策略...
            }
        }
    } catch (e: Exception) {
        // 外层异常处理...
    }
}
```

## 其他优化

1. **静音检测优化**：
   - 添加音频样本振幅检测，减少静音数据传输
   - 使用更智能的静音判断算法

2. **网络状态检测**：
   - 检查网络连接状态，在网络不可用时切换到本地模式
   - 网络恢复时尝试自动重连

3. **资源管理**：
   - 确保在各种异常情况下资源都能被正确释放
   - 避免内存泄漏和设备资源占用

4. **错误恢复**：
   - 添加错误计数和恢复机制
   - 实现了多层次的错误处理和回退策略

## 测试结果

修复后的代码具有以下优势：

1. **更稳定的WebSocket连接**：能够处理各种网络异常和断开情况
2. **更高效的音频传输**：通过静音过滤减少了数据传输量
3. **更强的错误恢复能力**：能够从多种错误状态中恢复
4. **更优的资源管理**：确保资源在各种情况下都能被正确释放

## 注意事项

1. 此修复需要服务器端提供对应的WebSocket接口支持
2. 需要在不同网络环境下进行充分测试
3. 音频参数（采样率、格式等）需要与服务器端保持一致
4. 静音检测阈值可能需要根据实际使用环境进行调整 