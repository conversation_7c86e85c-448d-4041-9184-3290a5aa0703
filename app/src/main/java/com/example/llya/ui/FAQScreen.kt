package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FAQScreen(
    navController: NavController? = null
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val cardBackgroundColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    
    // 常见问题分类和内容
    val faqCategories = listOf(
        "蓝牙连接" to listOf(
            "如何将智能翻译耳机与手机配对？" to "首先确保耳机有足够的电量，长按电源键5秒进入配对模式（指示灯会闪烁蓝光）。然后在手机的蓝牙设置中搜索并点击智能翻译耳机进行连接。成功连接后，指示灯会变为稳定的蓝光。",
            "为什么我的耳机无法保持连接？" to "连接不稳定可能有多种原因：1）耳机电量不足，请尝试充电；2）距离太远，请确保在10米范围内；3）有干扰源，如其他蓝牙设备或墙壁阻隔；4）可能需要重新配对，请删除已有配对并重新连接。"
        ),
        "同声传译" to listOf(
            "支持哪些语言的实时翻译？" to "我们目前支持40种语言的实时翻译，包括中文（简体和繁体）、英语、日语、韩语、法语、德语、西班牙语、意大利语、俄语等主要语言。完整列表可在App的语言设置中查看，我们也会定期更新添加新的语言支持。",
            "翻译准确度如何？" to "我们的翻译技术基于先进的神经网络模型，准确率在大多数场景下能达到90%以上。但在嘈杂环境、使用专业术语或方言时，准确度可能会受影响。我们建议在重要场合提前测试，并确保清晰地发音以获得最佳效果。"
        ),
        "AI功能" to listOf(
            "如何激活AI助手？" to "您可以通过两种方式激活AI助手：1）在App中打开AI助手页面，点击麦克风按钮开始交谈；2）戴着耳机时，可以连续轻点两次右耳机触控区域，然后说出您的问题。",
            "AI助手能帮我做什么？" to "AI助手可以回答各种问题、提供旅行建议、翻译句子、解释单词、推荐餐厅、记录备忘录、阅读消息通知、控制音乐播放等。随着系统更新，我们会不断增加新功能。"
        ),
        "故障排除" to listOf(
            "App闪退或无法打开怎么办？" to "请尝试以下步骤：1）重启App；2）重启手机；3）确认App是最新版本；4）清除App缓存；5）卸载并重新安装App。如果问题依然存在，请联系我们的客服团队获取进一步帮助。"
        )
    )
    
    // 当前选择的分类和问题
    var selectedCategory by remember { mutableStateOf(faqCategories.first().first) }
    var expandedQuestion by remember { mutableStateOf<String?>(null) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { 
                Text(
                    text = "帮助与常见问题",
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor,
                titleContentColor = Color.White
            )
        )
        
        // 分类选项卡
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            faqCategories.forEach { (category, _) ->
                val isSelected = category == selectedCategory
                Text(
                    text = category,
                    color = if (isSelected) purpleColor else Color.White,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    modifier = Modifier
                        .clip(RoundedCornerShape(16.dp))
                        .clickable { selectedCategory = category }
                        .background(
                            if (isSelected) purpleColor.copy(alpha = 0.2f)
                            else Color.Transparent
                        )
                        .padding(horizontal = 12.dp, vertical = 8.dp)
                )
            }
        }
        
        Divider(color = Color.White.copy(alpha = 0.1f))
        
        // 问题和答案列表
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp)
        ) {
            val questions = faqCategories.first { it.first == selectedCategory }.second
            
            items(questions.size) { index ->
                val (question, answer) = questions[index]
                val isExpanded = expandedQuestion == question
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        // 问题行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    expandedQuestion = if (isExpanded) null else question
                                },
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = question,
                                color = Color.White,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.weight(1f)
                            )
                            
                            Icon(
                                imageVector = if (isExpanded) 
                                    Icons.Default.KeyboardArrowDown 
                                else 
                                    Icons.Default.ChevronRight,
                                contentDescription = if (isExpanded) "收起" else "展开",
                                tint = Color.White.copy(alpha = 0.6f)
                            )
                        }
                        
                        // 答案部分 - 展开时显示
                        if (isExpanded) {
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Divider(
                                color = Color.White.copy(alpha = 0.1f),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            
                            Text(
                                text = answer,
                                color = Color.White.copy(alpha = 0.7f),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp)
                            )
                        }
                    }
                }
            }
            
            // 底部额外空间
            item { 
                Spacer(modifier = Modifier.height(16.dp))
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "需要更多帮助？",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.sp
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "如果您的问题没有在上述FAQ中得到解答，请通过以下方式联系我们的客服团队",
                            color = Color.White.copy(alpha = 0.7f)
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Button(
                            onClick = { /* 联系客服逻辑 */ },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = purpleColor
                            ),
                            modifier = Modifier.align(Alignment.End)
                        ) {
                            Text("联系客服")
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
} 