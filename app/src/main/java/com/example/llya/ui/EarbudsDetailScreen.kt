package com.example.llya.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material.icons.filled.VolumeDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.TextField
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.viewmodel.EarbudsViewModel
import com.example.llya.viewmodel.AppUpdateViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import android.util.Log
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.DisposableEffect
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import android.widget.Toast
import com.example.llya.ui.components.BottomNavItem
import androidx.compose.material3.OutlinedButton
import androidx.compose.foundation.BorderStroke
import com.example.llya.utils.LocalLifecycleOwner
import com.example.llya.ui.components.CommonBottomNavBar
import androidx.compose.material3.IconButton
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.outlined.Headphones
import androidx.compose.ui.platform.LocalContext

@Composable
fun EarbudsDetailScreen(
    navController: NavController? = null,
    viewModel: EarbudsViewModel = viewModel(),
    appUpdateViewModel: AppUpdateViewModel = viewModel()
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val cardBackgroundColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    val greenColor = Color(0xFF4CAF50)
    
    // 获取ViewModel中的状态
    val volume by viewModel.volume.collectAsState()
    val deviceName by viewModel.deviceName.collectAsState()
    val uiState by viewModel.uiState.collectAsState()
    
    // 获取应用更新状态（但不再自动检查）
    val updateInfo by appUpdateViewModel.updateInfo.collectAsState()
    val showUpdateDialog by appUpdateViewModel.showUpdateDialog.collectAsState()
    
    // 获取上下文
    val context = androidx.compose.ui.platform.LocalContext.current
    
    // 启动时只刷新音量，不再检查更新
    LaunchedEffect(Unit) {
        viewModel.refreshVolume()
        // 获取设备详情
        viewModel.getDeviceDetail()
        // 移除自动检查更新: appUpdateViewModel.checkForUpdates()
    }
    
    // 添加对话框状态
    var showEffectsDialog by remember { mutableStateOf(false) }
    
    // 添加加载指示器
    if (uiState.isLoading) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.5f)),
            contentAlignment = Alignment.Center
        ) {
            androidx.compose.material3.CircularProgressIndicator(
                color = purpleColor,
                modifier = Modifier.size(50.dp)
            )
        }
    }
    
    // 错误消息提示
    if (uiState.errorMessage.isNotEmpty()) {
        LaunchedEffect(uiState.errorMessage) {
            Toast.makeText(
                context,
                uiState.errorMessage,
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部导航栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 22.dp, vertical = 22.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 添加返回主页按钮
            IconButton(
                onClick = { navController?.navigate("main") }
            ) {
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = stringResource(R.string.decrease_volume),
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
          
            // 公司名称固定显示
            Text(
                text = stringResource(R.string.company_name),
                color = Color.White,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
            )
            
            // 添加一个占位，保持标题居中
            Spacer(modifier = Modifier.width(24.dp))
        }
        
        // 耳机图片 - 使用默认图片
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(2f),
            contentAlignment = Alignment.Center
        ) {
            // 简化图片加载逻辑，统一使用本地资源图片
            Image(
                painter = painterResource(id = R.drawable.earbuds),
                contentDescription = stringResource(R.string.earbuds_image),
                contentScale = ContentScale.Fit,
                modifier = Modifier.size(240.dp)
            )
            
            // 显示图片URL提示（如果有）
            if (uiState.image.isNotEmpty()) {
                Log.d("EarbudsDetailScreen", "设备图片URL: ${uiState.image}")
            }
        }
        
        // 解绑设备按钮
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            contentAlignment = Alignment.Center
        ) {
            var showUnbindConfirmDialog by remember { mutableStateOf(false) }
            
            OutlinedButton(
                onClick = { showUnbindConfirmDialog = true },
                border = BorderStroke(1.dp, purpleColor.copy(alpha = 0.7f)),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = purpleColor
                ),
                shape = RoundedCornerShape(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Clear,
                    contentDescription = stringResource(R.string.unbind_device),
                    tint = purpleColor,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = stringResource(R.string.unbind_device),
                    color = purpleColor,
                    fontSize = 14.sp
                )
            }
            
            // 解绑确认对话框
            if (showUnbindConfirmDialog) {
                AlertDialog(
                    onDismissRequest = { showUnbindConfirmDialog = false },
                    title = { Text(stringResource(R.string.unbind_device), color = Color.White) },
                    text = { 
                        Text(
                            stringResource(R.string.unbind_device_confirmation),
                            color = Color.White
                        ) 
                    },
                    confirmButton = {
                        Button(
                            onClick = {
                                // 关闭确认对话框
                                showUnbindConfirmDialog = false
                                
                                // 使用fullUnbindDevice同时完成本地和服务器解绑
                                viewModel.fullUnbindDevice()
                                
                                // 延迟2秒后跳转，给解绑操作留出时间
                                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                    // 解绑后跳转到BluetoothScreen，带上fromUnbind=true参数
                                    navController?.navigate("bluetooth?fromUnbind=true") {
                                        // 清除返回栈，防止用户按返回键时回到已解绑的设备界面
                                        popUpTo("main") { inclusive = true }
                                    }
                                }, 2000) // 2秒延迟
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = purpleColor
                            )
                        ) {
                            Text(stringResource(R.string.confirm_unbind))
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showUnbindConfirmDialog = false },
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = Color.White
                            )
                        ) {
                            Text(stringResource(R.string.cancel))
                        }
                    },
                    containerColor = cardBackgroundColor,
                    shape = RoundedCornerShape(16.dp)
                )
            }
        }
        
        // 电池状态和音量控制
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 设备详情显示
                if (uiState.deviceId.isNotEmpty()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 设备名称
                        Column {
                            Text(
                                text = stringResource(R.string.device_info),
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = uiState.deviceName,
                                color = Color.White.copy(alpha = 0.7f),
                                fontSize = 14.sp
                            )
                        }
                        
                        // 电池信息
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_battery),
                                contentDescription = stringResource(R.string.battery),
                                tint = purpleColor,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "${uiState.battery}%",
                                color = Color.White,
                                fontSize = 14.sp
                            )
                        }
                    }
                    
                    // 固件版本
                    if (uiState.firmwareVersion.isNotEmpty()) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.firmware_version_with_value, uiState.firmwareVersion),
                                color = Color.White.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    Divider(
                        color = Color.White.copy(alpha = 0.1f),
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 音量控制
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 减小音量按钮
                    Icon(
                        imageVector = Icons.Default.VolumeDown,
                        contentDescription = stringResource(R.string.decrease_volume),
                        tint = purpleColor,
                        modifier = Modifier
                            .size(24.dp)
                            .clickable { viewModel.decreaseVolume() }
                    )
                    
                    // 音量滑块
                    Slider(
                        value = volume,
                        onValueChange = { viewModel.setVolume(it) },
                        modifier = Modifier
                            .padding(start = 8.dp, end = 8.dp)
                            .weight(1f),
                        colors = SliderDefaults.colors(
                            thumbColor = purpleColor,
                            activeTrackColor = purpleColor,
                            inactiveTrackColor = Color.DarkGray
                        )
                    )
                    
                    // 增加音量按钮
                    Icon(
                        imageVector = Icons.Default.VolumeUp,
                        contentDescription = stringResource(R.string.increase_volume),
                        tint = purpleColor,
                        modifier = Modifier
                            .size(24.dp)
                            .clickable { viewModel.increaseVolume() }
                    )
                }
                
                // 显示音量百分比
                Text(
                    text = stringResource(R.string.volume_percentage, (volume * 100).toInt()),
                    color = Color.White,
                    fontSize = 14.sp,
                    modifier = Modifier.align(Alignment.End)
                )
            }
        }
        
        // 功能区域
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 查找耳机卡片
            Card(
                modifier = Modifier
                    .weight(1f)
                    .height(180.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.find_earbuds),
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 地址信息 - 使用接口返回的位置信息
                    Text(
                        text = uiState.location.takeIf { it.isNotEmpty() } 
                            ?: stringResource(R.string.default_location),
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 14.sp,
                        lineHeight = 20.sp
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 位置图标
                    Icon(
                        painter = painterResource(id = R.drawable.ic_location),
                        contentDescription = stringResource(R.string.location),
                        tint = Color(0xFFFF9800),
                        modifier = Modifier
                            .size(28.dp)
                            .align(Alignment.End)
                    )
                }
            }
            
            // 右侧功能卡片区域
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 会员到期时间卡片 (原声音特效卡片)
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = stringResource(R.string.device_name),
                                color = Color.White,
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            )
                            
                            Spacer(modifier = Modifier.height(4.dp))
                            
                            Text(
                                // 显示实际设备名称，如果接口没有返回则显示默认值"T06"
                                text = uiState.deviceName.takeIf { it.isNotEmpty() && it != stringResource(R.string.unknown_device) } ?: stringResource(R.string.default_device_model),
                                color = Color.White.copy(alpha = 0.7f),
                                fontSize = 14.sp
                            )
                        }
                        
                        Icon(
                            imageVector = Icons.Outlined.Headphones,
                            contentDescription = stringResource(R.string.earbuds),
                            tint = Color.White,
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }
                
                // 常见问题卡片 (原快捷操作卡片)
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp)
                        .clickable { navController?.navigate("faq") },
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.faq),
                            color = Color.White,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = stringResource(R.string.faq),
                            tint = Color(0xFF2196F3),
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 更多设置标题
        Text(
            text = stringResource(R.string.more_settings),
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(horizontal = 16.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 系统蓝牙设置卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp)
                .clickable { viewModel.openBluetoothSettings() },
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Menu,
                        contentDescription = stringResource(R.string.bluetooth_settings),
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Text(
                        text = stringResource(R.string.bluetooth_connection_management),
                        color = Color.White,
                        fontSize = 16.sp,
                        modifier = Modifier.padding(start = 12.dp)
                    )
                }
                
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = stringResource(R.string.view_more),
                    tint = Color.White.copy(alpha = 0.7f),
                    modifier = Modifier.size(24.dp)
                )
            }
        }

        // 腾讯云语音识别测试卡片
//        Card(
//            modifier = Modifier
//                .fillMaxWidth()
//                .padding(horizontal = 16.dp, vertical = 8.dp)
//                .clickable { navController?.navigate("tencent_cloud_asr") },
//            shape = RoundedCornerShape(16.dp),
//            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
//        ) {
//            Row(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(16.dp),
//                horizontalArrangement = Arrangement.SpaceBetween,
//                verticalAlignment = Alignment.CenterVertically
//            ) {
//                Row(
//                    verticalAlignment = Alignment.CenterVertically
//                ) {
//                    Icon(
//                        imageVector = Icons.Default.VolumeUp,
//                        contentDescription = stringResource(R.string.tencent_cloud_asr),
//                        tint = Color.White,
//                        modifier = Modifier.size(24.dp)
//                    )
//
//                    Text(
//                        text = stringResource(R.string.tencent_cloud_asr),
//                        color = Color.White,
//                        fontSize = 16.sp,
//                        modifier = Modifier.padding(start = 12.dp)
//                    )
//                }
//
//                Icon(
//                    imageVector = Icons.Default.ChevronRight,
//                    contentDescription = stringResource(R.string.view_more),
//                    tint = Color.White.copy(alpha = 0.7f),
//                    modifier = Modifier.size(24.dp)
//                )
//            }
//        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 使用公共底部导航栏
        CommonBottomNavBar(
            navController = navController,
            currentRoute = "earbuds_detail"
        )
    }
    
    // 声音特效对话框
    if (showEffectsDialog) {
        SoundEffectsDialog(
            onDismiss = { showEffectsDialog = false },
            onSelectEffect = { effect ->
                // 仅显示已选择效果的Toast，不实际应用效果
                Log.d("EarbudsDetailScreen", "已选择声音特效: $effect")
                showEffectsDialog = false
            }
        )
    }
    
    // 显示应用更新弹窗（仅当showUpdateDialog为true且updateInfo不为null时显示）
    if (showUpdateDialog && updateInfo != null) {
        AppUpdateDialog(
            updateInfo = updateInfo!!,
            onDownload = {
                appUpdateViewModel.downloadApp()
            },
            onDismiss = {
                appUpdateViewModel.dismissUpdateDialog()
            }
        )
    }
}

// 触发显示更新弹窗的扩展函数（可以在需要时从外部调用）
fun EarbudsViewModel.showAppUpdateDialog(appUpdateViewModel: AppUpdateViewModel) {
    appUpdateViewModel.showUpdateDialog()
}

@Composable
fun SoundEffectsDialog(
    onDismiss: () -> Unit,
    onSelectEffect: (String) -> Unit
) {
    val context = LocalContext.current
    val effectsList = listOf(
        stringResource(R.string.sound_effect_standard), 
        stringResource(R.string.sound_effect_pop), 
        stringResource(R.string.sound_effect_classic), 
        stringResource(R.string.sound_effect_rock), 
        stringResource(R.string.sound_effect_jazz), 
        stringResource(R.string.sound_effect_bass)
    )
    var selectedEffect by remember { mutableStateOf(context.getString(R.string.sound_effect_standard)) }
    
    // 半透明背景遮罩
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onDismiss() },
        contentAlignment = Alignment.Center
    ) {
        // 对话框卡片
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .clickable { /* 拦截点击，防止关闭对话框 */ },
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFF272636))
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = stringResource(R.string.select_sound_effect),
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 效果列表
                effectsList.forEach { effect ->
                    androidx.compose.material3.RadioButton(
                        selected = effect == selectedEffect,
                        onClick = { selectedEffect = effect },
                        colors = androidx.compose.material3.RadioButtonDefaults.colors(
                            selectedColor = Color(0xFF8364FD),
                            unselectedColor = Color.White
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                            .then(
                                Modifier.clickable { selectedEffect = effect }
                            )
                    )
                    
                    Text(
                        text = effect,
                        color = Color.White,
                        fontSize = 16.sp,
                        modifier = Modifier.padding(start = 36.dp, bottom = 8.dp, top = -24.dp)
                    )
                    
                    if (effect != effectsList.last()) {
                        Divider(
                            color = Color.White.copy(alpha = 0.1f),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    // 取消按钮
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color.White
                        )
                    ) {
                        Text(stringResource(R.string.cancel))
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // 应用按钮
                    androidx.compose.material3.Button(
                        onClick = { onSelectEffect(selectedEffect) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF8364FD)
                        )
                    ) {
                        Text(stringResource(R.string.apply_settings))
                    }
                }
            }
        }
    }
} 