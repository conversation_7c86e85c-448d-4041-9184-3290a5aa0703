package com.example.llya.ui

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.data.LanguageModel
import com.example.llya.network.LanguageListState
import com.example.llya.network.LanguageManager
import com.example.llya.utils.LanguageUtils
import com.example.llya.utils.LanguageHelper
import com.example.llya.utils.findActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.ui.graphics.toArgb
import androidx.core.view.WindowCompat
import androidx.compose.foundation.isSystemInDarkTheme
import android.content.res.Configuration

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageSettingsScreen(
    navController: NavController? = null,
    onLanguageSelected: (String) -> Unit = {}
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val cardBackgroundColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // 配置状态栏和导航栏颜色
    val activity = context.findActivity()
    activity?.let {
        val window = it.window
        
        // 设置状态栏和导航栏颜色
        window.statusBarColor = backgroundColor.toArgb()
        window.navigationBarColor = backgroundColor.toArgb()
        
        // 强制使用浅色图标，适合深色背景
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = false
            isAppearanceLightNavigationBars = false
        }
        
        // 确保设置生效
        window.decorView.systemUiVisibility = 0
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
    }
    
    // 获取当前语言
    val currentLanguage = remember { LanguageUtils.getCurrentLanguageDisplayName(context) }
    
    // 语言管理器
    val languageManager = remember { LanguageManager.getInstance(context) }
    
    // 收集语言列表状态
    val languageListState by languageManager.languageListState.collectAsState()
    
    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }
    var isSearchActive by remember { mutableStateOf(false) }
    
    // 显示语言切换成功的提示
    var showToast by remember { mutableStateOf(false) }
    var toastMessage by remember { mutableStateOf("") }
    
    // 刷新语言列表 - 显式声明为函数类型
    val refreshLanguages: () -> Unit = {
        scope.launch {
            // 显示提示信息
            val message = if (LanguageUtils.getCurrentLanguageDisplayName(context).contains("中文")) 
                "正在从服务器获取最新语言列表..." 
                else "Fetching latest languages from server..."
            
            toastMessage = message
            showToast = true
            
            // 强制从API获取最新数据
            languageManager.fetchLanguages(forceRefresh = true)
        }
    }
    
    // 所有支持的语言列表
    val allLanguages = remember(languageListState) {
        when (languageListState) {
            is LanguageListState.Success -> {
                // 从API获取的语言列表
                (languageListState as LanguageListState.Success).languages.map { it.languageName }
            }
            else -> {
                // 使用LanguageUtils默认语言列表
                LanguageUtils.supportedLanguages.values.toList()
            }
        }
    }
    
    // 显示加载中
    val isLoading = languageListState is LanguageListState.Loading
    
    // 筛选后的语言列表
    val filteredLanguages = if (searchQuery.isBlank()) {
        allLanguages
    } else {
        allLanguages.filter { it.contains(searchQuery, ignoreCase = true) }
    }
    
    // 显示Toast提示
    if (showToast) {
        LaunchedEffect(key1 = showToast) {
            delay(2000)
            showToast = false
        }
    }
    
    // 启动时加载语言列表 - 修改为强制刷新，每次都从API获取
    LaunchedEffect(Unit) {
        // 强制从API获取最新语言列表，不使用缓存
        languageManager.fetchLanguages(forceRefresh = true)
        
        // 显示正在刷新的提示
        val message = if (LanguageUtils.getCurrentLanguageDisplayName(context).contains("中文")) 
            "正在从服务器获取最新语言列表..." 
            else "Fetching latest languages from server..."
        
        toastMessage = message
        showToast = true
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部导航栏
        if (isSearchActive) {
            // 搜索顶部栏
            TextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(64.dp),
                placeholder = { Text(stringResource(R.string.search), color = Color.White.copy(alpha = 0.5f)) },
                leadingIcon = {
                    IconButton(onClick = { isSearchActive = false; searchQuery = "" }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back),
                            tint = Color.White
                        )
                    }
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(onClick = { searchQuery = "" }) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = stringResource(R.string.clear_history),
                                tint = Color.White
                            )
                        }
                    }
                },
                colors = TextFieldDefaults.textFieldColors(
                    containerColor = backgroundColor,
                    cursorColor = purpleColor,
                    focusedIndicatorColor = purpleColor,
                    unfocusedIndicatorColor = Color.Transparent
                ),
                singleLine = true
            )
        } else {
            // 常规顶部栏
            TopAppBar(
                title = { 
                    Text(
                        text = stringResource(R.string.language_settings),
                        color = Color.White
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController?.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back),
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 添加刷新按钮
                    IconButton(onClick = refreshLanguages) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "刷新语言列表",
                            tint = Color.White
                        )
                    }
                    IconButton(onClick = { isSearchActive = true }) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = stringResource(R.string.search),
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = backgroundColor,
                    titleContentColor = Color.White
                )
            )
        }
        
        // 当前语言卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = cardBackgroundColor
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Language,
                    contentDescription = stringResource(R.string.current_language),
                    tint = purpleColor,
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column {
                    Text(
                        text = stringResource(R.string.current_language),
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 14.sp
                    )
                    
                    Text(
                        text = currentLanguage,
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
        
        // 支持的语言列表
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = cardBackgroundColor
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Text(
                    text = stringResource(R.string.select_language),
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                when {
                    // 加载中显示进度条
                    isLoading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(color = purpleColor)
                        }
                    }
                    // 加载失败显示错误信息
                    languageListState is LanguageListState.Error -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = (languageListState as LanguageListState.Error).message,
                                    color = Color.Red,
                                    fontSize = 16.sp,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                Button(
                                    onClick = refreshLanguages,
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = purpleColor
                                    )
                                ) {
                                    Text("重试")
                                }
                            }
                        }
                    }
                    // 没有匹配的语言
                    filteredLanguages.isEmpty() -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.no_matching_languages),
                                color = Color.White.copy(alpha = 0.7f),
                                fontSize = 16.sp
                            )
                        }
                    }
                    // 显示语言列表
                    else -> {
                        LazyColumn(
                            modifier = Modifier.fillMaxSize()
                        ) {
                            items(filteredLanguages) { language ->
                                LanguageItem(
                                    language = language,
                                    isSelected = language == currentLanguage,
                                    purpleColor = purpleColor,
                                    onClick = {
                                        if (language != currentLanguage) {
                                            // 使用LanguageUtils切换语言，标记为用户手动选择
                                            val result = LanguageUtils.setLanguage(context, language, true) // true=用户选择
                                            
                                            // 调用回调函数
                                            if (result) {
                                                onLanguageSelected(language)
                                                
                                                // 显示切换成功提示
                                                toastMessage = context.getString(
                                                    R.string.language_changed, 
                                                    language
                                                )
                                                showToast = true
                                                
                                                // 重启应用以应用新的语言设置
                                                scope.launch {
                                                    delay(800) // 短暂延迟，让用户看到Toast消息
                                                    
                                                    // 保存系统栏颜色偏好
                                                    val prefs = context.getSharedPreferences("ui_settings", Context.MODE_PRIVATE)
                                                    prefs.edit()
                                                        .putInt("status_bar_color", backgroundColor.toArgb())
                                                        .putInt("navigation_bar_color", backgroundColor.toArgb())
                                                        .putBoolean("is_dark_theme", true) // 强制使用深色主题设置
                                                        .apply()
                                                    
                                                    // 直接使用LanguageHelper.restartApp方法重启应用
                                                    // 不再尝试使用activity.recreate()
                                                    com.example.llya.utils.LanguageHelper.restartApp(context)
                                                }
                                            }
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 显示Toast
    if (showToast) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 32.dp),
            contentAlignment = Alignment.BottomCenter
        ) {
            Card(
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF444444)
                )
            ) {
                Text(
                    text = toastMessage,
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 24.dp, vertical = 12.dp),
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Composable
fun LanguageItem(
    language: String,
    isSelected: Boolean,
    purpleColor: Color,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 12.dp, horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = language,
            color = if (isSelected) purpleColor else Color.White,
            fontSize = 16.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = purpleColor
            )
        }
    }
    
    Divider(
        color = Color.White.copy(alpha = 0.1f),
        thickness = 0.5.dp,
        modifier = Modifier.padding(start = 8.dp, end = 8.dp)
    )
}

/**
 * 计算颜色的亮度值
 * 返回0到1之间的值，0为黑色，1为白色
 */
private fun calculateLuminance(color: Color): Float {
    val red = color.red * 0.2126f
    val green = color.green * 0.7152f
    val blue = color.blue * 0.0722f
    return red + green + blue
} 