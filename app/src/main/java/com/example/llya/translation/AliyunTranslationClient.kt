package com.example.llya.translation

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import java.net.URLEncoder

/**
 * 阿里云翻译客户端，使用REST API接口
 */
class AliyunTranslationClient(
    private val accessKeyId: String,
    private val accessKeySecret: String,
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "AliyunTranslation"
        // 阿里云翻译服务地址 - 杭州区域
        private const val API_URL = "http://mt.cn-hangzhou.aliyuncs.com/api/translate/web/ecommerce"
        // 阿里云支持的所有语言列表 - 来自官方文档：https://help.aliyun.com/zh/machine-translation/developer-reference/machine-translation-language-code-list
        val SUPPORTED_LANGUAGES = mapOf(
            // 常用语言
            "zh" to "中文简体",
            "zh-tw" to "中文繁体",
            "en" to "英语",
            "ja" to "日语",
            "ko" to "韩语",
            "fr" to "法语",
            "es" to "西班牙语",
            "it" to "意大利语",
            "de" to "德语",
            "tr" to "土耳其语",
            "ru" to "俄语",
            "pt" to "葡萄牙语",
            "vi" to "越南语",
            "id" to "印尼语",
            "th" to "泰语",
            "ms" to "马来语",
            "ar" to "阿拉伯语",
            "hi" to "印地语",
            
            // A开头语言
            "ab" to "阿布哈兹语",
            "sq" to "阿尔巴尼亚语",
            "ak" to "阿肯语",
            "an" to "阿拉贡语",
            "am" to "阿姆哈拉语",
            "as" to "阿萨姆语",
            "az" to "阿塞拜疆语",
            "ast" to "阿斯图里亚斯语",
            "nch" to "阿兹特克语",
            "ee" to "埃维语",
            "ay" to "艾马拉语",
            "ga" to "爱尔兰语",
            "et" to "爱沙尼亚语",
            "oj" to "奥杰布瓦语",
            "oc" to "奥克语",
            "or" to "奥里亚语",
            "om" to "奥罗莫语",
            "os" to "奥塞梯语",
            
            // B开头语言
            "tpi" to "巴布亚皮钦语",
            "ba" to "巴什基尔语",
            "eu" to "巴斯克语",
            "be" to "白俄罗斯语",
            "ber" to "柏柏尔语",
            "bm" to "班巴拉语",
            "pag" to "邦阿西楠语",
            "bg" to "保加利亚语",
            "se" to "北萨米语",
            "bem" to "本巴语",
            "byn" to "比林语",
            "bi" to "比斯拉马语",
            "bal" to "俾路支语",
            "is" to "冰岛语",
            "pl" to "波兰语",
            "bs" to "波斯尼亚语",
            "fa" to "波斯语",
            "bho" to "博杰普尔语",
            "br" to "布列塔尼语",
            
            // C开头语言
            "ch" to "查莫罗语",
            "cbk" to "查瓦卡诺语",
            "cv" to "楚瓦什语",
            "ts" to "聪加语",
            
            // D开头语言
            "tt" to "鞑靼语",
            "da" to "丹麦语",
            "shn" to "掸语",
            "tet" to "德顿语",
            "nds" to "低地德语",
            "sco" to "低地苏格兰语",
            "dv" to "迪维西语",
            "kdx" to "侗语",
            "dtp" to "杜順語",
            
            // F开头语言
            "fo" to "法罗语",
            "sa" to "梵语",
            "fil" to "菲律宾语",
            "fj" to "斐济语",
            "fi" to "芬兰语",
            "fur" to "弗留利语",
            
            // G开头语言
            "gl" to "加利西亚语",
            "ca" to "加泰罗尼亚语",
            "gd" to "苏格兰盖尔语",
            "cs" to "捷克语",
            "ny" to "齐切瓦语",
            "rom" to "吉普赛语",
            "kab" to "卡拜尔语",
            "csb" to "卡舒比语",
            "kk" to "哈萨克语",
            "kl" to "格陵兰语",
            "gu" to "古吉拉特语",
            "gn" to "瓜拉尼语",
            
            // H开头语言
            "ht" to "海地克里奥尔语",
            "kr" to "卡努里语",
            "hak" to "客家语",
            "km" to "高棉语",
            "kj" to "宽亚玛语",
            "co" to "科西嘉语",
            "crh" to "克里米亚鞑靼语",
            "kw" to "康沃尔语",
            "kv" to "科米语",
            "kg" to "刚果语",
            "gom" to "孔卡尼语",
            "xh" to "科萨语",
            "ku" to "库尔德语",
            "qu" to "克丘亚语",
            "ky" to "吉尔吉斯语",
            "rw" to "卢旺达语",
            "gl" to "加利西亚语",
            
            // L开头语言
            "lv" to "拉脱维亚语",
            "ltg" to "拉特加莱语",
            "lo" to "老挝语",
            "la" to "拉丁语",
            "ln" to "林加拉语",
            "lt" to "立陶宛语",
            "li" to "林堡语",
            "lu" to "卢巴-加丹加语",
            "lg" to "卢干达语",
            "lb" to "卢森堡语",
            "rue" to "卢森尼亚语",
            "ro" to "罗马尼亚语",
            "rm" to "罗曼什语",
            "loz" to "洛齐语",
            
            // M开头语言
            "mt" to "马耳他语",
            "mg" to "马达加斯加语",
            "mr" to "马拉地语",
            "ml" to "马拉雅拉姆语",
            "mi" to "毛利语",
            "mk" to "马其顿语",
            "mh" to "马绍尔语",
            "bn" to "孟加拉语",
            "mfe" to "毛里求斯克里奥尔语",
            "my" to "缅甸语",
            "mn" to "蒙古语",
            "hmn" to "苗语",
            
            // N开头语言
            "na" to "瑙鲁语",
            "af" to "南非荷兰语",
            "st" to "南索托语",
            "ne" to "尼泊尔语",
            "pcm" to "尼日利亚皮钦语",
            "no" to "挪威语",
            "nn" to "新挪威语",
            
            // P开头语言
            "pap" to "帕皮阿门托语",
            "ps" to "普什图语",
            
            // Q开头语言
            "chr" to "切罗基语",
            "cu" to "教会斯拉夫语",
            
            // R开头语言
            "sm" to "萨摩亚语",
            "sg" to "桑戈语",
            "si" to "僧伽罗语",
            "sc" to "撒丁语",
            "sr" to "塞尔维亚语",
            "sk" to "斯洛伐克语",
            "sl" to "斯洛文尼亚语",
            "so" to "索马里语",
            "sw" to "斯瓦希里语",
            
            // T开头语言
            "tl" to "他加禄语",
            "tg" to "塔吉克语",
            "ty" to "塔希提语",
            "te" to "泰卢固语",
            "ta" to "泰米尔语",
            "to" to "汤加语(汤加群岛)",
            "toi" to "汤加语(赞比亚)",
            "ti" to "提格雷尼亚语",
            "tvl" to "图瓦卢语",
            "tyv" to "图瓦语",
            "tk" to "土库曼语",
            
            // W开头语言
            "wa" to "瓦隆语",
            "war" to "瓦瑞语(菲律宾)",
            "cy" to "威尔士语",
            "ve" to "文达语",
            "vo" to "沃拉普克语",
            "wo" to "沃洛夫语",
            "udm" to "乌德穆尔特语",
            "ur" to "乌尔都语",
            "uz" to "乌孜别克语",
            
            // X开头语言
            "ie" to "西方国际语",
            "fy" to "西弗里斯兰语",
            "szl" to "西里西亚语",
            "he" to "希伯来语",
            "hil" to "希利盖农语",
            "haw" to "夏威夷语",
            "el" to "现代希腊语",
            "lfn" to "新共同语言",
            "sd" to "信德语",
            "hu" to "匈牙利语",
            "sn" to "修纳语",
            "ceb" to "宿务语",
            "syr" to "叙利亚语",
            "su" to "巽他语",
            
            // Y开头语言
            "hy" to "亚美尼亚语",
            "ace" to "亚齐语",
            "iba" to "伊班语",
            "ig" to "伊博语",
            "io" to "伊多语",
            "ilo" to "伊洛卡诺语",
            "iu" to "伊努克提图特语",
            "yi" to "意第绪语",
            "ia" to "因特语",
            "inh" to "印古什语",
            "yo" to "约鲁巴语",
            
            // Z开头语言
            "zza" to "扎扎其语",
            "jv" to "爪哇语",
            "yue" to "中文粤语",
            "zu" to "祖鲁语",
            
            // 特殊代码
            "auto" to "自动检测"  // 自动检测源语言
        )
    }

    // OkHttp客户端
    private val client = OkHttpClient.Builder()
        .connectTimeout(5, TimeUnit.SECONDS)
        .readTimeout(5, TimeUnit.SECONDS)
        .build()

    // 翻译结果流
    private val _translationResult = MutableStateFlow<String>("")
    val translationResult: StateFlow<String> = _translationResult

    // 错误信息流
    private val _errorMessage = MutableStateFlow<String>("")
    val errorMessage: StateFlow<String> = _errorMessage

    /**
     * 执行翻译
     * @param text 要翻译的文本
     * @param sourceLanguage 源语言代码，如"zh"、"en"等
     * @param targetLanguage 目标语言代码
     */
    fun translate(text: String, sourceLanguage: String, targetLanguage: String) {
        if (text.isEmpty()) {
            _translationResult.value = ""
            return
        }

        coroutineScope.launch(Dispatchers.IO) {
            try {
                // 构建请求体
                val requestBody = JSONObject().apply {
                    put("FormatType", "text")
                    put("SourceLanguage", sourceLanguage)
                    put("TargetLanguage", targetLanguage)
                    put("SourceText", text)
                    put("Scene", "general") // 通用场景
                }.toString()

                Log.d(TAG, "翻译请求: $requestBody")

                // 创建请求体
                val mediaType = "application/json; charset=utf-8".toMediaType()
                val okHttpRequestBody = requestBody.toRequestBody(mediaType)

                // 生成GMT格式的日期字符串（阿里云API要求）
                val dateFormat = SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US)
                dateFormat.timeZone = TimeZone.getTimeZone("GMT")
                val dateString = dateFormat.format(Date())
                
                // 准备签名所需的请求头
                val signatureNonce = UUID.randomUUID().toString()
                val contentType = "application/json; charset=utf-8"
                val accept = "application/json"
                val acsSignatureMethod = "HMAC-SHA1"
                val acsVersion = "2019-01-02"
                
                // 构建用于签名的头信息Map（按照阿里云API要求的顺序）
                val headersToSign = mapOf(
                    "Content-Type" to contentType,
                    "Accept" to accept,
                    "Date" to dateString,
                    "x-acs-signature-method" to acsSignatureMethod,
                    "x-acs-signature-nonce" to signatureNonce,
                    "x-acs-version" to acsVersion
                )
                
                // 生成签名
                val signature = generateSignature(
                    method = "POST",
                    headers = headersToSign,
                    path = "/api/translate/web/ecommerce"
                )

                // 构建请求
                val request = Request.Builder()
                    .url(API_URL)
                    .post(okHttpRequestBody)
                    .addHeader("Content-Type", contentType)
                    .addHeader("Accept", accept)
                    .addHeader("Date", dateString)
                    .addHeader("x-acs-signature-method", acsSignatureMethod)
                    .addHeader("x-acs-signature-nonce", signatureNonce)
                    .addHeader("x-acs-version", acsVersion)
                    .addHeader("Authorization", signature)
                    .build()
                
                Log.d(TAG, "发送请求到: ${request.url}")
                Log.d(TAG, "请求头: ${request.headers}")
                
                // 发送请求
                client.newCall(request).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "翻译请求失败: ${e.message}", e)
                        coroutineScope.launch {
                            _errorMessage.value = "翻译失败: ${e.message}"
                        }
                    }

                    override fun onResponse(call: Call, response: Response) {
                        try {
                            val responseString = response.body?.string()
                            Log.d(TAG, "翻译API响应: $responseString")

                            if (responseString != null) {
                                try {
                                    val jsonObject = JSONObject(responseString)
                                    
                                    if (jsonObject.has("Data")) {
                                        // 成功响应
                                        val dataObj = jsonObject.optJSONObject("Data")
                                        if (dataObj != null && dataObj.has("Translated")) {
                                            // 从Data对象中提取Translated字段
                                            val translatedText = dataObj.getString("Translated")
                                            coroutineScope.launch {
                                                _translationResult.value = translatedText
                                            }
                                        } else {
                                            // 如果Data不是一个对象或没有Translated字段
                                            val dataString = jsonObject.getString("Data")
                                            coroutineScope.launch {
                                                _translationResult.value = dataString
                                            }
                                        }
                                    } else if (jsonObject.has("Code")) {
                                        // 错误响应
                                        val errorCode = jsonObject.optString("Code", "未知错误码")
                                        val errorMsg = jsonObject.optString("Message", "未知错误")
                                        
                                        Log.e(TAG, "翻译API错误: $errorCode - $errorMsg")
                                        coroutineScope.launch {
                                            _errorMessage.value = "翻译失败: $errorCode - $errorMsg"
                                        }
                                    } else if (jsonObject.has("errorCode")) {
                                        // 另一种格式的错误响应
                                        val errorCode = jsonObject.optString("errorCode", "未知错误码")
                                        val errorMsg = jsonObject.optString("errorMsg", "未知错误")
                                        
                                        Log.e(TAG, "翻译API错误: $errorCode - $errorMsg")
                                        coroutineScope.launch {
                                            _errorMessage.value = "翻译失败: $errorCode - $errorMsg"
                                        }
                                    } else {
                                        // 未知响应格式
                                        Log.e(TAG, "未识别的响应格式: $responseString")
                                        coroutineScope.launch {
                                            _errorMessage.value = "翻译失败: 未识别的响应格式"
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "解析响应JSON失败", e)
                                    coroutineScope.launch {
                                        _errorMessage.value = "处理翻译响应失败: ${e.message}"
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "处理翻译响应失败", e)
                            coroutineScope.launch {
                                _errorMessage.value = "处理翻译响应失败: ${e.message}"
                            }
                        }
                    }
                })

            } catch (e: Exception) {
                Log.e(TAG, "构建翻译请求失败", e)
                _errorMessage.value = "构建翻译请求失败: ${e.message}"
            }
        }
    }

    /**
     * 生成阿里云API签名，严格按照阿里云API要求的格式
     * @param method HTTP方法
     * @param headers 请求头
     * @param path API路径
     */
    private fun generateSignature(method: String, headers: Map<String, String>, path: String): String {
        try {
            // 按照阿里云API的要求构建待签名字符串
            val stringToSign = buildString {
                append("$method\n")                // HTTP方法
                append("${headers["Accept"] ?: ""}\n")  // Accept头
                append("\n")                       // Content-MD5 (空)
                append("${headers["Content-Type"] ?: ""}\n") // Content-Type头
                append("${headers["Date"] ?: ""}\n")   // Date头
                
                // 按字母顺序排序的规范化头（以x-acs-开头的头）
                val canonicalizedHeaders = headers.entries
                    .filter { it.key.startsWith("x-acs-") }
                    .sortedBy { it.key }
                    .joinToString("\n") { "${it.key}:${it.value}" }
                
                if (canonicalizedHeaders.isNotEmpty()) {
                    append("$canonicalizedHeaders\n")
                }
                
                // 规范化的资源路径
                append(path)
            }
            
            Log.d(TAG, "待签名字符串:\n$stringToSign")

            // 使用阿里云AccessKey和SecretKey生成签名
            val mac = Mac.getInstance("HmacSHA1")
            val keySpec = SecretKeySpec(accessKeySecret.toByteArray(), "HmacSHA1")
            mac.init(keySpec)
            
            // 对待签名字符串进行签名
            val signature = mac.doFinal(stringToSign.toByteArray())
            
            // Base64编码
            val encodedSignature = Base64.getEncoder().encodeToString(signature)
            
            // 构建认证头
            return "acs $accessKeyId:$encodedSignature"
        } catch (e: Exception) {
            Log.e(TAG, "生成签名失败", e)
            throw RuntimeException("生成签名失败: ${e.message}")
        }
    }

    /**
     * 清除翻译结果
     */
    fun clearResult() {
        _translationResult.value = ""
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = ""
    }
} 