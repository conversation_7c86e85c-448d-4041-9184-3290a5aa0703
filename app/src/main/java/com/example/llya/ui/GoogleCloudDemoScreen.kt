package com.example.llya.ui

import android.Manifest
import android.util.Log
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.MainActivity
import com.example.llya.network.LanguageInfo
import com.example.llya.network.VoiceInfo
import com.example.llya.viewmodel.GoogleCloudServiceViewModel
import com.example.llya.viewmodel.GoogleCloudServiceViewModel.PlaybackMode
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.example.llya.R
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import kotlinx.coroutines.delay
import com.example.llya.viewmodel.HistoryViewModel
import com.example.llya.data.HistoryRecord
import com.example.llya.ui.HistoryItemCard
import com.example.llya.ui.PlaybackModeSelectionDialog
import kotlinx.coroutines.flow.StateFlow
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun GoogleCloudDemoScreen(
    viewModel: GoogleCloudServiceViewModel = viewModel(),
    onNavigateBack: () -> Unit,
    navController: androidx.navigation.NavController? = null,
    speechViewModel: SpeechRecognitionViewModel = viewModel()
) {
    val context = LocalContext.current

    // 收集UI状态
    val isRecording by viewModel.isRecording.collectAsState()
    val recognizedText by viewModel.recognizedText.collectAsState()
    val translatedText by viewModel.translatedText.collectAsState()
    val isTranslating by viewModel.isTranslating.collectAsState()
    val isSpeaking by viewModel.isSpeaking.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    val supportedLanguages by viewModel.supportedLanguages.collectAsState()
    val availableVoices by viewModel.availableVoices.collectAsState()

    val selectedSourceLanguage by viewModel.selectedSourceLanguage.collectAsState()
    val selectedTargetLanguage by viewModel.selectedTargetLanguage.collectAsState()
    val selectedVoice by viewModel.selectedVoice.collectAsState()

    // 获取播放模式
    val playbackMode by viewModel.playbackMode.collectAsState()

    // 收集完整的识别文本和翻译结果
    val fullRecognizedText by viewModel.fullRecognizedText.collectAsState()
    val recognizedSentences by viewModel.recognizedSentences.collectAsState()
    val translatedSentences by viewModel.translatedSentences.collectAsState()

    // 下拉菜单状态
    var sourceLanguageMenuExpanded by remember { mutableStateOf(false) }
    var targetLanguageMenuExpanded by remember { mutableStateOf(false) }
    var voiceMenuExpanded by remember { mutableStateOf(false) }

    // 播放模式选择对话框
    var showPlaybackModeDialog by remember { mutableStateOf(false) }

    // 权限状态
    val microphonePermissionState = rememberPermissionState(Manifest.permission.RECORD_AUDIO)

    // 状态收集
    val recognitionResult by speechViewModel.recognitionResult.collectAsState()
    val lastValidResult by speechViewModel.lastValidResult.collectAsState()
    val cumulativeContent by speechViewModel.cumulativeContent.collectAsState()
    val selectedLanguage by speechViewModel.selectedLanguage.collectAsState()
    val transcriptionStats by speechViewModel.transcriptionStats.collectAsState()
    val currentAsrServiceName by speechViewModel.currentAsrServiceName.collectAsState()

    // 使用累积内容替代recognizedText，确保显示所有识别的文本
    val displayText = cumulativeContent.ifEmpty { recognizedText }

    // 添加一个状态，用于追踪上次翻译的累积文本
    var lastTranslatedCumulativeText by remember { mutableStateOf("") }

    // 在录音状态变化时重置累积文本翻译状态
    LaunchedEffect(isRecording) {
        if (isRecording) {
            // 开始新录音时，重置上次翻译的累积文本状态
            lastTranslatedCumulativeText = ""

            // 清除之前的识别文本，确保每次开始识别时都是全新的文本
            if (currentAsrServiceName == "Google Cloud") {
                // 清除谷歌云的识别文本
                viewModel.clearRecognizedText()
                // 清除累积内容
                speechViewModel.clearCumulativeContent()
                Log.d("GoogleCloudDemoScreen", "开始新录音会话，已清除之前的识别文本和累积内容")
            } else {
                Log.d("GoogleCloudDemoScreen", "开始新录音会话，重置翻译状态")
            }
        } else {
            // 录音结束时，确保累积文本已被正确处理
            Log.d("GoogleCloudDemoScreen", "录音会话结束，最终累积文本长度: ${cumulativeContent.length}")
        }
    }

    // 在沉默检测到时翻译累积文本
    LaunchedEffect(cumulativeContent, isRecording) {
        if (!isRecording && !isTranslating && cumulativeContent.isNotEmpty()) {
            // 获取当前累积文本
            val currentText = if (currentAsrServiceName == "腾讯云") {
                // 从腾讯云ASR客户端获取累积文本
                viewModel.getTencentAccumulatedText()
            } else {
                // 使用标准累积文本
                cumulativeContent
            }

            // 检查是否有新的内容需要翻译
            if (currentText.isNotEmpty() && currentText != lastTranslatedCumulativeText) {
                // 对于谷歌云，我们只翻译新增的部分，而不是整个累积文本
                if (currentAsrServiceName == "Google Cloud") {
                    // 找出新增的部分
                    val newContent = if (lastTranslatedCumulativeText.isEmpty()) {
                        currentText
                    } else {
                        // 检查新文本是否包含旧文本
                        if (currentText.contains(lastTranslatedCumulativeText)) {
                            // 提取新增部分
                            val newPart = currentText.substring(lastTranslatedCumulativeText.length).trim()
                            if (newPart.isNotEmpty()) {
                                newPart
                            } else {
                                // 没有新内容，跳过翻译
                                Log.d("GoogleCloudDemoScreen", "没有检测到新内容，跳过翻译")
                                return@LaunchedEffect
                            }
                        } else {
                            // 文本结构发生了变化，使用完整的当前文本
                            currentText
                        }
                    }

                    // 检查新内容是否有实质性变化
                    if (newContent.length > 3) {
                        Log.d("GoogleCloudDemoScreen", "检测到新内容，长度: ${newContent.length}, 内容: '${newContent.take(30)}${if (newContent.length > 30) "..." else ""}'")

                        // 短暂延迟以确保不会干扰沉默检测
                        delay(100)

                        // 只翻译新增的部分
                        viewModel.translateRecognizedText(newContent)

                        // 更新已翻译的累积文本
                        lastTranslatedCumulativeText = currentText
                    } else {
                        Log.d("GoogleCloudDemoScreen", "新内容太短，跳过翻译: '$newContent'")
                    }
                } else {
                    // 腾讯云使用原来的逻辑 - 翻译整个累积文本
                    // 检查累积内容是否发生了实质性变化
                    val significantChange = currentText.length - lastTranslatedCumulativeText.length > 5

                    if (significantChange) {
                        // 设置上次翻译的累积文本
                        lastTranslatedCumulativeText = currentText
                        Log.d("GoogleCloudDemoScreen", "检测到累积文本有实质性变化，长度: ${currentText.length}")

                        // 短暂延迟以确保不会干扰沉默检测
                        delay(100)

                        // 翻译整个累积文本
                        if (currentText.isNotEmpty()) {
                            Log.d("GoogleCloudDemoScreen", "翻译累积文本: '${currentText.take(30)}${if (currentText.length > 30) "..." else ""}'")
                            viewModel.translateCumulativeText(currentText)
                        }
                    } else {
                        Log.d("GoogleCloudDemoScreen", "累积文本变化不足以触发翻译，跳过")
                    }
                }
            }
        }
    }

    // 历史记录ViewModel及状态
    val historyViewModel: com.example.llya.viewmodel.HistoryViewModel = viewModel()
    val historyRecords by historyViewModel.historyRecords.collectAsState()
    // 筛选只有翻译文本的记录
    val translationRecords = remember(historyRecords) {
        historyRecords.filter { it.translatedText.isNotEmpty() }.take(5)
    }
    val isHistoryPlaying by historyViewModel.isPlaying.collectAsState()
    val isHistoryLoading by historyViewModel.isLoading.collectAsState()

    // 自动语言检测已禁用
    val autoLanguageDetectionEnabled by viewModel.autoLanguageDetectionEnabled.collectAsState()
    val translationDirection by viewModel.translationDirection.collectAsState()

    // 记录是否已显示权限请求的状态
    val hasShownPermission = remember { mutableStateOf(false) }
    val gcTranslationFailedText = stringResource(id = R.string.gc_translation_failed)
    val gcTranslationErrorText = stringResource(id = R.string.gc_translation_error)

    // 在页面销毁时关闭同声传译模式
    DisposableEffect(Unit) {
        onDispose {
            viewModel.setSimultaneousTranslationMode(false)
            Log.d("GoogleCloudDemoScreen", "已关闭同声传译模式")
        }
    }

    // 在适当的地方添加语言同步逻辑
    LaunchedEffect(Unit) {
        // 启用同声传译模式
        viewModel.setSimultaneousTranslationMode(true)
        Log.d("GoogleCloudDemoScreen", "已启用同声传译模式")

        // 确保初始化时正确设置语言和ASR服务
        speechViewModel.setLanguage(selectedSourceLanguage)

        // 自动语言检测功能已被禁用
        viewModel.setAutoLanguageDetection(false)

        // 如果源语言是中文，目标语言是日语，或者源语言是日语，目标语言是中文
        // 则设置为互译模式
        if ((selectedSourceLanguage == "zh-CN" && selectedTargetLanguage == "ja-JP") ||
            (selectedSourceLanguage == "ja-JP" && selectedTargetLanguage == "zh-CN")) {
            // 启用真正的互译模式
            viewModel.enableBidirectionalTranslation(true)
            Log.d("GoogleCloudDemoScreen", "已启用中日互译模式")
        }

        // 延迟一小段时间，确保ASR服务名称已更新
        delay(100)

        // 记录日志，使用更新后的ASR服务名称
        val updatedAsrServiceName = speechViewModel.currentAsrServiceName.value
        Log.d("GoogleCloudDemoScreen", "初始化语音服务 - 语言: $selectedSourceLanguage, ASR服务: $updatedAsrServiceName")
    }

    // 当语言变化时同步
    LaunchedEffect(selectedSourceLanguage) {
        // 同步当前选择的源语言到SpeechRecognitionViewModel
        // 这会触发基于语言的ASR服务切换
        speechViewModel.setLanguage(selectedSourceLanguage)

        // 延迟一小段时间，确保ASR服务名称已更新
        delay(100)

        // 记录日志，使用更新后的ASR服务名称
        val updatedAsrServiceName = speechViewModel.currentAsrServiceName.value
        Log.d("GoogleCloudDemoScreen", "语音服务更新 - 语言: $selectedSourceLanguage, ASR服务: $updatedAsrServiceName")

        // 检查是否需要启用中日互译模式
        if ((selectedSourceLanguage == "zh-CN" && selectedTargetLanguage == "ja-JP") ||
            (selectedSourceLanguage == "ja-JP" && selectedTargetLanguage == "zh-CN")) {
            // 启用真正的互译模式
            viewModel.enableBidirectionalTranslation(true)
            Log.d("GoogleCloudDemoScreen", "已启用中日互译模式")
        } else {
            // 禁用互译模式
            viewModel.enableBidirectionalTranslation(false)
            Log.d("GoogleCloudDemoScreen", "已禁用中日互译模式")
        }
    }

    LaunchedEffect(selectedTargetLanguage) {
        // 同步当前选择的目标语言到SpeechRecognitionViewModel
        speechViewModel.setTargetLanguage(selectedTargetLanguage)

        // 检查是否需要启用中日互译模式
        if ((selectedSourceLanguage == "zh-CN" && selectedTargetLanguage == "ja-JP") ||
            (selectedSourceLanguage == "ja-JP" && selectedTargetLanguage == "zh-CN")) {
            // 启用真正的互译模式
            viewModel.enableBidirectionalTranslation(true)
            Log.d("GoogleCloudDemoScreen", "已启用中日互译模式")
        } else {
            // 禁用互译模式
            viewModel.enableBidirectionalTranslation(false)
            Log.d("GoogleCloudDemoScreen", "已禁用中日互译模式")
        }
    }

    // 显示错误消息
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            Toast.makeText(context, it, Toast.LENGTH_LONG).show()
            viewModel.clearError()
        }
    }

    // 深色背景色和强调色
    val darkBgColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8364FD)
    val cardBgColor = Color(0xFF33324A)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(darkBgColor)
            .statusBarsPadding()
            // 移除navigationBarsPadding，确保底部控制栏不会被系统导航栏覆盖
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    text = stringResource(R.string.gc_simultaneous_translation),
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = stringResource(R.string.gc_back),
                        tint = Color.White
                    )
                }
            },
            actions = {
                // 添加播放模式选择按钮
                IconButton(onClick = { showPlaybackModeDialog = true }) {
                    Icon(
                        imageVector = when (playbackMode) {
                            PlaybackMode.SILENT -> Icons.Default.VolumeOff
                            PlaybackMode.EARBUDS -> Icons.Default.Headset
                            PlaybackMode.SPEAKER -> Icons.Default.VolumeUp
                            PlaybackMode.DUAL_EARBUDS -> Icons.Default.HeadsetMic
                        },
                        contentDescription = stringResource(R.string.gc_playback_mode),
                        tint = Color.White
                    )
                }

                // 添加导航到历史记录的按钮
                IconButton(onClick = { navController?.navigate("history") }) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = stringResource(R.string.gc_history),
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = darkBgColor,
                titleContentColor = Color.White
            )
        )

        // 语言选择面板
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            color = cardBgColor
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 自动语言检测功能已被移除

                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // 源语言
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .clickable { sourceLanguageMenuExpanded = true },
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = supportedLanguages.find { it.code == selectedSourceLanguage }?.name ?: stringResource(R.string.gc_select_source_language),
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = stringResource(R.string.gc_source_language),
                            color = Color.Gray,
                            fontSize = 12.sp
                        )

                        DropdownMenu(
                            expanded = sourceLanguageMenuExpanded,
                            onDismissRequest = { sourceLanguageMenuExpanded = false },
                            modifier = Modifier.background(cardBgColor)
                        ) {
                            supportedLanguages.forEach { language ->
                                DropdownMenuItem(
                                    text = {
                                        Text(
                                            language.name,
                                            color = Color.White
                                        )
                                    },
                                    onClick = {
                                        viewModel.setSourceLanguage(language.code)
                                        sourceLanguageMenuExpanded = false
                                    },
                                    colors = MenuDefaults.itemColors(
                                        textColor = Color.White
                                    )
                                )
                            }
                        }
                    }

                    // 交换按钮
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(purpleColor)
                            .clickable {
                                val sourceLang = selectedSourceLanguage
                                viewModel.setSourceLanguage(selectedTargetLanguage)
                                viewModel.setTargetLanguage(sourceLang)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_swap),
                            contentDescription = stringResource(R.string.gc_swap_languages),
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    // 目标语言
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .clickable { targetLanguageMenuExpanded = true },
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = supportedLanguages.find { it.code == selectedTargetLanguage }?.name ?: stringResource(R.string.gc_select_target_language),
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = stringResource(R.string.gc_target_language),
                            color = Color.Gray,
                            fontSize = 12.sp
                        )

                        DropdownMenu(
                            expanded = targetLanguageMenuExpanded,
                            onDismissRequest = { targetLanguageMenuExpanded = false },
                            modifier = Modifier.background(cardBgColor)
                        ) {
                            supportedLanguages.forEach { language ->
                                DropdownMenuItem(
                                    text = {
                                        Text(
                                            language.name,
                                            color = Color.White
                                        )
                                    },
                                    onClick = {
                                        viewModel.setTargetLanguage(language.code)
                                        targetLanguageMenuExpanded = false
                                    },
                                    colors = MenuDefaults.itemColors(
                                        textColor = Color.White
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }

        // 今天日期
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            val currentTime = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault()).format(java.util.Date())
            Text(
                text = stringResource(R.string.gc_today_time, currentTime),
                color = Color.Gray,
                fontSize = 14.sp
            )
        }

        // 整个内容区域改为可滚动，确保留出足够空间给底部控制栏
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {

            // 源语言区域
            if (cumulativeContent.isNotEmpty()) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(16.dp))
                            .background(cardBgColor)
                            .padding(16.dp)
                    ) {
                        Column {
                            // 使用直接来源的累积文本
                            val displayFullText = viewModel.fullRecognizedText.collectAsState().value
                            Text(
                                text = displayFullText,
                                color = Color.White,
                                fontSize = 16.sp
                            )

                            if (isRecording) {
                                Spacer(modifier = Modifier.height(4.dp))
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = stringResource(R.string.gc_listening),
                                        color = Color.White.copy(alpha = 0.6f),
                                        fontSize = 12.sp,
                                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                    )

                                    // 添加动态省略号
                                    val infiniteTransition = rememberInfiniteTransition(label = "typingDots")
                                    val dotsAlpha by infiniteTransition.animateFloat(
                                        initialValue = 0.4f,
                                        targetValue = 1.0f,
                                        animationSpec = infiniteRepeatable(
                                            animation = tween(500, easing = LinearEasing),
                                            repeatMode = RepeatMode.Reverse
                                        ),
                                        label = "dotAlpha"
                                    )

                                    Text(
                                        text = "...",
                                        color = Color.White.copy(alpha = dotsAlpha),
                                        fontSize = 12.sp,
                                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // 显示已翻译的句子
            items(translatedSentences.size) { index ->
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(16.dp))
                        .background(purpleColor.copy(alpha = 0.7f))
                        .padding(16.dp)
                ) {
                    Column {
                        // 显示原始句子
                        Text(
                            text = recognizedSentences[index],
                            color = Color.White.copy(alpha = 0.7f),
                            fontSize = 14.sp,
                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                        )

                        Divider(
                            color = Color.White.copy(alpha = 0.3f),
                            modifier = Modifier.padding(vertical = 6.dp)
                        )

                        // 显示翻译结果
                        Text(
                            text = translatedSentences[index],
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
            }
            // 历史记录列表（最近5条）
            if (translationRecords.isNotEmpty()) {
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                    ) {
                        Text(
                            text = "最近翻译记录",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        translationRecords.forEach { record ->
                            HistoryItemCard(
                                record = record,
                                onPlay = {
                                    historyViewModel.playTranslation(record)
                                },
                                isPlaying = isHistoryPlaying && historyViewModel.currentPlayingId.value == record.id,
                                isLoading = isHistoryLoading && historyViewModel.currentLoadingId.value == record.id,
                                purpleColor = purpleColor
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }
            }
        }

        // 底部控制栏 - 确保它始终可见
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            color = cardBgColor
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 录音状态提示
                if (isRecording) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.Red.copy(alpha = 0.1f))
                            .padding(vertical = 4.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.gc_recording),
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧按钮 - 播放功能
                    if (translatedText.isNotEmpty() && !translatedText.startsWith(gcTranslationFailedText) && !translatedText.startsWith(gcTranslationErrorText)) {
                        IconButton(
                            onClick = {
                                if (isSpeaking) {
                                    viewModel.stopSpeaking()
                                } else {
                                    viewModel.speakTranslatedText()
                                }
                            },
                            modifier = Modifier
                                .size(50.dp)
                                .clip(CircleShape)
                                .background(cardBgColor)
                        ) {
                            Icon(
                                imageVector = if (isSpeaking) Icons.Default.Stop else Icons.Default.PlayArrow,
                                contentDescription = if (isSpeaking) stringResource(R.string.gc_stop_speaking) else stringResource(R.string.gc_speak),
                                tint = Color.White
                            )
                        }
                    } else {
                        // 占位
                        Spacer(modifier = Modifier.size(50.dp))
                    }

                    // 语音输入按钮
                    Box(
                        modifier = Modifier
                            .size(70.dp)
                            .clip(CircleShape)
                            .background(if (isRecording) Color.Red else purpleColor)
                            .clickable {
                                if (microphonePermissionState.status.isGranted) {
                                    if (isRecording) {
                                        viewModel.stopSpeechRecognition()
                                        // 清除所有识别文本和缓存，防止停止后仍然处理缓存内容
                                        viewModel.clearRecognizedText()
                                    } else {
                                        viewModel.startSpeechRecognition()
                                    }
                                } else {
                                    microphonePermissionState.launchPermissionRequest()
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        // 录音中的波纹动画
                        if (isRecording) {
                            val animatedSize = remember { Animatable(1f) }

                            LaunchedEffect(Unit) {
                                animatedSize.animateTo(
                                    targetValue = 1.2f,
                                    animationSpec = infiniteRepeatable(
                                        animation = tween(800, easing = FastOutSlowInEasing),
                                        repeatMode = RepeatMode.Reverse
                                    )
                                )
                            }

                            Box(
                                modifier = Modifier
                                    .size(70.dp)
                                    .scale(animatedSize.value)
                                    .clip(CircleShape)
                                    .background(Color.Red.copy(alpha = 0.3f))
                            )
                        }

                        Icon(
                            imageVector = Icons.Default.Mic,
                            contentDescription = if (isRecording) stringResource(R.string.gc_stop_recording) else stringResource(R.string.gc_start_recording),
                            tint = Color.White,
                            modifier = Modifier.size(32.dp)
                        )
                    }

                    // 右侧按钮 - 清除文本
                    IconButton(
                        onClick = { viewModel.clearRecognizedText() },
                        modifier = Modifier
                            .size(50.dp)
                            .clip(CircleShape)
                            .background(cardBgColor)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = stringResource(R.string.gc_clear),
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }

    // 显示播放模式选择对话框
    if (showPlaybackModeDialog) {
        PlaybackModeSelectionDialog(
            currentMode = playbackMode,
            onModeSelected = { mode ->
                viewModel.setPlaybackMode(mode)
                showPlaybackModeDialog = false
            },
            onDismiss = { showPlaybackModeDialog = false }
        )
    }

    // 修改源语言区域，使用来自TencentCloudSpeechToTextClient的累积文本
    // 在LaunchedEffect中添加监听TencentCloud累积文本的逻辑
    LaunchedEffect(currentAsrServiceName) {
        // 使用腾讯云ASR时，确保直接从腾讯云ASR客户端获取累积文本
        if (currentAsrServiceName == "腾讯云") {
            Log.d("GoogleCloudDemoScreen", "使用腾讯云ASR，直接监听其累积文本")
            viewModel.enableDirectTencentTextCollection()
        } else {
            Log.d("GoogleCloudDemoScreen", "使用GoogleCloud ASR，使用标准累积文本")
            viewModel.disableDirectTencentTextCollection()
        }
    }
}
