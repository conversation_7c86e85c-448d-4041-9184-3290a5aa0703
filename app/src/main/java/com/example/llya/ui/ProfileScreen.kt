package com.example.llya.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.border
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.outlined.Chat
import androidx.compose.material.icons.outlined.Headphones
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.ui.components.CommonBottomNavBar
import com.example.llya.viewmodel.UserViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import com.example.llya.utils.UserCache
import android.util.Log
import androidx.compose.ui.res.stringResource
import com.example.llya.R
import com.example.llya.utils.LanguageUtils
import androidx.compose.ui.platform.LocalContext

@Composable
fun ProfileScreen(
    navController: NavController? = null,
    userViewModel: UserViewModel = viewModel()
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val cardBackgroundColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)

    // 获取用户状态
    val uiState by userViewModel.uiState.collectAsState()

    // 从UserCache直接获取当前用户信息
    val currentUser = UserCache.currentUser.value

    // 添加调试日志
    Log.d("ProfileScreen", "当前用户数据: ${currentUser?.toString() ?: "null"}")
    Log.d("ProfileScreen", "VIP到期时间: ${currentUser?.vipExpireTime}")
    Log.d("ProfileScreen", "设备列表: ${currentUser?.devices}")

    // 在组件首次加载时获取用户详情
    LaunchedEffect(Unit) {
        // 添加日志，检测页面加载
        Log.d("ProfileScreen", "页面加载 - 开始获取用户详情")

        // 无论如何都尝试获取最新用户详情
        userViewModel.getUserDetail()
    }

    // 监听用户状态变化
    LaunchedEffect(uiState) {
        when (uiState) {
            is UserViewModel.UiState.Success -> {
                Log.d("ProfileScreen", "用户详情获取成功")
                Log.d("ProfileScreen", "VIP到期时间: ${UserCache.currentUser.value?.vipExpireTime}")
                Log.d("ProfileScreen", "设备列表: ${UserCache.currentUser.value?.devices}")
            }
            is UserViewModel.UiState.Error -> {
                Log.e("ProfileScreen", "获取用户详情失败: ${(uiState as UserViewModel.UiState.Error).message}")
            }
            else -> {}
        }
    }

    // 添加测试数据开关，如果全局缓存没有数据就使用测试数据
    // 暂时强制使用测试数据，直到问题解决
    val useTestData = false // 改为使用真实数据

    Log.d("ProfileScreen", "是否使用测试数据: $useTestData")

    // 获取用户名称（优先使用username，如果为空则使用email）
    val displayName = when {
        !currentUser?.username.isNullOrEmpty() -> currentUser?.username
        !currentUser?.email.isNullOrEmpty() -> currentUser?.email
        else -> "XJ6598637256991" // 默认用户ID
    }

    // 获取VIP到期时间 - 使用真实数据
    val vipExpireTime = if (!useTestData && currentUser?.vipExpireTime != null) {
        when {
            // 如果VIP到期时间是日期字符串格式(例如 "2024-12-31")
            currentUser.vipExpireTime.toString().contains("-") -> {
                // 直接使用日期字符串，截取日期部分
                val dateStr = currentUser.vipExpireTime.toString()
                if (dateStr.length >= 10) dateStr.substring(0, 10) else "限免"
            }
            // 如果是时间戳格式且大于0
            currentUser.vipExpireTime.toString().toLongOrNull() ?: 0 > 0 -> {
                try {
                    // 格式化时间戳为日期字符串
                    val timestamp = currentUser.vipExpireTime.toString().toLong()
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    dateFormat.format(Date(timestamp))
                } catch (e: Exception) {
                    Log.e("ProfileScreen", "格式化VIP到期时间失败: ${e.message}")
                    "限免"
                }
            }
            // 其他情况
            else -> "限免"
        }
    } else {
        "限免" // 默认值
    }

    // 获取设备绑定状态 - 使用真实数据
    val deviceStatus = if (!useTestData && currentUser?.devices?.isNotEmpty() == true) {
        // 尝试获取设备的devicename
        val device = currentUser.devices[0]
        device.devicename.takeIf { it.isNotEmpty() } ?: "未知设备"
    } else {
        "未绑定"
    }

    // 对话框状态
    var showMoreSettingsDialog by remember { mutableStateOf(false) }
    var showLogoutConfirmDialog by remember { mutableStateOf(false) }
    var showDeleteAccountConfirmDialog by remember { mutableStateOf(false) }

    // 退出登录确认对话框
    if (showLogoutConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutConfirmDialog = false },
            title = { Text("退出登录") },
            text = { Text("确定要退出当前账号吗？") },
            confirmButton = {
                Button(
                    onClick = {
                        // 调用登出方法
                        userViewModel.logout()
                        showLogoutConfirmDialog = false
                        // 退出后跳转到登录页面
                        navController?.navigate("login") {
                            popUpTo("main") { inclusive = true }
                        }
                    },
                    colors = ButtonDefaults.buttonColors(containerColor = purpleColor)
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showLogoutConfirmDialog = false }) {
                    Text("取消")
                }
            },
            containerColor = cardBackgroundColor,
            titleContentColor = Color.White,
            textContentColor = Color.White
        )
    }

    // 注销账号确认对话框
    if (showDeleteAccountConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAccountConfirmDialog = false },
            title = { Text("注销账号") },
            text = {
                Text("注销账号将删除所有个人数据，此操作不可逆，确定要继续吗？")
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 调用账号注销API，这里假设要输入密码，先简单处理
                        // 实际实现可能需要先输入密码再注销
                        showDeleteAccountConfirmDialog = false
                        // 这里应该接入API，暂时只做登出处理
                        userViewModel.logout()
                        // 注销后跳转到登录页面
                        navController?.navigate("login") {
                            popUpTo("main") { inclusive = true }
                        }
                    },
                    colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                ) {
                    Text("确定注销")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteAccountConfirmDialog = false }) {
                    Text("取消")
                }
            },
            containerColor = cardBackgroundColor,
            titleContentColor = Color.White,
            textContentColor = Color.White
        )
    }

    // 更多设置对话框
    if (showMoreSettingsDialog) {
        AlertDialog(
            onDismissRequest = { showMoreSettingsDialog = false },
            title = { Text("更多设置") },
            text = {
                Column {
                    SettingDialogItem(
                        title = "退出登录",
                        onClick = {
                            showMoreSettingsDialog = false
                            showLogoutConfirmDialog = true
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    SettingDialogItem(
                        title = "注销账号",
                        onClick = {
                            showMoreSettingsDialog = false
                            showDeleteAccountConfirmDialog = true
                        },
                        textColor = Color.Red
                    )
                }
            },
            confirmButton = {},
            dismissButton = {
                TextButton(onClick = { showMoreSettingsDialog = false }) {
                    Text("取消")
                }
            },
            containerColor = cardBackgroundColor,
            titleContentColor = Color.White,
            textContentColor = Color.White
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
                .padding(bottom = 80.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // 顶部用户信息
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 用户头像
                Box(
                    modifier = Modifier
                        .size(70.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF8364FD)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "用户",
                        tint = Color.White,
                        modifier = Modifier.size(40.dp)
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))


                // 用户ID - 改为显示用户名或邮箱
                Text(
                    text = displayName ?: "XJ6598637256991",
                    color = Color.White,
                    fontSize = 16.sp
                )

                Spacer(modifier = Modifier.height(24.dp))
            }

            // 设备卡片区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // VIP到期时间卡片
                Card(
                    modifier = Modifier
                        .weight(1f)
                        .height(160.dp)
                        .padding(8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 8.dp
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(12.dp)
                            .background(
                                color = Color(0xFF30303E),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        // VIP图标
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = Color(0xFFFFD700),
                            modifier = Modifier.size(36.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // VIP到期时间标题 - 使用字符串资源
                        Text(
                            text = stringResource(R.string.vip_member),
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // VIP到期时间值
                        Text(
                            text = if (vipExpireTime == "限免") stringResource(R.string.vip_expired) else stringResource(R.string.vip_expires_at, vipExpireTime),
                            color = Color(0xFFFFD700),
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                // 耳机绑定状态卡片
                Card(
                    modifier = Modifier
                        .weight(1f)
                        .height(160.dp)
                        .padding(8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 8.dp
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(12.dp)
                            .background(
                                color = Color(0xFF30303E),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        // 耳机图标
                        Icon(
                            imageVector = Icons.Outlined.Headphones,
                            contentDescription = null,
                            tint = purpleColor,
                            modifier = Modifier.size(36.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 耳机状态标题 - 使用字符串资源
                        Text(
                            text = stringResource(R.string.devices),
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 耳机状态值
                        Text(
                            text = deviceStatus,
                            color = purpleColor,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }


            // 添加微软语音识别功能
            SettingItem(
                icon = Icons.Default.Mic,
                title = "微软语音识别与翻译",
                onClick = { 
                    // 启动微软语音识别Activity
                    val context = navController?.context
                    if (context != null) {
                        val intent = android.content.Intent(context, com.example.llya.MicrosoftSpeechActivity::class.java)
                        context.startActivity(intent)
                    }
                }
            )

            SettingItem(
                icon = Icons.Default.Language,
                title = stringResource(R.string.language_settings),
                onClick = { navController?.navigate("language_settings") }
            )

            SettingItem(
                icon = Icons.Default.Info,
                title = stringResource(R.string.about_us),
                onClick = { navController?.navigate("about_us") }
            )

            SettingItem(
                icon = Icons.Outlined.Chat,
                title = stringResource(R.string.feedback),
                onClick = { navController?.navigate("feedback") }
            )

            SettingItem(
                icon = Icons.Default.Security,
                title = stringResource(R.string.permissions),
                onClick = { navController?.navigate("permission_settings") }
            )

            SettingItem(
                icon = Icons.Default.MoreVert,
                title = stringResource(R.string.settings),
                onClick = { showMoreSettingsDialog = true }
            )

            // 添加底部额外空间
            Spacer(modifier = Modifier.height(40.dp))
        }

        // 使用公共底部导航栏
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(backgroundColor)
                .navigationBarsPadding()
        ) {
            CommonBottomNavBar(
                navController = navController,
                currentRoute = "profile"
            )
        }
    }
}

@Composable
fun SettingItem(
    icon: ImageVector,
    title: String,
    onClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(Color(0xFF272636))
            .clickable(onClick = onClick)
            .padding(vertical = 16.dp, horizontal = 20.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Text(
                text = title,
                color = Color.White,
                fontSize = 16.sp,
                modifier = Modifier.weight(1f)
            )

            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "更多",
                tint = Color.White.copy(alpha = 0.6f)
            )
        }
    }
}

@Composable
fun SettingDialogItem(
    title: String,
    onClick: () -> Unit,
    textColor: Color = Color.White
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp, horizontal = 8.dp)
    ) {
        Text(
            text = title,
            color = textColor,
            fontSize = 16.sp
        )
    }
}

@Composable
fun NavigationItem(
    icon: ImageVector,
    label: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    val tint = if (selected) Color(0xFF8364FD) else Color.White.copy(alpha = 0.6f)

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable(onClick = onClick)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = tint,
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = label,
            color = tint,
            fontSize = 12.sp
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ProfileScreenPreview() {
    Surface {
        ProfileScreen()
    }
}