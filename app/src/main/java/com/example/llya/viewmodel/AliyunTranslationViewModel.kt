package com.example.llya.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.example.llya.translation.AliyunTranslationClient
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 阿里云翻译 ViewModel
 */
class AliyunTranslationViewModel(
    private val accessKeyId: String,
    private val accessKeySecret: String
) : ViewModel() {

    companion object {
        private const val TAG = "AliyunTranslationVM"
    }

    // 翻译客户端
    private val translationClient = AliyunTranslationClient(
        accessKeyId,
        accessKeySecret,
        viewModelScope
    )

    // 源语言
    private val _sourceLanguage = MutableStateFlow("zh")
    val sourceLanguage: StateFlow<String> = _sourceLanguage.asStateFlow()

    // 目标语言
    private val _targetLanguage = MutableStateFlow("en")
    val targetLanguage: StateFlow<String> = _targetLanguage.asStateFlow()

    // 待翻译文本
    private val _sourceText = MutableStateFlow("")
    val sourceText: StateFlow<String> = _sourceText.asStateFlow()

    // 翻译结果
    val translationResult: StateFlow<String> = translationClient.translationResult

    // 错误信息
    val errorMessage: StateFlow<String> = translationClient.errorMessage

    // 正在翻译标志
    private val _isTranslating = MutableStateFlow(false)
    val isTranslating: StateFlow<Boolean> = _isTranslating.asStateFlow()

    // 可用的语言列表
    val availableLanguages = AliyunTranslationClient.SUPPORTED_LANGUAGES

    /**
     * 设置源语言
     */
    fun setSourceLanguage(language: String) {
        if (_sourceLanguage.value != language) {
            _sourceLanguage.value = language
            // 如果有待翻译文本，则重新翻译
            if (_sourceText.value.isNotEmpty()) {
                translate(_sourceText.value)
            }
        }
    }

    /**
     * 设置目标语言
     */
    fun setTargetLanguage(language: String) {
        if (_targetLanguage.value != language) {
            _targetLanguage.value = language
            // 如果有待翻译文本，则重新翻译
            if (_sourceText.value.isNotEmpty()) {
                translate(_sourceText.value)
            }
        }
    }

    /**
     * 设置待翻译文本
     */
    fun setSourceText(text: String) {
        _sourceText.value = text
    }

    /**
     * 执行翻译
     */
    fun translate(text: String = _sourceText.value) {
        if (text.isEmpty()) {
            return
        }

        viewModelScope.launch {
            try {
                _isTranslating.value = true
                _sourceText.value = text
                Log.d(TAG, "开始翻译: $text, 从 ${_sourceLanguage.value} 到 ${_targetLanguage.value}")
                
                translationClient.translate(
                    text,
                    _sourceLanguage.value,
                    _targetLanguage.value
                )
            } catch (e: Exception) {
                Log.e(TAG, "翻译失败", e)
            } finally {
                _isTranslating.value = false
            }
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        translationClient.clearError()
    }

    /**
     * 清除翻译结果
     */
    fun clearResult() {
        translationClient.clearResult()
    }

    /**
     * ViewModel 工厂
     */
    class Factory(
        private val accessKeyId: String,
        private val accessKeySecret: String
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(AliyunTranslationViewModel::class.java)) {
                return AliyunTranslationViewModel(accessKeyId, accessKeySecret) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
} 