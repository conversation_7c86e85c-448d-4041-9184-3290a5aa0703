package com.example.llya.network

import android.util.Log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor

/**
 * 更新网络管理器
 */
class UpdateNetworkManager {
    
    companion object {
        private const val TAG = "UpdateNetworkManager"
        private const val BASE_URL = "http://8.138.134.96/"
        
        @Volatile
        private var INSTANCE: UpdateNetworkManager? = null
        
        fun getInstance(): UpdateNetworkManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UpdateNetworkManager().also { INSTANCE = it }
            }
        }
    }
    
    private val loggingInterceptor = HttpLoggingInterceptor { message ->
        Log.d(TAG, message)
    }.apply {
        level = HttpLoggingInterceptor.Level.BODY
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    private val updateApiService = retrofit.create(UpdateApiService::class.java)
    
    /**
     * 检查更新
     */
    fun checkUpdate(platform: String, versionCode: String): Flow<CheckUpdateResponse> = flow {
        try {
            Log.d(TAG, "检查更新: platform=$platform, versionCode=$versionCode")
            
            val request = CheckUpdateRequest(
                platform = platform,
                versionCode = versionCode
            )
            
            val response = updateApiService.checkUpdate(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    Log.d(TAG, "检查更新成功: $body")
                    emit(body)
                } else {
                    Log.e(TAG, "响应体为空")
                    emit(CheckUpdateResponse(
                        status = 500,
                        data = null,
                        message = "响应体为空"
                    ))
                }
            } else {
                Log.e(TAG, "检查更新失败: ${response.code()} - ${response.message()}")
                emit(CheckUpdateResponse(
                    status = response.code(),
                    data = null,
                    message = response.message()
                ))
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查更新异常: ${e.message}", e)
            emit(CheckUpdateResponse(
                status = 500,
                data = null,
                message = e.message ?: "网络请求失败"
            ))
        }
    }
}
