package com.example.llya.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.viewmodel.MicrosoftTranslationViewModel
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 微软语音翻译界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MicrosoftTranslationScreen(
    navController: NavController? = null
) {
    val darkBgColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8364FD)
    val translationViewModel: MicrosoftTranslationViewModel = viewModel()
    
    // 收集状态
    val isRecognizing by translationViewModel.isRecognizing.collectAsState()
    val recognitionResult by translationViewModel.recognitionResult.collectAsState()
    val translationResult by translationViewModel.translationResult.collectAsState()
    val detectedLanguage by translationViewModel.detectedLanguage.collectAsState()
    val errorMessage by translationViewModel.errorMessage.collectAsState()
    val sourceLanguage by translationViewModel.sourceLanguage.collectAsState()
    val targetLanguage by translationViewModel.targetLanguage.collectAsState()
    val translationHistory by translationViewModel.translationHistory.collectAsState()
    val autoPlayTTS by translationViewModel.autoPlayTTS.collectAsState()
    val autoDetectLanguage by translationViewModel.autoDetectLanguage.collectAsState()
    val bidirectionalMode by translationViewModel.bidirectionalMode.collectAsState()
    val sensitivityLevel by translationViewModel.sensitivityLevel.collectAsState()
    val availableLanguages = translationViewModel.availableLanguages
    val accumulatedText by translationViewModel.accumulatedText.collectAsState()
    
    // 错误提示对话框状态
    val showErrorDialog = remember { mutableStateOf(false) }
    
    // 源语言和目标语言选择对话框状态
    val showSourceLanguageDialog = remember { mutableStateOf(false) }
    val showTargetLanguageDialog = remember { mutableStateOf(false) }
    
    // 设置对话框状态
    val showSettingsDialog = remember { mutableStateOf(false) }
    
    // 配置对话框状态
    val showConfigDialog = remember { mutableStateOf(false) }
    
    // 语言组合选择对话框状态
    val showLanguageCombinationDialog = remember { mutableStateOf(false) }
    
    // 如果有错误信息，显示错误对话框
    LaunchedEffect(errorMessage) {
        if (errorMessage != null) {
            showErrorDialog.value = true
        }
    }
    
    val coroutineScope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(darkBgColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    text = "微软语音识别与翻译",
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            actions = {
                // 设置按钮
                IconButton(
                    onClick = { showSettingsDialog.value = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置",
                        tint = Color.White
                    )
                }
                
                // 清空历史记录按钮
                IconButton(
                    onClick = { translationViewModel.clearHistory() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "清空历史记录",
                        tint = Color.Red
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = darkBgColor,
                titleContentColor = Color.White
            )
        )
        
        // 在工具栏下面添加状态信息
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(darkBgColor)
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            val statusText = if (detectedLanguage != null && (autoDetectLanguage || translationViewModel.bidirectionalMode.value)) {
                // 使用ViewModel中的方法获取更详细的语言检测信息
                translationViewModel.getLanguageDetectionInfo()
            } else {
                if (isRecognizing) "正在识别和翻译..." else "准备就绪"
            }
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                if (translationViewModel.bidirectionalMode.value) {
                    Icon(
                        imageVector = Icons.Default.Compare,
                        contentDescription = "互译模式",
                        tint = Color.Green,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                } else if (autoDetectLanguage) {
                    Icon(
                        imageVector = Icons.Default.Language,
                        contentDescription = "自动检测",
                        tint = Color.Green,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }
                
                Text(
                    text = statusText,
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        // 语言选择面板
        LanguageSelectionPanel(
            sourceLanguage = availableLanguages.find { it.first == sourceLanguage }?.second ?: "中文",
            targetLanguage = availableLanguages.find { it.first == targetLanguage }?.second ?: "英语",
            onSwitchLanguages = { translationViewModel.switchLanguages() },
            availableLanguages = availableLanguages,
            onSourceLanguageSelected = { langCode ->
                translationViewModel.setSourceLanguage(langCode)
                // 关闭自动检测模式
                translationViewModel.setAutoDetectLanguage(false)
            },
            onTargetLanguageSelected = { langCode ->
                translationViewModel.setTargetLanguage(langCode)
            },
            onSourceClick = { showSourceLanguageDialog.value = true },
            onTargetClick = { showTargetLanguageDialog.value = true },
            isAutoDetect = autoDetectLanguage,
            onLanguageCombinationClick = { showLanguageCombinationDialog.value = true }
        )
        
        // 今天日期
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "今天 ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())}",
                color = Color.Gray,
                fontSize = 14.sp
            )
        }
        
        // 消息列表和累积文本显示
        val listState = rememberLazyListState()
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 累积文本显示（如果有的话）
            if (accumulatedText.isNotEmpty()) {
                item {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFF2A2A3E)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "对话历史",
                                color = Color.White,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                            
                            Text(
                                text = accumulatedText,
                                color = Color.White,
                                fontSize = 14.sp,
                                lineHeight = 20.sp
                            )
                        }
                    }
                }
            }
            
            // 历史记录
            items(translationHistory) { historyItem ->
                TranslationHistoryItemUI(
                    historyItem = historyItem,
                    onPlaySourceText = { translationViewModel.speakSourceText(historyItem.sourceText) },
                    onPlayTargetText = { translationViewModel.speakTargetText(historyItem.translatedText) }
                )
            }
            
            // 当正在录音时，显示实时识别的文字
            if (isRecognizing && recognitionResult.isNotEmpty()) {
                item {
                    // 源语言实时显示
                    TranslationMessage(
                        text = recognitionResult,
                        isSourceLanguage = true,
                        timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date()),
                        isTyping = true,
                        onPlayText = { translationViewModel.speakSourceText() },
                        detectedLanguage = detectedLanguage
                    )
                }
                
                // 如果有翻译结果，实时显示
                if (translationResult.isNotEmpty()) {
                    item {
                        TranslationMessage(
                            text = translationResult,
                            isSourceLanguage = false,
                            timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date()),
                            isTyping = true,
                            onPlayText = { translationViewModel.speakTargetText() }
                        )
                    }
                }
            }
        }
        
        // 底部控制栏
        ControlPanel(
            isRecognizing = isRecognizing,
            onStartTranslation = { translationViewModel.startTranslation() },
            onStopTranslation = { translationViewModel.stopTranslation() },
            autoPlayTTS = autoPlayTTS,
            onToggleAutoPlayTTS = { translationViewModel.setAutoPlayTTS(!autoPlayTTS) },
            autoDetectLanguage = autoDetectLanguage,
            onToggleAutoDetectLanguage = { translationViewModel.setAutoDetectLanguage(!autoDetectLanguage) },
            bidirectionalMode = translationViewModel.bidirectionalMode.value,
            onToggleBidirectionalMode = { translationViewModel.setBidirectionalMode(!translationViewModel.bidirectionalMode.value) }
        )
    }
    
    // 错误对话框
    if (showErrorDialog.value && errorMessage != null) {
        AlertDialog(
            onDismissRequest = {
                showErrorDialog.value = false
                translationViewModel.clearErrorMessage()
            },
            title = {
                Text("错误")
            },
            text = {
                Text(errorMessage ?: "未知错误")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showErrorDialog.value = false
                        translationViewModel.clearErrorMessage()
                    }
                ) {
                    Text("确定")
                }
            },
            containerColor = Color.White,
            titleContentColor = Color.Black,
            textContentColor = Color.Black
        )
    }
    
    // 源语言选择对话框
    if (showSourceLanguageDialog.value) {
        LanguageSelectionDialog(
            onDismissRequest = { showSourceLanguageDialog.value = false },
            languages = availableLanguages,
            selectedLanguage = sourceLanguage,
            onLanguageSelected = { langCode ->
                translationViewModel.setSourceLanguage(langCode)
                showSourceLanguageDialog.value = false
                // 关闭自动检测模式
                translationViewModel.setAutoDetectLanguage(false)
            },
            title = "选择源语言"
        )
    }
    
    // 目标语言选择对话框
    if (showTargetLanguageDialog.value) {
        LanguageSelectionDialog(
            onDismissRequest = { showTargetLanguageDialog.value = false },
            languages = availableLanguages,
            selectedLanguage = targetLanguage,
            onLanguageSelected = { langCode ->
                translationViewModel.setTargetLanguage(langCode)
                showTargetLanguageDialog.value = false
            },
            title = "选择目标语言"
        )
    }
    
    // 设置对话框
    if (showSettingsDialog.value) {
        SettingsDialog(
            onDismissRequest = { showSettingsDialog.value = false },
            autoPlayTTS = autoPlayTTS,
            onToggleAutoPlayTTS = { translationViewModel.setAutoPlayTTS(!autoPlayTTS) },
            autoDetectLanguage = autoDetectLanguage,
            onToggleAutoDetectLanguage = { translationViewModel.setAutoDetectLanguage(!autoDetectLanguage) },
            onConfigClick = {
                showSettingsDialog.value = false
                showConfigDialog.value = true
            }
        )
    }
    
    // 配置对话框
    if (showConfigDialog.value) {
        ConfigDialog(
            onDismissRequest = { showConfigDialog.value = false },
            onSaveConfig = { key, region ->
                translationViewModel.updateConfig(key, region)
                showConfigDialog.value = false
            }
        )
    }
    
    // 语言组合选择对话框
    if (showLanguageCombinationDialog.value) {
        LanguageCombinationDialog(
            onDismissRequest = { showLanguageCombinationDialog.value = false },
            languageCombinations = translationViewModel.getSupportedLanguageCombinations(),
            onCombinationSelected = { groupName ->
                translationViewModel.setLanguageCombination(groupName)
                showLanguageCombinationDialog.value = false
            }
        )
    }
}

/**
 * 语言选择面板
 */
@Composable
private fun LanguageSelectionPanel(
    sourceLanguage: String,
    targetLanguage: String,
    onSwitchLanguages: () -> Unit,
    availableLanguages: List<Pair<String, String>>,
    onSourceLanguageSelected: (String) -> Unit,
    onTargetLanguageSelected: (String) -> Unit,
    onSourceClick: () -> Unit = {},
    onTargetClick: () -> Unit = {},
    isAutoDetect: Boolean = false,
    onLanguageCombinationClick: () -> Unit = {}
) {
    Column {
        // 语言组合快速选择按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            Button(
                onClick = onLanguageCombinationClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF3E3E4E)
                ),
                modifier = Modifier.padding(horizontal = 4.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Language,
                    contentDescription = "语言组合",
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "快速选择",
                    color = Color.White,
                    fontSize = 12.sp
                )
            }
        }
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 源语言
            Box(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onSourceClick() }
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = sourceLanguage,
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                    
                    if (isAutoDetect) {
                        Text(
                            text = "自动检测",
                            color = Color.Green,
                            fontSize = 12.sp
                        )
                    }
                }
            }
            
            // 切换按钮
            IconButton(
                onClick = onSwitchLanguages,
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF2E2E3E))
            ) {
                Icon(
                    imageVector = Icons.Default.SwapHoriz,
                    contentDescription = "切换语言",
                    tint = Color.White
                )
            }
            
            // 目标语言
            Box(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onTargetClick() }
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = targetLanguage,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }
        }
    }
}

/**
 * 翻译历史项UI
 */
@Composable
private fun TranslationHistoryItemUI(
    historyItem: MicrosoftTranslationViewModel.TranslationHistoryItem,
    onPlaySourceText: () -> Unit,
    onPlayTargetText: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        // 源语言消息
        TranslationMessage(
            text = historyItem.sourceText,
            isSourceLanguage = true,
            timestamp = historyItem.timestamp,
            onPlayText = onPlaySourceText
        )
        
        // 目标语言消息
        TranslationMessage(
            text = historyItem.translatedText,
            isSourceLanguage = false,
            timestamp = historyItem.timestamp,
            onPlayText = onPlayTargetText
        )
    }
}

/**
 * 翻译消息
 */
@Composable
private fun TranslationMessage(
    text: String,
    isSourceLanguage: Boolean,
    timestamp: String,
    isTyping: Boolean = false,
    onPlayText: () -> Unit = {},
    detectedLanguage: String? = null
) {
    val darkBgColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8364FD)
    
    val alpha by animateFloatAsState(
        targetValue = if (isTyping) 0.8f else 1f,
        label = "typingAnimation"
    )
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (isSourceLanguage) Arrangement.Start else Arrangement.End
    ) {
        Box(
            modifier = Modifier
                .widthIn(max = 280.dp)
                .clip(
                    RoundedCornerShape(
                        topStart = if (isSourceLanguage) 4.dp else 16.dp,
                        topEnd = if (isSourceLanguage) 16.dp else 4.dp,
                        bottomStart = 16.dp,
                        bottomEnd = 16.dp
                    )
                )
                .background(if (isSourceLanguage) Color(0xFF2E2E3E) else purpleColor)
                .alpha(alpha)
                .padding(12.dp)
        ) {
            Column {
                // 如果是源语言且有检测到的语言，显示检测结果
                if (isSourceLanguage && detectedLanguage != null) {
                    Text(
                        text = "检测: $detectedLanguage",
                        color = Color.Green,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                }
                
                Text(
                    text = text,
                    color = Color.White,
                    fontSize = 16.sp
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = timestamp,
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 12.sp
                    )
                    
                    // 播放按钮
                    IconButton(
                        onClick = onPlayText,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.VolumeUp,
                            contentDescription = "播放",
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }
}

/**
 * 控制面板
 */
@Composable
private fun ControlPanel(
    isRecognizing: Boolean,
    onStartTranslation: () -> Unit,
    onStopTranslation: () -> Unit,
    autoPlayTTS: Boolean,
    onToggleAutoPlayTTS: () -> Unit,
    autoDetectLanguage: Boolean,
    onToggleAutoDetectLanguage: () -> Unit,
    bidirectionalMode: Boolean = false,
    onToggleBidirectionalMode: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFF2E2E3E))
            .padding(16.dp),
    ) {
        // 主控制按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 自动播放TTS开关
            IconButton(
                onClick = onToggleAutoPlayTTS,
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(if (autoPlayTTS) Color(0xFF8364FD) else Color(0xFF3E3E4E))
            ) {
                Icon(
                    imageVector = Icons.Default.VolumeUp,
                    contentDescription = "自动播放TTS",
                    tint = Color.White
                )
            }
            
            // 识别控制按钮
            IconButton(
                onClick = if (isRecognizing) onStopTranslation else onStartTranslation,
                modifier = Modifier
                    .size(64.dp)
                    .clip(CircleShape)
                    .background(if (isRecognizing) Color.Red else Color(0xFF8364FD))
            ) {
                Icon(
                    imageVector = if (isRecognizing) Icons.Default.Stop else Icons.Default.Mic,
                    contentDescription = if (isRecognizing) "停止识别" else "开始识别",
                    tint = Color.White,
                    modifier = Modifier.size(32.dp)
                )
            }
            
            // 自动检测语言开关
            IconButton(
                onClick = onToggleAutoDetectLanguage,
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(if (autoDetectLanguage) Color(0xFF8364FD) else Color(0xFF3E3E4E))
                    .alpha(if (bidirectionalMode) 0.5f else 1f) // 互译模式下禁用
                    .then(if (bidirectionalMode) Modifier.clickable(enabled = false) {} else Modifier)
            ) {
                Icon(
                    imageVector = Icons.Default.Language,
                    contentDescription = "自动检测语言",
                    tint = Color.White
                )
            }
        }
        
        // 高级功能开关
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 互译模式开关
            Row(
                modifier = Modifier
                    .clip(RoundedCornerShape(20.dp))
                    .clickable { onToggleBidirectionalMode() }
                    .background(if (bidirectionalMode) Color(0xFF8364FD) else Color(0xFF3E3E4E))
                    .padding(horizontal = 12.dp, vertical = 6.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Compare,
                    contentDescription = "互译模式",
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "互译模式",
                    color = Color.White,
                    fontSize = 12.sp
                )
                Spacer(modifier = Modifier.width(4.dp))
                Switch(
                    checked = bidirectionalMode,
                    onCheckedChange = { onToggleBidirectionalMode() },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = Color(0xFF6344CD),
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = Color(0xFF2E2E3E)
                    )
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 添加文本说明
            if (bidirectionalMode) {
                Text(
                    text = "已启用连续多语言检测",
                    color = Color.Green,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }
        }
    }
}

/**
 * 语言选择对话框
 */
@Composable
private fun LanguageSelectionDialog(
    onDismissRequest: () -> Unit,
    languages: List<Pair<String, String>>,
    selectedLanguage: String,
    onLanguageSelected: (String) -> Unit,
    title: String
) {
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text(title)
        },
        text = {
            LazyColumn(
                modifier = Modifier.heightIn(max = 300.dp)
            ) {
                items(languages) { (code, name) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onLanguageSelected(code) }
                            .padding(vertical = 8.dp, horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = code == selectedLanguage,
                            onClick = { onLanguageSelected(code) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = name,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismissRequest
            ) {
                Text("取消")
            }
        },
        containerColor = Color.White,
        titleContentColor = Color.Black,
        textContentColor = Color.Black
    )
}

/**
 * 设置对话框
 */
@Composable
private fun SettingsDialog(
    onDismissRequest: () -> Unit,
    autoPlayTTS: Boolean,
    onToggleAutoPlayTTS: () -> Unit,
    autoDetectLanguage: Boolean,
    onToggleAutoDetectLanguage: () -> Unit,
    onConfigClick: () -> Unit
) {
    val viewModel: MicrosoftTranslationViewModel = viewModel()
    val sensitivityLevel by viewModel.sensitivityLevel.collectAsState()
    val sensitivityOptions = listOf("高灵敏度", "中等灵敏度", "低灵敏度")
    
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text("设置")
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 自动播放TTS设置
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("自动播放翻译结果")
                    Switch(
                        checked = autoPlayTTS,
                        onCheckedChange = { onToggleAutoPlayTTS() }
                    )
                }
                
                // 自动检测语言设置
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("自动检测语言")
                    Switch(
                        checked = autoDetectLanguage,
                        onCheckedChange = { onToggleAutoDetectLanguage() }
                    )
                }
                
                // 语言检测灵敏度设置
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Text(
                        text = "语言检测灵敏度",
                        fontWeight = FontWeight.Medium
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    sensitivityOptions.forEachIndexed { index, option ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { 
                                    viewModel.setLanguageDetectionSensitivity(index)
                                }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = sensitivityLevel == index,
                                onClick = { 
                                    viewModel.setLanguageDetectionSensitivity(index)
                                }
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(option)
                        }
                    }
                    
                    Text(
                        text = when(sensitivityLevel) {
                            0 -> "快速检测语言变化，适合频繁切换语言"
                            1 -> "平衡检测速度和准确性"
                            else -> "减少误判，适合在嘈杂环境使用"
                        },
                        color = Color.Gray,
                        fontSize = 12.sp
                    )
                }
                
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                
                // 配置按钮
                Button(
                    onClick = onConfigClick,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Text("配置API密钥和区域")
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismissRequest
            ) {
                Text("关闭")
            }
        },
        containerColor = Color.White,
        titleContentColor = Color.Black,
        textContentColor = Color.Black
    )
}

/**
 * 配置对话框
 */
@Composable
private fun ConfigDialog(
    onDismissRequest: () -> Unit,
    onSaveConfig: (String, String) -> Unit
) {
    var subscriptionKey by remember { mutableStateOf("") }
    var region by remember { mutableStateOf("eastasia") }
    
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text("配置微软语音服务")
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 订阅密钥输入
                OutlinedTextField(
                    value = subscriptionKey,
                    onValueChange = { subscriptionKey = it },
                    label = { Text("订阅密钥") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                )
                
                // 区域输入
                OutlinedTextField(
                    value = region,
                    onValueChange = { region = it },
                    label = { Text("区域") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                )
                
                Text(
                    "请输入您的微软语音服务订阅密钥和区域。您可以在Azure门户网站上获取这些信息。",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onSaveConfig(subscriptionKey, region) }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismissRequest
            ) {
                Text("取消")
            }
        },
        containerColor = Color.White,
        titleContentColor = Color.Black,
        textContentColor = Color.Black
    )
}

/**
 * 语言组合选择对话框
 */
@Composable
private fun LanguageCombinationDialog(
    onDismissRequest: () -> Unit,
    languageCombinations: Map<String, List<String>>,
    onCombinationSelected: (String) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text("选择语言组合")
        },
        text = {
            LazyColumn(
                modifier = Modifier.heightIn(max = 300.dp)
            ) {
                items(languageCombinations.entries.toList()) { (groupName, languages) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onCombinationSelected(groupName) }
                            .padding(vertical = 12.dp, horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = groupName,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = languages.joinToString(" ⟷ "),
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismissRequest
            ) {
                Text("取消")
            }
        },
        containerColor = Color.White,
        titleContentColor = Color.Black,
        textContentColor = Color.Black
    )
}

