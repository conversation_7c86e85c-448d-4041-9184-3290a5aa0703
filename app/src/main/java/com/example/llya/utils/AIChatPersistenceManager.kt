package com.example.llya.utils

import android.content.Context
import android.util.Log
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * AI聊天记录持久化管理器
 * 负责保存、加载和管理AI聊天记录
 */
class AIChatPersistenceManager(private val context: Context) {
    companion object {
        private const val TAG = "AIChatPersistenceManager"
        private const val AI_CHAT_HISTORY_FILENAME = "ai_chat_history.json"
    }
    
    // 使用Moshi处理JSON序列化/反序列化
    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()
    
    /**
     * 聊天会话数据类
     */
    data class ChatSession(
        val id: String = UUID.randomUUID().toString(),
        val title: String,
        val messages: List<ChatMessage>,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 聊天消息数据类
     */
    data class ChatMessage(
        val content: String,
        val isFromUser: Boolean,
        val timestamp: String = SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())
    )
    
    /**
     * 保存聊天会话
     * @param title 会话标题
     * @param messages 聊天消息列表
     * @return 是否保存成功
     */
    fun saveChatSession(title: String, messages: List<ChatMessage>): Boolean {
        try {
            // 如果消息列表为空，不进行保存
            if (messages.isEmpty()) {
                Log.d(TAG, "消息列表为空，不保存聊天会话")
                return false
            }
            
            // 创建聊天会话对象
            val chatSession = ChatSession(
                title = title,
                messages = messages
            )
            
            // 读取现有历史记录
            val historySessions = loadAllChatSessions().toMutableList()
            
            // 添加新会话
            historySessions.add(chatSession)
            
            // 保存更新后的会话列表
            val type = Types.newParameterizedType(List::class.java, ChatSession::class.java)
            val adapter: JsonAdapter<List<ChatSession>> = moshi.adapter(type)
            val json = adapter.toJson(historySessions)
            
            val historyFile = File(context.filesDir, AI_CHAT_HISTORY_FILENAME)
            FileOutputStream(historyFile).use { it.write(json.toByteArray()) }
            
            Log.d(TAG, "聊天会话已保存: $title, ID: ${chatSession.id}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "保存聊天会话失败: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 加载所有聊天会话
     * @return 聊天会话列表，按时间倒序排序
     */
    fun loadAllChatSessions(): List<ChatSession> {
        try {
            val historyFile = File(context.filesDir, AI_CHAT_HISTORY_FILENAME)
            
            if (!historyFile.exists()) {
                Log.d(TAG, "聊天记录文件不存在，返回空列表")
                return emptyList()
            }
            
            val json = historyFile.readText()
            
            if (json.isBlank()) {
                return emptyList()
            }
            
            val type = Types.newParameterizedType(List::class.java, ChatSession::class.java)
            val adapter: JsonAdapter<List<ChatSession>> = moshi.adapter(type)
            val sessions = adapter.fromJson(json) ?: emptyList()
            
            // 按时间戳倒序排序，最新的会话排在前面
            return sessions.sortedByDescending { it.timestamp }
        } catch (e: Exception) {
            Log.e(TAG, "加载聊天会话失败: ${e.message}", e)
            return emptyList()
        }
    }
    
    /**
     * 根据ID加载聊天会话
     * @param id 会话ID
     * @return 聊天会话对象，如果找不到则返回null
     */
    fun getChatSessionById(id: String): ChatSession? {
        try {
            val sessions = loadAllChatSessions()
            return sessions.find { it.id == id }
        } catch (e: Exception) {
            Log.e(TAG, "通过ID加载聊天会话失败: ${e.message}", e)
            return null
        }
    }
    
    /**
     * 删除聊天会话
     * @param id 会话ID
     * @return 是否删除成功
     */
    fun deleteChatSession(id: String): Boolean {
        try {
            val sessions = loadAllChatSessions().toMutableList()
            val originalSize = sessions.size
            
            // 过滤掉要删除的会话
            val updatedSessions = sessions.filter { it.id != id }
            
            if (updatedSessions.size == originalSize) {
                // 没有找到要删除的会话
                return false
            }
            
            // 保存更新后的会话列表
            val type = Types.newParameterizedType(List::class.java, ChatSession::class.java)
            val adapter: JsonAdapter<List<ChatSession>> = moshi.adapter(type)
            val json = adapter.toJson(updatedSessions)
            
            val historyFile = File(context.filesDir, AI_CHAT_HISTORY_FILENAME)
            FileOutputStream(historyFile).use { it.write(json.toByteArray()) }
            
            Log.d(TAG, "聊天会话已删除: $id")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "删除聊天会话失败: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 清空所有聊天记录
     * @return 是否清空成功
     */
    fun clearAllChatSessions(): Boolean {
        try {
            val historyFile = File(context.filesDir, AI_CHAT_HISTORY_FILENAME)
            
            if (historyFile.exists()) {
                val deleted = historyFile.delete()
                if (deleted) {
                    Log.d(TAG, "所有聊天记录已清空")
                    return true
                }
            }
            
            return false
        } catch (e: Exception) {
            Log.e(TAG, "清空聊天记录失败: ${e.message}", e)
            return false
        }
    }
} 