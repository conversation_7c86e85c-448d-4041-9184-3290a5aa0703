package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.viewmodel.MeetingRecordViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MeetingRecordDetailScreen(
    navController: NavController,
    recordId: String,
    speechViewModel: MeetingRecordViewModel = viewModel()
) {
    val darkBgColor = Color(0xFF222131)
    val contentBgColor = Color(0xFF2D2C3E)
    val purpleColor = Color(0xFF8675E6)
    
    // 加载会议记录详情
    val selectedRecord by speechViewModel.selectedRecord.collectAsState()
    val scrollState = rememberScrollState()
    
    // 删除确认对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    // 首次进入页面时加载会议记录详情
    LaunchedEffect(recordId) {
        println("正在加载会议记录，ID: $recordId")
        speechViewModel.loadMeetingRecord(recordId)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        selectedRecord?.title ?: "会议记录详情", 
                        color = Color.White,
                        maxLines = 1
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 分享按钮
                    IconButton(onClick = { /* 分享功能 */ }) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "分享",
                            tint = Color.White
                        )
                    }
                    // 删除按钮
                    IconButton(onClick = { showDeleteDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = darkBgColor
                )
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(darkBgColor)
                .padding(padding)
        ) {
            if (selectedRecord == null) {
                // 加载中或记录不存在
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(darkBgColor)
                        .padding(padding),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(color = purpleColor)
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "正在加载记录 ID: $recordId",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                        
                        // 显示错误信息（如果有）
                        val error by speechViewModel.errorMessage.collectAsState()
                        if (error != null) {
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "错误: $error",
                                color = Color.Red,
                                fontSize = 16.sp
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(
                                onClick = { navController.popBackStack() },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = purpleColor
                                )
                            ) {
                                Text("返回")
                            }
                        }
                    }
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    // 会议信息
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 日期和时间
                        val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                        val dateString = dateFormatter.format(Date(selectedRecord!!.timestamp))
                        
                        Text(
                            text = dateString,
                            color = purpleColor,
                            fontSize = 16.sp
                        )
                        
                        // 会议时长
                        val durationMinutes = selectedRecord!!.duration / 60000
                        val durationString = if (durationMinutes > 0) "$durationMinutes 分钟" else "不到1分钟"
                        
                        Text(
                            text = "时长: $durationString",
                            color = purpleColor,
                            fontSize = 16.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 会议内容
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .clip(RoundedCornerShape(16.dp)),
                        color = contentBgColor
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp)
                        ) {
                            Text(
                                text = selectedRecord!!.content,
                                color = Color.White,
                                fontSize = 16.sp,
                                lineHeight = 24.sp,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .verticalScroll(scrollState)
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 加载到当前会话按钮
                    Button(
                        onClick = { 
                            // 导航回会议记录屏幕，同时将当前记录内容加载到会议记录屏幕
                            speechViewModel.setCurrentMeetingContent(selectedRecord!!.content)
                            navController.navigate("google_meeting_record") {
                                popUpTo("google_meeting_record") { inclusive = true }
                            }
                        },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = purpleColor
                        )
                    ) {
                        Text(
                            text = "继续编辑此会议记录",
                            fontSize = 16.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }
            }
            
            // 删除确认对话框
            if (showDeleteDialog) {
                AlertDialog(
                    containerColor = Color(0xFF363547),
                    textContentColor = Color.White,
                    titleContentColor = Color.White,
                    onDismissRequest = { showDeleteDialog = false },
                    title = { Text("删除会议记录") },
                    text = { Text("确定要删除此会议记录吗？此操作不可撤销。") },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                speechViewModel.deleteMeetingRecord(recordId)
                                showDeleteDialog = false
                                navController.popBackStack()
                            }
                        ) {
                            Text("删除", color = Color.Red)
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showDeleteDialog = false }
                        ) {
                            Text("取消", color = Color.White)
                        }
                    }
                )
            }
        }
    }
} 