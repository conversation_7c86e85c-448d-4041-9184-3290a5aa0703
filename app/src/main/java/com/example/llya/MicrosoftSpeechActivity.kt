package com.example.llya

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.microsoft.cognitiveservices.speech.translation.SpeechTranslationConfig
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognizer
import kotlinx.coroutines.*
import com.example.llya.ui.theme.LlyaTheme
import com.example.llya.viewmodel.HistoryViewModel
import com.example.llya.data.HistoryRecord
import com.example.llya.ui.HistoryItemCard
import androidx.lifecycle.viewmodel.compose.viewModel
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class MicrosoftSpeechActivity : ComponentActivity() {
    private val TAG = "MicrosoftSpeechActivity"

    // 微软语音服务的订阅密钥和区域
    private val speechSubscriptionKey = "7lJ1e4p4LppCs62hMBvzyzxYv5vCOauJTzIKqnY3U0NVw7hFXiGaJQQJ99BEACYeBjFXJ3w3AAAYACOGuXCg"
    private val speechRegion = "eastus"

    // 双向翻译模式开关
    private var enableBidirectionalTranslation = true

    // 语音识别器
    private var translationRecognizer: TranslationRecognizer? = null
    private var synthesizer: SpeechSynthesizer? = null

    // TTS播放队列
    private val ttsQueue = ConcurrentLinkedQueue<Pair<String, String>>()
    private val ttsExecutor = Executors.newSingleThreadExecutor()
    private var isProcessingTts = false

    // 识别历史记录 - 修复累积文本消失问题
    private val recognitionHistory = mutableListOf<String>()

    // 添加停止状态标志，防止频繁点击
    private var isStopping = false

    // 累积文本状态 - 确保不会丢失
    private var accumulatedText = ""

    // 请求麦克风权限
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            Log.d(TAG, "麦克风权限已授予")
        } else {
            Log.e(TAG, "麦克风权限被拒绝")
            Toast.makeText(this, "需要麦克风权限才能进行语音识别", Toast.LENGTH_LONG).show()
        }
    }

    // 支持的语音识别语言
    private val supportedSpeechLanguages = mapOf(
        "zh-CN" to "中文(简体)",
        "zh-TW" to "中文(台湾)",
        "en-US" to "英语(美国)",
        "ja-JP" to "日语",
        "ko-KR" to "韩语",
        "fr-FR" to "法语",
        "de-DE" to "德语",
        "es-ES" to "西班牙语",
        "it-IT" to "意大利语",
        "ru-RU" to "俄语"
    )

    // 支持的翻译语言
    private val supportedTranslationLanguages = mapOf(
        "zh-Hans" to "中文(简体)",
        "zh-Hant" to "中文(繁体)",
        "en" to "英语",
        "ja" to "日语",
        "ko" to "韩语",
        "fr" to "法语",
        "de" to "德语",
        "es" to "西班牙语",
        "it" to "意大利语",
        "ru" to "俄语"
    )

    // TTS语音偏好
    private var preferredChineseVoice = "zh-CN-XiaoxiaoNeural"
    private var preferredEnglishVoice = "en-US-JennyNeural"
    private var preferredJapaneseVoice = "ja-JP-NanamiNeural"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 请求麦克风权限
        requestMicrophonePermission()

        Log.d(TAG, "微软语音识别应用启动")

        setContent {
            LlyaTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SpeechRecognitionScreen()
                }
            }
        }
    }

    private fun requestMicrophonePermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED -> {
                Log.d(TAG, "麦克风权限已预先授予")
            }
            else -> {
                Log.d(TAG, "请求麦克风权限")
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun SpeechRecognitionScreen() {
        val context = LocalContext.current
        val primaryLanguage = remember { mutableStateOf("zh-CN") }
        val targetLanguage = remember { mutableStateOf("en") }
        val recognizedText = remember { mutableStateOf("") }
        val translatedText = remember { mutableStateOf("") }
        val accumulatedTextState = remember { mutableStateOf("") }
        val isRecognizing = remember { mutableStateOf(false) }
        val statusMessage = remember { mutableStateOf("准备就绪") }
        val bidirectionalMode = remember { mutableStateOf(enableBidirectionalTranslation) }

        // TTS播放状态
        val isSpeaking = remember { mutableStateOf(false) }

        // 历史记录ViewModel及状态
        val historyViewModel: HistoryViewModel = viewModel()
        val historyRecords by historyViewModel.historyRecords.collectAsState()
        // 筛选只有翻译文本的记录
        val translationRecords = remember(historyRecords) {
            historyRecords.filter { it.translatedText.isNotEmpty() }.take(5)
        }
        val isHistoryPlaying by historyViewModel.isPlaying.collectAsState()
        val isHistoryLoading by historyViewModel.isLoading.collectAsState()

        // 下拉菜单状态
        var sourceLanguageMenuExpanded by remember { mutableStateOf(false) }
        var targetLanguageMenuExpanded by remember { mutableStateOf(false) }

        // 深色背景色和强调色
        val darkBgColor = Color(0xFF1E1D2B)
        val purpleColor = Color(0xFF8364FD)
        val cardBgColor = Color(0xFF33324A)

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(darkBgColor)
        ) {
            // 顶部标题栏
            TopAppBar(
                title = {
                    Text(
                        text = "微软多语言连续语音识别与翻译",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { finish() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = darkBgColor,
                    titleContentColor = Color.White
                )
            )

            // 语言选择面板
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                color = cardBgColor
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 源语言
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { sourceLanguageMenuExpanded = true },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = supportedSpeechLanguages[primaryLanguage.value] ?: "选择源语言",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "源语言",
                                color = Color.Gray,
                                fontSize = 12.sp
                            )

                            DropdownMenu(
                                expanded = sourceLanguageMenuExpanded,
                                onDismissRequest = { sourceLanguageMenuExpanded = false },
                                modifier = Modifier.background(cardBgColor)
                            ) {
                                supportedSpeechLanguages.forEach { (code, name) ->
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                name,
                                                color = Color.White
                                            )
                                        },
                                        onClick = {
                                            primaryLanguage.value = code
                                            sourceLanguageMenuExpanded = false
                                        },
                                        colors = MenuDefaults.itemColors(
                                            textColor = Color.White
                                        )
                                    )
                                }
                            }
                        }

                        // 交换按钮
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(purpleColor)
                                .clickable {
                                    val sourceLang = primaryLanguage.value
                                    val targetLang = targetLanguage.value
                                    // 交换语言
                                    primaryLanguage.value = mapTranslationToSpeechLanguage(targetLang) ?: "en-US"
                                    targetLanguage.value = getTranslationLanguageCode(sourceLang)
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.SwapHoriz,
                                contentDescription = "交换语言",
                                tint = Color.White,
                                modifier = Modifier.size(24.dp)
                            )
                        }

                        // 目标语言
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { targetLanguageMenuExpanded = true },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = supportedTranslationLanguages[targetLanguage.value] ?: "选择目标语言",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "目标语言",
                                color = Color.Gray,
                                fontSize = 12.sp
                            )

                            DropdownMenu(
                                expanded = targetLanguageMenuExpanded,
                                onDismissRequest = { targetLanguageMenuExpanded = false },
                                modifier = Modifier.background(cardBgColor)
                            ) {
                                supportedTranslationLanguages.forEach { (code, name) ->
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                name,
                                                color = Color.White
                                            )
                                        },
                                        onClick = {
                                            targetLanguage.value = code
                                            targetLanguageMenuExpanded = false
                                        },
                                        colors = MenuDefaults.itemColors(
                                            textColor = Color.White
                                        )
                                    )
                                }
                            }
                        }
                    }

                    // 双向翻译默认开启，不显示开关
                    // 确保双向翻译始终开启
                    LaunchedEffect(Unit) {
                        bidirectionalMode.value = true
                        enableBidirectionalTranslation = true
                    }
                }
            }

            // 今天日期
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                val currentTime = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault()).format(java.util.Date())
                Text(
                    text = "今天 $currentTime",
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }

            // 整个内容区域改为可滚动
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                // 状态信息
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(12.dp))
                            .background(cardBgColor.copy(alpha = 0.7f))
                            .padding(12.dp)
                    ) {
                        Text(
                            text = "状态: ${statusMessage.value}",
                            color = Color.White,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                    Spacer(modifier = Modifier.height(12.dp))
                }

                // 当前识别的文本
                if (recognizedText.value.isNotEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(16.dp))
                                .background(cardBgColor)
                                .padding(16.dp)
                        ) {
                            Column {
                                Text(
                                    text = "识别结果",
                                    color = Color.Gray,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = recognizedText.value,
                                    color = Color.White,
                                    fontSize = 16.sp
                                )

                                if (isRecognizing.value) {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "正在识别",
                                            color = Color.White.copy(alpha = 0.6f),
                                            fontSize = 12.sp,
                                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                        )

                                        // 添加动态省略号
                                        val infiniteTransition = rememberInfiniteTransition(label = "typingDots")
                                        val dotsAlpha by infiniteTransition.animateFloat(
                                            initialValue = 0.4f,
                                            targetValue = 1.0f,
                                            animationSpec = infiniteRepeatable(
                                                animation = tween(500, easing = LinearEasing),
                                                repeatMode = RepeatMode.Reverse
                                            ),
                                            label = "dotAlpha"
                                        )

                                        Text(
                                            text = "...",
                                            color = Color.White.copy(alpha = dotsAlpha),
                                            fontSize = 12.sp,
                                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                        )
                                    }
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }

                // 当前翻译的文本
                if (translatedText.value.isNotEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(16.dp))
                                .background(purpleColor.copy(alpha = 0.7f))
                                .padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.Top
                            ) {
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = "翻译结果",
                                        color = Color.White.copy(alpha = 0.7f),
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = translatedText.value,
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }

                                // 播放按钮
                                if (translatedText.value.isNotEmpty() && !translatedText.value.startsWith("翻译中:")) {
                                    IconButton(
                                        onClick = {
                                            if (isSpeaking.value) {
                                                stopCurrentTts()
                                                isSpeaking.value = false
                                            } else {
                                                // 播放翻译文本
                                                val textToSpeak = translatedText.value.removePrefix("[").substringAfter("] ")
                                                if (textToSpeak.isNotEmpty()) {
                                                    isSpeaking.value = true
                                                    playTranslatedText(textToSpeak, targetLanguage.value) {
                                                        isSpeaking.value = false
                                                    }
                                                }
                                            }
                                        },
                                        modifier = Modifier.size(32.dp)
                                    ) {
                                        Icon(
                                            imageVector = if (isSpeaking.value) Icons.Default.Stop else Icons.Default.PlayArrow,
                                            contentDescription = if (isSpeaking.value) "停止播放" else "播放翻译",
                                            tint = Color.White,
                                            modifier = Modifier.size(20.dp)
                                        )
                                    }
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }

                // 累积识别的文本
                if (accumulatedTextState.value.isNotEmpty()) {
                    item {
                        Column {
                            Text(
                                text = "识别与翻译历史:",
                                color = Color.White,
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(16.dp))
                                    .background(cardBgColor)
                                    .padding(16.dp)
                            ) {
                                Text(
                                    text = accumulatedTextState.value,
                                    color = Color.White,
                                    fontSize = 14.sp,
                                    lineHeight = 20.sp
                                )
                            }
                        }
                    }
                }

                // 历史记录列表（最近5条）
                if (translationRecords.isNotEmpty()) {
                    item {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {
                            Text(
                                text = "最近翻译记录",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            translationRecords.forEach { record ->
                                HistoryItemCard(
                                    record = record,
                                    onPlay = {
                                        historyViewModel.playTranslation(record)
                                    },
                                    isPlaying = isHistoryPlaying && historyViewModel.currentPlayingId.value == record.id,
                                    isLoading = isHistoryLoading && historyViewModel.currentLoadingId.value == record.id,
                                    purpleColor = purpleColor
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                    }
                }
            }

            // 底部控制栏 - 确保它始终可见
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                color = cardBgColor
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 录音状态提示
                    if (isRecognizing.value) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.Red.copy(alpha = 0.1f))
                                .padding(vertical = 4.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "正在录音",
                                color = Color.White,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 左侧按钮 - 播放功能
                        if (translatedText.value.isNotEmpty() && !translatedText.value.startsWith("翻译中:")) {
                            IconButton(
                                onClick = {
                                    if (isSpeaking.value) {
                                        stopCurrentTts()
                                        isSpeaking.value = false
                                    } else {
                                        // 播放翻译文本
                                        val textToSpeak = translatedText.value.removePrefix("[").substringAfter("] ")
                                        if (textToSpeak.isNotEmpty()) {
                                            isSpeaking.value = true
                                            playTranslatedText(textToSpeak, targetLanguage.value) {
                                                isSpeaking.value = false
                                            }
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .size(50.dp)
                                    .clip(CircleShape)
                                    .background(cardBgColor)
                            ) {
                                Icon(
                                    imageVector = if (isSpeaking.value) Icons.Default.Stop else Icons.Default.PlayArrow,
                                    contentDescription = if (isSpeaking.value) "停止播放" else "播放翻译",
                                    tint = Color.White
                                )
                            }
                        } else {
                            // 占位
                            Spacer(modifier = Modifier.size(50.dp))
                        }

                        // 语音输入按钮
                        Box(
                            modifier = Modifier
                                .size(70.dp)
                                .clip(CircleShape)
                                .background(if (isRecognizing.value) Color.Red else purpleColor)
                                .clickable {
                                    if (isRecognizing.value) {
                                        // 防止频繁点击
                                        if (isStopping) {
                                            Toast.makeText(context, "正在停止中，请稍候...", Toast.LENGTH_SHORT).show()
                                            return@clickable
                                        }

                                        statusMessage.value = "正在停止识别..."
                                        isStopping = true

                                        // 异步停止识别，避免阻塞UI
                                        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                                            try {
                                                stopRecognitionAsync()

                                                // 在主线程更新UI
                                                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                                                    isRecognizing.value = false
                                                    statusMessage.value = "识别已停止"
                                                    isStopping = false
                                                    Toast.makeText(context, "识别已停止", Toast.LENGTH_SHORT).show()
                                                }
                                            } catch (e: Exception) {
                                                Log.e(TAG, "停止识别失败: ${e.message}", e)
                                                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                                                    statusMessage.value = "停止失败: ${e.message}"
                                                    isStopping = false
                                                    Toast.makeText(context, "停止识别失败", Toast.LENGTH_SHORT).show()
                                                }
                                            }
                                        }
                                    } else {
                                        statusMessage.value = "初始化语音识别..."
                                        try {
                                            startContinuousRecognitionWithTranslation(
                                                sourceLanguage = primaryLanguage.value,
                                                targetLanguage = targetLanguage.value,
                                                onRecognized = { text ->
                                                    recognizedText.value = text
                                                    Log.d(TAG, "识别结果: $text")
                                                },
                                                onTranslated = { translated ->
                                                    translatedText.value = translated
                                                    Log.d(TAG, "翻译结果: $translated")
                                                },
                                                onAccumulated = { accumulated ->
                                                    accumulatedTextState.value = accumulated
                                                }
                                            )
                                            isRecognizing.value = true
                                            statusMessage.value = "识别中..."
                                            Toast.makeText(context, "开始识别，请说话", Toast.LENGTH_SHORT).show()
                                        } catch (e: Exception) {
                                            Log.e(TAG, "启动识别失败: ${e.message}", e)
                                            statusMessage.value = "启动失败: ${e.message}"
                                            Toast.makeText(context, "启动识别失败: ${e.message}", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            // 录音中的波纹动画
                            if (isRecognizing.value) {
                                val animatedSize = remember { Animatable(1f) }

                                LaunchedEffect(Unit) {
                                    animatedSize.animateTo(
                                        targetValue = 1.2f,
                                        animationSpec = infiniteRepeatable(
                                            animation = tween(800, easing = FastOutSlowInEasing),
                                            repeatMode = RepeatMode.Reverse
                                        )
                                    )
                                }

                                Box(
                                    modifier = Modifier
                                        .size(70.dp)
                                        .scale(animatedSize.value)
                                        .clip(CircleShape)
                                        .background(Color.Red.copy(alpha = 0.3f))
                                )
                            }

                            Icon(
                                imageVector = Icons.Default.Mic,
                                contentDescription = if (isRecognizing.value) "停止录音" else "开始录音",
                                tint = Color.White,
                                modifier = Modifier.size(32.dp)
                            )
                        }

                        // 右侧按钮 - 清除文本
                        IconButton(
                            onClick = {
                                recognizedText.value = ""
                                translatedText.value = ""
                                accumulatedTextState.value = ""
                                recognitionHistory.clear()
                                accumulatedText = ""
                            },
                            modifier = Modifier
                                .size(50.dp)
                                .clip(CircleShape)
                                .background(cardBgColor)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "清除",
                                tint = Color.White
                            )
                        }
                    }
                }
            }
        }
    }


    // 修复后的连续识别和翻译方法 - 解决翻译丢失和累积文本消失问题
    private fun startContinuousRecognitionWithTranslation(
        sourceLanguage: String,
        targetLanguage: String,
        onRecognized: (String) -> Unit,
        onTranslated: (String) -> Unit,
        onAccumulated: (String) -> Unit
    ) {
        try {
            Log.d(TAG, "开始创建翻译配置，源语言: $sourceLanguage, 目标语言: $targetLanguage")

            // 使用语音密钥创建翻译配置
            val translationConfig = SpeechTranslationConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            Log.d(TAG, "翻译配置创建成功")

            // 设置源语言和目标语言
            translationConfig.speechRecognitionLanguage = sourceLanguage
            Log.d(TAG, "已设置识别语言: $sourceLanguage")

            // 启用自动语言检测（支持双向翻译）
            val candidateLanguages = if (enableBidirectionalTranslation) {
                listOf(sourceLanguage, mapTranslationToSpeechLanguage(targetLanguage) ?: "en-US")
            } else {
                listOf(sourceLanguage)
            }

            val autoDetectConfig = AutoDetectSourceLanguageConfig.fromLanguages(candidateLanguages)
            Log.d(TAG, "已创建自动语言检测配置，候选语言: $candidateLanguages")

            // 指定翻译目标语言
            val targetLangCode = getTranslationLanguageCode(targetLanguage)
            translationConfig.addTargetLanguage(targetLangCode)
            Log.d(TAG, "添加目标语言: $targetLangCode")

            // 双向翻译：同时添加源语言作为翻译目标
            if (enableBidirectionalTranslation) {
                val sourceLangCode = getTranslationLanguageCode(sourceLanguage)
                if (sourceLangCode != targetLangCode) {
                    translationConfig.addTargetLanguage(sourceLangCode)
                    Log.d(TAG, "添加源语言作为翻译目标: $sourceLangCode")
                }
            }

            // 启用连续语言检测
            translationConfig.setProperty(PropertyId.SpeechServiceConnection_LanguageIdMode, "Continuous")
            translationConfig.setProperty(PropertyId.SpeechServiceResponse_PostProcessingOption, "TrueText")
            translationConfig.setProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "1000")
            Log.d(TAG, "已设置连续检测属性")

            // 创建音频配置
            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()
            Log.d(TAG, "音频配置创建成功")

            // 创建识别器 - 使用自动语言检测配置
            translationRecognizer = TranslationRecognizer(translationConfig, autoDetectConfig, audioConfig)
            Log.d(TAG, "翻译识别器创建成功")

            // 创建语音合成器
            val synthesizerConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            synthesizerConfig.speechSynthesisVoiceName = getVoiceNameForLanguage(targetLanguage)
            synthesizer = SpeechSynthesizer(synthesizerConfig)
            Log.d(TAG, "语音合成器创建成功")

            // 注册识别事件
            translationRecognizer?.let { recognizer ->
                Log.d(TAG, "开始注册事件处理器")

                // 识别中事件
                recognizer.recognizing.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    if (recognizedText.isNotEmpty()) {
                        Log.d(TAG, "识别中: $recognizedText")
                        onRecognized("识别中: $recognizedText")

                        // 获取实时翻译结果
                        val detectedLanguage = getDetectedLanguageFromEvent(event)
                        val effectiveTargetLanguage = getEffectiveTargetLanguage(detectedLanguage, targetLanguage, sourceLanguage)

                        val translatedText = event.result.translations[effectiveTargetLanguage] ?: ""
                        if (translatedText.isNotEmpty()) {
                            Log.d(TAG, "实时翻译: $translatedText")
                            onTranslated("翻译中: $translatedText")
                        }
                    }
                }

                // 语言检测和翻译完成事件 - 修复翻译丢失问题的关键代码
                recognizer.recognized.addEventListener { _, event ->
                    Log.d(TAG, "识别结果: ${event.result.reason} - ${event.result.text}")

                    if (event.result.reason == ResultReason.TranslatedSpeech || event.result.reason == ResultReason.RecognizedSpeech) {
                        val detectedLanguage = getDetectedLanguageFromEvent(event)
                        val recognizedText = event.result.text

                        Log.d(TAG, "检测到语言: $detectedLanguage, 文本: $recognizedText")

                        // 过滤掉无用内容
                        if (shouldProcessText(recognizedText)) {
                            // 判断检测到的语言与目标语言是否匹配
                            val effectiveTargetLanguage = getEffectiveTargetLanguage(detectedLanguage, targetLanguage, sourceLanguage)

                            // 获取翻译文本 - 修复翻译丢失的关键代码
                            val translatedText = event.result.translations[effectiveTargetLanguage] ?: ""
                            Log.d(TAG, "最终翻译结果 ($effectiveTargetLanguage): $translatedText")

                            // 构建显示文本
                            val sourceLangName = getLanguageDisplayName(detectedLanguage ?: sourceLanguage)
                            val targetLangName = getLanguageDisplayName(effectiveTargetLanguage)

                            val displayText = "[$sourceLangName] $recognizedText"
                            val translationDisplayText = "[$targetLangName] $translatedText"

                            // 更新UI
                            onRecognized(displayText)
                            onTranslated(translationDisplayText)

                            // 添加到累积文本和历史记录 - 修复累积文本消失问题
                            synchronized(recognitionHistory) {
                                val fullDisplayText = "$displayText\n⟹ $translationDisplayText"
                                recognitionHistory.add(fullDisplayText)

                                // 确保累积文本不会丢失 - 保持所有历史记录
                                accumulatedText = recognitionHistory.joinToString("\n\n")
                                onAccumulated(accumulatedText)

                                Log.d(TAG, "已添加到历史记录，当前历史记录数量: ${recognitionHistory.size}")
                                Log.d(TAG, "累积文本长度: ${accumulatedText.length}")
                            }

                            // 将翻译文本加入TTS队列
                            if (translatedText.isNotEmpty()) {
                                Log.d(TAG, "将文本加入TTS队列: $translatedText")
                                enqueueTts(translatedText, effectiveTargetLanguage)
                            }
                        } else {
                            Log.d(TAG, "过滤掉无用文本: $recognizedText")
                        }
                    } else {
                        Log.d(TAG, "识别结果不是语音或翻译: ${event.result.reason}")
                    }
                }

                // 取消事件
                recognizer.canceled.addEventListener { _, event ->
                    Log.e(TAG, "识别被取消: ${event.reason}, 详情: ${event.errorDetails}")
                    onRecognized("识别被取消: ${event.errorDetails}")
                }

                // 识别结束事件
                recognizer.sessionStopped.addEventListener { _, _ ->
                    Log.d(TAG, "识别会话结束")
                }

                // 启动连续识别
                try {
                    Log.d(TAG, "开始启动连续识别...")
                    recognizer.startContinuousRecognitionAsync().get()
                    Log.d(TAG, "连续识别已启动")
                    onRecognized("请开始说话...")
                } catch (e: Exception) {
                    Log.e(TAG, "连续识别启动失败: ${e.message}")
                    throw e
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "识别初始化失败: ${ex.message}")
            ex.printStackTrace()
            throw ex
        }
    }

    // 获取检测到的语言
    private fun getDetectedLanguageFromEvent(event: com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionEventArgs): String? {
        try {
            val properties = event.result.properties
            if (properties != null) {
                val autoDetectResult = properties.getProperty(PropertyId.SpeechServiceConnection_AutoDetectSourceLanguageResult)
                if (autoDetectResult != null && autoDetectResult.isNotEmpty()) {
                    Log.d(TAG, "检测到的语言: $autoDetectResult")
                    return autoDetectResult
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "获取检测到的语言时出错", ex)
        }
        return null
    }

    // 将翻译语言代码映射为语音识别语言代码
    private fun mapTranslationToSpeechLanguage(translationCode: String): String? {
        return when (translationCode) {
            "en" -> "en-US"
            "zh-Hans" -> "zh-CN"
            "zh-Hant" -> "zh-TW"
            "ja" -> "ja-JP"
            "ko" -> "ko-KR"
            "fr" -> "fr-FR"
            "es" -> "es-ES"
            "de" -> "de-DE"
            "it" -> "it-IT"
            "ru" -> "ru-RU"
            else -> null
        }
    }

    // 获取翻译语言代码
    private fun getTranslationLanguageCode(speechLanguageCode: String): String {
        return when {
            speechLanguageCode.startsWith("zh-CN") -> "zh-Hans"
            speechLanguageCode.startsWith("zh-TW") -> "zh-Hant"
            speechLanguageCode.startsWith("en") -> "en"
            speechLanguageCode.startsWith("ja") -> "ja"
            speechLanguageCode.startsWith("ko") -> "ko"
            speechLanguageCode.startsWith("fr") -> "fr"
            speechLanguageCode.startsWith("de") -> "de"
            speechLanguageCode.startsWith("es") -> "es"
            speechLanguageCode.startsWith("it") -> "it"
            speechLanguageCode.startsWith("ru") -> "ru"
            speechLanguageCode.contains("-") -> speechLanguageCode.split("-")[0]
            else -> speechLanguageCode
        }
    }

    private fun getVoiceNameForLanguage(language: String): String {
        return when {
            language.startsWith("zh") -> preferredChineseVoice
            language.startsWith("en") -> preferredEnglishVoice
            language.startsWith("ja") -> preferredJapaneseVoice
            language.startsWith("de") -> "de-DE-KatjaNeural"
            language.startsWith("fr") -> "fr-FR-DeniseNeural"
            language.startsWith("es") -> "es-ES-ElviraNeural"
            language.startsWith("it") -> "it-IT-ElsaNeural"
            language.startsWith("ru") -> "ru-RU-SvetlanaNeural"
            language.startsWith("ko") -> "ko-KR-SunHiNeural"
            else -> "en-US-JennyNeural"
        }
    }

    private fun shouldProcessText(text: String): Boolean {
        return text.length > 2 && !text.matches(Regex("^[\\p{Punct}\\s]+$"))
    }

    private fun getLanguageDisplayName(languageCode: String): String {
        val translationName = supportedTranslationLanguages[languageCode]
        if (translationName != null) return translationName

        val speechName = supportedSpeechLanguages[languageCode]
        if (speechName != null) return speechName

        return languageCode
    }

    // 根据双向翻译逻辑确定实际的目标语言
    private fun getEffectiveTargetLanguage(detectedLanguage: String?, targetLanguage: String, sourceLanguage: String): String {
        if (!enableBidirectionalTranslation || detectedLanguage.isNullOrEmpty()) {
            return getTranslationLanguageCode(targetLanguage)
        }

        val detectedShortCode = if (detectedLanguage.contains("-")) {
            detectedLanguage.split("-")[0]
        } else detectedLanguage

        val targetShortCode = getTranslationLanguageCode(targetLanguage)

        // 如果检测到的语言与目标语言匹配，则使用源语言作为翻译目标
        if (detectedShortCode.equals(targetShortCode, ignoreCase = true)) {
            Log.d(TAG, "检测到的语言($detectedLanguage)与目标语言($targetLanguage)匹配，切换为源语言翻译")
            return getTranslationLanguageCode(sourceLanguage)
        }

        return getTranslationLanguageCode(targetLanguage)
    }

    private fun enqueueTts(text: String, language: String) {
        val ttsLanguage = getTtsLanguageFromTranslation(language)
        Log.d(TAG, "加入TTS队列: '$text', 语言: $language -> TTS语言: $ttsLanguage")

        ttsQueue.add(Pair(text, ttsLanguage))

        if (!isProcessingTts) {
            processTtsQueue()
        }
    }

    private fun processTtsQueue() {
        ttsExecutor.execute {
            isProcessingTts = true
            while (ttsQueue.isNotEmpty()) {
                val (text, language) = ttsQueue.poll() ?: break
                try {
                    Log.d(TAG, "准备合成语音: '$text', 语言: $language")

                    val voiceName = getVoiceNameForLanguage(language)
                    Log.d(TAG, "使用语音: $voiceName")

                    synchronized(this) {
                        try {
                            synthesizer?.close()
                        } catch (e: Exception) {
                            Log.e(TAG, "关闭现有语音合成器异常: ${e.message}")
                        }

                        val synthesizerConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
                        synthesizerConfig.speechSynthesisVoiceName = voiceName
                        synthesizer = SpeechSynthesizer(synthesizerConfig)

                        val synth = synthesizer
                        if (synth != null) {
                            Log.d(TAG, "开始语音合成: '$text'")
                            val result = synth.SpeakTextAsync(text).get()
                            if (result?.reason != ResultReason.SynthesizingAudioCompleted) {
                                Log.e(TAG, "语音合成失败: ${result?.reason}")
                            } else {
                                Log.d(TAG, "语音合成成功完成: '$text'")
                            }
                        }
                    }
                } catch (ex: Exception) {
                    Log.e(TAG, "语音合成异常: ${ex.message}", ex)
                }

                try { Thread.sleep(300) } catch (e: InterruptedException) { }
            }
            isProcessingTts = false
        }
    }

    private fun getTtsLanguageFromTranslation(translationLanguage: String): String {
        return when {
            translationLanguage == "zh-Hans" || translationLanguage == "zh" -> "zh-CN"
            translationLanguage == "zh-Hant" -> "zh-TW"
            translationLanguage == "en" -> "en-US"
            translationLanguage == "ja" -> "ja-JP"
            translationLanguage == "ko" -> "ko-KR"
            translationLanguage == "fr" -> "fr-FR"
            translationLanguage == "de" -> "de-DE"
            translationLanguage == "es" -> "es-ES"
            translationLanguage == "it" -> "it-IT"
            translationLanguage == "ru" -> "ru-RU"
            translationLanguage.contains("-") -> translationLanguage
            else -> "$translationLanguage-$translationLanguage".uppercase()
        }
    }

    // 优化后的异步停止方法，解决卡顿问题
    private suspend fun stopRecognitionAsync() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始异步停止语音识别...")

            // 1. 首先停止TTS队列，避免新的合成任务
            ttsQueue.clear()
            isProcessingTts = false
            Log.d(TAG, "已清空TTS队列")

            // 2. 异步停止翻译识别器，设置超时
            translationRecognizer?.let { recognizer ->
                try {
                    Log.d(TAG, "正在停止翻译识别器...")

                    // 使用withTimeout设置超时，避免无限等待
                    withTimeout(3000) { // 3秒超时
                        // 异步停止，不使用.get()阻塞
                        val stopFuture = recognizer.stopContinuousRecognitionAsync()

                        // 在协程中等待完成
                        try {
                            // 使用Java Future的get方法，但在IO线程中执行
                            stopFuture.get()
                            Log.d(TAG, "翻译识别器已成功停止")
                        } catch (e: Exception) {
                            Log.w(TAG, "停止识别器时出现异常: ${e.message}")
                        }
                    }
                } catch (e: kotlinx.coroutines.TimeoutCancellationException) {
                    Log.w(TAG, "停止识别器超时，将强制关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "停止识别器失败: ${e.message}")
                }

                // 3. 关闭识别器资源
                try {
                    recognizer.close()
                    Log.d(TAG, "翻译识别器资源已关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "关闭翻译识别器资源异常: ${e.message}")
                }
            }

            translationRecognizer = null

            // 4. 异步关闭语音合成器
            launch {
                try {
                    synchronized(this@MicrosoftSpeechActivity) {
                        synthesizer?.let { synth ->
                            try {
                                synth.close()
                                Log.d(TAG, "语音合成器已关闭")
                            } catch (e: Exception) {
                                Log.e(TAG, "关闭语音合成器异常: ${e.message}")
                            }
                        }
                        synthesizer = null
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "关闭语音合成器时出错: ${e.message}")
                }
            }

            Log.d(TAG, "异步停止语音识别完成")

        } catch (ex: Exception) {
            Log.e(TAG, "异步停止识别时发生错误: ${ex.message}")
            ex.printStackTrace()
            throw ex
        }
    }

    // 保留原来的同步方法，用于onDestroy调用
    private fun stopRecognition() {
        try {
            Log.d(TAG, "开始停止语音识别...")

            // 快速清理，不等待网络响应
            ttsQueue.clear()
            isProcessingTts = false

            translationRecognizer?.let { recognizer ->
                try {
                    // 不使用.get()，避免阻塞
                    recognizer.stopContinuousRecognitionAsync()
                    Log.d(TAG, "已发送停止识别请求")
                } catch (e: Exception) {
                    Log.e(TAG, "发送停止请求失败: ${e.message}")
                }

                try {
                    recognizer.close()
                    Log.d(TAG, "翻译识别器已关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "关闭翻译识别器异常: ${e.message}")
                }
            }

            translationRecognizer = null

            synchronized(this) {
                try {
                    synthesizer?.close()
                } catch (e: Exception) {
                    Log.e(TAG, "关闭语音合成器异常: ${e.message}")
                }
                synthesizer = null
            }

            Log.d(TAG, "所有资源已关闭")
        } catch (ex: Exception) {
            Log.e(TAG, "停止识别时发生错误: ${ex.message}")
            ex.printStackTrace()
        }
    }

    // 播放翻译文本的方法
    private fun playTranslatedText(text: String, language: String, onComplete: () -> Unit) {
        try {
            val ttsLanguage = getTtsLanguageFromTranslation(language)
            Log.d(TAG, "播放翻译文本: '$text', 语言: $language -> TTS语言: $ttsLanguage")

            // 停止当前播放
            stopCurrentTts()

            // 创建新的语音合成器
            val synthesizerConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            synthesizerConfig.speechSynthesisVoiceName = getVoiceNameForLanguage(ttsLanguage)

            val currentSynthesizer = SpeechSynthesizer(synthesizerConfig)

            // 在后台线程执行TTS
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                try {
                    Log.d(TAG, "开始语音合成播放: '$text'")
                    val result = currentSynthesizer.SpeakTextAsync(text).get()

                    kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                        if (result?.reason == ResultReason.SynthesizingAudioCompleted) {
                            Log.d(TAG, "语音合成播放成功完成: '$text'")
                        } else {
                            Log.e(TAG, "语音合成播放失败: ${result?.reason}")
                        }
                        onComplete()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "语音合成播放异常: ${e.message}", e)
                    kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                        onComplete()
                    }
                } finally {
                    try {
                        currentSynthesizer.close()
                    } catch (e: Exception) {
                        Log.e(TAG, "关闭语音合成器异常: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "播放翻译文本失败: ${e.message}", e)
            onComplete()
        }
    }

    // 停止当前TTS播放
    private fun stopCurrentTts() {
        try {
            // 清空TTS队列
            ttsQueue.clear()
            isProcessingTts = false

            // 停止当前合成器
            synchronized(this) {
                try {
                    synthesizer?.let { synth ->
                        // 尝试停止当前播放
                        synth.close()
                        Log.d(TAG, "已停止当前TTS播放")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止TTS播放异常: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止当前TTS失败: ${e.message}")
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "活动销毁，清理资源")

        // 设置停止标志，防止新的操作
        isStopping = true

        // 停止TTS播放
        stopCurrentTts()

        // 快速停止识别，不等待网络响应
        stopRecognition()

        // 强制关闭TTS执行器
        try {
            ttsExecutor.shutdownNow()
            Log.d(TAG, "TTS执行器已强制关闭")
        } catch (e: Exception) {
            Log.e(TAG, "关闭TTS执行器异常: ${e.message}")
        }

        super.onDestroy()
    }
}

@Preview(showBackground = true)
@Composable
fun MicrosoftSpeechPreview() {
    LlyaTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            // Preview content
        }
    }
}