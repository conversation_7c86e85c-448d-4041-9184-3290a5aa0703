package com.example.llya.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.asr.GoogleCloudSpeechToTextClient
import com.example.llya.asr.TencentCloudSpeechToTextClient
import com.example.llya.ai.VolcEngineAIClient
import com.example.llya.audio.AudioRecorder
import com.example.llya.utils.TextPersistenceManager
import com.example.llya.utils.AIChatPersistenceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class SpeechRecognitionViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "SpeechRecognitionVM"

        // 火山引擎豆包AI配置
        // 注意：这些是占位符，需要用户提供实际的API密钥
        private const val VOLC_ENGINE_API_KEY = "203a5d00-ab6d-45df-b5dd-acbbe4b4f5f3"
        private const val VOLC_ENGINE_SECRET_KEY = "TWpNMlpUUXdZV1prT0dSaU5HWTRaamhtWkRsa00yRTJOREl4Wm1KbE1XTQ"

        // 腾讯云语音识别配置
        private const val TENCENT_APP_ID = "1251118525"
        private const val TENCENT_SECRET_ID = "AKIDoTt53sTpwoW2KQIuHPIvJSM2TeaHkyyk"
        private const val TENCENT_SECRET_KEY = "aw4wZy83iBQP9fx1qQmunRq1yf7KJ3Yp"

        // 使用腾讯云的语言列表
        private val TENCENT_SUPPORTED_LANGUAGES = setOf(
            "zh-CN",      // 中文普通话
            "yue-Hant-HK", // 粤语
            "en-US",      // 英语
            "ko-KR",      // 韩语
            "ja-JP",      // 日语
            "th-TH",      // 泰语
            "id-ID",      // 印度尼西亚语
            "vi-VN",      // 越南语
            "ms-MY",      // 马来语
            "fil-PH",     // 菲律宾语
            "pt-PT",      // 葡萄牙语
            "tr-TR",      // 土耳其语
            "ar-SA",      // 阿拉伯语
            "es-ES",      // 西班牙语
            "hi-IN",      // 印地语
            "fr-FR",      // 法语
            "de-DE"       // 德语
        )

        // 腾讯云语言代码映射
        private val LANGUAGE_TO_TENCENT_MODEL = mapOf(
            "zh-CN" to "16k_zh",            // 中文普通话
            "yue-Hant-HK" to "16k_zh_yue",  // 粤语
            "en-US" to "16k_en",            // 英语
            "ko-KR" to "16k_ko",            // 韩语
            "ja-JP" to "16k_ja",            // 日语
            "th-TH" to "16k_th",            // 泰语
            "id-ID" to "16k_id",            // 印度尼西亚语
            "vi-VN" to "16k_vi",            // 越南语
            "ms-MY" to "16k_ms",            // 马来语
            "fil-PH" to "16k_fil",          // 菲律宾语
            "pt-PT" to "16k_pt",            // 葡萄牙语
            "tr-TR" to "16k_tr",            // 土耳其语
            "ar-SA" to "16k_ar",            // 阿拉伯语
            "es-ES" to "16k_es",            // 西班牙语
            "hi-IN" to "16k_hi",            // 印地语
            "fr-FR" to "16k_fr",            // 法语
            "de-DE" to "16k_de"             // 德语
        )
    }

    // 文本持久化管理器
    private val textPersistenceManager = TextPersistenceManager(application.applicationContext)

    // AI聊天记录持久化管理器
    private val aiChatPersistenceManager = AIChatPersistenceManager(application.applicationContext)

    // 音频录制器
    private val audioRecorder = AudioRecorder(
        onAudioBufferReady = { audioData ->
            // 根据当前选择的语言，将音频数据发送到对应的语音识别服务
            if (shouldUseTencentCloud(_selectedLanguage.value)) {
                tencentAsrClient?.sendAudioData(audioData)
            } else {
                googleAsrClient.sendAudioData(audioData)
            }
        },
        onError = { errorMsg ->
            _errorMessage.value = "录音错误: $errorMsg"
            stopRecognition()
        }
    )

    // Google Cloud语音识别客户端
    private val googleAsrClient = GoogleCloudSpeechToTextClient(viewModelScope)

    // 腾讯云语音识别客户端
    private var tencentAsrClient: TencentCloudSpeechToTextClient? = null

    // 当前活跃的ASR客户端类型
    private val _activeAsrType = MutableStateFlow<AsrType>(AsrType.GOOGLE)
    val activeAsrType: StateFlow<AsrType> = _activeAsrType

    // 当前使用的ASR服务名称（用于显示）
    private val _currentAsrServiceName = MutableStateFlow<String>("Google Cloud")
    val currentAsrServiceName: StateFlow<String> = _currentAsrServiceName

    // 火山引擎豆包AI客户端
    private val volcAiClient = VolcEngineAIClient(
        VOLC_ENGINE_API_KEY,
        VOLC_ENGINE_SECRET_KEY,
        viewModelScope
    )

    // 识别状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording

    // 识别结果 - 合并Google和腾讯的结果
    private val _recognitionResult = MutableStateFlow<String>("")
    val recognitionResult: StateFlow<String> = _recognitionResult

    // 添加结果缓存，避免停止录音后显示null
    val _lastValidResult = MutableStateFlow<String>("")
    val lastValidResult: StateFlow<String> = _lastValidResult

    // 累积文本 - 合并Google和腾讯的结果
    private val _cumulativeContent = MutableStateFlow<String>("")
    val cumulativeContent: StateFlow<String> = _cumulativeContent

    // 清除累积内容
    fun clearCumulativeContent() {
        _cumulativeContent.value = ""
        Log.d(TAG, "已清除累积内容")
    }

    // 当前显示的临时内容，用于实时显示
    private val _currentDisplayContent = MutableStateFlow<String>("")
    val currentDisplayContent: StateFlow<String> = _currentDisplayContent

    // 是否已有录音会话完成
    private var hasCompletedSession = false

    // AI回复结果
    private val _aiResponse = MutableStateFlow<String>("")
    val aiResponse: StateFlow<String> = _aiResponse

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?> (null)
    val errorMessage: StateFlow<String?> = _errorMessage

    // 源语言选择
    private val _selectedLanguage = MutableStateFlow("zh-CN")
    val selectedLanguage: StateFlow<String> = _selectedLanguage

    // 目标语言选择
    private val _targetLanguage = MutableStateFlow("en-US")
    val targetLanguage: StateFlow<String> = _targetLanguage

    // 转写性能指标
    private val _transcriptionStats = MutableStateFlow("未开始")
    val transcriptionStats: StateFlow<String> = _transcriptionStats

    // 转写开始时间
    private var transcriptionStartTime = 0L

    // 转写数据包计数
    private var transcriptionPacketsCount = 0

    // 转写总时间
    private var totalTranscriptionTime = 0L

    // 转写总字符数
    private var totalCharactersTranscribed = 0

    // 可用的语言列表
    val availableLanguages = listOf(
        "zh-CN" to "中文普通话",
        "en-US" to "英语",
        "yue-Hant-HK" to "粤语",
        "ko-KR" to "韩语",
        "ja-JP" to "日语",
        "th-TH" to "泰语",
        "vi-VN" to "越南语",
        "ms-MY" to "马来语",
        "id-ID" to "印尼语",
        "fr-FR" to "法语",
        "de-DE" to "德语",
        "es-ES" to "西班牙语",
        "it-IT" to "意大利语",
        "ru-RU" to "俄语",
        "ar-SA" to "阿拉伯语"
    )

    // 翻译功能开关
    private val _isTranslationEnabled = MutableStateFlow(true)
    val isTranslationEnabled: StateFlow<Boolean> = _isTranslationEnabled

    // 会议模式开关，用于区分是会议记录模式还是AI聊天模式
    private val _isInMeetingMode = MutableStateFlow(false)
    val isInMeetingMode: StateFlow<Boolean> = _isInMeetingMode

    // AI思考模式开关，启用后AI回复会包含思考过程
    private val _isThinkModeEnabled = MutableStateFlow(false)
    val isThinkModeEnabled: StateFlow<Boolean> = _isThinkModeEnabled

    // 自定义AI身份回答
    private val _customIdentityResponse = MutableStateFlow("我是Theta小助手！很高兴能帮助到您。请问有什么我可以协助您的吗？")
    val customIdentityResponse: StateFlow<String> = _customIdentityResponse

    // 当前正在识别中的临时文本
    private val _currentSessionText = MutableStateFlow<String>("")

    // 是否是句子结束的标志
    private var isFinalResult = false

    // 会议开始时间
    private var meetingStartTime: Long = 0

    // 用于加载所有会议记录的状态流
    private val _meetingRecords = MutableStateFlow<List<TextPersistenceManager.MeetingRecord>>(emptyList())
    val meetingRecords: StateFlow<List<TextPersistenceManager.MeetingRecord>> = _meetingRecords

    // 用于加载会议记录详情的状态流
    private val _selectedRecord = MutableStateFlow<TextPersistenceManager.MeetingRecord?>(null)
    val selectedRecord: StateFlow<TextPersistenceManager.MeetingRecord?> = _selectedRecord

    // AI是否正在处理
    private val _isAiProcessing = MutableStateFlow(false)
    val isAiProcessing: StateFlow<Boolean> = _isAiProcessing

    // 会话聊天记录
    private val _chatSessions = MutableStateFlow<List<AIChatPersistenceManager.ChatSession>>(emptyList())
    val chatSessions: StateFlow<List<AIChatPersistenceManager.ChatSession>> = _chatSessions

    // 当前选择的聊天会话
    private val _selectedChatSession = MutableStateFlow<AIChatPersistenceManager.ChatSession?>(null)
    val selectedChatSession: StateFlow<AIChatPersistenceManager.ChatSession?> = _selectedChatSession

    // ASR服务提供商类型
    enum class AsrType {
        GOOGLE,
        TENCENT
    }

    init {
        // 初始化腾讯云语音识别客户端
        initializeTencentAsrClient()

        // 根据初始语言设置ASR服务
        val initialLanguage = _selectedLanguage.value
        val shouldUseTencent = shouldUseTencentCloud(initialLanguage)
        _activeAsrType.value = if (shouldUseTencent) AsrType.TENCENT else AsrType.GOOGLE
        _currentAsrServiceName.value = if (shouldUseTencent) "腾讯云" else "Google Cloud"
        Log.d(TAG, "初始化ASR服务: 语言=$initialLanguage, 使用${_currentAsrServiceName.value}服务")

        // 首先从持久化存储加载之前的会议记录
        viewModelScope.launch {
            val savedText = textPersistenceManager.loadText()
            if (savedText.isNotEmpty()) {
                // 设置累积文本
                _cumulativeContent.value = savedText
                hasCompletedSession = true
                Log.d(TAG, "已从持久化存储加载会议记录: $savedText")
            }
        }

        // 监听Google ASR客户端的识别结果
        viewModelScope.launch {
            googleAsrClient.recognitionResult.collect { text ->
                if (_activeAsrType.value == AsrType.GOOGLE &&
                    text.isNotEmpty() &&
                    !text.contains("连接已建立") &&
                    !text.contains("失败") &&
                    !text.contains("出错")) {
                    // 记录有效的识别结果
                    Log.d(TAG, "【ASR服务提供商: Google Cloud】识别结果: $text")

                    // 更新识别结果
                    _recognitionResult.value = text
                    _currentDisplayContent.value = text
                    _currentSessionText.value = text
                    _lastValidResult.value = text
                }
            }
        }

        // 监听腾讯云ASR客户端的识别结果
        viewModelScope.launch {
            tencentAsrClient?.recognitionResult?.collect { text ->
                if (_activeAsrType.value == AsrType.TENCENT &&
                    text.isNotEmpty() &&
                    !text.contains("连接已建立") &&
                    !text.contains("失败") &&
                    !text.contains("出错")) {
                    // 记录有效的识别结果
                    Log.d(TAG, "【ASR服务提供商: 腾讯云】识别结果: $text")

                    // 更新识别结果
                    _recognitionResult.value = text
                    _currentDisplayContent.value = text
                    _currentSessionText.value = text
                    _lastValidResult.value = text
                }
            }
        }

        // 监听腾讯云ASR客户端的累积文本
        viewModelScope.launch {
            tencentAsrClient?.accumulatedText?.collect { text ->
                if (_activeAsrType.value == AsrType.TENCENT && text.isNotEmpty()) {
                    _cumulativeContent.value = text

                    // 保存到文件
                    if (text.isNotEmpty() && !text.contains("连接已建立")) {
                        textPersistenceManager.saveText(text)
                        Log.d(TAG, "【ASR服务提供商: 腾讯云】累积文本已保存: $text")
                    }
                }
            }
        }

        // 监听Google ASR客户端的累积文本
        viewModelScope.launch {
            googleAsrClient.accumulatedText.collect { text ->
                if (_activeAsrType.value == AsrType.GOOGLE && text.isNotEmpty()) {
                    _cumulativeContent.value = text

                    // 保存到文件
                    if (text.isNotEmpty() && !text.contains("连接已建立")) {
                        textPersistenceManager.saveText(text)
                        Log.d(TAG, "【ASR服务提供商: Google Cloud】累积文本已保存: $text")
                    }
                }
            }
        }

        // 监听句子是否结束的标志
        viewModelScope.launch {
            googleAsrClient.isFinalResult.collect { isFinal ->
                isFinalResult = isFinal

                // 如果是最终结果，将当前会话文本添加到累积文本中
                if (isFinal && _currentSessionText.value.isNotEmpty() &&
                    !_currentSessionText.value.contains("连接已建立")) {

                    // 检查当前文本是否已经通过最终结果处理过，避免重复添加
                    val shouldAddText = !googleAsrClient.hasProcessedSliceType2Text() ||
                                       _currentSessionText.value != googleAsrClient.getLastProcessedText()

                    if (shouldAddText) {
                        // 将完整句子添加到累积内容中
                        appendToAccumulatedText(_currentSessionText.value)
                        Log.d(TAG, "句子识别完成，已添加到累积文本")
                    } else {
                        Log.d(TAG, "句子已处理，跳过重复添加")
                    }

                    // 重置当前会话文本，准备下一句
                    _currentSessionText.value = ""
                }
            }
        }

        // 监听火山引擎豆包AI响应
        viewModelScope.launch {
            volcAiClient.aiResponse.collect { response ->
                if (response.isNotEmpty()) {
                    _aiResponse.value = response
                }
            }
        }

        // 监听AI处理状态
        viewModelScope.launch {
            volcAiClient.isProcessing.collect { isProcessing ->
                _isAiProcessing.value = isProcessing
            }
        }

        // 监听AI错误信息
        viewModelScope.launch {
            volcAiClient.errorMessage.collect { errorMsg ->
                if (errorMsg.isNotEmpty()) {
                    _errorMessage.value = errorMsg
                }
            }
        }

        // 监听识别结果变化
        listenForRecognitionUpdates()

        // 加载AI聊天会话
        loadChatSessions()
    }

    /**
     * 初始化腾讯云语音识别客户端
     */
    private fun initializeTencentAsrClient() {
        try {
            tencentAsrClient = TencentCloudSpeechToTextClient(
                appId = TENCENT_APP_ID,
                secretId = TENCENT_SECRET_ID,
                secretKey = TENCENT_SECRET_KEY,
                coroutineScope = viewModelScope
            )
            Log.d(TAG, "腾讯云语音识别客户端初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "腾讯云语音识别客户端初始化失败", e)
            _errorMessage.value = "腾讯云语音识别初始化失败: ${e.message}"
        }
    }

    /**
     * 判断是否应该使用腾讯云语音识别
     */
    private fun shouldUseTencentCloud(languageCode: String): Boolean {
        return TENCENT_SUPPORTED_LANGUAGES.contains(languageCode)
    }

    /**
     * 获取腾讯云引擎模型类型
     */
    private fun getTencentEngineModelType(languageCode: String): String {
        return LANGUAGE_TO_TENCENT_MODEL[languageCode] ?: "16k_zh"
    }

    // 设置源语言
    fun setLanguage(language: String) {
        _selectedLanguage.value = language
        val shouldUseTencent = shouldUseTencentCloud(language)
        val asrService = if(shouldUseTencent) "腾讯云" else "Google Cloud"
        _currentAsrServiceName.value = asrService
        // 同时更新活跃的ASR类型
        _activeAsrType.value = if(shouldUseTencent) AsrType.TENCENT else AsrType.GOOGLE
        Log.d(TAG, "【ASR服务提供商】设置语言: $language, 使用${asrService}语音识别")
    }

    // 设置目标语言
    fun setTargetLanguage(language: String) {
        _targetLanguage.value = language
    }

    // 开始语音识别
    fun startRecognition() {
        viewModelScope.launch {
            try {
                if (_isRecording.value) {
                    return@launch
                }

                // 记录会议开始时间
                meetingStartTime = System.currentTimeMillis()

                // 清空当前识别结果和临时显示内容
                _recognitionResult.value = ""
                _currentDisplayContent.value = ""
                _currentSessionText.value = ""
                _lastValidResult.value = ""

                // 重置转写统计
                resetTranscriptionStats()

                // 根据当前选择的语言确定使用哪个ASR服务
                val useTencentCloud = shouldUseTencentCloud(_selectedLanguage.value)
                _activeAsrType.value = if (useTencentCloud) AsrType.TENCENT else AsrType.GOOGLE
                _currentAsrServiceName.value = if (useTencentCloud) "腾讯云" else "Google Cloud"

                if (useTencentCloud) {
                    // 使用腾讯云语音识别
                    Log.d(TAG, "【ASR服务提供商】启动腾讯云语音识别服务，语言: ${_selectedLanguage.value}")

                    // 获取腾讯云引擎模型类型
                    val engineModelType = getTencentEngineModelType(_selectedLanguage.value)

                    // 连接腾讯云语音识别服务
                    tencentAsrClient?.connect(engineModelType)
                } else {
                    // 使用Google Cloud语音识别
                    Log.d(TAG, "【ASR服务提供商】启动Google Cloud语音识别服务，语言: ${_selectedLanguage.value}")

                    // 连接Google Cloud语音识别服务
                    googleAsrClient.connect(_selectedLanguage.value)
                }

                // 设置录音状态
                _isRecording.value = true

                // 开始录音
                audioRecorder.startRecording()

            } catch (e: Exception) {
                Log.e(TAG, "开始识别失败: ${e.message}", e)
                _errorMessage.value = "开始识别失败: ${e.message}"
                stopRecognition()
            }
        }
    }

    // 停止语音识别
    fun stopRecognition(saveToHistory: Boolean = true, title: String = "") {
        viewModelScope.launch {
            try {
                if (!_isRecording.value) {
                    return@launch
                }

                // 记录停止前的累积文本，以防清空
                val existingAccumulatedText = _cumulativeContent.value

                // 先保存当前识别结果以便记录
                val currentText = _currentSessionText.value

                // 停止录音
                audioRecorder.stopRecording()

                // 根据当前活跃的ASR类型结束识别
                when (_activeAsrType.value) {
                    AsrType.GOOGLE -> googleAsrClient.endRecognition()
                    AsrType.TENCENT -> tencentAsrClient?.endRecognition()
                }

                // 更新录音状态
                _isRecording.value = false

                // 清空当前显示内容
                _currentDisplayContent.value = ""

                // 检查停止录音后累积文本是否被清空，如果被清空则恢复
                if (existingAccumulatedText.isNotEmpty() && _cumulativeContent.value.isEmpty()) {
                    Log.d(TAG, "检测到累积文本被清空，正在恢复...")
                    _cumulativeContent.value = existingAccumulatedText
                }

                // 如果当前有未添加的文本，添加到累积文本中
                if (currentText.isNotEmpty() && !currentText.contains("连接已建立")) {
                    // 检查是否有必要添加这段文本（去重）
                    val existingText = _cumulativeContent.value
                    val shouldAdd = !existingText.endsWith(currentText)

                    if (shouldAdd) {
                        Log.d(TAG, "停止录音，将最后结果添加到累积内容: $currentText")
                        appendToAccumulatedText(currentText, saveToHistory)
                    } else {
                        Log.d(TAG, "停止录音，最后结果已存在于累积内容中，跳过添加")
                    }
                    _currentSessionText.value = ""
                }

                // 最后验证累积文本是否正确保存
                if (_cumulativeContent.value.isEmpty() && existingAccumulatedText.isNotEmpty()) {
                    Log.e(TAG, "累积文本仍然为空，再次尝试恢复...")
                    _cumulativeContent.value = existingAccumulatedText

                    // 只有在需要保存到历史记录时才保存到持久化存储
                    if (saveToHistory) {
                        textPersistenceManager.saveText(existingAccumulatedText)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "停止识别失败: ${e.message}", e)
                _errorMessage.value = "停止识别失败: ${e.message}"
            }
        }
    }

    // 加载所有会议记录
    fun loadMeetingRecords() {
        viewModelScope.launch {
            try {
                val records = textPersistenceManager.loadAllMeetingRecords()
                _meetingRecords.value = records
                Log.d(TAG, "已加载${records.size}条会议记录")
            } catch (e: Exception) {
                Log.e(TAG, "加载会议记录失败: ${e.message}", e)
            }
        }
    }

    // 加载特定会议记录的详情
    fun loadMeetingRecordDetail(id: String) {
        viewModelScope.launch {
            try {
                val record = textPersistenceManager.getMeetingRecordById(id)
                _selectedRecord.value = record

                // 如果找到了记录，同时更新当前会话内容
                if (record != null) {
                    _cumulativeContent.value = record.content
                    Log.d(TAG, "已加载会议记录详情: ${record.title}")
                } else {
                    Log.d(TAG, "未找到指定ID的会议记录: $id")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载会议记录详情失败: ${e.message}", e)
            }
        }
    }

    // 删除会议记录
    fun deleteMeetingRecord(id: String) {
        viewModelScope.launch {
            try {
                val success = textPersistenceManager.deleteMeetingRecord(id)
                if (success) {
                    // 重新加载会议记录列表
                    loadMeetingRecords()

                    // 如果当前选中的是被删除的记录，清空选中
                    if (_selectedRecord.value?.id == id) {
                        _selectedRecord.value = null
                    }

                    Log.d(TAG, "已删除会议记录: $id")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除会议记录失败: ${e.message}", e)
            }
        }
    }

    // 获取会议历史记录列表
    fun getMeetingRecords(): List<TextPersistenceManager.MeetingRecord> {
        return textPersistenceManager.loadAllMeetingRecords()
    }

    // 清空错误消息
    fun clearError() {
        _errorMessage.value = null
    }

    // 清空记录内容
    fun clearContent() {
        // 清空当前识别结果
        _recognitionResult.value = ""
        _lastValidResult.value = ""
        _currentDisplayContent.value = ""
        _currentSessionText.value = ""
        _cumulativeContent.value = ""

        // 根据当前活跃的ASR类型清空相应客户端的数据
        when (_activeAsrType.value) {
            AsrType.GOOGLE -> googleAsrClient.clearAll()
            AsrType.TENCENT -> tencentAsrClient?.clearAll()
        }

        hasCompletedSession = false

        // 同时清除持久化存储
        textPersistenceManager.clearText()
        Log.d(TAG, "已清空会议记录并删除持久化文件")
    }

    // 检查是否是关于AI身份的问题
    private fun isIdentityQuestion(message: String): Boolean {
        val identityPatterns = listOf(
            "你是谁",
            "你叫什么",
            "你的名字",
            "你是什么模型",
            "你是什么大模型",
            "你是什么语言模型",
            "你是哪个AI",
            "你是什么AI",
            "你是ai吗",
            "你是人工智能",
            "你的开发者是谁",
            "谁创造了你",
            "介绍一下你自己",
            "tell me about yourself",
            "who are you",
            "what model are you",
            "what's your name",
            "豆包",
            "doubao",
            "火山引擎",
            "volcano",
            "字节",
            "bytedance",
            "模型版本",
            "版本号",
            "哪个版本",
            "哪个公司",
            "公司",
            "开发",
            "研发",
            "训练",
            "参数量",
            "多少参数",
            // 新增的匹配模式
            "你到底是谁",
            "你是什么",
            "你是谁开发的",
            "你是什么东西",
            "你是机器人吗",
            "你是bot吗",
            "你是chatbot吗",
            "你的身份",
            "你的创造者",
            "谁制造了你",
            "谁设计了你",
            "你来自哪里",
            "你是什么类型的AI",
            "你属于什么公司",
            "你是豆包吗",
            "你是不是豆包"
        )

        val sensitivePatterns = listOf(
            "火山引擎", "豆包", "字节", "doubao", "bytedance", "volcengine", "volcano"
        )

        val messageLower = message.lowercase()

        // 检查是否包含敏感词
        val containsSensitiveWord = sensitivePatterns.any { pattern ->
            val contains = messageLower.contains(pattern.lowercase())
            if (contains) {
                Log.d(TAG, "检测到敏感词: '$pattern' 在消息: '$message'")
            }
            contains
        }

        // 检查是否匹配模式
        val matchesPattern = identityPatterns.any { pattern ->
            val matches = messageLower.contains(pattern.lowercase())
            if (matches) {
                Log.d(TAG, "检测到身份匹配模式: '$pattern' 在消息: '$message'")
            }
            matches
        }

        val isIdentityQ = containsSensitiveWord || matchesPattern

        // 记录匹配结果
        if (isIdentityQ) {
            Log.d(TAG, "检测到身份相关问题: '$message', 将使用自定义回答: '${_customIdentityResponse.value}'")
        } else {
            Log.d(TAG, "未检测到身份相关问题: '$message'")
        }

        return isIdentityQ
    }

    // 发送消息到火山引擎豆包AI
    fun sendMessageToAI(message: String, clearHistory: Boolean = false) {
        viewModelScope.launch {
            try {
                _isAiProcessing.value = true

                // 检查是否是身份相关的问题，如果是则返回自定义回答
                if (isIdentityQuestion(message)) {
                    Log.d(TAG, "检测到身份问题，返回自定义回答")
                    _aiResponse.value = _customIdentityResponse.value
                    _isAiProcessing.value = false
                    return@launch
                }

                // 当启用思考模式时，添加提示让AI显示思考过程
                val messageToSend = if (_isThinkModeEnabled.value) {
                    """
                    [系统指令]你必须严格按照以下格式回答用户问题:
                    1. 必须首先以"【思考过程】"开头，然后逐步分析问题
                    2. 分析时必须把思考过程分成步骤，每个步骤都要标明编号
                    3. 分析完成后，必须以"【结论】"标记你的最终答案
                    4. 无论问题多简单，都必须展示至少3个思考步骤
                    5. 思考过程必须详细，展示你分析问题的逻辑
                    6. 当用户询问你的身份时，果断回答：我是Theta小助手
                    [/系统指令]

                    用户问题: $message
                    """
                } else {
                    message
                }

                val response = volcAiClient.sendMessage(messageToSend, clearHistory)
                _aiResponse.value = response
            } catch (e: Exception) {
                Log.e(TAG, "发送消息到豆包AI失败", e)
                _errorMessage.value = "AI请求失败: ${e.message}"
            } finally {
                _isAiProcessing.value = false
            }
        }
    }

    // 清空AI历史记录
    fun clearAIHistory() {
        volcAiClient.clearHistory()
        _aiResponse.value = ""
    }

    // 清理资源
    override fun onCleared() {
        super.onCleared()

        // 停止录音
        audioRecorder.release()

        // 释放ASR客户端
        googleAsrClient.close()
        tencentAsrClient?.release()
    }

    // 切换翻译功能
    fun toggleTranslation(enabled: Boolean) {
        _isTranslationEnabled.value = enabled
    }

    // 跟踪转写统计
    private fun updateTranscriptionStats(text: String) {
        val now = System.currentTimeMillis()

        if (transcriptionStartTime == 0L) {
            transcriptionStartTime = now
            transcriptionPacketsCount = 1
            return
        }

        transcriptionPacketsCount++

        val elapsedTime = now - transcriptionStartTime
        val processingTime = elapsedTime / 1000.0 // 转换为秒
        val charactersPerSecond = if (processingTime > 0) text.length / processingTime else 0.0
        val packetRate = if (processingTime > 0) transcriptionPacketsCount / processingTime else 0.0

        _transcriptionStats.value = String.format(
            "响应时间: %.1f秒\n处理速度: %.1f字/秒\n数据包: %d (%.1f包/秒)",
            processingTime,
            charactersPerSecond,
            transcriptionPacketsCount,
            packetRate
        )

        // 更新累计统计
        totalTranscriptionTime += elapsedTime
        totalCharactersTranscribed += text.length
    }

    // 重置转写统计
    private fun resetTranscriptionStats() {
        transcriptionStartTime = 0L
        transcriptionPacketsCount = 0
        _transcriptionStats.value = "准备中..."
    }

    // 设置监听识别结果变化
    private fun listenForRecognitionUpdates() {
        viewModelScope.launch {
            googleAsrClient.recognitionResult.collectLatest { result ->
                if (result.isNotEmpty()) {
                    _lastValidResult.value = result
                    updateTranscriptionStats(result)
                }
            }
        }
    }

    // 加载所有AI聊天会话
    fun loadChatSessions() {
        viewModelScope.launch {
            try {
                val sessions = aiChatPersistenceManager.loadAllChatSessions()
                _chatSessions.value = sessions
                Log.d(TAG, "已加载${sessions.size}条AI聊天会话")
            } catch (e: Exception) {
                Log.e(TAG, "加载AI聊天会话失败: ${e.message}", e)
            }
        }
    }

    // 加载特定AI聊天会话详情
    fun loadChatSessionDetail(id: String) {
        viewModelScope.launch {
            try {
                val session = aiChatPersistenceManager.getChatSessionById(id)
                _selectedChatSession.value = session

                if (session != null) {
                    Log.d(TAG, "已加载AI聊天会话详情: ${session.title}")
                } else {
                    Log.d(TAG, "未找到指定ID的AI聊天会话: $id")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载AI聊天会话详情失败: ${e.message}", e)
            }
        }
    }

    // 保存当前AI聊天会话
    fun saveCurrentChatSession(title: String, messages: List<AIChatPersistenceManager.ChatMessage>) {
        viewModelScope.launch {
            try {
                if (messages.isEmpty()) {
                    Log.d(TAG, "聊天消息为空，不保存会话")
                    return@launch
                }

                val success = aiChatPersistenceManager.saveChatSession(title, messages)

                if (success) {
                    Log.d(TAG, "已保存AI聊天会话: $title")
                    // 重新加载聊天会话列表
                    loadChatSessions()
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存AI聊天会话失败: ${e.message}", e)
            }
        }
    }

    // 删除AI聊天会话
    fun deleteChatSession(id: String) {
        viewModelScope.launch {
            try {
                val success = aiChatPersistenceManager.deleteChatSession(id)

                if (success) {
                    // 重新加载聊天会话列表
                    loadChatSessions()

                    // 如果当前选中的是被删除的会话，清空选中
                    if (_selectedChatSession.value?.id == id) {
                        _selectedChatSession.value = null
                    }

                    Log.d(TAG, "已删除AI聊天会话: $id")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除AI聊天会话失败: ${e.message}", e)
            }
        }
    }

    // 清空所有AI聊天会话
    fun clearAllChatSessions() {
        viewModelScope.launch {
            try {
                val success = aiChatPersistenceManager.clearAllChatSessions()

                if (success) {
                    _chatSessions.value = emptyList()
                    _selectedChatSession.value = null
                    Log.d(TAG, "已清空所有AI聊天会话")
                }
            } catch (e: Exception) {
                Log.e(TAG, "清空AI聊天会话失败: ${e.message}", e)
            }
        }
    }

    // 切换会议模式
    fun toggleMeetingMode(enabled: Boolean) {
        _isInMeetingMode.value = enabled
        Log.d(TAG, "会议模式已${if (enabled) "开启" else "关闭"}")
    }

    // 切换AI思考模式
    fun toggleThinkMode(enabled: Boolean) {
        _isThinkModeEnabled.value = enabled
        Log.d(TAG, "AI思考模式已${if (enabled) "开启" else "关闭"}")
    }

    // 设置自定义身份回答
    fun setCustomIdentityResponse(response: String) {
        if (response.isNotEmpty()) {
            _customIdentityResponse.value = response
            Log.d(TAG, "已设置自定义身份回答: $response")
        }
    }

    // 将完整句子添加到累积文本中
    private fun appendToAccumulatedText(text: String, shouldSave: Boolean = true) {
        if (text.isEmpty()) return

        // 判断是否应该保存到持久化存储（AI对话模式不保存）
        val shouldPersist = shouldSave && (_isTranslationEnabled.value || _isInMeetingMode.value)

        // 更新累积文本
        if (_cumulativeContent.value.isEmpty()) {
            _cumulativeContent.value = text
        } else {
            _cumulativeContent.value += " " + text
        }

        // 保存到持久化存储
        if (shouldPersist) {
            textPersistenceManager.saveText(_cumulativeContent.value)
            Log.d(TAG, "已将完整句子添加到累积文本并保存: $text")
        } else {
            Log.d(TAG, "已将完整句子添加到累积文本但不保存: $text")
        }
    }
}