<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>耳机APP - API文档</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f7;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            overflow: hidden; /* 确保浮动元素不会溢出 */
        }
        h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 30px;
            color: #222;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        h2 {
            font-size: 22px;
            font-weight: 600;
            margin-top: 40px;
            margin-bottom: 15px;
            color: #8675E6;
        }
        h3 {
            font-size: 18px;
            font-weight: 500;
            margin-top: 25px;
            margin-bottom: 10px;
            color: #333;
        }
        .api-item {
            margin-bottom: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 20px;
            background-color: #fcfcfc;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
        }
        .get {
            background-color: #e7f5ff;
            color: #0080ff;
        }
        .post {
            background-color: #d4f4e2;
            color: #00b373;
        }
        .put {
            background-color: #fff0c1;
            color: #cf9b00;
        }
        .delete {
            background-color: #ffe0e0;
            color: #ff4d4d;
        }
        .endpoint {
            display: inline-block;
            font-family: monospace;
            font-size: 16px;
            background-color: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .params table, .response table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .params th, .response th, .params td, .response td {
            padding: 10px;
            text-align: left;
            border: 1px solid #e0e0e0;
        }
        .params th, .response th {
            background-color: #f7f7f7;
            font-weight: 500;
            color: #444;
        }
        .code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            line-height: 1.4;
        }
        .required {
            color: #ff4d4d;
            font-size: 14px;
            margin-left: 5px;
        }
        .tag {
            display: inline-block;
            font-size: 12px;
            padding: 2px 5px;
            border-radius: 3px;
            margin-left: 5px;
            background-color: #e0e0e0;
            color: #666;
        }
        .tag.numeric {
            background-color: #e7f5ff;
            color: #0080ff;
        }
        .tag.string {
            background-color: #d4f4e2;
            color: #00b373;
        }
        .tag.boolean {
            background-color: #fff0c1;
            color: #cf9b00;
        }
        .tag.object {
            background-color: #f0e0ff;
            color: #7030a0;
        }
        .tag.array {
            background-color: #ffe0e0;
            color: #ff4d4d;
        }
        .description {
            margin: 10px 0;
            color: #666;
        }
        .note {
            background-color: #fff8e0;
            padding: 10px 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
            color: #856404;
        }
        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 5px;
            background-color: #e0e0e0;
        }
        .status-200 {
            background-color: #d4f4e2;
            color: #00b373;
        }
        .status-400, .status-401, .status-403 {
            background-color: #ffe0e0;
            color: #ff4d4d;
        }
        .status-500 {
            background-color: #ff4d4d;
            color: white;
        }
        .toc {
            position: sticky;
            top: 20px;
            float: left;
            width: 250px;
            margin-right: 20px;
            margin-left: 0;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .toc ul {
            list-style-type: none;
            padding-left: 15px;
        }
        .toc ul li {
            margin-bottom: 8px;
        }
        .toc a {
            color: #333;
            text-decoration: none;
        }
        .toc a:hover {
            color: #8675E6;
            text-decoration: underline;
        }
        .toc h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        @media (max-width: 768px) {
            .toc {
                display: none;
            }
        }
        /* 为主内容添加左侧间距 */
        section {
            margin-left: 270px;
        }
        /* 在小屏幕上移除左侧间距 */
        @media (max-width: 768px) {
            section {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>耳机APP - API文档</h1>
        
        <div class="toc">
            <h3>目录</h3>
            <ul>
                <li><a href="#user-login">用户登录</a></li>
                <li><a href="#bluetooth">蓝牙功能</a></li>
                <li><a href="#public-storage">公共存储</a></li>
                <li><a href="#app-update">APP更新</a></li>
                <li><a href="#faq">常见问题</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2>概述</h2>
            <p>本文档描述了小莱雅耳机APP的后端API接口。所有API均采用RESTful风格设计，使用HTTP协议进行通信。</p>
            <p>API的基础URL为：<code>https://api.llya.com/v1</code></p>
            
            <div class="note">
                <strong>注意：</strong> 除了登录、注册、用户协议和隐私政策接口外，所有接口都需要在请求头中包含认证信息。
            </div>
        </section>

        <section id="auth">
            <h2>用户认证</h2>
            <p>API采用Token认证机制。客户端需要在所有受保护的请求中包含认证头：</p>
            <div class="code">
Authorization: Bearer {access_token}
            </div>
            <p>access_token在登录或注册成功后获取。</p>
        </section>

        <section id="api-list">
            <h2>API列表</h2>
            
            <!-- 登录/注册API -->
            <div id="login" class="api-item">
                <h3>
                    <span class="method post">POST</span>
                    <span class="endpoint">/auth/login</span>
                </h3>
                <p class="description">用户登录接口，首次登录会自动注册</p>
                
                <div class="params">
                    <h4>请求参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>loginType</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>登录类型: "phone"(手机号) 或 "email"(邮箱)</td>
                        </tr>
                        <tr>
                            <td>account</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>登录账号，手机号或邮箱</td>
                        </tr>
                        <tr>
                            <td>verifyCode</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>验证码</td>
                        </tr>
                        <tr>
                            <td>userType</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>用户类型: 1(国内用户) 或 2(国外用户)</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
POST /auth/login
Content-Type: application/json

{
    "loginType": "phone",
    "account": "***********",
    "verifyCode": "123456",
    "userType": 1
}
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.token</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>访问令牌，有效期30天</td>
                        </tr>
                        <tr>
                            <td>data.userId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>用户ID</td>
                        </tr>
                        <tr>
                            <td>data.isNewUser</td>
                            <td>Boolean<span class="tag boolean">布尔值</span></td>
                            <td>是否新用户</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "userId": "u123456789",
        "isNewUser": false
    }
}
                    </div>
                </div>
                
                <div class="status-codes">
                    <h4>状态码</h4>
                    <p>
                        <span class="status-code status-200">200 OK</span> 请求成功
                    </p>
                    <p>
                        <span class="status-code status-400">400 Bad Request</span> 参数错误
                    </p>
                    <p>
                        <span class="status-code status-500">500 Internal Server Error</span> 服务器内部错误
                    </p>
                </div>
            </div>
            
            <!-- 获取验证码API -->
            <div id="get-verify-code" class="api-item">
                <h3>
                    <span class="method post">POST</span>
                    <span class="endpoint">/auth/verifyCode</span>
                </h3>
                <p class="description">获取登录验证码</p>
                
                <div class="params">
                    <h4>请求参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>type</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>类型: "phone"(手机号) 或 "email"(邮箱)</td>
                        </tr>
                        <tr>
                            <td>account</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>手机号或邮箱</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
POST /auth/verifyCode
Content-Type: application/json

{
    "type": "phone",
    "account": "***********"
}
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "验证码已发送"
}
                    </div>
                </div>
                
                <div class="note">
                    验证码有效期为5分钟，同一账号1分钟内只能获取一次验证码。
                </div>
            </div>
            
            <!-- 蓝牙连接绑定API -->
            <div id="bluetooth" class="api-item">
                <h3>
                    <span class="method post">POST</span>
                    <span class="endpoint">/device/bind</span>
                </h3>
                <p class="description">绑定蓝牙设备</p>
                
                <div class="params">
                    <h4>请求参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>deviceName</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>蓝牙设备名称</td>
                        </tr>
                        <tr>
                            <td>macAddress</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>蓝牙设备MAC地址</td>
                        </tr>
                        <tr>
                            <td>location</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>否</td>
                            <td>用户定位信息</td>
                        </tr>
                        <tr>
                            <td>location.longitude</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>否</td>
                            <td>经度</td>
                        </tr>
                        <tr>
                            <td>location.latitude</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>否</td>
                            <td>纬度</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
POST /device/bind
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
    "deviceName": "LLYA EarPods Pro",
    "macAddress": "00:11:22:33:44:55",
    "location": {
        "longitude": 116.397428,
        "latitude": 39.90923
    }
}
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.deviceId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>设备ID</td>
                        </tr>
                        <tr>
                            <td>data.bindTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>绑定时间，ISO 8601格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "设备绑定成功",
    "data": {
        "deviceId": "d987654321",
        "bindTime": "2023-07-01T12:30:45Z"
    }
}
                    </div>
                </div>
                
                <div class="status-codes">
                    <h4>状态码</h4>
                    <p>
                        <span class="status-code status-200">200 OK</span> 请求成功
                    </p>
                    <p>
                        <span class="status-code status-400">400 Bad Request</span> 参数错误
                    </p>
                    <p>
                        <span class="status-code status-401">401 Unauthorized</span> 未授权，请登录
                    </p>
                    <p>
                        <span class="status-code status-500">500 Internal Server Error</span> 服务器内部错误
                    </p>
                </div>
            </div>
            
            <!-- 用户信息接口 -->
            <div id="user-info" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/user/info</span>
                </h3>
                <p class="description">获取用户信息</p>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /user/info
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.userId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>用户ID</td>
                        </tr>
                        <tr>
                            <td>data.username</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>用户名</td>
                        </tr>
                        <tr>
                            <td>data.phone</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>手机号，可能为空</td>
                        </tr>
                        <tr>
                            <td>data.email</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>邮箱，可能为空</td>
                        </tr>
                        <tr>
                            <td>data.vipExpireTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>VIP到期时间，ISO 8601格式</td>
                        </tr>
                        <tr>
                            <td>data.userType</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>用户类型：1(国内用户) 或 2(国外用户)</td>
                        </tr>
                        <tr>
                            <td>data.registrationTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>注册时间，ISO 8601格式</td>
                        </tr>
                        <tr>
                            <td>data.location</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>用户定位信息</td>
                        </tr>
                        <tr>
                            <td>data.location.longitude</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>经度</td>
                        </tr>
                        <tr>
                            <td>data.location.latitude</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>纬度</td>
                        </tr>
                        <tr>
                            <td>data.devices</td>
                            <td>Array<span class="tag array">数组</span></td>
                            <td>绑定的设备列表</td>
                        </tr>
                        <tr>
                            <td>data.devices[].deviceId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>设备ID</td>
                        </tr>
                        <tr>
                            <td>data.devices[].deviceName</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>设备名称</td>
                        </tr>
                        <tr>
                            <td>data.devices[].macAddress</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>MAC地址</td>
                        </tr>
                        <tr>
                            <td>data.devices[].bindTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>绑定时间，ISO 8601格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "成功",
    "data": {
        "userId": "u123456789",
        "username": "张三",
        "phone": "***********",
        "email": "",
        "vipExpireTime": "2024-12-31T23:59:59Z",
        "userType": 1,
        "registrationTime": "2023-01-15T08:30:45Z",
        "location": {
            "longitude": 116.397428,
            "latitude": 39.90923
        },
        "devices": [
            {
                "deviceId": "d987654321",
                "deviceName": "LLYA EarPods Pro",
                "macAddress": "00:11:22:33:44:55",
                "bindTime": "2023-07-01T12:30:45Z"
            }
        ]
    }
}
                    </div>
                </div>
                
                <div class="status-codes">
                    <h4>状态码</h4>
                    <p>
                        <span class="status-code status-200">200 OK</span> 请求成功
                    </p>
                    <p>
                        <span class="status-code status-401">401 Unauthorized</span> 未授权，请登录
                    </p>
                    <p>
                        <span class="status-code status-500">500 Internal Server Error</span> 服务器内部错误
                    </p>
                </div>
            </div>
            
            <!-- 设备信息接口 -->
            <div id="device-info" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/device/info/{deviceId}</span>
                </h3>
                <p class="description">获取设备详细信息</p>
                
                <div class="params">
                    <h4>路径参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>deviceId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>设备ID</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /device/info/d987654321
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.deviceId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>设备ID</td>
                        </tr>
                        <tr>
                            <td>data.companyName</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>公司名称</td>
                        </tr>
                        <tr>
                            <td>data.deviceName</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>设备名称</td>
                        </tr>
                        <tr>
                            <td>data.macAddress</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>MAC地址</td>
                        </tr>
                        <tr>
                            <td>data.imageUrl</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>设备白底图片URL</td>
                        </tr>
                        <tr>
                            <td>data.productModel</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>产品型号</td>
                        </tr>
                        <tr>
                            <td>data.firmwareVersion</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>固件版本</td>
                        </tr>
                        <tr>
                            <td>data.manufactureDate</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>生产日期，ISO 8601格式</td>
                        </tr>
                        <tr>
                            <td>data.features</td>
                            <td>Array<span class="tag array">数组</span></td>
                            <td>设备功能特性列表</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "成功",
    "data": {
        "deviceId": "d987654321",
        "companyName": "小莱雅科技有限公司",
        "deviceName": "LLYA EarPods Pro",
        "macAddress": "00:11:22:33:44:55",
        "imageUrl": "https://cdn.llya.com/images/devices/earpods-pro-white.png",
        "productModel": "EP-PRO-2023",
        "firmwareVersion": "1.2.5",
        "manufactureDate": "2023-05-20T00:00:00Z",
        "features": [
            "主动降噪",
            "环境音模式",
            "空间音频",
            "自动切换",
            "双设备连接"
        ]
    }
}
                    </div>
                </div>
            </div>
            
            <!-- 意见反馈提交接口 -->
            <div id="feedback" class="api-item">
                <h3>
                    <span class="method post">POST</span>
                    <span class="endpoint">/feedback/submit</span>
                </h3>
                <p class="description">提交意见反馈</p>
                
                <div class="params">
                    <h4>请求参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>type</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>反馈类型：'bug'(问题反馈)、'suggestion'(功能建议)、'other'(其他)</td>
                        </tr>
                        <tr>
                            <td>content</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>反馈内容</td>
                        </tr>
                        <tr>
                            <td>contactInfo</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>联系方式，如电话或邮箱</td>
                        </tr>
                        <tr>
                            <td>deviceId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>相关设备ID</td>
                        </tr>
                        <tr>
                            <td>images</td>
                            <td>Array<span class="tag array">数组</span></td>
                            <td>否</td>
                            <td>图片资源列表（Base64编码）</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
POST /feedback/submit
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
    "type": "bug",
    "content": "使用耳机时，右耳经常断连",
    "contactInfo": "***********",
    "deviceId": "d987654321",
    "images": [
        "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
        "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
    ]
}
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.feedbackId</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>反馈ID</td>
                        </tr>
                        <tr>
                            <td>data.createTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>创建时间，ISO 8601格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "反馈提交成功",
    "data": {
        "feedbackId": "fb12345678",
        "createTime": "2023-07-05T15:30:25Z"
    }
}
                    </div>
                </div>
                
                <div class="note">
                    图片大小限制为每张不超过5MB，最多上传5张图片。
                </div>
            </div>
            
            <!-- 注销账号接口 -->
            <div id="account-delete" class="api-item">
                <h3>
                    <span class="method post">POST</span>
                    <span class="endpoint">/user/delete</span>
                </h3>
                <p class="description">注销用户账号</p>
                
                <div class="params">
                    <h4>请求参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>verifyCode</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>验证码，通过 /auth/verifyCode 接口获取</td>
                        </tr>
                        <tr>
                            <td>reason</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>注销原因</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
POST /user/delete
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
    "verifyCode": "123456",
    "reason": "不再使用此应用"
}
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "账号已注销"
}
                    </div>
                </div>
                
                <div class="note">
                    <strong>注意：</strong> 账号注销后，用户所有数据将被删除，且无法恢复。绑定的设备将自动解绑。
                </div>
            </div>
            
            <!-- 用户协议内容接口 -->
            <div id="user-agreement" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/content/agreement</span>
                </h3>
                <p class="description">获取用户协议内容</p>
                
                <div class="params">
                    <h4>查询参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>language</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>语言代码，默认zh-CN。支持：zh-CN(简体中文)、en-US(英文)、ja-JP(日语)</td>
                        </tr>
                        <tr>
                            <td>version</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>协议版本，不传则返回最新版本</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /content/agreement?language=zh-CN
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.title</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>协议标题</td>
                        </tr>
                        <tr>
                            <td>data.version</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>协议版本</td>
                        </tr>
                        <tr>
                            <td>data.effectiveDate</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>生效日期，ISO 8601格式</td>
                        </tr>
                        <tr>
                            <td>data.content</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>协议内容，支持HTML格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "成功",
    "data": {
        "title": "小莱雅用户服务协议",
        "version": "1.2.0",
        "effectiveDate": "2023-01-01T00:00:00Z",
        "content": "&lt;h1&gt;小莱雅用户服务协议&lt;/h1&gt;&lt;p&gt;欢迎使用小莱雅耳机及相关服务！&lt;/p&gt;..."
    }
}
                    </div>
                </div>
            </div>
            
            <!-- 隐私政策内容接口 -->
            <div id="privacy-policy" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/content/privacy</span>
                </h3>
                <p class="description">获取隐私政策内容</p>
                
                <div class="params">
                    <h4>查询参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>language</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>语言代码，默认zh-CN。支持：zh-CN(简体中文)、en-US(英文)、ja-JP(日语)</td>
                        </tr>
                        <tr>
                            <td>version</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>政策版本，不传则返回最新版本</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /content/privacy?language=en-US
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.title</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>政策标题</td>
                        </tr>
                        <tr>
                            <td>data.version</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>政策版本</td>
                        </tr>
                        <tr>
                            <td>data.effectiveDate</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>生效日期，ISO 8601格式</td>
                        </tr>
                        <tr>
                            <td>data.content</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>政策内容，支持HTML格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "Success",
    "data": {
        "title": "LLYA Privacy Policy",
        "version": "1.3.0",
        "effectiveDate": "2023-03-15T00:00:00Z",
        "content": "&lt;h1&gt;LLYA Privacy Policy&lt;/h1&gt;&lt;p&gt;Your privacy is important to us...&lt;/p&gt;..."
    }
}
                    </div>
                </div>
            </div>
            
            <!-- 公共存储接口 - 获取数据 -->
            <div id="public-storage-get" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/storage/data/{key}</span>
                </h3>
                <p class="description">从公共存储获取JSON格式数据</p>
                
                <div class="params">
                    <h4>路径参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>key</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>要获取的数据键名</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /storage/data/user_preferences
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.key</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>数据键名</td>
                        </tr>
                        <tr>
                            <td>data.value</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>存储的JSON数据</td>
                        </tr>
                        <tr>
                            <td>data.expireTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>过期时间，ISO 8601格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "成功",
    "data": {
        "key": "user_preferences",
        "value": {
            "theme": "dark",
            "language": "zh-CN",
            "notifications": {
                "enabled": true,
                "types": ["battery", "updates"]
            }
        },
        "expireTime": "2023-07-12T15:30:45Z"
    }
}
                    </div>
                </div>
            </div>
            
            <!-- APP更新接口 -->
            <div id="app-update" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/app/version</span>
                </h3>
                <p class="description">获取APP最新版本信息</p>
                
                <div class="params">
                    <h4>查询参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>platform</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>平台类型：'android'或'ios'</td>
                        </tr>
                        <tr>
                            <td>versionCode</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>是<span class="required">*</span></td>
                            <td>当前APP版本号</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /app/version?platform=android&versionCode=105
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.hasUpdate</td>
                            <td>Boolean<span class="tag boolean">布尔值</span></td>
                            <td>是否有更新</td>
                        </tr>
                        <tr>
                            <td>data.latestVersion</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>最新版本号，如"1.2.0"</td>
                        </tr>
                        <tr>
                            <td>data.versionCode</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>最新版本内部版本号</td>
                        </tr>
                        <tr>
                            <td>data.forceUpdate</td>
                            <td>Boolean<span class="tag boolean">布尔值</span></td>
                            <td>是否强制更新</td>
                        </tr>
                        <tr>
                            <td>data.downloadUrl</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>更新包下载地址</td>
                        </tr>
                        <tr>
                            <td>data.releaseNotes</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>更新内容说明</td>
                        </tr>
                        <tr>
                            <td>data.fileSize</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>更新包大小(字节)</td>
                        </tr>
                        <tr>
                            <td>data.publishTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>发布时间，ISO 8601格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "成功",
    "data": {
        "hasUpdate": true,
        "latestVersion": "1.2.5",
        "versionCode": 125,
        "forceUpdate": false,
        "downloadUrl": "https://cdn.llya.com/app/llya-v1.2.5.apk",
        "releaseNotes": "1. 修复了蓝牙连接稳定性问题\n2. 优化了电池电量显示\n3. 新增睡眠监测功能",
        "fileSize": 18365440,
        "publishTime": "2023-08-25T10:00:00Z"
    }
}
                    </div>
                </div>
                
                <div class="note">
                    <strong>注意：</strong> 当没有可用更新时，hasUpdate为false，其他版本信息仍会返回当前最新版本的信息。
                </div>
            </div>
            
            <!-- 常见问题接口 -->
            <div id="faq" class="api-item">
                <h3>
                    <span class="method get">GET</span>
                    <span class="endpoint">/content/faq</span>
                </h3>
                <p class="description">获取常见问题列表</p>
                
                <div class="params">
                    <h4>查询参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>是否必须</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>language</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>语言代码，默认zh-CN。支持：zh-CN(简体中文)、en-US(英文)</td>
                        </tr>
                        <tr>
                            <td>category</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>否</td>
                            <td>问题分类：'connection'(连接问题)、'audio'(音频问题)、'battery'(电池问题)等，不传则返回所有分类</td>
                        </tr>
                        <tr>
                            <td>limit</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>否</td>
                            <td>返回条数限制，默认20，最大50</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>请求示例</h4>
                    <div class="code">
GET /content/faq?language=zh-CN&category=connection
                    </div>
                </div>
                
                <div class="response">
                    <h4>响应参数</h4>
                    <table>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>状态码，0表示成功</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>提示信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object<span class="tag object">对象</span></td>
                            <td>返回数据</td>
                        </tr>
                        <tr>
                            <td>data.total</td>
                            <td>Number<span class="tag numeric">数值</span></td>
                            <td>总条数</td>
                        </tr>
                        <tr>
                            <td>data.items</td>
                            <td>Array<span class="tag array">数组</span></td>
                            <td>常见问题列表</td>
                        </tr>
                        <tr>
                            <td>data.items[].id</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>问题ID</td>
                        </tr>
                        <tr>
                            <td>data.items[].category</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>问题分类</td>
                        </tr>
                        <tr>
                            <td>data.items[].question</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>问题标题</td>
                        </tr>
                        <tr>
                            <td>data.items[].answer</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>问题答案，支持HTML格式</td>
                        </tr>
                        <tr>
                            <td>data.items[].updateTime</td>
                            <td>String<span class="tag string">字符串</span></td>
                            <td>更新时间，ISO 8601格式</td>
                        </tr>
                    </table>
                </div>
                
                <div class="example">
                    <h4>响应示例</h4>
                    <div class="code">
{
    "code": 0,
    "message": "成功",
    "data": {
        "total": 2,
        "items": [
            {
                "id": "faq001",
                "category": "connection",
                "question": "耳机无法连接到手机怎么办？",
                "answer": "&lt;p&gt;请按照以下步骤尝试解决：&lt;/p&gt;&lt;ol&gt;&lt;li&gt;确保耳机电量充足&lt;/li&gt;&lt;li&gt;将耳机放回充电盒并重新取出&lt;/li&gt;&lt;li&gt;在手机蓝牙设置中删除已配对的耳机，然后重新配对&lt;/li&gt;&lt;li&gt;重启手机&lt;/li&gt;&lt;/ol&gt;",
                "updateTime": "2023-06-15T09:30:00Z"
            },
            {
                "id": "faq002",
                "category": "connection",
                "question": "耳机连接后经常断开连接怎么办？",
                "answer": "&lt;p&gt;以下原因可能导致连接不稳定：&lt;/p&gt;&lt;ul&gt;&lt;li&gt;距离过远（超过10米）&lt;/li&gt;&lt;li&gt;有障碍物阻隔&lt;/li&gt;&lt;li&gt;周围存在强电磁干扰&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;建议更新耳机固件至最新版本，并确保手机系统也是最新版本。&lt;/p&gt;",
                "updateTime": "2023-07-20T14:45:00Z"
            }
        ]
    }
}
                    </div>
                </div>
            </div>
            
        </section>

        <section id="error-codes">
            <h2>错误码</h2>
            <table>
                <tr>
                    <th>错误码</th>
                    <th>描述</th>
                </tr>
                <tr>
                    <td>400</td>
                    <td>请求参数错误</td>
                </tr>
                <tr>
                    <td>401</td>
                    <td>未授权，请登录</td>
                </tr>
                <tr>
                    <td>403</td>
                    <td>权限不足</td>
                </tr>
                <tr>
                    <td>404</td>
                    <td>资源不存在</td>
                </tr>
                <tr>
                    <td>500</td>
                    <td>服务器内部错误</td>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>用户不存在</td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>设备不存在</td>
                </tr>
                <tr>
                    <td>1003</td>
                    <td>设备已被绑定</td>
                </tr>
                <tr>
                    <td>1004</td>
                    <td>验证码错误或已过期</td>
                </tr>
                <tr>
                    <td>1005</td>
                    <td>账号已存在</td>
                </tr>
                <tr>
                    <td>1006</td>
                    <td>蓝牙设备连接失败</td>
                </tr>
            </table>
        </section>
    </div>
</body>
</html> 