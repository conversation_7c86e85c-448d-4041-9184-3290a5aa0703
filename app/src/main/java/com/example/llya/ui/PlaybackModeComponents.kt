package com.example.llya.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.llya.R
import com.example.llya.viewmodel.GoogleCloudServiceViewModel.PlaybackMode
import kotlinx.coroutines.flow.StateFlow

/**
 * 播放模式选择对话框
 */
@Composable
fun PlaybackModeSelectionDialog(
    currentMode: PlaybackMode,
    onModeSelected: (PlaybackMode) -> Unit,
    onDismiss: () -> Unit
) {
    val cardBgColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    val currentModeValue = currentMode

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = cardBgColor)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = stringResource(R.string.gc_select_playback_mode),
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // 静音模式
                PlaybackModeItem(
                    title = stringResource(R.string.gc_silent_mode),
                    description = stringResource(R.string.gc_silent_mode_desc),
                    iconId = R.drawable.ic_volume_off,
                    isSelected = currentModeValue == PlaybackMode.SILENT,
                    onClick = { onModeSelected(PlaybackMode.SILENT) }
                )

                Divider(
                    color = Color.Gray.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )

                // 耳机模式
                PlaybackModeItem(
                    title = stringResource(R.string.gc_earbuds_mode),
                    description = stringResource(R.string.gc_earbuds_mode_desc),
                    iconId = R.drawable.ic_headset,
                    isSelected = currentModeValue == PlaybackMode.EARBUDS,
                    onClick = { onModeSelected(PlaybackMode.EARBUDS) }
                )

                Divider(
                    color = Color.Gray.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )

                // 外放模式
                PlaybackModeItem(
                    title = stringResource(R.string.gc_speaker_mode),
                    description = stringResource(R.string.gc_speaker_mode_desc),
                    iconId = R.drawable.ic_volume_up,
                    isSelected = currentModeValue == PlaybackMode.SPEAKER,
                    onClick = { onModeSelected(PlaybackMode.SPEAKER) }
                )

                Divider(
                    color = Color.Gray.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )

                // 双耳模式
                PlaybackModeItem(
                    title = stringResource(R.string.gc_dual_earbuds_mode),
                    description = stringResource(R.string.gc_dual_earbuds_mode_desc),
                    iconId = R.drawable.ic_headset_mic,
                    isSelected = currentModeValue == PlaybackMode.DUAL_EARBUDS,
                    onClick = { onModeSelected(PlaybackMode.DUAL_EARBUDS) }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 取消按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.End),
                    colors = ButtonDefaults.buttonColors(containerColor = purpleColor)
                ) {
                    Text(stringResource(R.string.gc_cancel))
                }
            }
        }
    }
}

/**
 * 播放模式项
 */
@Composable
fun PlaybackModeItem(
    title: String,
    description: String,
    iconId: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val purpleColor = Color(0xFF8364FD)

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = isSelected,
            onClick = onClick,
            colors = RadioButtonDefaults.colors(
                selectedColor = purpleColor,
                unselectedColor = Color.White
            )
        )

        Spacer(modifier = Modifier.width(8.dp))

        Icon(
            painter = painterResource(id = iconId),
            contentDescription = title,
            tint = if (isSelected) purpleColor else Color.White,
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                color = if (isSelected) purpleColor else Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = description,
                color = Color.Gray,
                fontSize = 12.sp,
                lineHeight = 16.sp
            )
        }
    }
}
