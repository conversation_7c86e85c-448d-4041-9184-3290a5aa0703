package com.example.llya.utils

import android.content.Context
import android.media.MediaRecorder
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.*

class AudioRecorder(
    private val onAudioBufferReady: ((ByteArray) -> Unit)? = null,
    private val onError: ((String) -> Unit)? = null
) {
    companion object {
        private const val TAG = "AudioRecorder"
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = android.media.AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = android.media.AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE = 3200 // 200ms of audio at 16kHz
    }

    private var mediaRecorder: MediaRecorder? = null
    private var audioRecord: android.media.AudioRecord? = null
    private var isRecording = false
    private var recordingThread: Thread? = null
    private var outputFile: File? = null

    /**
     * 开始录音
     */
    fun startRecording() {
        if (isRecording) return

        try {
            // 初始化AudioRecord
            val minBufferSize = android.media.AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            )

            audioRecord = android.media.AudioRecord(
                android.media.MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                minBufferSize
            )

            if (audioRecord?.state != android.media.AudioRecord.STATE_INITIALIZED) {
                throw IOException("AudioRecord初始化失败")
            }

            // 开始录音
            audioRecord?.startRecording()
            isRecording = true

            // 启动录音线程
            recordingThread = Thread(recordingRunnable).apply {
                start()
            }

            Log.d(TAG, "开始录音")
        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            onError?.invoke("开始录音失败: ${e.message}")
            release()
        }
    }

    /**
     * 停止录音
     * @return 录音文件
     */
    fun stopRecording(): File? {
        if (!isRecording) return null

        try {
            isRecording = false
            recordingThread?.join()
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            Log.d(TAG, "停止录音")
            return outputFile
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
            onError?.invoke("停止录音失败: ${e.message}")
            return null
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            isRecording = false
            recordingThread?.join()
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
            outputFile = null
        } catch (e: Exception) {
            Log.e(TAG, "释放资源失败", e)
        }
    }

    private val recordingRunnable = Runnable {
        val buffer = ByteArray(BUFFER_SIZE)
        
        while (isRecording) {
            val readSize = audioRecord?.read(buffer, 0, buffer.size) ?: 0
            if (readSize > 0) {
                // 回调音频数据
                onAudioBufferReady?.invoke(buffer.copyOf(readSize))
            }
        }
    }
} 