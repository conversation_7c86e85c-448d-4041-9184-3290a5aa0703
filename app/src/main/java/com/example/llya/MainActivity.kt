package com.example.llya

import android.Manifest
import android.content.Context
import android.content.ContextWrapper
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import android.app.Application
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Chat
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.example.llya.ui.TranslationScreen
import com.example.llya.ui.theme.LlyaTheme
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import com.example.llya.bluetooth.BluetoothScreen
import com.example.llya.bluetooth.BluetoothViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.ui.ProfileScreen
import com.example.llya.ui.NoDeviceScreen
import com.example.llya.ui.LoginScreen
import com.example.llya.ui.EarbudsDetailScreen
import com.example.llya.ui.AIChatScreen
import com.example.llya.ui.SimultaneousTranslationScreen
import com.example.llya.ui.MeetingRecordScreen
import com.example.llya.ui.MeetingRecordListScreen
import com.example.llya.ui.MeetingRecordDetailScreen
import com.example.llya.ui.GoogleMeetingRecordScreen
import com.example.llya.ui.GoogleMeetingListScreen
import androidx.compose.runtime.CompositionLocalProvider
import com.example.llya.utils.LocalLifecycleOwner
import com.example.llya.ui.GoogleCloudDemoScreen
import com.example.llya.viewmodel.GoogleCloudServiceViewModel
import com.example.llya.ui.HistoryScreen
import com.example.llya.ui.AIChatHistoryScreen
import com.example.llya.ui.AIChatDetailScreen
import com.example.llya.ui.FAQScreen
import com.example.llya.ui.AboutUsScreen
import com.example.llya.ui.LanguageSettingsScreen
import com.example.llya.ui.FeedbackScreen
import com.example.llya.ui.PermissionSettingsScreen
import com.example.llya.viewmodel.MeetingRecordViewModel
import com.example.llya.utils.findActivity
import android.util.Log
import com.example.llya.ui.BaseActivity
import androidx.activity.compose.setContent
import com.example.llya.ui.screens.TencentCloudAsrScreen
import com.example.llya.viewmodel.TencentCloudAsrViewModel
import com.example.llya.ui.MicrosoftTranslationScreen
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import android.content.Intent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.ui.Alignment

import com.microsoft.cognitiveservices.speech.translation.SpeechTranslationConfig
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognizer
import kotlinx.coroutines.*
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
/**
 * APP更新功能使用说明:
 *
 * 1. 使用AppUpdateHelper直接调用
 * ```
 * // 简单调用（使用默认值）
 * AppUpdateHelper.showUpdateDialog(this)
 *
 * // 自定义参数调用
 * AppUpdateHelper.showUpdateDialog(
 *     activity = this,
 *     forceUpdate = true,  // 是否强制更新
 *     versionName = "2.1.0",  // 新版本号
 *     updateContent = "1. 修复了已知问题\n2. 优化了性能",  // 更新内容
 *     downloadUrl = "https://your-download-url.com/app.apk"  // 下载地址
 * )
 * ```
 *
 * 2. 使用ViewModel调用（适用于已有ViewModel的页面）
 * ```
 * val appUpdateViewModel: AppUpdateViewModel = viewModel()
 *
 * // 设置更新信息
 * appUpdateViewModel.setUpdateInfo(
 *     versionName = "2.1.0",
 *     versionCode = 21,
 *     updateContent = "1. 修复了已知问题\n2. 优化了性能",
 *     downloadUrl = "https://your-download-url.com/app.apk",
 *     forceUpdate = true
 * )
 *
 * // 显示更新弹窗
 * appUpdateViewModel.showUpdateDialog()
 * ```
 */

class MainActivity : BaseActivity() {

    private val viewModel: SpeechRecognitionViewModel by viewModels()

    // 添加TAG常量用于日志记录
    companion object {
        private const val TAG = "MainActivity"
        private var instance: MainActivity? = null

        fun getInstance(): MainActivity? {
            return instance
        }
    }

    // 录音权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 权限已授权，可以开始录音
            viewModel.startRecognition()
        } else {
            // 权限被拒绝
            Toast.makeText(this, "需要录音权限才能进行语音识别", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 保存实例引用
        instance = this

        // 检查是否是token过期跳转
        val isTokenExpired = intent.getBooleanExtra("TOKEN_EXPIRED", false)
        if (isTokenExpired) {
            Log.d(TAG, "检测到TOKEN_EXPIRED参数，清除用户信息")
            // 确保用户信息已被清除
            com.example.llya.utils.UserCache.clearUserInfo()
            // 显示token过期提示
            Toast.makeText(this, "登录已过期，请重新登录", Toast.LENGTH_LONG).show()
        }

        // 默认开启翻译功能
        viewModel.toggleTranslation(true)

        // 启用边缘到边缘显示，允许内容显示在状态栏下方
        enableEdgeToEdge()

        // 在enableEdgeToEdge之后恢复系统栏颜色设置
        restoreSystemBarsColor()

        setContent {
            LlyaTheme {
                // 提供LocalLifecycleOwner
                CompositionLocalProvider(LocalLifecycleOwner provides this) {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        AppNavigation()
                    }
                }
            }
        }

        // 在setContent之后再次调用以确保系统栏颜色不会被覆盖
        restoreSystemBarsColor()
    }

    /**
     * 恢复保存的系统栏颜色设置
     */
    private fun restoreSystemBarsColor() {
        try {
            val prefs = getSharedPreferences("ui_settings", Context.MODE_PRIVATE)

            // 默认深色主题颜色
            val defaultColor = android.graphics.Color.parseColor("#1E1D2B")

            if (prefs.contains("status_bar_color") && prefs.contains("navigation_bar_color")) {
                val statusBarColor = prefs.getInt("status_bar_color", defaultColor)
                val navigationBarColor = prefs.getInt("navigation_bar_color", defaultColor)

                // 设置状态栏和导航栏颜色
                window.statusBarColor = statusBarColor
                window.navigationBarColor = navigationBarColor

                // 强制使用浅色图标，适合深色背景
                androidx.core.view.WindowCompat.getInsetsController(window, window.decorView).apply {
                    isAppearanceLightStatusBars = false
                    isAppearanceLightNavigationBars = false
                }

                // 确保设置生效
                window.decorView.systemUiVisibility = 0
                window.addFlags(android.view.WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)

                Log.d(TAG, "成功应用系统栏颜色：状态栏=$statusBarColor, 导航栏=$navigationBarColor")
            } else {
                // 如果没有保存的设置，使用默认深色背景
                window.statusBarColor = defaultColor
                window.navigationBarColor = defaultColor

                // 使用浅色图标
                androidx.core.view.WindowCompat.getInsetsController(window, window.decorView).apply {
                    isAppearanceLightStatusBars = false
                    isAppearanceLightNavigationBars = false
                }

                // 保存这些设置到SharedPreferences
                prefs.edit()
                    .putInt("status_bar_color", defaultColor)
                    .putInt("navigation_bar_color", defaultColor)
                    .putBoolean("is_dark_theme", true)
                    .apply()

                Log.d(TAG, "应用并保存默认系统栏颜色：#1E1D2B")
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复系统栏颜色设置失败", e)

            // 出错时使用安全的默认值
            try {
                val defaultColor = android.graphics.Color.parseColor("#1E1D2B")
                window.statusBarColor = defaultColor
                window.navigationBarColor = defaultColor

                androidx.core.view.WindowCompat.getInsetsController(window, window.decorView).apply {
                    isAppearanceLightStatusBars = false
                    isAppearanceLightNavigationBars = false
                }

                // 确保设置生效
                window.decorView.systemUiVisibility = 0
                window.addFlags(android.view.WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)

                Log.d(TAG, "应用备用系统栏颜色：#1E1D2B")
            } catch (ex: Exception) {
                Log.e(TAG, "无法应用备用系统栏颜色", ex)
            }
        }
    }

    /**
     * 计算Android颜色值的亮度
     * 返回0到1之间的值
     */
    private fun calculateColorLuminance(color: Int): Float {
        val red = android.graphics.Color.red(color) / 255f * 0.2126f
        val green = android.graphics.Color.green(color) / 255f * 0.7152f
        val blue = android.graphics.Color.blue(color) / 255f * 0.0722f
        return red + green + blue
    }

    override fun onResume() {
        super.onResume()

        // 在Activity恢复时再次应用系统栏颜色设置
        // 这可以确保在各种生命周期事件后系统栏颜色都能保持一致
        restoreSystemBarsColor()

        Log.d(TAG, "Activity恢复，重新应用系统栏颜色设置")
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清除实例引用
        if (instance == this) {
            instance = null
        }
    }

    // 检查并请求麦克风权限，公开方法供Compose组件调用
    fun checkAndRequestMicrophonePermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED -> {
                // 已经有权限，开始录音
                viewModel.startRecognition()
            }
            else -> {
                // 请求权限
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: SpeechRecognitionViewModel,
    checkAndRequestPermission: () -> Unit,
    onNavigateToBluetooth: () -> Unit,
    onNavigateToProfile: () -> Unit
) {
    val isRecording by viewModel.isRecording.collectAsState()
    val recognitionResult by viewModel.recognitionResult.collectAsState()
    val aiResponse by viewModel.aiResponse.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val selectedLanguage by viewModel.selectedLanguage.collectAsState()

    val context = LocalContext.current
    var languageMenuExpanded by remember { mutableStateOf(false) }

    // 显示错误消息
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            Toast.makeText(context, it, Toast.LENGTH_LONG).show()
            viewModel.clearError()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = { Text("实时语音识别与AI助手") },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                ),
                actions = {
                    // 添加蓝牙按钮
                    IconButton(onClick = onNavigateToBluetooth) {
                        Icon(
                            imageVector = Icons.Filled.Settings,
                            contentDescription = "蓝牙功能"
                        )
                    }
                }
            )
        },
        bottomBar = {
            NavigationBar {
                NavigationBarItem(
                    selected = true,
                    onClick = { /* 已在主页 */ },
                    icon = { Icon(Icons.Default.Home, contentDescription = "首页") },
                    label = { Text("首页") }
                )
                NavigationBarItem(
                    selected = false,
                    onClick = onNavigateToProfile,
                    icon = { Icon(Icons.Outlined.Person, contentDescription = "我的") },
                    label = { Text("我的") }
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 语言选择下拉菜单
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
            ) {
                Row(
                    modifier = Modifier
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                        .padding(16.dp)
                        .fillMaxWidth()
                        .clickable { languageMenuExpanded = true },
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "识别语言: ${viewModel.availableLanguages.find { it.first == selectedLanguage }?.second ?: "中文普通话"}",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "选择语言"
                    )
                }

                DropdownMenu(
                    expanded = languageMenuExpanded,
                    onDismissRequest = { languageMenuExpanded = false },
                    modifier = Modifier.width(IntrinsicSize.Max)
                ) {
                    viewModel.availableLanguages.forEach { (code, name) ->
                        DropdownMenuItem(
                            text = { Text(name) },
                            onClick = {
                                viewModel.setLanguage(code)
                                languageMenuExpanded = false
                            }
                        )
                    }
                }
            }

            // 识别结果显示区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.5f)
                    .clip(RoundedCornerShape(16.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant)
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier.align(Alignment.Center)
                ) {
                    Text(
                        text = "识别结果:",
                        style = MaterialTheme.typography.labelLarge,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    Text(
                        text = recognitionResult.ifEmpty { "请点击下方按钮开始语音识别..." },
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // AI回复显示区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.5f)
                    .clip(RoundedCornerShape(16.dp))
                    .background(MaterialTheme.colorScheme.secondaryContainer)
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier.align(Alignment.Center)
                ) {
                    Text(
                        text = "AI回复:",
                        style = MaterialTheme.typography.labelLarge,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    Text(
                        text = aiResponse.ifEmpty { "语音识别后，AI会在此回复..." },
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // 底部控制区
            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 录音按钮
                FloatingActionButton(
                    onClick = {
                        if (isRecording) {
                            viewModel.stopRecognition(saveToHistory = false)
                        } else {
                            // 检查麦克风权限
                            checkAndRequestPermission()
                            viewModel.startRecognition()
                        }
                    },
                    containerColor = if (isRecording) Color.Red else MaterialTheme.colorScheme.primary
                ) {
                    Icon(
                        imageVector = if (isRecording) Icons.Default.Stop else Icons.Default.Mic,
                        contentDescription = if (isRecording) "停止录音" else "开始录音"
                    )
                }

                // AI对话按钮
                ExtendedFloatingActionButton(
                    onClick = {
                        if (recognitionResult.isNotEmpty()) {
                            viewModel.sendMessageToAI(recognitionResult)
                        } else {
                            Toast.makeText(context, "请先进行语音识别", Toast.LENGTH_SHORT).show()
                        }
                    },
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    icon = { Icon(Icons.Default.Chat, contentDescription = "AI对话") },
                    text = { Text("发送给AI") }
                )
            }
        }
    }
}

@Composable
fun BluetoothNavigation(navController: NavController) {
    val bluetoothViewModel: BluetoothViewModel = viewModel()

    BluetoothScreen(
        viewModel = bluetoothViewModel,
        onBluetoothConnected = { navController.navigate("main") },
        onNavigateToEarbudsDetail = { navController.navigate("earbuds_detail") }
    )
}

@Composable
fun AppNavigation() {
    val navController = rememberNavController()
    val speechRecognitionViewModel: SpeechRecognitionViewModel = viewModel()
    val context = LocalContext.current

    // 添加权限检查函数
    val checkAndRequestMicrophonePermission: () -> Unit = {
        // 尝试获取MainActivity实例来请求权限
        val mainActivity = MainActivity.getInstance()
        if (mainActivity != null) {
            // 使用MainActivity的方法请求权限
            mainActivity.checkAndRequestMicrophonePermission()
        } else {
            // 如果无法获取MainActivity实例，使用Toast提示用户
            Toast.makeText(context, "无法请求录音权限，请重新打开应用", Toast.LENGTH_LONG).show()
        }
        // 确保明确返回Unit
        Unit
    }

    // 检查用户是否已登录
    val isLoggedIn = remember {
        com.example.llya.utils.UserSession.isLoggedIn()
    }

    // 根据登录状态决定起始页面
    val startDestination = if (isLoggedIn) "no_device" else "login"

    NavHost(navController = navController, startDestination = startDestination) {
        // 登录页面
        composable("login") {
            LoginScreen(navController = navController)
        }

        // 无设备页面
        composable(
            route = "no_device?fromDeviceUnbind={fromDeviceUnbind}",
            arguments = listOf(
                navArgument("fromDeviceUnbind") {
                    type = NavType.BoolType
                    defaultValue = false
                }
            )
        ) { backStackEntry ->
            val fromDeviceUnbind = backStackEntry.arguments?.getBoolean("fromDeviceUnbind") ?: false
            NoDeviceScreen(
                navController = navController,
                fromDeviceUnbind = fromDeviceUnbind
            )
        }

        // 主界面
        composable("main") {
            MainScreen(
                viewModel = speechRecognitionViewModel,
                checkAndRequestPermission = checkAndRequestMicrophonePermission,
                onNavigateToBluetooth = { navController.navigate("bluetooth") },
                onNavigateToProfile = { navController.navigate("profile") }
            )
        }

        // 个人中心页面
        composable("profile") {
            ProfileScreen(
                navController = navController
            )
        }

        // 翻译页面
        composable("translation") {
            TranslationScreen(
                navController = navController
            )
        }

        // AI聊天界面
        composable("aichat") {
            AIChatScreen(
                navController = navController
            )
        }

        // AI聊天历史记录界面
        composable("aichat_history") {
            AIChatHistoryScreen(
                navController = navController
            )
        }

        // AI聊天详情界面
        composable(
            route = "aichat_detail/{chatSessionId}",
            arguments = listOf(navArgument("chatSessionId") { type = NavType.StringType })
        ) { backStackEntry ->
            val chatSessionId = backStackEntry.arguments?.getString("chatSessionId") ?: ""
            AIChatDetailScreen(
                navController = navController,
                chatSessionId = chatSessionId
            )
        }

        // 同声传译页面
        composable("simultaneous_translation") {
            SimultaneousTranslationScreen(
                navController = navController
            )
        }

        // 微软语音翻译页面 - 跳转到 MicrosoftSpeechActivity
        composable("microsoft_translation") {
            var hasLaunched by remember { mutableStateOf(false) }

            LaunchedEffect(Unit) {
                if (!hasLaunched) {
                    hasLaunched = true
                    val intent = Intent(context, MicrosoftSpeechActivity::class.java)
                    context.startActivity(intent)
                    // 启动Activity后立即返回上一页
                    navController.popBackStack()
                }
            }

            // 显示一个简单的加载界面
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }

        // 会议记录页面
        composable("meeting_record") {
            MeetingRecordScreen(navController)
        }

        // 会议记录历史列表
        composable("meeting_list") {
            MeetingRecordListScreen(navController)
        }

        // 会议记录详情
        composable(
            "meeting_detail/{recordId}",
            arguments = listOf(navArgument("recordId") { type = NavType.StringType })
        ) { backStackEntry ->
            val recordId = backStackEntry.arguments?.getString("recordId") ?: ""
            MeetingRecordDetailScreen(navController, recordId)
        }

        // Google会议记录页面
        composable("google_meeting_record") {
            GoogleMeetingRecordScreen(navController)
        }

        // Google会议记录历史列表
        composable("google_meeting_list") {
            GoogleMeetingListScreen(navController)
        }

        // Google会议记录详情页面
        composable(
            route = "meeting_record_detail/{recordId}",
            arguments = listOf(navArgument("recordId") { type = NavType.StringType })
        ) { backStackEntry ->
            val recordId = backStackEntry.arguments?.getString("recordId") ?: ""
            println("导航到会议记录详情页面，ID: $recordId")
            MeetingRecordDetailScreen(
                navController = navController,
                recordId = recordId,
                speechViewModel = MeetingRecordViewModel(LocalContext.current.applicationContext as Application)
            )
        }

        // 历史记录页面
        composable("history") {
            HistoryScreen(navController = navController)
        }

        // 常见问题页面
        composable("faq") {
            FAQScreen(navController = navController)
        }

        // 语言设置页面
        composable("language_settings") {
            LanguageSettingsScreen(navController = navController)
        }

        // 关于我们页面
        composable("about_us") {
            AboutUsScreen(navController = navController)
        }

        // 意见反馈页面
        composable("feedback") {
            FeedbackScreen(navController = navController)
        }

        // 权限设置页面
        composable("permission_settings") {
            PermissionSettingsScreen(navController = navController)
        }

        // 耳机详情页面
        composable("earbuds_detail") {
            EarbudsDetailScreen(
                navController = navController
            )
        }

        // 腾讯云语音识别测试页面
        composable("tencent_cloud_asr") {
            TencentCloudAsrScreen(navController = navController)
        }

        // 蓝牙功能页面
        composable(
            route = "bluetooth?fromUnbind={fromUnbind}",
            arguments = listOf(
                navArgument("fromUnbind") {
                    type = NavType.BoolType
                    defaultValue = false
                }
            )
        ) { backStackEntry ->
            val fromUnbind = backStackEntry.arguments?.getBoolean("fromUnbind") ?: false
            val bluetoothViewModel: BluetoothViewModel = viewModel()
            BluetoothScreen(
                viewModel = bluetoothViewModel,
                fromUnbind = fromUnbind,
                onBluetoothConnected = { /* 已经在设备页面，不需要额外操作 */ },
                onNoDeviceDetected = { navController.navigate("no_device") },
                onNavigateToMain = { navController.navigate("main") },
                onNavigateToEarbudsDetail = { navController.navigate("earbuds_detail") }
            )
        }

        // 谷歌云服务Demo页面
        composable("google_cloud_demo") {
            val googleCloudServiceViewModel: GoogleCloudServiceViewModel = viewModel()
            GoogleCloudDemoScreen(
                viewModel = googleCloudServiceViewModel,
                onNavigateBack = { navController.popBackStack() },
                navController = navController
            )
        }
    }
}