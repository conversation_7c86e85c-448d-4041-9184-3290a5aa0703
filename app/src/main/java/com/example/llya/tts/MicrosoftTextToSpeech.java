package com.example.llya.tts;

import android.util.Log;

import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisEventArgs;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisResult;
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer;
import com.microsoft.cognitiveservices.speech.util.EventHandler;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;

/**
 * 微软文本转语音客户端
 * 使用官方Speech SDK实现文本转语音
 */
public class MicrosoftTextToSpeech {
    private static final String TAG = "MicrosoftTextToSpeech";

    // 微软语音服务配置
    private final String subscriptionKey;
    private final String region;

    // 错误回调
    private OnErrorListener errorListener;

    // 语音列表缓存
    private final Map<String, List<Voice>> voiceCache = new HashMap<>();

    /**
     * 构造函数
     *
     * @param subscriptionKey 订阅密钥
     * @param region         区域
     */
    public MicrosoftTextToSpeech(String subscriptionKey, String region) {
        this.subscriptionKey = subscriptionKey;
        this.region = region;
    }

    /**
     * 语音模型
     */
    public static class Voice {
        private final String name;
        private final String displayName;
        private final String gender;
        private final String locale;

        public Voice(String name, String displayName, String gender, String locale) {
            this.name = name;
            this.displayName = displayName;
            this.gender = gender;
            this.locale = locale;
        }

        public String getName() {
            return name;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getGender() {
            return gender;
        }

        public String getLocale() {
            return locale;
        }
    }

    /**
     * 获取指定语言的可用语音列表
     */
    public List<Voice> getAvailableVoices(String languageCode) {
        // 检查缓存
        if (voiceCache.containsKey(languageCode)) {
            return voiceCache.get(languageCode);
        }

        // 根据语言代码返回预定义的语音列表
        List<Voice> voices = new ArrayList<>();

        if (languageCode.startsWith("zh-CN")) {
            voices.add(new Voice("zh-CN-XiaoxiaoNeural", "晓晓 (女声)", "Female", "zh-CN"));
            voices.add(new Voice("zh-CN-YunxiNeural", "云希 (男声)", "Male", "zh-CN"));
            voices.add(new Voice("zh-CN-YunjianNeural", "云健 (男声)", "Male", "zh-CN"));
            voices.add(new Voice("zh-CN-XiaoyiNeural", "晓伊 (女声)", "Female", "zh-CN"));
            voices.add(new Voice("zh-CN-YunyangNeural", "云扬 (男声)", "Male", "zh-CN"));
        } else if (languageCode.startsWith("en-US")) {
            voices.add(new Voice("en-US-JennyNeural", "Jenny (Female)", "Female", "en-US"));
            voices.add(new Voice("en-US-GuyNeural", "Guy (Male)", "Male", "en-US"));
            voices.add(new Voice("en-US-AriaNeural", "Aria (Female)", "Female", "en-US"));
            voices.add(new Voice("en-US-DavisNeural", "Davis (Male)", "Male", "en-US"));
            voices.add(new Voice("en-US-AmberNeural", "Amber (Female)", "Female", "en-US"));
        } else if (languageCode.startsWith("ja-JP")) {
            voices.add(new Voice("ja-JP-NanamiNeural", "Nanami (Female)", "Female", "ja-JP"));
            voices.add(new Voice("ja-JP-KeitaNeural", "Keita (Male)", "Male", "ja-JP"));
            voices.add(new Voice("ja-JP-AoiNeural", "Aoi (Female)", "Female", "ja-JP"));
            voices.add(new Voice("ja-JP-DaichiNeural", "Daichi (Male)", "Male", "ja-JP"));
            voices.add(new Voice("ja-JP-ShioriNeural", "Shiori (Female)", "Female", "ja-JP"));
        } else if (languageCode.startsWith("ko-KR")) {
            voices.add(new Voice("ko-KR-SunHiNeural", "SunHi (Female)", "Female", "ko-KR"));
            voices.add(new Voice("ko-KR-InJoonNeural", "InJoon (Male)", "Male", "ko-KR"));
            voices.add(new Voice("ko-KR-YuJinNeural", "YuJin (Female)", "Female", "ko-KR"));
        } else if (languageCode.startsWith("fr-FR")) {
            voices.add(new Voice("fr-FR-DeniseNeural", "Denise (Female)", "Female", "fr-FR"));
            voices.add(new Voice("fr-FR-HenriNeural", "Henri (Male)", "Male", "fr-FR"));
            voices.add(new Voice("fr-FR-AlainNeural", "Alain (Male)", "Male", "fr-FR"));
        } else if (languageCode.startsWith("de-DE")) {
            voices.add(new Voice("de-DE-KatjaNeural", "Katja (Female)", "Female", "de-DE"));
            voices.add(new Voice("de-DE-ConradNeural", "Conrad (Male)", "Male", "de-DE"));
            voices.add(new Voice("de-DE-AmalaNeural", "Amala (Female)", "Female", "de-DE"));
        } else if (languageCode.startsWith("es-ES")) {
            voices.add(new Voice("es-ES-ElviraNeural", "Elvira (Female)", "Female", "es-ES"));
            voices.add(new Voice("es-ES-AlvaroNeural", "Alvaro (Male)", "Male", "es-ES"));
            voices.add(new Voice("es-ES-AbrilNeural", "Abril (Female)", "Female", "es-ES"));
        } else if (languageCode.startsWith("it-IT")) {
            voices.add(new Voice("it-IT-ElsaNeural", "Elsa (Female)", "Female", "it-IT"));
            voices.add(new Voice("it-IT-DiegoNeural", "Diego (Male)", "Male", "it-IT"));
            voices.add(new Voice("it-IT-IsabellaNeural", "Isabella (Female)", "Female", "it-IT"));
        } else if (languageCode.startsWith("ru-RU")) {
            voices.add(new Voice("ru-RU-SvetlanaNeural", "Svetlana (Female)", "Female", "ru-RU"));
            voices.add(new Voice("ru-RU-DmitryNeural", "Dmitry (Male)", "Male", "ru-RU"));
            voices.add(new Voice("ru-RU-DariyaNeural", "Dariya (Female)", "Female", "ru-RU"));
        }

        // 缓存结果
        voiceCache.put(languageCode, voices);
        return voices;
    }

    /**
     * 文本转语音
     * @param text 要转换的文本
     * @param languageCode 语言代码，例如zh-CN、en-US等
     * @param voiceName 语音名称，例如zh-CN-XiaoxiaoNeural
     * @return 音频数据（MP3格式）
     */
    public byte[] textToSpeech(String text, String languageCode, String voiceName) {
        try {
            // 创建语音配置
            SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);

            // 设置语音合成输出格式为MP3
            speechConfig.setSpeechSynthesisOutputFormat(com.microsoft.cognitiveservices.speech.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3);

            // 设置语音
            if (voiceName != null && !voiceName.isEmpty()) {
                speechConfig.setSpeechSynthesisVoiceName(voiceName);
            } else {
                // 如果未指定语音，使用默认语音
                String defaultVoice = getDefaultVoice(languageCode);
                speechConfig.setSpeechSynthesisVoiceName(defaultVoice);
            }

            // 创建语音合成器
            SpeechSynthesizer synthesizer = new SpeechSynthesizer(speechConfig);

            // 用于等待合成完成的信号量
            Semaphore synthesisCompleted = new Semaphore(0);

            // 存储音频数据的输出流
            ByteArrayOutputStream audioOutputStream = new ByteArrayOutputStream();

            // 存储错误信息
            final String[] errorInfo = {null};

            // 注册事件处理程序
            synthesizer.SynthesisCompleted.addEventListener((o, e) -> {
                Log.d(TAG, "语音合成完成");
                synthesisCompleted.release();
            });

            synthesizer.SynthesisStarted.addEventListener((o, e) -> {
                Log.d(TAG, "语音合成开始");
            });

            synthesizer.Synthesizing.addEventListener((o, e) -> {
                // 接收合成的音频数据
                byte[] audioData = e.getResult().getAudioData();
                if (audioData != null && audioData.length > 0) {
                    try {
                        audioOutputStream.write(audioData, 0, audioData.length);
                    } catch (Exception ex) {
                        Log.e(TAG, "写入音频数据失败", ex);
                    }
                }
            });

            synthesizer.SynthesisCanceled.addEventListener((o, e) -> {
                Log.e(TAG, "语音合成取消");
                errorInfo[0] = "语音合成取消";
                synthesisCompleted.release();
            });

            // 开始语音合成
            Log.d(TAG, "开始合成文本: '" + text + "', 语音: " + voiceName);
            SpeechSynthesisResult result = synthesizer.SpeakTextAsync(text).get();

            // 等待合成完成
            synthesisCompleted.acquire();

            // 关闭合成器
            synthesizer.close();

            // 检查是否有错误
            if (errorInfo[0] != null) {
                if (errorListener != null) {
                    errorListener.onError(errorInfo[0]);
                }
                return null;
            }

            // 检查合成结果
            if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                // 返回合成的音频数据
                byte[] audioData = audioOutputStream.toByteArray();
                Log.d(TAG, "语音合成成功，音频大小: " + audioData.length + " 字节");
                return audioData;
            } else {
                // 合成失败
                String errorMessage = "语音合成失败: " + result.getReason();
                Log.e(TAG, errorMessage);
                if (errorListener != null) {
                    errorListener.onError(errorMessage);
                }
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "文本转语音失败", e);
            if (errorListener != null) {
                errorListener.onError("文本转语音失败: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * 获取默认语音
     */
    private String getDefaultVoice(String languageCode) {
        if (languageCode.startsWith("zh-CN")) {
            return "zh-CN-XiaoxiaoNeural";
        } else if (languageCode.startsWith("en-US")) {
            return "en-US-JennyNeural";
        } else if (languageCode.startsWith("ja-JP")) {
            return "ja-JP-NanamiNeural";
        } else if (languageCode.startsWith("ko-KR")) {
            return "ko-KR-SunHiNeural";
        } else if (languageCode.startsWith("fr-FR")) {
            return "fr-FR-DeniseNeural";
        } else if (languageCode.startsWith("de-DE")) {
            return "de-DE-KatjaNeural";
        } else if (languageCode.startsWith("es-ES")) {
            return "es-ES-ElviraNeural";
        } else if (languageCode.startsWith("it-IT")) {
            return "it-IT-ElsaNeural";
        } else if (languageCode.startsWith("ru-RU")) {
            return "ru-RU-SvetlanaNeural";
        } else {
            return "en-US-JennyNeural"; // 默认使用英语
        }
    }

    /**
     * 设置错误回调
     */
    public void setErrorListener(OnErrorListener listener) {
        this.errorListener = listener;
    }

    /**
     * 错误回调接口
     */
    public interface OnErrorListener {
        void onError(String errorMessage);
    }
}
