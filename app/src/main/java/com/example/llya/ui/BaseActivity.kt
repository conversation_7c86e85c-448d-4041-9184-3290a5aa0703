package com.example.llya.ui

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.ComponentActivity
import android.util.Log
import com.example.llya.utils.ActivityHelper
import com.example.llya.utils.LanguageHelper

/**
 * 基础Activity
 * 所有Activity都应该继承这个类，以确保正确应用语言设置
 */
open class BaseActivity : ComponentActivity() {
    private val TAG = "BaseActivity[${javaClass.simpleName}]"
    
    override fun attachBaseContext(newBase: Context) {
        // 使用LanguageHelper包装基础上下文，确保应用当前的语言设置
        val context = LanguageHelper.wrapContext(newBase)
        super.attachBaseContext(context)
        Log.d(TAG, "attachBaseContext: 应用语言设置")
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 应用当前的语言设置
        ActivityHelper.applyLanguageSettings(this)
        Log.d(TAG, "onCreate: 应用语言设置")
    }
    
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        // 当配置变化时（如旋转屏幕），确保语言设置不变
        ActivityHelper.applyLanguageSettings(this)
        Log.d(TAG, "onConfigurationChanged: 重新应用语言设置")
    }
} 