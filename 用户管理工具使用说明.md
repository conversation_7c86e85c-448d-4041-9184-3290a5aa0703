# 用户信息管理工具

这个工具类封装了与用户相关的所有API调用，提供了统一的用户信息管理功能。

## 主要功能

1. 用户登录（手机号、邮箱）
2. 获取/更新用户信息
3. 账号注销
4. 发送验证码
5. 管理用户设备信息
6. 本地用户信息缓存

## 结构介绍

### 1. 数据模型 (UserModel.kt)

```kotlin
data class UserModel(
    val user_id: Int = 0,
    val username: String = "",
    val email: String = "",
    val userType: Int = 0,
    val createtime: Long = 0,
    val updatetime: Long = 0,
    val status: Int = 1,
    val vipExpireTime: Long = 0,
    val devices: List<DeviceModel> = emptyList()
)

data class DeviceModel(
    val devicename: String = "",
    val macaddress: String = "",
    val location: String = "",
    val bindtime: Long = 0,
    val bindtime_text: String = ""
)

data class ApiResponse<T>(
    val status: Int = 0,
    val msg: String = "",
    val data: T? = null,
    val token: String? = null,
    val key: String? = null
)
```

### 2. API服务接口 (ApiService.kt)

定义了所有与用户相关的API调用接口，使用Retrofit注解实现HTTP请求：

```kotlin
interface ApiService {
    @FormUrlEncoded
    @POST("/api/user/update")
    suspend fun updateUserInfo(...): ApiResponse<Any>

    @POST("/api/user/detail")
    suspend fun getUserDetail(): ApiResponse<UserModel>
    
    // 更多API...
}
```

### 3. Retrofit网络客户端 (RetrofitClient.kt)

管理网络请求的配置和创建：

```kotlin
object RetrofitClient {
    // 设置Token
    fun setToken(token: String) { ... }
    
    // 获取API服务实例
    fun createApiService(): ApiService { ... }
}
```

### 4. 用户管理工具类 (UserManager.kt)

核心工具类，封装了所有用户相关操作：

```kotlin
class UserManager private constructor(context: Context) {
    // 获取实例
    companion object {
        fun getInstance(context: Context): UserManager { ... }
    }
    
    // 用户登录
    fun login(...): Flow<ApiResponse<UserModel>> { ... }
    
    // 获取用户信息
    fun getUserDetail(): Flow<ApiResponse<UserModel>> { ... }
    
    // 更多操作...
}
```

### 5. 用户ViewModel (UserViewModel.kt)

用于在UI层使用用户管理工具：

```kotlin
class UserViewModel(application: Application) : AndroidViewModel(application) {
    // UI状态
    val uiState: StateFlow<UiState> = _uiState
    
    // 用户登录
    fun login(...) { ... }
    
    // 更多UI操作...
}
```

## 使用方法

### 1. 初始化

在应用启动时初始化用户管理工具：

```kotlin
// 在Application类中初始化
UserManager.getInstance(context)
```

### 2. 用户登录

手机号登录：

```kotlin
viewModel.login("13812345678", "verify_id", "123456")
```

邮箱登录：

```kotlin
viewModel.emailLogin("<EMAIL>", "password")
```

### 3. 获取用户信息

```kotlin
viewModel.getUserDetail()
```

### 4. 观察用户信息变化

```kotlin
// 在Compose中
val userState by viewModel.currentUser.observeAsState()
userState?.let { user ->
    // 使用用户信息
    Text(text = "用户名: ${user.username}")
}
```

### 5. 观察操作状态

```kotlin
// 在Compose中
val uiState by viewModel.uiState.collectAsState()

when (uiState) {
    is UiState.Loading -> {
        // 显示加载中
        CircularProgressIndicator()
    }
    is UiState.Success -> {
        // 操作成功
        Text(text = (uiState as UiState.Success).message)
    }
    is UiState.Error -> {
        // 操作失败
        Text(text = (uiState as UiState.Error).message)
    }
    // 其他状态...
}
```

### 6. 退出登录

```kotlin
viewModel.logout()
```

## 注意事项

1. 所有网络请求都在IO线程上执行，不会阻塞主线程
2. 用户信息会自动缓存到本地，应用重启后仍然可用
3. Token自动管理，无需手动处理
4. 所有操作都提供了状态反馈，方便处理成功/失败情况
5. 使用Kotlin Flow或LiveData观察数据变化，支持响应式编程 