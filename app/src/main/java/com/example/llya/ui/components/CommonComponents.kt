package com.example.llya.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.R

/**
 * 底部导航栏项目
 * 
 * @param icon 图标（可以是ImageVector或Painter）
 * @param label 标签文本
 * @param isSelected 是否选中
 * @param tint 图标和文字颜色
 * @param onClick 点击回调
 */
@Composable
fun BottomNavItem(
    icon: Any, // 可以是ImageVector或Painter
    label: String,
    isSelected: Boolean,
    tint: Color = Color.White.copy(alpha = 0.6f),
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable(onClick = onClick)
            .padding(4.dp)
    ) {
        when (icon) {
            is ImageVector -> {
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    tint = if (isSelected) tint else Color.White.copy(alpha = 0.6f),
                    modifier = Modifier.size(24.dp)
                )
            }
            is Painter -> {
                Icon(
                    painter = icon,
                    contentDescription = label,
                    tint = if (isSelected) tint else Color.White.copy(alpha = 0.6f),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            color = if (isSelected) tint else Color.White.copy(alpha = 0.6f),
            fontSize = 12.sp
        )
    }
}

/**
 * 公共底部导航栏组件
 * 
 * @param navController 导航控制器
 * @param currentRoute 当前路由，用于高亮显示当前选中的选项
 */
@Composable
fun CommonBottomNavBar(
    navController: NavController? = null,
    currentRoute: String = "main"
) {
    val purpleColor = Color(0xFF8364FD)
    
    Column(modifier = Modifier.fillMaxWidth()) {
        Divider(color = Color.White.copy(alpha = 0.1f))
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 首页
            BottomNavItem(
                icon = Icons.Default.Home,
                label = "首页",
                isSelected = currentRoute == "main" || currentRoute == "earbuds_detail",
                tint = purpleColor,
                onClick = { 
                    if (currentRoute != "main" && currentRoute != "earbuds_detail") {
                        navController?.navigate("earbuds_detail") 
                    }
                }
            )
            
            // 翻译
            BottomNavItem(
                icon = painterResource(id = R.drawable.ic_translate),
                label = "翻译",
                isSelected = currentRoute == "translation",
                tint = purpleColor,
                onClick = { 
                    if (currentRoute != "translation") {
                        navController?.navigate("translation") 
                    }
                }
            )
            
            // 我的
            BottomNavItem(
                icon = Icons.Outlined.Person,
                label = "我的",
                isSelected = currentRoute == "profile",
                tint = purpleColor,
                onClick = { 
                    if (currentRoute != "profile") {
                        navController?.navigate("profile") 
                    }
                }
            )
        }
    }
} 