package com.example.llya.viewmodel

import android.app.Application
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.audio.AudioRecorder
import com.example.llya.asr.TencentCloudSpeechToTextClient
import com.example.llya.network.GoogleCloudServiceAdapter
import com.example.llya.utils.TextPersistenceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 会议记录ViewModel
 * 支持Google Cloud和腾讯云的语音识别服务，专门用于会议记录功能
 * 根据选择的语言自动切换服务
 */
class MeetingRecordViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "MeetingRecordViewModel"

        // 腾讯云配置
        private const val TENCENT_APP_ID = "1251118525"
        private const val TENCENT_SECRET_ID = "AKIDoTt53sTpwoW2KQIuHPIvJSM2TeaHkyyk"
        private const val TENCENT_SECRET_KEY = "aw4wZy83iBQP9fx1qQmunRq1yf7KJ3Yp"

        // 沉默检测阈值（毫秒）
        private const val SILENCE_THRESHOLD = 1500L

        // 常见语气词、助词等（用于文本质量评估）
        private val FILLER_WORDS = setOf(
            "嗯", "啊", "那个", "就是", "这个", "然后", "所以", "因为", "但是",
            "um", "uh", "like", "you know", "well", "so", "then", "basically",
            "actually", "literally"
        )

        // 使用腾讯云的语言列表
        private val TENCENT_SUPPORTED_LANGUAGES = setOf(
            "zh-CN",      // 中文普通话
            "yue-Hant-HK", // 粤语
            "en-US",      // 英语
            "ko-KR",      // 韩语
            "ja-JP",      // 日语
            "th-TH",      // 泰语
            "id-ID",      // 印度尼西亚语
            "vi-VN",      // 越南语
            "ms-MY",      // 马来语
            "fil-PH",     // 菲律宾语
            "pt-PT",      // 葡萄牙语
            "tr-TR",      // 土耳其语
            "ar-SA",      // 阿拉伯语
            "es-ES",      // 西班牙语
            "hi-IN",      // 印地语
            "fr-FR",      // 法语
            "de-DE"       // 德语
        )

        // 腾讯云语言代码映射
        private val LANGUAGE_TO_TENCENT_MODEL = mapOf(
            "zh-CN" to "16k_zh",            // 中文普通话
            "yue-Hant-HK" to "16k_ca",      // 粤语
            "en-US" to "16k_en",            // 英语
            "ko-KR" to "16k_ko",            // 韩语
            "ja-JP" to "16k_ja",            // 日语
            "th-TH" to "16k_th",            // 泰语
            "id-ID" to "16k_id",            // 印度尼西亚语
            "vi-VN" to "16k_vi",            // 越南语
            "ms-MY" to "16k_ms",            // 马来语
            "fil-PH" to "16k_fil",          // 菲律宾语
            "pt-PT" to "16k_pt",            // 葡萄牙语
            "tr-TR" to "16k_tr",            // 土耳其语
            "ar-SA" to "16k_ar",            // 阿拉伯语
            "es-ES" to "16k_es",            // 西班牙语
            "hi-IN" to "16k_hi",            // 印地语
            "fr-FR" to "16k_fr",            // 法语
            "de-DE" to "16k_de"             // 德语
        )
    }

    // Google Cloud服务适配器
    private val cloudServiceAdapter = GoogleCloudServiceAdapter.getInstance()

    // 腾讯云语音识别客户端
    private var tencentAsrClient: TencentCloudSpeechToTextClient? = null

    // 文本持久化管理器
    private val textPersistenceManager = TextPersistenceManager(application)

    // 音频录制器
    private var audioRecorder: AudioRecorder? = null

    // 录音状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    // 当前语言
    private val _currentLanguage = MutableStateFlow("zh-CN")
    val currentLanguage: StateFlow<String> = _currentLanguage.asStateFlow()

    // 当前会议记录内容
    private val _meetingContent = MutableStateFlow("")
    val meetingContent: StateFlow<String> = _meetingContent.asStateFlow()

    // 当前显示的识别结果(实时)
    private val _currentRecognitionResult = MutableStateFlow("")
    val currentRecognitionResult: StateFlow<String> = _currentRecognitionResult.asStateFlow()

    // 当前显示的最终识别结果
    private val _currentFinalRecognitionResult = MutableStateFlow("")
    val currentFinalRecognitionResult: StateFlow<String> = _currentFinalRecognitionResult.asStateFlow()

    // 控制是否显示实时转写预览
    private val _showRealtimePreview = MutableStateFlow(true)
    val showRealtimePreview: StateFlow<Boolean> = _showRealtimePreview.asStateFlow()

    // 谷歌云累积文本，类似腾讯云的accumulatedText
    private val _googleCloudAccumulatedText = MutableStateFlow("")

    // 谷歌云最终结果列表，用于存储所有最终识别结果
    private val _googleCloudFinalResults = mutableListOf<String>()

    // 谷歌云当前临时结果，用于实时显示但不累积
    private val _googleCloudTemporaryResult = MutableStateFlow("")

    // 用于跟踪最后一个最终结果
    private var lastFinalResult = ""

    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 会议记录列表
    private val _meetingRecords = MutableStateFlow<List<TextPersistenceManager.MeetingRecord>>(emptyList())
    val meetingRecords: StateFlow<List<TextPersistenceManager.MeetingRecord>> = _meetingRecords.asStateFlow()

    // 当前选中的会议记录
    private val _selectedRecord = MutableStateFlow<TextPersistenceManager.MeetingRecord?>(null)
    val selectedRecord: StateFlow<TextPersistenceManager.MeetingRecord?> = _selectedRecord.asStateFlow()

    // 支持的语言列表
    private val _supportedLanguages = MutableStateFlow<List<Pair<String, String>>>(
        listOf(
            "zh-CN" to "中文(简体)",
            "en-US" to "英语(美国)",
            "ja-JP" to "日语",
            "ko-KR" to "韩语",
            "fr-FR" to "法语",
            "de-DE" to "德语",
            "ru-RU" to "俄语",
            "es-ES" to "西班牙语"
        )
    )
    val supportedLanguages: StateFlow<List<Pair<String, String>>> = _supportedLanguages.asStateFlow()

    // 是否正在加载语言列表
    private val _isLoadingLanguages = MutableStateFlow(false)
    val isLoadingLanguages: StateFlow<Boolean> = _isLoadingLanguages.asStateFlow()

    // 当前使用的ASR服务名称
    private val _currentAsrServiceName = MutableStateFlow<String>("Google Cloud")
    val currentAsrServiceName: StateFlow<String> = _currentAsrServiceName.asStateFlow()

    // 会议开始时间
    private var meetingStartTime = 0L

    // 最后一次语音识别时间（用于沉默检测）
    private var lastVoiceActivityTime = 0L

    // 防止重复添加识别结果的标志
    private var isLastResultProcessed = false

    // 当前正在使用的ASR服务类型
    enum class AsrServiceType {
        GOOGLE_CLOUD,
        TENCENT_CLOUD
    }

    // 当前使用的ASR服务
    private val _currentAsrService = MutableStateFlow(AsrServiceType.GOOGLE_CLOUD)

    init {
        // 初始化腾讯云语音识别客户端
        initializeTencentAsrClient()

        // 加载已有会议记录内容
        loadCurrentMeetingContent()

        // 加载会议记录列表
        loadMeetingRecords()

        // 加载支持的语言列表
        loadSupportedLanguages()

        // 根据初始语言设置ASR服务
        updateAsrServiceByLanguage(_currentLanguage.value)

        // 监听腾讯云ASR客户端的识别结果
        viewModelScope.launch {
            tencentAsrClient?.recognitionResult?.collect { result ->
                // 对于腾讯云，我们不再更新实时预览，只记录日志
                // 腾讯云的累积文本功能将通过accumulatedText流来处理
                if (_currentAsrService.value == AsrServiceType.TENCENT_CLOUD &&
                    result.isNotEmpty() &&
                    !result.contains("连接已建立") &&
                    !result.contains("失败")) {
                    // 不再更新当前显示的识别结果，只记录日志
                    // 这样腾讯云就不会显示实时预览
                    // _currentRecognitionResult.value = result

                    // 这里不再尝试更新会议内容，只记录日志
                    // 这是一个UI显示效果，最终累积的文本通过accumulatedText流处理

                    Log.d(TAG, "【腾讯云ASR】实时识别结果(不显示预览): $result")
                }
            }
        }

        // 监听腾讯云ASR客户端的累积文本
        viewModelScope.launch {
            tencentAsrClient?.accumulatedText?.collect { text ->
                if (_currentAsrService.value == AsrServiceType.TENCENT_CLOUD &&
                    text.isNotEmpty()) {
                    // 不再直接覆盖会议内容，而是将新识别的部分追加到会议内容
                    val currentContent = _meetingContent.value
                    val newText = text

                    // 检查新文本是否已经包含在当前内容中
                    if (!currentContent.contains(newText)) {
                        // 判断是全新文本还是追加文本
                        if (newText.length > currentContent.length && newText.startsWith(currentContent)) {
                            // 追加新识别的部分
                            val additionalText = newText.substring(currentContent.length)
                            _meetingContent.value = currentContent + additionalText
                            Log.d(TAG, "【腾讯云ASR】追加新识别文本: $additionalText")
                        } else if (!newText.isEmpty()) {
                            // 处理不完全重叠的情况
                            // 查找重叠部分
                            val overlap = findOverlap(currentContent, newText)
                            if (overlap.length > 5) { // 有显著重叠
                                val nonOverlappingPart = newText.substring(overlap.length)
                                if (nonOverlappingPart.isNotEmpty()) {
                                    _meetingContent.value = currentContent + nonOverlappingPart
                                    Log.d(TAG, "【腾讯云ASR】添加非重叠部分: $nonOverlappingPart")
                                }
                            } else {
                                // 没有明显重叠，添加分隔符并追加
                                if (currentContent.isNotEmpty()) {
                                    val separator = if (currentContent.endsWith(".") || currentContent.endsWith("。") ||
                                                      currentContent.endsWith("!") || currentContent.endsWith("！") ||
                                                      currentContent.endsWith("?") || currentContent.endsWith("？")) {
                                        "\n" // 句子结束，添加换行
                                    } else {
                                        " " // 否则添加空格
                                    }
                                    _meetingContent.value = currentContent + separator + newText
                                } else {
                                    _meetingContent.value = newText
                                }
                                Log.d(TAG, "【腾讯云ASR】添加新文本: $newText")
                            }
                        }
                        // 保存更新后的会议内容
                        saveCurrentMeetingContent()
                    }
                }
            }
        }

        // 添加状态变化监听
        viewModelScope.launch {
            _isRecording.collect { isRecording ->
                if (_currentAsrService.value == AsrServiceType.TENCENT_CLOUD) {
                    if (isRecording) {
                        // 开始录音时，确保腾讯云ASR客户端准备就绪
                        Log.d(TAG, "腾讯云ASR：录音开始，准备接收语音")
                    } else {
                        // 停止录音时，清除当前识别结果，但保留累积文本
                        _currentRecognitionResult.value = ""
                        Log.d(TAG, "腾讯云ASR：录音停止，当前累积文本长度为 ${_meetingContent.value.length}")
                    }
                }
            }
        }
    }

    /**
     * 初始化腾讯云语音识别客户端
     */
    private fun initializeTencentAsrClient() {
        try {
            tencentAsrClient = TencentCloudSpeechToTextClient(
                appId = TENCENT_APP_ID,
                secretId = TENCENT_SECRET_ID,
                secretKey = TENCENT_SECRET_KEY,
                coroutineScope = viewModelScope
            )
            Log.d(TAG, "腾讯云语音识别客户端初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "腾讯云语音识别客户端初始化失败", e)
            _errorMessage.value = "腾讯云语音识别初始化失败: ${e.message}"
        }
    }

    /**
     * 判断是否应该使用腾讯云语音识别
     */
    private fun shouldUseTencentCloud(languageCode: String): Boolean {
        return TENCENT_SUPPORTED_LANGUAGES.contains(languageCode)
    }

    /**
     * 获取腾讯云引擎模型类型
     */
    private fun getTencentEngineModelType(languageCode: String): String {
        return LANGUAGE_TO_TENCENT_MODEL[languageCode] ?: "16k_zh"
    }

    /**
     * 根据语言更新ASR服务
     */
    private fun updateAsrServiceByLanguage(languageCode: String) {
        val useTencentCloud = shouldUseTencentCloud(languageCode)
        _currentAsrService.value = if (useTencentCloud) AsrServiceType.TENCENT_CLOUD else AsrServiceType.GOOGLE_CLOUD
        _currentAsrServiceName.value = if (useTencentCloud) "腾讯云" else "Google Cloud"
        Log.d(TAG, "根据语言[$languageCode]切换ASR服务为: ${_currentAsrServiceName.value}")
    }

    /**
     * 加载支持的语言列表
     */
    fun loadSupportedLanguages() {
        viewModelScope.launch {
            try {
                _isLoadingLanguages.value = true
                val languages = cloudServiceAdapter.getSupportedLanguages()

                if (languages.isNotEmpty()) {
                    // 转换为UI需要的格式
                    val languagePairs = languages.map { it.code to it.name }
                    _supportedLanguages.value = languagePairs
                    Log.d(TAG, "成功加载${languagePairs.size}种支持的语言")
                } else {
                    Log.w(TAG, "获取到的语言列表为空，使用默认语言列表")
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取支持的语言列表失败: ${e.message}", e)
                _errorMessage.value = "无法获取支持的语言列表，使用默认语言"
            } finally {
                _isLoadingLanguages.value = false
            }
        }
    }

    /**
     * 加载当前会议记录内容
     */
    private fun loadCurrentMeetingContent() {
        viewModelScope.launch {
            val content = textPersistenceManager.loadText()
            _meetingContent.value = content
            Log.d(TAG, "已加载当前会议记录，长度: ${content.length}字符")
        }
    }

    /**
     * 开始会议记录
     */
    fun startMeetingRecording() {
        if (_isRecording.value) {
            Log.d(TAG, "已经在录音中，先停止当前录音")
            stopMeetingRecording()
            return
        }

        // 重置防重复标志
        isLastResultProcessed = false

        // 记录会议开始时间
        meetingStartTime = System.currentTimeMillis()

        // 初始化最后一次语音活动时间
        lastVoiceActivityTime = System.currentTimeMillis()

        // 创建音频录制器
        audioRecorder = AudioRecorder(
            onAudioBufferReady = { data ->
                // 根据当前选择的ASR服务发送音频数据
                when (_currentAsrService.value) {
                    AsrServiceType.GOOGLE_CLOUD -> {
                        // 发送音频数据到Google Cloud服务
                        cloudServiceAdapter.sendAudioData(data)
                    }
                    AsrServiceType.TENCENT_CLOUD -> {
                        // 发送音频数据到腾讯云服务
                        tencentAsrClient?.sendAudioData(data)
                    }
                }
            },
            onError = { error ->
                _errorMessage.value = "录音错误: $error"
                stopMeetingRecording()
            }
        )

        // 根据当前ASR服务类型启动不同的语音识别
        when (_currentAsrService.value) {
            AsrServiceType.GOOGLE_CLOUD -> {
                // 开始Google Cloud会议记录语音识别
                startGoogleCloudRecognition()

                // 开始录音
                startRecording()
            }
            AsrServiceType.TENCENT_CLOUD -> {
                // 开始腾讯云会议记录语音识别
                // 腾讯云需要等待连接建立后再开始录音
                startTencentCloudRecognition {
                    // 回调中开始录音
                    startRecording()

                    // 设置录音状态
                    _isRecording.value = true
                }
            }
        }
    }

    /**
     * 开始录音的实际操作
     */
    private fun startRecording() {
        try {
            audioRecorder?.startRecording()
            _isRecording.value = true
        } catch (e: Exception) {
            Log.e(TAG, "启动会议记录录音失败", e)
            _errorMessage.value = "启动会议记录录音失败: ${e.message}"
            stopMeetingRecording()
        }
    }

    /**
     * 开始Google Cloud语音识别
     */
    private fun startGoogleCloudRecognition() {
        cloudServiceAdapter.startMeetingRecognition(
            languageCode = _currentLanguage.value,
            model = "default",
            callback = object : GoogleCloudServiceAdapter.MeetingRecognitionCallback {
                override fun onConnectionEstablished() {
                    Log.d(TAG, "Google Cloud会议记录语音识别连接已建立")
                }

                // 用于跟踪最后一个最终结果
                private var lastFinalResult = ""

                override fun onIncrementalRecognitionResult(text: String, isFinal: Boolean) {
                    // 更新当前显示的识别结果，用于UI实时显示
                    if (text.isNotEmpty()) {
                        // 更新语音活动时间
                        updateVoiceActivityTime()

                        if (isFinal) {
                            // 这是一个最终结果
                            handleGoogleCloudFinalResult(text)
                        } else {
                            // 这是一个临时结果，用于UI显示
                            _googleCloudTemporaryResult.value = text

                            // 直接更新当前识别结果，确保UI实时显示
                            _currentRecognitionResult.value = text

                            // 记录日志
                            Log.d(TAG, "【谷歌云ASR】更新临时识别结果: ${text.take(20)}${if (text.length > 20) "..." else ""}")
                        }
                    }
                }

                override fun onFinalRecognitionResult(text: String) {
                    if (text.isNotEmpty() && !isLastResultProcessed) {
                        // 更新语音活动时间
                        updateVoiceActivityTime()

                        // 处理最终结果
                        handleGoogleCloudFinalResult(text)

                        // 标记最后结果已处理
                        isLastResultProcessed = true
                    }
                }

                override fun onError(errorMessage: String) {
                    Log.e(TAG, "Google Cloud会议记录语音识别错误: $errorMessage")
                    _errorMessage.value = errorMessage
                }

                override fun onConnectionClosed() {
                    Log.d(TAG, "Google Cloud会议记录语音识别连接已关闭")
                    _isRecording.value = false
                }

                override val lastLanguageCode: String?
                    get() = _currentLanguage.value
            }
        )
    }

    /**
     * 开始腾讯云语音识别
     */
    private fun startTencentCloudRecognition(onConnected: () -> Unit) {
        try {
            // 在启动新会话前先清空当前识别结果和状态
            _currentRecognitionResult.value = ""

            // 获取腾讯云引擎模型类型
            val engineModelType = getTencentEngineModelType(_currentLanguage.value)

            // 确保先结束之前的识别会话（如果有）
            try {
                tencentAsrClient?.endRecognition()
                Log.d(TAG, "结束之前的腾讯云会话（如果存在）")
            } catch (e: Exception) {
                Log.w(TAG, "尝试结束之前的会话时出错，可能没有正在进行的会话", e)
            }

            // 使用非阻塞延迟，确保之前的会话完全清理
            viewModelScope.launch {
                kotlinx.coroutines.delay(300) // 增加延迟时间，确保会话完全清理

                // 在延迟后执行剩余的初始化操作
                try {
                    // 连接腾讯云语音识别服务
                    tencentAsrClient?.connect(engineModelType)
                    Log.d(TAG, "腾讯云会议记录语音识别已启动，引擎模型: $engineModelType")

                    // 更新状态
                    _currentAsrServiceName.value = "腾讯云"

                    // 延迟一小段时间再开始录音，确保连接已完全建立
                    kotlinx.coroutines.delay(200)

                    // 回调
                    onConnected()
                } catch (e: Exception) {
                    Log.e(TAG, "在延迟后启动腾讯云语音识别失败", e)
                    _errorMessage.value = "启动腾讯云语音识别失败: ${e.message}"
                    _isRecording.value = false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动腾讯云会议记录语音识别失败", e)
            _errorMessage.value = "启动腾讯云会议记录语音识别失败: ${e.message}"
            stopMeetingRecording()
        }
    }

    /**
     * 停止会议记录
     */
    fun stopMeetingRecording() {
        if (!_isRecording.value) return

        Log.d(TAG, "停止会议记录，当前ASR服务: ${_currentAsrServiceName.value}")

        // 只有在使用Google Cloud服务时，才需要处理最后的识别结果
        if (_currentAsrService.value == AsrServiceType.GOOGLE_CLOUD) {
            val temporaryResult = _googleCloudTemporaryResult.value
            if (temporaryResult.isNotEmpty()) {
                // 将最后的临时结果作为最终结果处理
                handleGoogleCloudFinalResult(temporaryResult)
                Log.d(TAG, "停止录音时处理最后的临时结果: $temporaryResult")
            }

            // 不清空临时结果和当前识别结果，保留文本内容
            Log.d(TAG, "停止录音，保留当前识别结果")
        }

        // 关闭音频录制器
        audioRecorder?.let {
            try {
                it.stopRecording()
            } catch (e: Exception) {
                Log.e(TAG, "停止录音时发生错误", e)
            }
            audioRecorder = null
        }

        // 关闭ASR服务
        try {
            if (_currentAsrService.value == AsrServiceType.GOOGLE_CLOUD) {
                cloudServiceAdapter.stopMeetingRecognition()
            } else {
                tencentAsrClient?.endRecognition()
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止语音识别服务时发生错误", e)
        }

        // 如果是腾讯云，不再重新初始化，而是保留现有实例
        // 这样可以保留累积文本，确保在停止录音后再次开始录音时能够继续追加文本
        if (_currentAsrService.value == AsrServiceType.TENCENT_CLOUD) {
            Log.d(TAG, "保留腾讯云客户端实例，确保累积文本不丢失")
            // 不再执行 tencentAsrClient = null 和 initializeTencentAsrClient()
        }

        // 更新录音状态
        _isRecording.value = false

        // 只重置识别状态，不清空文本内容
        isLastResultProcessed = false

        // 记录日志
        Log.d(TAG, "停止录音，保留当前文本内容")

        Log.d(TAG, "会议记录停止完成")
    }

    /**
     * 将新的文本添加到会议记录中
     */
    private fun appendToMeetingContent(newText: String) {
        if (newText.isEmpty()) return

        if (_meetingContent.value.isEmpty()) {
            _meetingContent.value = newText
        } else {
            _meetingContent.value += "\n$newText"
        }

        // 保存到文件
        saveCurrentMeetingContent()
    }

    /**
     * 保存当前会议记录到历史
     */
    fun saveCurrentMeetingToHistory(title: String = "") {
        if (_meetingContent.value.isEmpty()) {
            Log.d(TAG, "当前没有会议记录内容，不保存")
            return
        }

        // 计算会议持续时间
        val duration = if (meetingStartTime > 0) {
            System.currentTimeMillis() - meetingStartTime
        } else {
            0
        }

        // 生成默认标题
        val actualTitle = if (title.isEmpty()) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            val dateStr = dateFormat.format(Date())
            "会议记录 $dateStr"
        } else {
            title
        }

        // 保存到历史记录
        val success = textPersistenceManager.saveCurrentMeetingToHistory(actualTitle, duration)

        if (success) {
            Log.d(TAG, "已将当前会议记录保存到历史: $actualTitle")
            // 重新加载会议记录列表
            loadMeetingRecords()

            // 清空当前会议记录
            clearCurrentMeeting()
        } else {
            Log.e(TAG, "保存当前会议记录到历史失败")
            _errorMessage.value = "保存会议记录失败"
        }
    }

    /**
     * 加载会议记录列表
     */
    fun loadMeetingRecords() {
        viewModelScope.launch {
            val records = textPersistenceManager.loadAllMeetingRecords()
            _meetingRecords.value = records
            Log.d(TAG, "已加载${records.size}条会议记录")
        }
    }

    /**
     * 加载特定会议记录
     */
    fun loadMeetingRecord(id: String) {
        Log.d(TAG, "开始加载会议记录，ID: $id")
        viewModelScope.launch {
            try {
                val record = textPersistenceManager.getMeetingRecordById(id)
                if (record != null) {
                    _selectedRecord.value = record

                    // 将记录内容设置为当前会议记录
                    _meetingContent.value = record.content
                    textPersistenceManager.saveText(record.content)

                    Log.d(TAG, "已成功加载会议记录: ${record.title}, ID: ${record.id}")
                } else {
                    Log.e(TAG, "未找到指定ID的会议记录: $id")
                    _errorMessage.value = "未找到会议记录"
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载会议记录时发生异常: ${e.message}", e)
                _errorMessage.value = "加载会议记录失败: ${e.message}"
            }
        }
    }

    /**
     * 删除会议记录
     */
    fun deleteMeetingRecord(id: String) {
        viewModelScope.launch {
            val success = textPersistenceManager.deleteMeetingRecord(id)
            if (success) {
                Log.d(TAG, "已删除会议记录: $id")

                // 重新加载会议记录列表
                loadMeetingRecords()

                // 如果当前选中的是被删除的记录，清空选中
                if (_selectedRecord.value?.id == id) {
                    _selectedRecord.value = null
                }
            } else {
                Log.e(TAG, "删除会议记录失败: $id")
                _errorMessage.value = "删除会议记录失败"
            }
        }
    }

    /**
     * 清空当前会议记录
     */
    fun clearCurrentMeeting() {
        _meetingContent.value = ""
        _currentRecognitionResult.value = ""

        // 清除腾讯云客户端的累积文本
        if (_currentAsrService.value == AsrServiceType.TENCENT_CLOUD) {
            tencentAsrClient?.clearAll()
            Log.d(TAG, "已清除腾讯云客户端的累积文本")
        }

        // 清除谷歌云的累积文本
        _googleCloudAccumulatedText.value = ""
        _googleCloudFinalResults.clear()
        _googleCloudTemporaryResult.value = ""

        // 保存空内容
        saveCurrentMeetingContent()

        Log.d(TAG, "已清空当前会议记录")
    }

    /**
     * 保存当前会议内容到文件
     */
    private fun saveCurrentMeetingContent() {
        viewModelScope.launch {
            textPersistenceManager.saveText(_meetingContent.value)
            Log.d(TAG, "已保存当前会议内容，长度: ${_meetingContent.value.length}字符")
        }
    }

    /**
     * 设置当前会议内容
     * 用于从历史记录加载内容到当前会议
     */
    fun setCurrentMeetingContent(content: String) {
        _meetingContent.value = content
        _currentRecognitionResult.value = ""
        // 保存到本地
        saveCurrentMeetingContent()
        Log.d(TAG, "已加载历史会议内容到当前会议，长度: ${content.length}字符")
    }

    /**
     * 清空所有会议历史记录
     */
    fun clearAllMeetingRecords() {
        viewModelScope.launch {
            val success = textPersistenceManager.clearAllMeetingRecords()
            if (success) {
                // 清空当前记录列表
                _meetingRecords.value = emptyList()
                Log.d(TAG, "已清空所有会议历史记录")
            } else {
                _errorMessage.value = "清空会议历史记录失败"
                Log.e(TAG, "清空所有会议历史记录失败")
            }
        }
    }

    /**
     * 设置当前语言
     */
    fun setLanguage(languageCode: String) {
        // 检查是否与当前语言相同
        if (_currentLanguage.value == languageCode) {
            Log.d(TAG, "无需切换语言，当前已经是: $languageCode")
            return
        }

        Log.d(TAG, "正在切换语言: ${_currentLanguage.value} -> $languageCode")

        // 记录当前会议内容，确保语言切换不会丢失已有内容
        val currentContent = _meetingContent.value

        val wasRecording = _isRecording.value

        // 如果正在录音，先停止
        if (wasRecording) {
            Log.d(TAG, "语言切换: 正在录音中，先停止录音")
            stopMeetingRecording()
        }

        // 获取之前的ASR服务
        val previousAsrService = _currentAsrService.value

        // 设置新语言
        _currentLanguage.value = languageCode

        // 根据语言自动切换ASR服务
        updateAsrServiceByLanguage(languageCode)

        // 如果ASR服务发生变化，需要进行状态清理和会话重置
        if (previousAsrService != _currentAsrService.value) {
            Log.d(TAG, "ASR服务切换: ${previousAsrService} -> ${_currentAsrService.value}")

            // 如果之前使用的是腾讯云，保留累积文本但清理其他状态
            if (previousAsrService == AsrServiceType.TENCENT_CLOUD) {
                // 不再调用 clearAll()，而是使用 clearExceptAccumulatedText()
                tencentAsrClient?.clearExceptAccumulatedText()
                Log.d(TAG, "从腾讯云切换到其它服务，已清理临时状态但保留累积文本")
            }

            // 如果新服务是腾讯云
            if (_currentAsrService.value == AsrServiceType.TENCENT_CLOUD) {
                // 不再调用 clearAll()，而是使用 clearExceptAccumulatedText()
                tencentAsrClient?.clearExceptAccumulatedText()
                Log.d(TAG, "切换到腾讯云，已清理临时状态但保留累积文本")
            }
        }

        // 确保保留原有会议内容
        if (_meetingContent.value != currentContent) {
            Log.d(TAG, "语言切换过程中会议内容被改变，恢复原有内容")
            _meetingContent.value = currentContent
            saveCurrentMeetingContent()
        }

        Log.d(TAG, "设置会议记录语言为: $languageCode, 使用ASR服务: ${_currentAsrServiceName.value}")

        // 如果之前在录音，使用新语言重新开始录音
        if (wasRecording) {
            Log.d(TAG, "语言切换: 之前正在录音，延迟500ms后重新开始")
            // 使用更长的延迟，确保之前的识别会话完全结束
            viewModelScope.launch {
                kotlinx.coroutines.delay(500)
                startMeetingRecording()
            }
        }
    }

    /**
     * 清空错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 切换实时转写预览的显示状态
     * @param show 如果为true，显示实时预览；如果为false，隐藏实时预览
     */
    fun toggleRealtimePreview(show: Boolean? = null) {
        // 如果提供了具体的显示状态，则设置为该状态
        // 否则切换当前状态（显示变隐藏，隐藏变显示）
        _showRealtimePreview.value = show ?: !_showRealtimePreview.value
        Log.d(TAG, "实时转写预览显示状态: ${if (_showRealtimePreview.value) "显示" else "隐藏"}")
    }

    /**
     * 资源清理
     */
    override fun onCleared() {
        super.onCleared()
        stopMeetingRecording()

        // 释放腾讯云ASR客户端
        tencentAsrClient?.release()
    }

    /**
     * 将同声传译的结果追加到会议记录中
     * 新增函数：用于手动将当前识别结果添加到会议记录
     */
    fun appendCurrentRecognitionToMeeting() {
        val currentResult = _currentRecognitionResult.value
        if (currentResult.isNotEmpty()) {
            appendToMeetingContent(currentResult)
            _currentRecognitionResult.value = ""
            Log.d(TAG, "已手动将识别结果添加到会议记录")
        }
    }

    /**
     * 查找两个字符串的最大重叠部分
     */
    private fun findOverlap(end: String, start: String): String {
        // 从最长的可能重叠开始检查
        val maxOverlap = minOf(end.length, start.length)

        for (overlapSize in maxOverlap downTo 1) {
            val endSuffix = end.substring(end.length - overlapSize)
            val startPrefix = start.substring(0, overlapSize)

            if (endSuffix == startPrefix) {
                return endSuffix
            }
        }

        return ""
    }

    /**
     * 提取文本中包含的关键技术术语
     */
    private fun extractTechTerms(text: String): Set<String> {
        // 定义技术术语的正则表达式模式
        val techTermPattern = Regex("(?:HTTP|HTTPS|URL|API|SDK|XML|JSON|GET|POST|PUT|DELETE|REST|SOAP|IP|TCP|UDP|FTP|SSH)[a-zA-Z0-9]*")

        // 查找所有匹配项并返回
        return techTermPattern.findAll(text).map { it.value }.toSet()
    }

    /**
     * 智能合并两段文本内容，避免重复
     * @param original 原始文本
     * @param new 新文本
     * @return 合并后的文本
     */
    private fun intelligentMergeContent(original: String, new: String): String {
        if (original.isEmpty()) return new
        if (new.isEmpty()) return original

        // 提取两个文本中的技术术语
        val originalTerms = extractTechTerms(original)
        val newTerms = extractTechTerms(new)

        // 如果新文本不包含任何技术术语，可能是普通内容，直接使用标准合并
        if (newTerms.isEmpty()) {
            return findBestMerge(original, new)
        }

        // 检查技术术语在两段文本中的分布
        val commonTerms = originalTerms.intersect(newTerms)

        // 如果没有共同的技术术语，可能是不相关内容，使用标准合并
        if (commonTerms.isEmpty()) {
            return findBestMerge(original, new)
        }

        // 比较文本相似度来检测重复
        val similarityScore = calculateSimilarityScore(original, new)
        if (similarityScore > 0.7) { // 高相似度，可能是重复内容
            return if (new.length > original.length) new else original
        }

        // 查找最佳合并点
        return findBestMerge(original, new)
    }

    /**
     * 检测是否已经达到沉默阈值
     * @return 如果距离上次语音活动超过阈值，返回true
     */
    private fun isSilenceThresholdReached(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastActivity = currentTime - lastVoiceActivityTime

        // 检查是否超过沉默阈值
        val isThresholdReached = timeSinceLastActivity >= SILENCE_THRESHOLD

        if (isThresholdReached) {
            Log.d(TAG, "检测到沉默超过阈值: ${timeSinceLastActivity}ms，触发添加文本")
        }

        return isThresholdReached
    }

    /**
     * 更新最后一次语音活动时间
     */
    private fun updateVoiceActivityTime() {
        lastVoiceActivityTime = System.currentTimeMillis()
    }

    /**
     * 评估文本质量，决定是否值得添加到会议内容中
     * @param text 要评估的文本
     * @return 如果文本质量足够高，返回true
     */
    private fun isTextQualityAcceptable(text: String): Boolean {
        // 过滤掉太短的文本
        if (text.length < 5) {
            Log.d(TAG, "文本太短，质量不足: '$text'")
            return false
        }

        // 过滤只包含标点或空白的文本
        if (text.trim().replace(Regex("[\\p{P}\\s]"), "").isEmpty()) {
            Log.d(TAG, "文本只包含标点或空白，质量不足: '$text'")
            return false
        }

        // 检测可能的重复片段
        val words = text.split(Regex("\\s+"))
        val uniqueWords = words.toSet()

        // 如果重复单词过多，质量可能不好
        if (words.size > 4 && uniqueWords.size < words.size * 0.5) {
            Log.d(TAG, "文本包含大量重复单词，质量不足: '$text'")
            return false
        }

        // 检查是否包含有意义的内容词（排除语气词、助词等）
        val contentWords = words.filter { word ->
            word.length > 1 && !FILLER_WORDS.contains(word.lowercase())
        }

        if (contentWords.isEmpty() && words.size > 3) {
            Log.d(TAG, "文本不包含有意义的内容词，质量不足: '$text'")
            return false
        }

        return true
    }

    /**
     * 计算两段文本的相似度分数
     * @param text1 第一段文本
     * @param text2 第二段文本
     * @return 相似度分数(0-1)，1为完全相同
     */
    private fun calculateSimilarityScore(text1: String, text2: String): Double {
        // 如果有一个文本为空，返回0
        if (text1.isEmpty() || text2.isEmpty()) return 0.0

        // 如果两个文本完全一样，返回1
        if (text1 == text2) return 1.0

        // 分词
        val words1 = text1.split(Regex("\\s+|[,.。，！？!?;；]"))
            .filter { it.isNotEmpty() }
        val words2 = text2.split(Regex("\\s+|[,.。，！？!?;；]"))
            .filter { it.isNotEmpty() }

        // 如果分词后为空，返回0
        if (words1.isEmpty() || words2.isEmpty()) return 0.0

        // 计算共同的单词数量
        val commonWords = words1.toSet().intersect(words2.toSet()).size

        // 计算相似度
        return commonWords.toDouble() / (words1.size + words2.size - commonWords)
    }

    /**
     * 寻找两段文本的最佳合并方案
     * @param text1 第一段文本
     * @param text2 第二段文本
     * @return 合并后的文本
     */
    private fun findBestMerge(text1: String, text2: String): String {
        // 查找重叠部分
        val overlap = findOverlap(text1, text2)

        // 如果有显著重叠(超过5个字符)，只添加非重叠部分
        if (overlap.length > 5) {
            val nonOverlapPart = text2.substring(overlap.length)
            if (nonOverlapPart.isNotEmpty()) {
                // 根据句子结束符选择合适的分隔符
                val separator = if (text1.endsWith(".") ||
                                    text1.endsWith("。") ||
                                    text1.endsWith("!") ||
                                    text1.endsWith("！") ||
                                    text1.endsWith("?") ||
                                    text1.endsWith("？")) {
                    "\n" // 句子结束，添加换行
                } else {
                    " " // 否则添加空格
                }

                return text1 + separator + nonOverlapPart
            }
            return text1
        }

        // 没有显著重叠，使用分隔符连接
        val separator = if (text1.endsWith(".") ||
                            text1.endsWith("。") ||
                            text1.endsWith("!") ||
                            text1.endsWith("！") ||
                            text1.endsWith("?") ||
                            text1.endsWith("？")) {
            "\n" // 句子结束，添加换行
        } else {
            " " // 否则添加空格
        }

        return text1 + separator + text2
    }

    /**
     * 判断新文本是否是已有会议内容的重复
     * @param newText 新文本
     * @param existingContent 已有内容
     * @return 是否重复
     */
    private fun isDuplicateText(newText: String, existingContent: String): Boolean {
        if (newText.isEmpty()) return true

        // 完全相同
        if (newText == existingContent) {
            return true
        }

        // 已有内容完全包含新文本
        if (existingContent.contains(newText)) {
            return true
        }

        // 检查技术术语在两段文本中的分布
        val existingTerms = extractTechTerms(existingContent)
        val newTerms = extractTechTerms(newText)

        if (newTerms.isNotEmpty() && existingTerms.isNotEmpty()) {
            val commonTerms = newTerms.intersect(existingTerms)

            // 如果共同术语数量足够多，并且占比超过70%，认为是重复
            if (newTerms.size > 1 &&
                commonTerms.size >= 2 &&
                commonTerms.size.toFloat() / newTerms.size > 0.7f) {

                Log.d(TAG, "检测到技术术语大量重复，认为是重复内容")
                return true
            }
        }

        // 计算文本相似度
        val similarity = calculateSimilarityScore(newText, existingContent)
        if (similarity > 0.7) {
            Log.d(TAG, "文本相似度高(${similarity})，认为是重复内容")
            return true
        }

        return false
    }

    /**
     * 智能追加文本到会议内容
     * @param newText 要追加的新文本
     */
    private fun intelligentlyAppendToMeetingContent(newText: String) {
        if (newText.isEmpty()) return

        val currentContent = _meetingContent.value

        // 如果当前会议内容为空，直接设置
        if (currentContent.isEmpty()) {
            _meetingContent.value = newText
            saveCurrentMeetingContent()
            return
        }

        // 检查新文本是否已经包含在累积文本中
        if (currentContent.contains(newText)) {
            Log.d(TAG, "新文本已包含在累积文本中，跳过添加")
            return
        }

        // 检查是否累积文本是新文本的一部分
        if (newText.contains(currentContent) && currentContent.length > 10) {
            // 只有当新文本显著大于累积文本时才替换
            if (newText.length > currentContent.length * 1.2) {
                Log.d(TAG, "累积文本是新文本的子串，使用更完整的新文本")
                _meetingContent.value = newText
                saveCurrentMeetingContent()
                return
            }
        }

        // 使用智能合并处理新文本
        val mergedContent = intelligentMergeContent(currentContent, newText)

        // 确保合并后的内容比原内容长
        if (mergedContent.length > currentContent.length) {
            _meetingContent.value = mergedContent
            saveCurrentMeetingContent()
        } else {
            Log.d(TAG, "合并后内容无变化，跳过添加")
        }
    }

    /**
     * 简单追加文本到会议内容，不进行复杂的文本叠加合并
     * @param newText 要追加的新文本
     */
    private fun simpleAppendToMeetingContent(newText: String) {
        if (newText.isEmpty()) return

        val currentContent = _meetingContent.value

        // 如果当前会议内容为空，直接设置
        if (currentContent.isEmpty()) {
            _meetingContent.value = newText
            saveCurrentMeetingContent()
            return
        }

        // 避免重复添加完全相同的文本
        if (currentContent == newText) {
            return
        }

        // 简单地添加分隔符和新文本
        val separator = getAppropriateTextSeparator(currentContent)
        _meetingContent.value = currentContent + separator + newText
        saveCurrentMeetingContent()
        Log.d(TAG, "【谷歌云ASR】简单追加文本: $newText")
    }

    /**
     * 处理谷歌云的最终识别结果
     * 采用全新的方法，避免重复问题，优化增量显示
     * @param text 最终识别的文本
     */
    private fun handleGoogleCloudFinalResult(text: String) {
        if (text.isEmpty()) return

        // 清理文本，去除可能的重复
        val cleanedText = cleanGoogleCloudText(text)
        if (cleanedText.isEmpty()) return

        // 检查是否与上一个最终结果相同或高度相似
        if (lastFinalResult.isNotEmpty()) {
            val similarity = calculateSimilarityScore(cleanedText, lastFinalResult)
            if (similarity > 0.9) { // 提高相似度阈值，只有几乎完全相同才跳过
                Log.d(TAG, "【谷歌云ASR】最终结果与上一个结果几乎相同，跳过: $cleanedText")
                return
            }
        }

        // 检查是否是现有文本的子集
        val currentContent = _meetingContent.value
        if (currentContent.isNotEmpty() && currentContent.contains(cleanedText) &&
            cleanedText.length < currentContent.length * 0.8) {
            Log.d(TAG, "【谷歌云ASR】最终结果是现有文本的子集，跳过: $cleanedText")
            return
        }

        // 检查是否现有文本是新文本的子集
        if (currentContent.isNotEmpty() && cleanedText.contains(currentContent) &&
            cleanedText.length > currentContent.length * 1.2) {
            // 新文本包含了现有文本，且明显更长，直接替换
            _meetingContent.value = cleanedText
            _googleCloudAccumulatedText.value = cleanedText
            lastFinalResult = cleanedText

            // 更新当前识别结果，显示最终结果
            _currentRecognitionResult.value = cleanedText
            _currentFinalRecognitionResult.value = cleanedText

            // 保存会议内容
            saveCurrentMeetingContent()

            Log.d(TAG, "【谷歌云ASR】新文本包含现有文本且更长，直接替换: $cleanedText")
            return
        }

        // 更新最后一个最终结果
        lastFinalResult = cleanedText

        // 添加到最终结果列表
        _googleCloudFinalResults.add(cleanedText)

        // 清空临时结果
        _googleCloudTemporaryResult.value = ""

        // 更新累积文本
        updateGoogleCloudAccumulatedText()

        // 更新当前识别结果，显示最终结果
        _currentRecognitionResult.value = cleanedText
        _currentFinalRecognitionResult.value = cleanedText

        Log.d(TAG, "【谷歌云ASR】添加新的最终结果: $cleanedText")
    }

    /**
     * 更新谷歌云累积文本
     * 基于最终结果列表重建累积文本，优化增量显示
     */
    private fun updateGoogleCloudAccumulatedText() {
        if (_googleCloudFinalResults.isEmpty()) return

        // 使用最终结果列表构建累积文本
        val newAccumulatedText = buildGoogleCloudAccumulatedText()

        // 检查新累积文本是否比当前会议内容短
        val currentContent = _meetingContent.value
        if (currentContent.isNotEmpty() && newAccumulatedText.length < currentContent.length * 0.9) {
            // 新累积文本明显比当前内容短，可能是丢失了一些内容
            // 检查当前内容是否包含新累积文本
            if (currentContent.contains(newAccumulatedText)) {
                // 当前内容包含新累积文本，保留当前内容
                Log.d(TAG, "【谷歌云ASR】新累积文本比当前内容短且被包含，保留当前内容")
                _googleCloudAccumulatedText.value = currentContent
                return
            }
        }

        // 更新累积文本
        _googleCloudAccumulatedText.value = newAccumulatedText

        // 更新会议内容
        _meetingContent.value = newAccumulatedText

        // 保存会议内容
        saveCurrentMeetingContent()

        Log.d(TAG, "【谷歌云ASR】更新累积文本，长度: ${newAccumulatedText.length}")
    }

    /**
     * 构建谷歌云累积文本
     * 基于最终结果列表，智能合并相邻结果，优化增量显示
     */
    private fun buildGoogleCloudAccumulatedText(): String {
        if (_googleCloudFinalResults.isEmpty()) return ""

        // 如果只有一个结果，直接返回
        if (_googleCloudFinalResults.size == 1) {
            return _googleCloudFinalResults[0]
        }

        // 首先检查是否有一个结果包含了其他所有结果
        // 这种情况下直接使用最长的结果
        var longestResult = ""
        var longestResultIndex = -1

        // 找出最长的结果
        for (i in _googleCloudFinalResults.indices) {
            val result = _googleCloudFinalResults[i]
            if (result.length > longestResult.length) {
                longestResult = result
                longestResultIndex = i
            }
        }

        // 检查最长的结果是否包含了其他所有结果
        var containsAll = true
        for (i in _googleCloudFinalResults.indices) {
            if (i != longestResultIndex) {
                val result = _googleCloudFinalResults[i]
                // 如果结果长度超过5个字符且不被最长结果包含
                if (result.length > 5 && !longestResult.contains(result)) {
                    containsAll = false
                    break
                }
            }
        }

        // 如果最长结果包含了所有其他结果，直接返回最长结果
        if (containsAll && longestResult.isNotEmpty()) {
            Log.d(TAG, "【谷歌云ASR】最长结果包含所有其他结果，直接使用最长结果")
            return longestResult
        }

        // 构建累积文本，智能合并相邻结果
        val builder = StringBuilder(_googleCloudFinalResults[0])

        for (i in 1 until _googleCloudFinalResults.size) {
            val currentResult = _googleCloudFinalResults[i]
            val previousResult = _googleCloudFinalResults[i-1]

            // 如果当前结果是前一个结果的子串，跳过
            if (previousResult.contains(currentResult)) {
                continue
            }

            // 如果前一个结果是当前结果的子串，用当前结果替换前一个结果
            if (currentResult.contains(previousResult) && i == _googleCloudFinalResults.size - 1) {
                // 只在最后一个结果时执行替换，避免中间结果替换导致丢失内容
                return currentResult
            }

            // 检查是否有重叠
            val overlap = findOverlap(previousResult, currentResult)

            if (overlap.length > 5) {
                // 有显著重叠，只添加非重叠部分
                val nonOverlappingPart = currentResult.substring(overlap.length)
                if (nonOverlappingPart.isNotEmpty()) {
                    builder.append(nonOverlappingPart)
                }
            } else {
                // 没有显著重叠，添加适当的分隔符
                val separator = getAppropriateTextSeparator(builder.toString())
                builder.append(separator).append(currentResult)
            }
        }

        return builder.toString()
    }

    /**
     * 更新谷歌云显示文本
     * 组合最终结果和当前临时结果，用于UI显示
     * 注意：此方法不再直接设置_currentRecognitionResult，而是返回组合后的文本
     * 这样可以避免覆盖onIncrementalRecognitionResult中设置的实时结果
     */
    private fun updateGoogleCloudDisplayText() {
        val accumulatedText = _googleCloudAccumulatedText.value
        val temporaryResult = _googleCloudTemporaryResult.value
        var displayText = ""

        // 如果没有任何文本，直接返回
        if (accumulatedText.isEmpty() && temporaryResult.isEmpty()) {
            return
        }

        // 如果只有临时结果，直接使用临时结果
        if (accumulatedText.isEmpty()) {
            // 不需要更新_currentRecognitionResult，因为onIncrementalRecognitionResult已经设置了
            return
        }

        // 如果只有累积文本，没有临时结果，使用累积文本
        if (temporaryResult.isEmpty()) {
            _meetingContent.value = accumulatedText
            return
        }

        // 检查临时结果是否与累积文本末尾有重叠
        val overlap = findOverlap(accumulatedText, temporaryResult)

        if (overlap.length > 5) {
            // 有显著重叠，只显示累积文本加上临时结果的非重叠部分
            val nonOverlappingPart = temporaryResult.substring(overlap.length)
            if (nonOverlappingPart.isNotEmpty()) {
                displayText = accumulatedText + nonOverlappingPart
            } else {
                displayText = accumulatedText
            }
        } else {
            // 没有显著重叠，添加适当的分隔符
            val separator = getAppropriateTextSeparator(accumulatedText)
            displayText = accumulatedText + separator + temporaryResult
        }

        // 更新会议内容，但不覆盖当前识别结果
        if (displayText.isNotEmpty()) {
            _meetingContent.value = displayText
        }

        Log.d(TAG, "【谷歌云ASR】更新显示文本: ${displayText.take(30)}${if (displayText.length > 30) "..." else ""}")
    }

    /**
     * 清理谷歌云文本
     * 去除重复内容和无意义内容
     */
    private fun cleanGoogleCloudText(text: String): String {
        if (text.isEmpty()) return ""

        // 检查文本质量
        if (!isTextQualityAcceptable(text)) {
            Log.d(TAG, "【谷歌云ASR】文本质量不合格，跳过: $text")
            return ""
        }

        // 检测并清理内部重复
        val repeatedPhrase = detectRepeatedPhrases(text)
        if (repeatedPhrase != null && repeatedPhrase.length > 5) {
            Log.d(TAG, "【谷歌云ASR】检测到重复短语: $repeatedPhrase")

            // 找到第一次出现的位置
            val firstOccurrence = text.indexOf(repeatedPhrase)
            if (firstOccurrence >= 0) {
                // 找到第二次出现的位置
                val secondOccurrence = text.indexOf(repeatedPhrase, firstOccurrence + repeatedPhrase.length)
                if (secondOccurrence >= 0) {
                    // 只保留第一次出现之前的内容和第一次出现的内容
                    val cleanedText = text.substring(0, secondOccurrence)
                    Log.d(TAG, "【谷歌云ASR】清理后的文本: $cleanedText")
                    return cleanedText
                }
            }
        }

        return text
    }

    /**
     * 检查谷歌云文本是否重复
     * 参考腾讯云的重复检测逻辑，并增强针对连续重复模式的检测
     */
    private fun isGoogleCloudDuplicateText(text: String, accumulatedText: String): Boolean {
        if (text.isEmpty()) return true

        // 检查完全包含
        if (accumulatedText.contains(text)) {
            Log.d(TAG, "【谷歌云ASR】累积文本已完全包含新文本")
            return true
        }

        // 检查文本是否是累积文本的子串
        if (text.length < accumulatedText.length * 0.3 &&
            accumulatedText.contains(text)) {
            Log.d(TAG, "【谷歌云ASR】新文本是累积文本的小片段")
            return true
        }

        // 检测连续重复模式 - 针对截图中的情况
        val repeatedPhrasePattern = detectRepeatedPhrases(text)
        if (repeatedPhrasePattern != null) {
            Log.d(TAG, "【谷歌云ASR】检测到文本内部重复模式: $repeatedPhrasePattern")
            return true
        }

        // 检测文本是否包含在累积文本的末尾部分
        val lastPartOfAccumulated = if (accumulatedText.length > 100) {
            accumulatedText.substring(accumulatedText.length - 100)
        } else {
            accumulatedText
        }

        // 检查新文本是否与累积文本末尾有大量重叠
        val overlap = findLargestCommonSubstring(lastPartOfAccumulated, text)
        if (overlap.length > text.length * 0.7) {
            Log.d(TAG, "【谷歌云ASR】新文本与累积文本末尾有大量重叠: ${overlap.length}/${text.length}")
            return true
        }

        // 检查技术术语在两段文本中的分布
        val accTerms = extractTechTerms(accumulatedText)
        val newTerms = extractTechTerms(text)

        // 如果新文本中的技术术语全部存在于累积文本中，且数量足够多，认为是重复
        if (newTerms.size >= 2 &&
            accTerms.containsAll(newTerms)) {
            Log.d(TAG, "【谷歌云ASR】新文本中的技术术语全部存在于累积文本中")
            return true
        }

        // 计算相似度
        val similarity = calculateSimilarityScore(text, accumulatedText)
        if (similarity > 0.7) { // 降低阈值，更严格地过滤
            Log.d(TAG, "【谷歌云ASR】文本相似度高(${similarity})，认为是重复")
            return true
        }

        // 检测快速重复的语音模式
        val words = text.split(Regex("\\s+|，|。|？|！|,|\\.|\\?|!|;|；"))
            .filter { it.isNotEmpty() }
        if (words.size > 3 && detectRepeatedWordGroups(words)) {
            Log.d(TAG, "【谷歌云ASR】检测到快速语音重复模式")
            return true
        }

        // 检测句子内部的重复模式
        if (detectInternalRepetition(text)) {
            Log.d(TAG, "【谷歌云ASR】检测到句子内部重复")
            return true
        }

        return false
    }

    /**
     * 检测文本中的重复短语模式
     * 特别针对"比上年检查及网络活动通过那边Android"这种连续重复的情况
     */
    private fun detectRepeatedPhrases(text: String): String? {
        if (text.length < 20) return null

        // 查找长度至少为5的重复短语
        for (phraseLength in 10 downTo 5) {
            if (text.length < phraseLength * 2) continue

            for (i in 0..text.length - phraseLength * 2) {
                val phrase = text.substring(i, i + phraseLength)

                // 如果短语太短或只包含常见语气词，跳过
                if (phrase.length < 5 || FILLER_WORDS.contains(phrase)) continue

                // 计算这个短语在文本中出现的次数
                var count = 0
                var lastIndex = -1
                while (true) {
                    lastIndex = text.indexOf(phrase, lastIndex + 1)
                    if (lastIndex == -1) break
                    count++
                }

                // 如果短语出现3次以上，认为是重复模式
                if (count >= 3) {
                    return phrase
                }
            }
        }

        return null
    }

    /**
     * 查找两个字符串中的最长公共子串
     */
    private fun findLargestCommonSubstring(str1: String, str2: String): String {
        if (str1.isEmpty() || str2.isEmpty()) return ""

        val dp = Array(str1.length + 1) { IntArray(str2.length + 1) }
        var maxLength = 0
        var endIndex = 0

        for (i in 1..str1.length) {
            for (j in 1..str2.length) {
                if (str1[i-1] == str2[j-1]) {
                    dp[i][j] = dp[i-1][j-1] + 1
                    if (dp[i][j] > maxLength) {
                        maxLength = dp[i][j]
                        endIndex = i
                    }
                }
            }
        }

        return if (maxLength > 0) {
            str1.substring(endIndex - maxLength, endIndex)
        } else {
            ""
        }
    }

    /**
     * 检测句子内部的重复
     * 例如："比上年检查及网络活动通过，比上年检查及网络活动通过"
     */
    private fun detectInternalRepetition(text: String): Boolean {
        if (text.length < 10) return false

        // 将文本分割成短语
        val phrases = text.split(Regex("[，。！？,.!?;；]"))
            .filter { it.length > 5 } // 只考虑长度大于5的短语

        if (phrases.size < 2) return false

        // 检查相邻短语是否高度相似
        for (i in 0 until phrases.size - 1) {
            val similarity = calculateSimilarityScore(phrases[i], phrases[i+1])
            if (similarity > 0.7) {
                return true
            }
        }

        // 检查文本中是否有连续重复的字符模式
        val chars = text.toCharArray()
        var repeatedCount = 1
        var maxRepeatedCount = 1

        for (i in 1 until chars.size) {
            if (chars[i] == chars[i-1]) {
                repeatedCount++
                maxRepeatedCount = maxOf(maxRepeatedCount, repeatedCount)
            } else {
                repeatedCount = 1
            }
        }

        // 如果有连续5个以上相同字符，可能是重复
        if (maxRepeatedCount >= 5) {
            return true
        }

        return false
    }

    /**
     * 提取文本中的句子
     */
    private fun extractSentences(text: String): List<String> {
        // 使用常见的句子结束符分割文本
        val sentenceDelimiters = Regex("[。.！!？?；;]")
        val sentences = text.split(sentenceDelimiters).filter { it.isNotEmpty() }

        // 如果没有找到句子（没有分隔符），则将整个文本作为一个句子
        return if (sentences.isEmpty()) listOf(text) else sentences
    }

    /**
     * 检查句子是否已存在于文本中
     */
    private fun sentenceExistsInText(text: String, sentence: String): Boolean {
        // 直接包含
        if (text.contains(sentence)) {
            return true
        }

        // 计算相似度
        val textSentences = extractSentences(text)
        for (existingSentence in textSentences) {
            val similarity = calculateSimilarityScore(existingSentence, sentence)
            if (similarity > 0.7) {
                return true
            }
        }

        return false
    }

    /**
     * 查找文本1结尾和文本2开头的最大重叠长度
     */
    private fun findLargestOverlap(text1: String, text2: String): Int {
        val minLength = minOf(text1.length, text2.length)

        // 从最大可能的重叠开始检查
        for (overlapSize in minLength downTo 3) { // 至少3个字符才算重叠
            val suffix = text1.substring(text1.length - overlapSize)
            val prefix = text2.substring(0, overlapSize)

            if (suffix == prefix) {
                return overlapSize
            }
        }

        return 0
    }

    /**
     * 检测文本中是否存在重复的词组模式
     * 用于识别快速语音中的重复
     */
    private fun detectRepeatedWordGroups(words: List<String>): Boolean {
        if (words.size < 4) return false

        // 检查连续重复的单词
        for (i in 0 until words.size - 1) {
            if (words[i] == words[i + 1] && words[i].length > 1) {
                return true
            }
        }

        // 检查重复的词组模式
        for (groupSize in 2..3) { // 检查2-3个词的组合
            if (words.size < groupSize * 2) continue

            for (i in 0 until words.size - groupSize * 2 + 1) {
                val group1 = words.subList(i, i + groupSize)
                val group2 = words.subList(i + groupSize, i + groupSize * 2)

                if (group1 == group2) {
                    return true
                }
            }
        }

        return false
    }

    /**
     * 找出两段文本中的非重叠部分
     * 特别针对技术术语的情况，避免重复
     */
    private fun findNonOverlappingText(original: String, newText: String): String {
        // 如果新文本包含在原文中，返回空
        if (original.contains(newText)) return ""

        // 将文本分割成单词进行比较
        val originalWords = original.split(Regex("\\s+|[,.。，！？!?;；]"))
                           .filter { it.isNotEmpty() }
        val newWords = newText.split(Regex("\\s+|[,.。，！？!?;；]"))
                      .filter { it.isNotEmpty() }

        // 找到新文本中不在原文中的词
        val uniqueWords = newWords.filter { newWord ->
            !originalWords.any { it == newWord }
        }

        // 如果找不到独特的词，尝试提取新文本的后半部分
        if (uniqueWords.isEmpty() && newText.length > original.length) {
            // 尝试找出新文本中可能的新增部分
            var startIndex = 0
            while (startIndex < newText.length) {
                val substring = newText.substring(startIndex)
                if (!original.contains(substring) && substring.length > 5) {
                    return substring
                }
                startIndex++
            }
        }

        // 如果找到了独特的词，构建包含这些词的文本片段
        if (uniqueWords.isNotEmpty()) {
            // 尝试从新文本中提取包含这些独特词的完整句子或短语
            val startIndex = newText.indexOf(uniqueWords.first())
            if (startIndex >= 0) {
                return newText.substring(startIndex)
            }
        }

        return ""
    }

    /**
     * 根据文本内容获取适当的分隔符
     * 根据句子结束符选择换行或空格
     */
    private fun getAppropriateTextSeparator(text: String): String {
        return if (text.endsWith(".") ||
                   text.endsWith("。") ||
                   text.endsWith("!") ||
                   text.endsWith("！") ||
                   text.endsWith("?") ||
                   text.endsWith("？")) {
            "\n" // 句子结束，添加换行
        } else {
            " " // 否则添加空格
        }
    }

    /**
     * 简单追加谷歌云文本到会议内容，专门处理沉默检测场景
     * @param text 要追加的文本
     */
    private fun simpleAppendGoogleText(text: String) {
        if (text.isEmpty()) return

        val currentContent = _meetingContent.value

        // 如果当前会议内容为空，直接设置
        if (currentContent.isEmpty()) {
            _meetingContent.value = text
            _googleCloudAccumulatedText.value = text
            saveCurrentMeetingContent()
            Log.d(TAG, "【谷歌云ASR】沉默检测：设置初始文本: $text")
            return
        }

        // 避免重复添加完全相同的文本
        if (currentContent == text) {
            return
        }

        // 文本可能是最终结果，直接处理为最终结果
        handleGoogleCloudFinalResult(text)
        Log.d(TAG, "【谷歌云ASR】沉默检测：追加文本: $text")
    }
}