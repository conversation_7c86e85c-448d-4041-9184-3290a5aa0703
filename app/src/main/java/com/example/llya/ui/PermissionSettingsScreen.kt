package com.example.llya.ui

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale

data class PermissionItem(
    val permission: String,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val isRequired: Boolean = true
)

@OptIn(ExperimentalPermissionsApi::class, ExperimentalMaterial3Api::class)
@Composable
fun PermissionSettingsScreen(
    navController: NavController? = null
) {
    val context = LocalContext.current
    
    val backgroundColor = Color(0xFF1E1D2B)
    val cardBackgroundColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    
    // 定义应用需要的权限列表
    val permissionsList = listOf(
        PermissionItem(
            permission = Manifest.permission.RECORD_AUDIO,
            title = "麦克风权限",
            description = "用于语音录入和实时语音翻译功能，是应用核心功能所必需的",
            icon = Icons.Default.Mic,
            isRequired = true
        ),
        PermissionItem(
            permission = Manifest.permission.BLUETOOTH,
            title = "蓝牙权限",
            description = "用于连接和管理蓝牙耳机设备，实现音频输入输出功能",
            icon = Icons.Default.Bluetooth,
            isRequired = true
        ),
        PermissionItem(
            permission = Manifest.permission.ACCESS_FINE_LOCATION,
            title = "位置权限",
            description = "在Android上扫描蓝牙设备需要位置权限，用于发现附近的蓝牙耳机设备",
            icon = Icons.Default.LocationOn,
            isRequired = true
        ),
        PermissionItem(
            permission = Manifest.permission.READ_EXTERNAL_STORAGE,
            title = "存储权限",
            description = "用于保存翻译结果和会议记录到本地设备",
            icon = Icons.Default.Storage,
            isRequired = false
        )
    )
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { 
                Text(
                    text = "权限设置",
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor,
                titleContentColor = Color.White
            )
        )
        
        // 权限说明卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = cardBackgroundColor
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "权限说明",
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "LLYA需要以下权限才能正常工作。请确保授予必要的权限，以便应用能够提供完整的功能体验。",
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 14.sp
                )
            }
        }
        
        // 权限列表
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp)
        ) {
            items(permissionsList) { permissionItem ->
                PermissionItemCard(
                    permissionItem = permissionItem,
                    purpleColor = purpleColor,
                    cardBackgroundColor = cardBackgroundColor,
                    onPermissionClick = {
                        // 打开应用设置页面
                        openAppSettings(context)
                    }
                )
                
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
        
        // 底部信息
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = cardBackgroundColor
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "如何管理权限?",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "您可以随时通过系统设置来管理应用权限。点击下方按钮前往设置页面修改权限。",
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 14.sp
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = { openAppSettings(context) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = purpleColor
                    ),
                    shape = RoundedCornerShape(25.dp)
                ) {
                    Text(
                        text = "前往系统设置",
                        color = Color.White,
                        fontSize = 16.sp
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionItemCard(
    permissionItem: PermissionItem,
    purpleColor: Color,
    cardBackgroundColor: Color,
    onPermissionClick: () -> Unit
) {
    val permissionState = rememberPermissionState(permissionItem.permission)
    val isGranted = permissionState.status.isGranted
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onPermissionClick() },
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 权限图标
            Icon(
                imageVector = permissionItem.icon,
                contentDescription = permissionItem.title,
                tint = if (isGranted) purpleColor else Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 权限信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = permissionItem.title + if (permissionItem.isRequired) " (必需)" else " (可选)",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = permissionItem.description,
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 12.sp
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 权限状态
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = if (isGranted) "已授权" else "未授权",
                    color = if (isGranted) purpleColor else Color.Red.copy(alpha = 0.7f),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                
                if (!isGranted && permissionItem.isRequired) {
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "点击授权",
                        color = purpleColor,
                        fontSize = 12.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 前往箭头
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "前往设置",
                tint = Color.White.copy(alpha = 0.5f),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

// 打开应用系统设置页面
private fun openAppSettings(context: Context) {
    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
        data = Uri.fromParts("package", context.packageName, null)
    }
    context.startActivity(intent)
} 