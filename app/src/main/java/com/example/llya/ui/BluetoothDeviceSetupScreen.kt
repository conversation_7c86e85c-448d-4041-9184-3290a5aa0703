package com.example.llya.ui

import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.bluetooth.BluetoothViewModel
import com.example.llya.utils.BluetoothPermissionFix
import com.example.llya.utils.BluetoothPermissionHelper

/**
 * 蓝牙设备设置屏幕
 * 用于初始化和配置蓝牙设备
 */
@Composable
fun BluetoothDeviceSetupScreen(
    onNavigateBack: () -> Unit = {},
    viewModel: BluetoothViewModel = viewModel()
) {
    val context = LocalContext.current
    
    // 状态
    var showPermissionRequest by remember { mutableStateOf(false) }
    
    // 在初始化或加载页面时增加权限检查
    LaunchedEffect(Unit) {
        // 先检查蓝牙权限
        val hasPermissions = BluetoothPermissionHelper.hasRequiredPermissions(context)
        if (!hasPermissions) {
            showPermissionRequest = true
        } else {
            // 有权限，开始检测蓝牙设备
            viewModel.refreshOnResume()
        }
    }
    
    // 添加权限请求对话框
    if (showPermissionRequest) {
        BluetoothPermissionHelper.RequestBluetoothPermissions { granted ->
            showPermissionRequest = false
            if (granted) {
                // 权限已授予，刷新蓝牙设备状态
                viewModel.refreshOnResume()
            } else {
                // 权限被拒绝，显示提示
                Toast.makeText(
                    context,
                    "需要蓝牙权限才能连接设备，请在设置中手动开启",
                    Toast.LENGTH_LONG
                ).show()
                
                // 三秒后自动打开系统设置
                viewModel.openSystemBluetoothSettings()
            }
        }
    }
    
    // UI界面
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1E1D2B))
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "蓝牙设备设置",
            color = Color.White,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = {
                BluetoothPermissionFix.showBluetoothPermissionSetting(context)
            },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF8364FD)
            )
        ) {
            Text("打开蓝牙设置")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = {
                showPermissionRequest = true
            },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF475896)
            )
        ) {
            Text("请求蓝牙权限")
        }
    }
} 