package com.example.llya.asr

import android.util.Log
import com.example.llya.utils.SignatureUtil
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import okhttp3.*
import okio.ByteString
import okio.ByteString.Companion.toByteString
import java.util.concurrent.TimeUnit

class TencentAsrClient(
    private val appId: String,
    private val secretId: String,
    private val secretKey: String,
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "TencentAsrClient"
        private const val BASE_URL = "asr.cloud.tencent.com/asr/v2"
        private const val ASR_URL = "wss://$BASE_URL"
    }

    private val _recognitionResult = MutableStateFlow<String>("")
    val recognitionResult: StateFlow<String> = _recognitionResult
    
    // 添加句子是否完成的标志
    private val _isFinalResult = MutableStateFlow<Boolean>(false)
    val isFinalResult: StateFlow<Boolean> = _isFinalResult
    
    // 存储当前语句的识别结果
    private var currentSentence: String = ""
    
    // 存储累积的所有语句
    private val _accumulatedText = MutableStateFlow<String>("")
    val accumulatedText: StateFlow<String> = _accumulatedText

    // 保存最后一次有效结果
    private var lastValidResult: String = ""

    // 记录已经通过slice_type=2处理过的文本，避免重复
    private var hasProcessedSliceType2 = false
    private var lastProcessedText = ""

    // 存储最近一次处理的文本及其时间戳，用于去重
    private var recentlyProcessedTexts = mutableListOf<String>()
    private val maxRecentTexts = 10  // 保留最近10条文本记录

    private var webSocket: WebSocket? = null
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()

    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()
    
    private val asrResponseAdapter: JsonAdapter<AsrResponse> = moshi.adapter(AsrResponse::class.java)
    private val endSignalAdapter: JsonAdapter<EndSignal> = moshi.adapter(EndSignal::class.java)

    // 连接WebSocket
    fun connect(engineModelType: String = "16k_zh") {
        coroutineScope.launch(Dispatchers.IO) {
            try {
                // 重置当前句子
                currentSentence = ""
                
                // 会议记录模式，不清空累积文本，保留之前的内容
                // _accumulatedText.value = ""
                
                // 生成参数和签名
                val voiceId = SignatureUtil.generateVoiceId()
                val timestamp = (System.currentTimeMillis() / 1000).toString()
                val expireTime = (System.currentTimeMillis() / 1000 + 86400).toString()
                val nonce = SignatureUtil.generateNonce()
                
                val params = mutableMapOf(
                    "engine_model_type" to engineModelType,
                    "voice_id" to voiceId,
                    "timestamp" to timestamp,
                    "expired" to expireTime,
                    "nonce" to nonce,
                    "voice_format" to "1", // 1: PCM
                    "secretid" to secretId,
                    "needvad" to "1" // 1: 需要VAD
                )
                
                // 生成签名
                val signature = SignatureUtil.generateSignature(
                    secretId,
                    secretKey,
                    "asr.cloud.tencent.com/asr/v2/$appId",
                    params
                )
                
                // 构建请求URL - 修正URL构建格式
                val urlBuilder = StringBuilder()
                urlBuilder.append("wss://asr.cloud.tencent.com/asr/v2/")
                urlBuilder.append(appId)
                urlBuilder.append("?")
                
                params.forEach { (key, value) ->
                    urlBuilder.append("$key=$value&")
                }
                urlBuilder.append("signature=$signature")
                val url = urlBuilder.toString()
                
                Log.d(TAG, "连接URL: $url")
                
                // 创建请求
                val request = Request.Builder()
                    .url(url)
                    .build()
                
                // 连接WebSocket
                webSocket = client.newWebSocket(request, createWebSocketListener())
                
                Log.d(TAG, "已发起WebSocket连接")
            } catch (e: Exception) {
                Log.e(TAG, "连接失败: ${e.message}", e)
                _recognitionResult.value = "连接失败: ${e.message}"
            }
        }
    }

    // 发送音频数据
    fun sendAudioData(data: ByteArray) {
        webSocket?.send(data.toByteString())
    }

    // 结束识别
    fun endRecognition() {
        try {
            // 保存结束前的累积文本副本
            val existingAccumulatedText = _accumulatedText.value
            
            // 停止录音时不需要额外保存内容，因为已经实时更新到累积文本了
            // 只需要清空当前句子状态
            if (currentSentence.isNotEmpty() && !existingAccumulatedText.contains(currentSentence)) {
                // 如果当前句子不为空，并且不在累积文本中，添加到累积文本
                addToAccumulatedText(currentSentence)
            }
            currentSentence = ""
            
            // 发送结束信号
            val endSignal = EndSignal(type = "end")
            val endSignalJson = endSignalAdapter.toJson(endSignal)
            webSocket?.send(endSignalJson)
            Log.d(TAG, "已发送结束信号")
            
            // 确保累积文本没有被清空
            if (_accumulatedText.value.isEmpty() && existingAccumulatedText.isNotEmpty()) {
                Log.d(TAG, "检测到累积文本被清空，正在恢复...")
                _accumulatedText.value = existingAccumulatedText
            }
        } catch (e: Exception) {
            Log.e(TAG, "发送结束信号失败: ${e.message}", e)
        }
    }
    
    // 添加一个方法来获取当前累积的全部文字
    fun getFullTranscriptText(): String {
        val accumulated = _accumulatedText.value
        return if (currentSentence.isEmpty()) {
            accumulated
        } else {
            if (accumulated.isEmpty()) currentSentence else "$accumulated $currentSentence"
        }
    }

    // 关闭连接
    fun close() {
        try {
            // 保存当前结果到累积文本
            if (currentSentence.isNotEmpty()) {
                addToAccumulatedText(currentSentence)
                currentSentence = ""
            }
            
            webSocket?.close(1000, "正常关闭")
            webSocket = null
            Log.d(TAG, "已关闭WebSocket连接")
        } catch (e: Exception) {
            Log.e(TAG, "关闭连接失败: ${e.message}", e)
        }
    }
    
    // 创建WebSocket监听器
    private fun createWebSocketListener(): WebSocketListener {
        return object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接已打开")
                _recognitionResult.value = "连接已建立，请开始说话... 哦"
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到消息: $text")
                try {
                    val response = asrResponseAdapter.fromJson(text)
                    response?.let {
                        if (it.code == 0) {
                            // 成功接收到识别结果
                            it.result?.voice_text_str?.let { newText ->
                                if (newText.isNotEmpty()) {
                                    // 重置句子完成标志
                                    _isFinalResult.value = false
                                    
                                    // 更新为当前识别的临时结果
                                    _recognitionResult.value = newText
                                    
                                    // 处理slice_type=2的情况 - 这是需要实时增量添加的句子片段
                                    if (it.result.slice_type == 2) {
                                        Log.d(TAG, "检测到slice_type=2，实时增量添加文本: $newText")
                                        
                                        // 先检查是否与累积文本有重叠，避免重复
                                        if (wouldCreateDuplicate(newText)) {
                                            Log.d(TAG, "检测到添加此文本会导致重复，跳过: $newText")
                                            return@let
                                        }
                                        
                                        // 记录已处理slice_type=2的状态
                                        hasProcessedSliceType2 = true
                                        lastProcessedText = newText
                                        addToRecentProcessedTexts(newText)
                                        
                                        // 直接更新累积文本，不添加换行符
                                        if (_accumulatedText.value.isEmpty()) {
                                            _accumulatedText.value = newText
                                        } else {
                                            // 安全追加文本，确保不会造成重复
                                            safeAppendText(newText)
                                        }
                                        
                                        // 记录当前句子，方便后续更新
                                        currentSentence = newText
                                    }
                                    // 如果不是slice_type=2，则按照原逻辑处理
                                    else {
                                        // 实时更新累积文本 - 无需等待句子结束
                                        // 1. 如果是全新的会话，直接更新累积文本
                                        if (currentSentence.isEmpty()) {
                                            // 保留原有文本，不覆盖
                                            if (_accumulatedText.value.isEmpty()) {
                                                _accumulatedText.value = newText
                                            } else {
                                                // 安全追加文本，确保不会造成重复
                                                safeAppendText(newText)
                                            }
                                            currentSentence = newText
                                            Log.d(TAG, "新会话开始，追加累积: $newText")
                                        } 
                                        // 2. 如果是对当前句子的更新，替换最后一段内容
                                        else if (newText.length > currentSentence.length) {
                                            // 删除旧版本，添加新版本
                                            if (_accumulatedText.value == currentSentence) {
                                                _accumulatedText.value = newText
                                            } else if (_accumulatedText.value.endsWith(currentSentence)) {
                                                // 替换结尾部分
                                                val prefix = _accumulatedText.value.substring(
                                                    0, 
                                                    _accumulatedText.value.length - currentSentence.length
                                                )
                                                _accumulatedText.value = prefix + newText
                                            } else {
                                                // 找到当前句子在累积文本中的位置
                                                val lastIndex = _accumulatedText.value.lastIndexOf(currentSentence)
                                                if (lastIndex >= 0) {
                                                    // 替换最后出现的当前句子
                                                    val prefix = _accumulatedText.value.substring(0, lastIndex)
                                                    val suffix = _accumulatedText.value.substring(lastIndex + currentSentence.length)
                                                    _accumulatedText.value = prefix + newText + suffix
                                                } else {
                                                    // 如果找不到匹配的位置，安全追加新内容
                                                    safeAppendText(newText)
                                                }
                                            }
                                            currentSentence = newText
                                            Log.d(TAG, "更新实时累积: $newText")
                                        }
                                    }
                                    
                                    // 如果是最终结果(一句话结束)，更新状态并准备下一句
                                    if (it.final == 1) {
                                        // 处理最终结果，确保不会导致重复
                                        handleFinalResult(newText)
                                        
                                        // 设置句子完成标志
                                        _isFinalResult.value = true
                                        
                                        // 清空当前句子，准备下一句
                                        currentSentence = ""
                                        Log.d(TAG, "句子结束，最终结果: $newText")
                                    }
                                }
                            }
                        } else {
                            // 识别出错，但不要清空当前显示结果，以免中断用户体验
                            Log.e(TAG, "识别错误: [${it.code}] ${it.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析消息失败: ${e.message}", e)
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败: ${t.message}", t)
                
                // 如果当前有句子，添加到累积文本
                if (currentSentence.isNotEmpty()) {
                    addToAccumulatedText(currentSentence)
                    currentSentence = ""
                }
                
                // 如果之前有有效结果，则不覆盖它
                if (lastValidResult.isNotEmpty()) {
                    Log.d(TAG, "保留上次有效结果: $lastValidResult")
                } else {
                    _recognitionResult.value = "连接失败: ${t.message}"
                }
                
                // 输出更详细的错误信息
                response?.let {
                    Log.e(TAG, "错误状态码: ${it.code}, 消息: ${it.message}")
                    if (it.body != null) {
                        try {
                            Log.e(TAG, "错误详情: ${it.body!!.string()}")
                        } catch (e: Exception) {
                            Log.e(TAG, "无法读取错误详情", e)
                        }
                    }
                }
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket正在关闭, code: $code, reason: $reason")
                
                // 保存关闭前的累积文本
                val existingAccumulatedText = _accumulatedText.value
                
                // 确保将当前句子添加到累积文本
                if (currentSentence.isNotEmpty()) {
                    addToAccumulatedText(currentSentence)
                    currentSentence = ""
                }
                
                // 确保关闭后累积文本不被清空
                if (_accumulatedText.value.isEmpty() && existingAccumulatedText.isNotEmpty()) {
                    Log.d(TAG, "WebSocket关闭过程中累积文本被清空，正在恢复...")
                    _accumulatedText.value = existingAccumulatedText
                }
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket已关闭, code: $code, reason: $reason")
            }
        }
    }

    // 将当前句子添加到累积文本中 - 用于新句子或句子结束时
    private fun addToAccumulatedText(text: String) {
        if (text.isEmpty() || text.contains("连接已建立")) return
        
        // 会议记录模式：累积添加文本，而不是覆盖
        val current = _accumulatedText.value
        _accumulatedText.value = if (current.isEmpty()) {
            text
        } else {
            // 直接追加，不添加换行符
            current + text
        }
        Log.d(TAG, "累积会议记录: ${_accumulatedText.value}")
    }

    // 清空当前识别结果 - 为新的录音会话准备
    fun clearCurrentResult() {
        _recognitionResult.value = "连接已建立，请开始说话..."
        _isFinalResult.value = false
        currentSentence = ""
        hasProcessedSliceType2 = false
        lastProcessedText = ""
        // 不清空累积文本，保留之前的记录
        
        // 清空最近处理过的文本列表
        recentlyProcessedTexts.clear()
    }
    
    // 清空所有内容 - 仅当用户明确要求清空会议记录时使用
    fun clearAll() {
        _recognitionResult.value = "连接已建立，请开始说话..."
        _isFinalResult.value = false
        currentSentence = ""
        _accumulatedText.value = ""
        lastValidResult = ""
        hasProcessedSliceType2 = false
        lastProcessedText = ""
        recentlyProcessedTexts.clear()
    }

    // 设置累积文本 - 用于从持久化存储加载文本
    fun setAccumulatedText(text: String) {
        _accumulatedText.value = text
        Log.d(TAG, "已手动设置累积文本: $text")
    }

    // 是否已经处理过slice_type=2的文本
    fun hasProcessedSliceType2Text(): Boolean {
        return hasProcessedSliceType2
    }
    
    // 获取最后处理过的slice_type=2文本
    fun getLastProcessedText(): String {
        return lastProcessedText
    }

    // 检查文本是否已经最近处理过（去重）
    private fun isTextDuplicate(text: String): Boolean {
        // 检查累积文本是否已包含此文本（完全匹配）
        if (_accumulatedText.value.endsWith(text)) {
            return true
        }
        
        // 检查累积文本是否包含当前文本（子串匹配）
        if (text.length > 3 && _accumulatedText.value.contains(text)) {
            return true
        }
        
        // 如果文本很短，检查是否作为一部分已存在于累积文本中（避免重复短语）
        if (text.length <= 10 && text.length > 2) {
            val lastChars = _accumulatedText.value.takeLast(20)
            if (lastChars.contains(text)) {
                return true
            }
        }
        
        // 检查是否在最近处理过的文本列表中
        for (recentText in recentlyProcessedTexts) {
            // 精确匹配
            if (recentText == text) {
                return true
            }
            
            // 检查新文本是否是最近文本的一部分
            if (recentText.length > 3 && recentText.contains(text)) {
                return true
            }
            
            // 检查最近文本是否是新文本的一部分
            if (text.length > 3 && text.contains(recentText)) {
                return true
            }
        }
        
        return false
    }
    
    // 添加到最近处理过的文本列表
    private fun addToRecentProcessedTexts(text: String) {
        recentlyProcessedTexts.add(text)
        // 保持列表大小在限制范围内
        if (recentlyProcessedTexts.size > maxRecentTexts) {
            recentlyProcessedTexts.removeAt(0)
        }
    }

    // 处理最终结果，确保不会导致重复
    private fun handleFinalResult(finalText: String) {
        lastValidResult = finalText
        
        // 如果之前通过slice_type=2已经处理过，检查是否与之前处理的文本相同
        if (hasProcessedSliceType2) {
            // 计算相似度
            val similarity = calculateSimilarity(lastProcessedText, finalText)
            
            if (similarity > 0.6) { // 如果相似度超过60%，认为是同一句话
                Log.d(TAG, "最终结果与slice_type=2结果相似，不重复添加")
                // 不需要额外处理，已经通过slice_type=2添加过了
            } else {
                // 相似度不高，可能是不同的内容，但仍然需要检查重复
                if (!wouldCreateDuplicate(finalText)) {
                    // 安全追加文本
                    safeAppendText(finalText)
                }
            }
        } else {
            // 之前没有通过slice_type=2处理过，直接添加
            if (!wouldCreateDuplicate(finalText)) {
                safeAppendText(finalText)
            }
        }
        
        // 重置slice_type=2的处理状态
        hasProcessedSliceType2 = false
        lastProcessedText = ""
    }
    
    // 修改wouldCreateDuplicate方法，增强长句重复检测和快速语音重复检测
    private fun wouldCreateDuplicate(newText: String): Boolean {
        val current = _accumulatedText.value
        
        // 如果累积文本为空，不会造成重复
        if (current.isEmpty()) {
            return false
        }
        
        // 检查完全包含
        if (current.contains(newText)) {
            Log.d(TAG, "累积文本已完全包含新文本")
            return true
        }
        
        // 提取句子进行句子级别的去重检查
        val sentences = extractSentences(newText)
        var allSentencesDuplicate = true
        
        for (sentence in sentences) {
            if (sentence.length < 2) continue
            
            if (!sentenceExistsInText(current, sentence)) {
                allSentencesDuplicate = false
                break
            }
        }
        
        if (allSentencesDuplicate && sentences.isNotEmpty()) {
            Log.d(TAG, "所有句子都已存在于累积文本中")
            return true
        }
        
        // 快速语音重复检测 - 检查是否是快速重复说话造成的重复
        val words = newText.split(Regex("\\s+|，|。|？|！|,|\\.|\\?|!|;|；"))
            .filter { it.isNotEmpty() }
        
        // 检查连续重复词组模式
        if (words.size > 1) {
            val repeatedWordGroups = detectRepeatedWordGroups(words)
            if (repeatedWordGroups) {
                Log.d(TAG, "检测到快速语音中的重复词组模式: $newText")
                return true
            }
        }
        
        // 检查最近处理的文本列表
        for (recentText in recentlyProcessedTexts) {
            if (recentText == newText) {
                Log.d(TAG, "新文本与最近处理的文本完全相同")
                return true
            }
            
            // 计算整体相似度
            val similarity = calculateSimilarity(recentText, newText)
            if (similarity > 0.7 && (recentText.length > 10 || newText.length > 10)) {
                Log.d(TAG, "新文本与最近处理的文本相似度高: $similarity")
                return true
            }
            
            // 如果新文本包含在最近文本中且长度超过一定比例
            if (recentText.length > 3 && recentText.contains(newText) && 
                newText.length.toDouble() / recentText.length.toDouble() > 0.5) {
                Log.d(TAG, "新文本是最近文本的主要部分")
                return true
            }
            
            // 如果最近文本包含在新文本中且长度超过一定比例
            if (newText.length > 3 && newText.contains(recentText) && 
                recentText.length.toDouble() / newText.length.toDouble() > 0.5) {
                Log.d(TAG, "最近文本是新文本的主要部分")
                return true
            }
        }
        
        // 检查长段落中的短语重复
        if (newText.length > 20) {
            val segments = mutableListOf<String>()
            // 将文本分成多个10字符长的段落
            for (i in 0..(newText.length - 10) step 5) {
                segments.add(newText.substring(i, i + 10))
            }
            
            var segmentsDuplicate = 0
            for (segment in segments) {
                if (current.contains(segment)) {
                    segmentsDuplicate++
                }
            }
            
            // 如果超过50%的段落都存在于当前文本中，认为是重复
            if (segmentsDuplicate.toDouble() / segments.size.toDouble() > 0.5) {
                Log.d(TAG, "大部分文本段落已存在于累积文本中")
                return true
            }
        }
        
        // 重复性词语检测
        for (word in words) {
            if (word.length < 2) continue
            
            // 检测"好的好的"这样的重复模式
            val repeatedPattern = "$word$word"
            if (current.endsWith(word) && newText.startsWith(word)) {
                Log.d(TAG, "检测到累积文本结尾和新文本开头有重复词: $word")
                return true
            }
            
            // 检测短语重复
            if (word.length > 1 && current.contains(repeatedPattern)) {
                Log.d(TAG, "检测到重复短语: $repeatedPattern")
                return true
            }
        }
        
        // 整体文本相似度检查
        if (newText.length > 20) {
            val similarity = calculateSimilarity(current.takeLast(minOf(current.length, 100)), newText)
            if (similarity > 0.6) {
                Log.d(TAG, "整体文本相似度高: $similarity")
                return true
            }
        }
        
        // 模糊匹配 - 计算Jaccard相似度
        val similarity = calculateSimilarity(
            current.takeLast(minOf(current.length, 30)), // 取最后30个字符
            newText.take(minOf(newText.length, 30))      // 取前30个字符
        )
        
        if (similarity > 0.5) { // 如果相似度超过50%
            Log.d(TAG, "累积文本结尾与新文本开头相似度高: $similarity")
            return true
        }
        
        return false
    }

    // 新增方法：检测重复词组模式（针对快速说话时的重复）
    private fun detectRepeatedWordGroups(words: List<String>): Boolean {
        if (words.size < 4) return false // 至少需要4个词才能形成有意义的重复模式
        
        // 检查相邻重复词
        var repeatedCount = 0
        for (i in 0 until words.size - 1) {
            if (words[i] == words[i + 1]) {
                repeatedCount++
                if (repeatedCount >= 2) { // 连续出现3个相同词
                    Log.d(TAG, "检测到连续重复词: ${words[i]}")
                    return true
                }
            } else {
                repeatedCount = 0
            }
        }
        
        // 检查词组重复模式 (如"我想我想")
        for (groupSize in 1..minOf(5, words.size / 2)) { // 最多检查5个词的组合
            for (i in 0 until words.size - groupSize * 2 + 1) {
                var isGroupRepeated = true
                for (j in 0 until groupSize) {
                    if (i + j >= words.size || i + j + groupSize >= words.size || 
                        words[i + j] != words[i + j + groupSize]) {
                        isGroupRepeated = false
                        break
                    }
                }
                
                if (isGroupRepeated) {
                    val repeatedGroup = words.subList(i, i + groupSize).joinToString(" ")
                    Log.d(TAG, "检测到重复词组: $repeatedGroup")
                    return true
                }
            }
        }
        
        // 检查长距离重复模式 (如"我希望...我希望")
        if (words.size >= 6) { // 只在句子较长时检查
            // 将单词两两组合，检查是否有相同的组合反复出现
            val wordPairs = mutableMapOf<String, Int>()
            for (i in 0 until words.size - 1) {
                val pair = "${words[i]} ${words[i+1]}"
                wordPairs[pair] = (wordPairs[pair] ?: 0) + 1
                
                // 如果某个词对出现超过2次，可能是重复
                if ((wordPairs[pair] ?: 0) > 2) {
                    Log.d(TAG, "检测到词对多次重复: $pair")
                    return true
                }
            }
        }
        
        return false
    }

    // 修改safeAppendText方法，改进去重逻辑
    private fun safeAppendText(newText: String) {
        val current = _accumulatedText.value
        
        // 如果当前累积文本为空，直接设置
        if (current.isEmpty()) {
            _accumulatedText.value = newText
            return
        }
        
        // 提取句子，按标点符号分割
        val sentences = extractSentences(newText)
        
        // 对每个句子进行去重后再添加
        var cleanedText = ""
        for (sentence in sentences) {
            if (sentence.length < 2) continue
            
            // 检查句子是否已在累积文本中存在
            if (sentenceExistsInText(current, sentence)) {
                Log.d(TAG, "跳过已存在的句子: $sentence")
                continue
            }
            
            // 检查与累积文本结尾的重叠
            val overlap = findLargestOverlap(current + cleanedText, sentence)
            if (overlap > 0) {
                // 只添加非重叠部分
                val nonOverlappingPart = sentence.substring(overlap)
                if (nonOverlappingPart.isNotEmpty()) {
                    // 对非重叠部分进行快速语音重复检测
                    val words = nonOverlappingPart.split(Regex("\\s+|，|。|？|！|,|\\.|\\?|!|;|；"))
                        .filter { it.isNotEmpty() }
                    if (words.size > 1 && detectRepeatedWordGroups(words)) {
                        Log.d(TAG, "在非重叠部分检测到快速语音重复模式，跳过: $nonOverlappingPart")
                        continue
                    }
                    
                    cleanedText += nonOverlappingPart
                    Log.d(TAG, "添加句子的非重叠部分: $nonOverlappingPart")
                }
            } else {
                // 没有重叠，添加整个句子
                // 对整个句子进行快速语音重复检测
                val words = sentence.split(Regex("\\s+|，|。|？|！|,|\\.|\\?|!|;|；"))
                    .filter { it.isNotEmpty() }
                if (words.size > 1 && detectRepeatedWordGroups(words)) {
                    Log.d(TAG, "在句子中检测到快速语音重复模式，跳过: $sentence")
                    continue
                }
                
                cleanedText += sentence
                Log.d(TAG, "添加新句子: $sentence")
            }
        }
        
        // 只有当有新内容时才更新累积文本
        if (cleanedText.isNotEmpty()) {
            _accumulatedText.value += cleanedText
            Log.d(TAG, "更新累积文本，添加内容: $cleanedText")
            
            // 将新添加的文本加入到最近处理过的文本列表
            addToRecentProcessedTexts(cleanedText)
        } else {
            Log.d(TAG, "没有新内容需要添加")
        }
    }
    
    // 从文本中提取句子
    private fun extractSentences(text: String): List<String> {
        // 中文标点符号和空格作为分隔符
        val delimiters = Regex("[，。？！,.?!;；]")
        val parts = text.split(delimiters).filter { it.isNotEmpty() }
        
        // 重新加上分隔符
        val sentences = mutableListOf<String>()
        var startIndex = 0
        
        for (part in parts) {
            // 寻找part在原始文本中的位置
            val partIndex = text.indexOf(part, startIndex)
            if (partIndex >= 0) {
                // 找到下一个分隔符的位置
                val endIndex = text.indexOfAny(charArrayOf('，', '。', '？', '！', ',', '.', '?', '!', ';', '；'), partIndex + part.length)
                
                // 如果找到分隔符，提取包含分隔符的完整句子
                val sentence = if (endIndex >= 0) {
                    text.substring(partIndex, endIndex + 1)
                } else {
                    // 最后一个句子可能没有分隔符
                    text.substring(partIndex)
                }
                
                sentences.add(sentence)
                startIndex = partIndex + part.length
            }
        }
        
        // 如果没有成功分割句子，可能是没有标点，将整个文本作为一个句子
        if (sentences.isEmpty() && text.isNotEmpty()) {
            sentences.add(text)
        }
        
        return sentences
    }
    
    // 检查句子是否已存在于文本中
    private fun sentenceExistsInText(text: String, sentence: String): Boolean {
        // 检查完全匹配
        if (text.contains(sentence)) {
            return true
        }
        
        // 检查句子的主要部分（剔除标点符号后）是否存在
        val sentenceMainPart = sentence.replace(Regex("[，。？！,.?!;；]"), "")
        if (sentenceMainPart.length > 3 && text.contains(sentenceMainPart)) {
            return true
        }
        
        // 检查长句的模糊匹配
        if (sentence.length > 5) {
            // 将句子分成几个段落进行检查
            val segmentLength = sentence.length / 2
            for (i in 0..(sentence.length - segmentLength)) {
                val segment = sentence.substring(i, i + segmentLength)
                if (segment.length > 3 && text.contains(segment)) {
                    return true
                }
            }
            
            // 计算与文本中已有句子的相似度
            val existingSentences = extractSentences(text)
            for (existingSentence in existingSentences) {
                val similarity = calculateSimilarity(existingSentence, sentence)
                if (similarity > 0.7) { // 相似度高于70%
                    Log.d(TAG, "句子相似度高: $similarity, 已有句子: $existingSentence, 新句子: $sentence")
                    return true
                }
            }
        }
        
        return false
    }
    
    // 增强相似度计算方法，使用bi-gram比较
    private fun calculateSimilarity(text1: String, text2: String): Double {
        if (text1.isEmpty() || text2.isEmpty()) return 0.0
        
        // 如果采用字符级别的相似度
        val charSimilarity = calculateJaccardSimilarity(text1.toCharArray().toSet(), text2.toCharArray().toSet())
        
        // 如果文本较长，还计算bi-gram相似度
        var bigramSimilarity = 0.0
        if (text1.length > 3 && text2.length > 3) {
            val bigrams1 = mutableSetOf<String>()
            val bigrams2 = mutableSetOf<String>()
            
            for (i in 0 until text1.length - 1) {
                bigrams1.add(text1.substring(i, i + 2))
            }
            
            for (i in 0 until text2.length - 1) {
                bigrams2.add(text2.substring(i, i + 2))
            }
            
            bigramSimilarity = calculateJaccardSimilarity(bigrams1, bigrams2)
        }
        
        // 返回较高的相似度
        return maxOf(charSimilarity, bigramSimilarity)
    }
    
    // Jaccard相似度计算
    private fun <T> calculateJaccardSimilarity(set1: Set<T>, set2: Set<T>): Double {
        val intersection = set1.intersect(set2)
        val union = set1.union(set2)
        
        return intersection.size.toDouble() / union.size.toDouble()
    }

    // 查找两段文本之间的最大重叠
    private fun findLargestOverlap(first: String, second: String): Int {
        if (first.isEmpty() || second.isEmpty()) return 0
        
        // 需要找到first末尾和second开头的最大重叠
        val minLength = minOf(first.length, second.length)
        
        // 从最大可能的重叠开始检查
        for (overlapSize in minLength downTo 1) {
            if (overlapSize < 2) return 0 // 只有一个字符的重叠忽略
            
            val endOfFirst = first.substring(first.length - overlapSize)
            val startOfSecond = second.substring(0, overlapSize)
            
            if (endOfFirst == startOfSecond) {
                return overlapSize
            }
        }
        
        return 0
    }

    // 数据类
    data class AsrResponse(
        val code: Int,
        val message: String,
        val voice_id: String? = null,
        val message_id: String? = null,
        val result: AsrResult? = null,
        val final: Int? = null
    )

    data class AsrResult(
        val slice_type: Int? = null,
        val index: Int? = null,
        val start_time: Int? = null,
        val end_time: Int? = null,
        val voice_text_str: String? = null,
        val word_size: Int? = null,
        val word_list: List<Any>? = null
    )

    data class EndSignal(val type: String)
} 