apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace "com.example.llya"
    compileSdkVersion 35

    defaultConfig {
        applicationId "com.example.llya"
        minSdkVersion 26
        targetSdkVersion 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    // 添加对实验性API的支持
    buildFeatures {
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.5.1'
    }
}

dependencies {
    // 核心库
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'

    // Compose相关 - 使用更新的BOM版本来确保一致性
    implementation platform('androidx.compose:compose-bom:2024.02.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material:material-icons-extended'
    implementation 'androidx.compose.runtime:runtime-livedata'

    // 移除特定版本的Material图标库，由BOM统一管理
    // implementation 'androidx.compose.material:material-icons-extended:1.6.1'

    // 生命周期组件 - 使用与其他Compose库一致的版本
    implementation 'androidx.lifecycle:lifecycle-runtime-compose:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    // implementation 'androidx.compose.runtime:runtime-livedata:1.5.4'

    // Retrofit相关依赖
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // OkHttp和WebSocket相关
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'

    // 音频录制和处理
    implementation 'androidx.media3:media3-exoplayer:1.2.1'

    // 加密库 - 用于生成签名
    implementation 'commons-codec:commons-codec:1.16.0'

    // Kotlin协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // JSON处理
    implementation 'com.squareup.moshi:moshi-kotlin:1.15.0'

    // 导航组件 - 更新版本以匹配其他Compose库
    implementation 'androidx.navigation:navigation-compose:2.7.7'

    // 权限处理库 - 更新到稳定版本
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'

    // 系统UI控制器
    implementation 'com.google.accompanist:accompanist-systemuicontroller:0.32.0'

    // 微软语音服务SDK
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.43.0'

    // 测试库
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation platform('androidx.compose:compose-bom:2024.02.00')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}