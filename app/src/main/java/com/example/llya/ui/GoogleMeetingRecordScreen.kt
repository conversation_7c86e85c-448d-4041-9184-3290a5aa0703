package com.example.llya.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.viewmodel.MeetingRecordViewModel
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoogleMeetingRecordScreen(
    navController: NavController? = null,
    viewModel: MeetingRecordViewModel = viewModel()
) {
    val darkBgColor = Color(0xFF222131)
    val purpleColor = Color(0xFF8675E6)
    val cardBgColor = Color(0xFF2D2C3E)

    val isRecording by viewModel.isRecording.collectAsState()
    val meetingContent by viewModel.meetingContent.collectAsState()
    val currentRecognitionResult by viewModel.currentRecognitionResult.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val currentLanguage by viewModel.currentLanguage.collectAsState()
    val supportedLanguages by viewModel.supportedLanguages.collectAsState()
    val isLoadingLanguages by viewModel.isLoadingLanguages.collectAsState()
    val currentAsrServiceName by viewModel.currentAsrServiceName.collectAsState()

    // 获取字符串资源（在Composable上下文中）
    val dateFormatString = stringResource(R.string.gm_date_format)
    val defaultLanguageString = stringResource(R.string.gm_default_language)
    val unknownErrorString = stringResource(R.string.gm_unknown_error)

    // 根据当前ASR服务类型决定是否显示实时预览
    val displayContent = remember(meetingContent, currentRecognitionResult, isRecording, currentAsrServiceName) {
        if (isRecording && currentRecognitionResult.isNotEmpty() && currentAsrServiceName == "Google Cloud") {
            // 如果正在录音且有实时识别结果，并且使用的是谷歌云，则显示会议内容加上当前识别结果
            if (meetingContent.isEmpty()) {
                currentRecognitionResult
            } else {
                // 添加适当的分隔符
                val separator = if (meetingContent.endsWith(".") ||
                                   meetingContent.endsWith("。") ||
                                   meetingContent.endsWith("!") ||
                                   meetingContent.endsWith("！") ||
                                   meetingContent.endsWith("?") ||
                                   meetingContent.endsWith("？") ||
                                   meetingContent.endsWith("\n")) {
                    "" // 已经有结束符号或换行，不需要额外分隔符
                } else {
                    "\n" // 添加换行作为分隔符
                }
                meetingContent + separator + currentRecognitionResult
            }
        } else {
            // 如果没有录音、没有实时识别结果或使用的是腾讯云，只显示会议内容
            meetingContent
        }
    }

    // 计算字数
    val wordCount = displayContent.length

    // 获取当前日期
    val currentDate = remember {
        SimpleDateFormat(dateFormatString, Locale.getDefault()).format(Date())
    }

    // 滚动状态
    val scrollState = rememberScrollState()

    // 保存对话框状态
    var showSaveDialog by remember { mutableStateOf(false) }
    var meetingTitle by remember { mutableStateOf("") }

    // 语言选择下拉菜单状态
    var languageMenuExpanded by remember { mutableStateOf(false) }

    // 获取当前选中语言的显示名称
    val currentLanguageDisplay = remember(currentLanguage, supportedLanguages) {
        supportedLanguages.find { it.first == currentLanguage }?.second ?: defaultLanguageString
    }

    // 动画值
    val micScale by animateFloatAsState(
        targetValue = if (isRecording) 1.2f else 1.0f,
        label = "micScale"
    )

    // 错误消息Snackbar状态
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()

    // 当错误消息变化时显示Snackbar
    LaunchedEffect(errorMessage) {
        if (errorMessage != null) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar(
                    message = errorMessage ?: unknownErrorString,
                    duration = SnackbarDuration.Short
                )
            }
            viewModel.clearError()
        }
    }

    // 自动滚动到底部
    LaunchedEffect(displayContent) {
        scrollState.animateScrollTo(scrollState.maxValue)
    }

    // 添加清空确认对话框状态变量
    var showClearConfirmDialog by remember { mutableStateOf(false) }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        containerColor = darkBgColor,
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text(stringResource(R.string.meeting_record_title), color = Color.White)

                    }
                },
                navigationIcon = {
                    IconButton(onClick = { navController?.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back),
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 语言选择器
                    Box {
                        IconButton(onClick = { languageMenuExpanded = true }) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Language,
                                    contentDescription = stringResource(R.string.meeting_select_language),
                                    tint = Color.White
                                )
                                if (isLoadingLanguages) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                }
                            }
                        }

                        DropdownMenu(
                            expanded = languageMenuExpanded,
                            onDismissRequest = { languageMenuExpanded = false },
                            modifier = Modifier.width(IntrinsicSize.Max)
                        ) {
                            supportedLanguages.forEach { (code, name) ->
                                DropdownMenuItem(
                                    text = { Text(name) },
                                    onClick = {
                                        // 根据选择的语言设置，如果当前正在录音，则先停止录音
                                        if (isRecording) {
                                            viewModel.stopMeetingRecording()
                                        }
                                        // 设置新的语言
                                        viewModel.setLanguage(code)
                                        // 关闭语言菜单
                                        languageMenuExpanded = false

                                        // 提供反馈
//                                        coroutineScope.launch {
//                                            snackbarHostState.showSnackbar(
//                                                message = "已切换到 $name，使用 $currentAsrServiceName 语音识别",
//                                                duration = SnackbarDuration.Short
//                                            )
//                                        }
                                    },
                                    trailingIcon = {
                                        if (code == currentLanguage) {
                                            Icon(
                                                imageVector = Icons.Default.Check,
                                                contentDescription = stringResource(R.string.ok),
                                                tint = purpleColor
                                            )
                                        }
                                    }
                                )
                            }
                        }
                    }

                    // 保存按钮
                    IconButton(
                        onClick = {
                            if (meetingContent.isNotEmpty()) {
                                showSaveDialog = true
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = stringResource(R.string.meeting_save_title),
                            tint = Color.White
                        )
                    }

                    // 历史记录按钮
                    IconButton(
                        onClick = {
                            navController?.navigate("google_meeting_list")
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = stringResource(R.string.gm_history),
                            tint = Color.White
                        )
                    }

                    // 清空按钮
                    IconButton(
                        onClick = {
                            // 不再直接清空，而是显示确认对话框
                            showClearConfirmDialog = true
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = stringResource(R.string.gm_clear_record),
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = darkBgColor
                )
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = {
                    if (isRecording) {
                        viewModel.stopMeetingRecording()
                    } else {
                        viewModel.startMeetingRecording()
                    }
                },
                containerColor = purpleColor,
                modifier = Modifier
                    .size(76.dp)
                    .scale(micScale)
            ) {
                Icon(
                    imageVector = if (isRecording) Icons.Default.Stop else Icons.Default.Mic,
                    contentDescription = if (isRecording) stringResource(R.string.gm_stop_recording) else stringResource(R.string.gm_start_recording),
                    tint = Color.White,
                    modifier = Modifier.size(32.dp)
                )
            }
        },
        floatingActionButtonPosition = FabPosition.Center
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                // 日期和字数
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = currentDate,
                        color = Color.White,
                        fontSize = 16.sp
                    )

                    Text(
                        text = stringResource(R.string.gm_word_count, wordCount),
                        color = purpleColor,
                        fontSize = 14.sp
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 会议记录内容
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f), // 使用全部空间
                    colors = CardDefaults.cardColors(containerColor = cardBgColor),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                    ) {
                        // 标题: 会议记录
                        Text(
                            text = stringResource(R.string.gm_meeting_record),
                            color = purpleColor,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // 会议记录内容
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            if (displayContent.isNotEmpty()) {
                                Text(
                                    text = displayContent,
                                    color = Color.White,
                                    fontSize = 16.sp,
                                    lineHeight = 24.sp,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .verticalScroll(scrollState)
                                )
                            } else {
                                Text(
                                    text = if (isRecording) stringResource(R.string.gm_listening) else stringResource(R.string.gm_start_hint),
                                    color = Color.Gray,
                                    fontSize = 16.sp,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .align(Alignment.Center)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(60.dp)) // 为FAB留出空间
            }

            // 录音指示器
            AnimatedVisibility(
                visible = isRecording,
                enter = fadeIn(),
                exit = fadeOut(),
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 80.dp, end = 24.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .clip(CircleShape)
                        .background(Color.Red)
                )
            }
        }

        // 保存对话框
        if (showSaveDialog) {
            AlertDialog(
                onDismissRequest = { showSaveDialog = false },
                title = { Text(stringResource(R.string.gm_save_title)) },
                text = {
                    Column {
                        Text(stringResource(R.string.gm_save_prompt))
                        Spacer(modifier = Modifier.height(8.dp))
                        OutlinedTextField(
                            value = meetingTitle,
                            onValueChange = { meetingTitle = it },
                            placeholder = { Text(stringResource(R.string.gm_save_placeholder)) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                },
                confirmButton = {
                    TextButton(
                        onClick = {
                            viewModel.saveCurrentMeetingToHistory(meetingTitle)
                            showSaveDialog = false
                            meetingTitle = ""
                        }
                    ) {
                        Text(stringResource(R.string.gm_save))
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showSaveDialog = false }
                    ) {
                        Text(stringResource(R.string.gm_cancel))
                    }
                }
            )
        }

        // 清空确认对话框
        if (showClearConfirmDialog) {
            AlertDialog(
                onDismissRequest = { showClearConfirmDialog = false },
                title = { Text(stringResource(R.string.gm_clear_record)) },
                text = { Text(stringResource(R.string.gm_clear_confirm_message)) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            viewModel.clearCurrentMeeting()
                            showClearConfirmDialog = false
                        }
                    ) {
                        Text(stringResource(R.string.gm_clear_button), color = Color.Red)
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showClearConfirmDialog = false }
                    ) {
                        Text(stringResource(R.string.gm_cancel))
                    }
                }
            )
        }
    }
}