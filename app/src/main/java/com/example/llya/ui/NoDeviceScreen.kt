package com.example.llya.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.R
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import android.content.Intent
import android.provider.Settings
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import kotlinx.coroutines.delay
import android.widget.Toast
import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import com.example.llya.utils.LocalLifecycleOwner
import com.example.llya.utils.BluetoothPermissionHelper
import com.example.llya.viewmodel.UserViewModel

@Composable
fun NoDeviceScreen(
    navController: NavController? = null,
    fromDeviceUnbind: Boolean = false
) {
    // 设置状态栏文字颜色为白色
    val systemUiController = rememberSystemUiController()
    // 背景颜色 - 深蓝色
    val backgroundColor = Color(0xFF1E1D2B)
    
    // 预先获取context
    val context = LocalContext.current
    
    // 添加UserViewModel
    val userViewModel: UserViewModel = viewModel()
    val uiState by userViewModel.uiState.collectAsState()
    // 监听登录状态
    val isLoggedInState by userViewModel.isLoggedInState.collectAsState()
    
    // 检测蓝牙状态的变量
    var isBluetoothConnected by remember { mutableStateOf(false) }
    
    // 添加一个标志位，用于跳过蓝牙连接检测
    var skipBluetoothCheck by remember { mutableStateOf(false) }
    
    // 添加一个标志位，防止重复跳转到蓝牙设备页面
    var hasNavigatedToBluetooth by remember { mutableStateOf(false) }
    
    // 新增检测状态跟踪
    var deviceCheckComplete by remember { mutableStateOf(false) }
    var hasBindedDevice by remember { mutableStateOf(false) }
    
    // 权限状态
    var hasBluetoothPermissions by remember { mutableStateOf(BluetoothPermissionHelper.hasRequiredPermissions(context)) }
    var showPermissionRequest by remember { mutableStateOf(false) }
    
    // 添加加载状态
    var isCheckingDevices by remember { mutableStateOf(true) }
    // 添加加载提示文本
    var loadingMessage by remember { mutableStateOf(context.getString(R.string.checking_device_status)) }
    
    // 添加超时检查状态
    var loadingStartTime by remember { mutableStateOf(System.currentTimeMillis()) }
    
    // 初始化时记录开始加载时间
    LaunchedEffect(Unit) {
        loadingStartTime = System.currentTimeMillis()
    }
    
    // 定期检查是否加载超时
    LaunchedEffect(Unit) {
        while (isCheckingDevices) {
            delay(5000) // 每5秒检查一次
            if (isCheckingDevices && System.currentTimeMillis() - loadingStartTime > 20000) {
                Log.d("检测流程", "整体加载过程超时，强制显示无设备界面")
                isCheckingDevices = false
                deviceCheckComplete = true
                Toast.makeText(context, "获取设备信息超时，请稍后重试", Toast.LENGTH_LONG).show()
                break
            }
        }
    }
    
    // 修改为先检测蓝牙连接状态，再检测绑定设备 - 优化版
    LaunchedEffect(Unit) {
        Log.d("检测流程", "开始检测蓝牙连接状态")
        isCheckingDevices = true
        loadingMessage = context.getString(R.string.checking_bluetooth_status)
        
        // 判断是否有蓝牙权限
        if (!BluetoothPermissionHelper.hasRequiredPermissions(context)) {
            Log.d("检测流程", "没有蓝牙权限，无法检测连接状态")
            isCheckingDevices = false
            showPermissionRequest = true
            return@LaunchedEffect
        }
        
        // 检查蓝牙连接状态
        val isConnected = checkBluetoothConnected(context)
        Log.d("检测流程", "蓝牙连接状态检测: $isConnected")
        isBluetoothConnected = isConnected
        
        // 逻辑简化：判断登录状态并获取用户信息
        if (userViewModel.isLoggedIn()) {
            // 无论蓝牙是否连接，都检查用户绑定设备
            loadingMessage = context.getString(R.string.checking_bound_devices)
            userViewModel.getUserDetail()
        } else {
            // 用户未登录
            if (isConnected) {
                // 蓝牙已连接，跳转到蓝牙页面
                Log.d("检测流程", "用户未登录，但蓝牙已连接，跳转到蓝牙页面")
                loadingMessage = context.getString(R.string.bluetooth_detected_navigating)
                delay(200) // 减少延迟
                navController?.navigate("bluetooth")
            } else {
                // 用户未登录且蓝牙未连接，显示无设备界面
                Log.d("检测流程", "用户未登录，蓝牙未连接，显示无设备界面")
                isCheckingDevices = false
            }
        }
    }
    
    // 监听登录状态变化
    LaunchedEffect(isLoggedInState) {
        Log.d("检测流程", "登录状态变化: $isLoggedInState")
        if (isLoggedInState) {
            // 用户已登录，重新获取设备信息
            isCheckingDevices = true
            loadingMessage = context.getString(R.string.checking_bound_devices)
            
            // 重置检测状态
            deviceCheckComplete = false
            hasBindedDevice = false
            skipBluetoothCheck = false
            hasNavigatedToBluetooth = false
            
            userViewModel.getUserDetail()
        } else {
            // 用户已退出登录，重置标志
            skipBluetoothCheck = false
            isCheckingDevices = false
            deviceCheckComplete = true
            hasBindedDevice = false
        }
    }
    
    // 监听UI状态变化，处理用户设备检查结果
    LaunchedEffect(uiState) {
        when (uiState) {
            is UserViewModel.UiState.Success -> {
                val successMsg = (uiState as UserViewModel.UiState.Success).message
                if (successMsg.contains("获取用户信息成功")) {
                    // 获取用户信息成功，检查是否有设备
                    val currentUser = userViewModel.currentUser.value
                    Log.d("检测流程", "步骤2结果: 用户信息获取成功，设备数量: ${currentUser?.devices?.size ?: 0}")
                    
                    // 重置计时器
                    loadingStartTime = System.currentTimeMillis()
                    
                    if (currentUser != null && currentUser.devices.isNotEmpty()) {
                        // 有设备，但需要确认蓝牙是否真正连接
                        Log.d("检测流程", "检测到已绑定设备，判断蓝牙连接状态")
                        hasBindedDevice = true
                        deviceCheckComplete = true
                        
                        // 已经在初始化时检查过蓝牙状态，这里需要重新检测确保准确性
                        loadingMessage = context.getString(R.string.checking_bluetooth_status)
                        // 减少检测次数，从3次减为2次
                        var isReallyConnected = false
                        // 只进行两次检测，减少延迟
                        for (i in 1..2) {
                            val checkResult = checkBluetoothConnected(context)
                            Log.d("检测流程", "检查蓝牙连接状态 #$i: $checkResult")
                            if (checkResult) {
                                isReallyConnected = true
                                break
                            }
                            // 减少延迟时间
                            delay(150) // 短暂延迟后再次检测
                        }
                        
                        if (isReallyConnected) {
                            // 蓝牙确实已连接，可以跳转到详情页面
                            Log.d("检测流程", "检查确认蓝牙已连接，准备跳转到耳机详情页面")
                            loadingMessage = context.getString(R.string.device_detected_navigating)
                            
                            // 跳转前再次确认连接状态
                            MainScope().launch {
                                // 延迟一下确保UI更新
                                delay(300)
                                
                                // 最后确认蓝牙仍然连接
                                val finalCheck = checkBluetoothConnected(context)
                                Log.d("检测流程", "跳转前最终确认蓝牙连接: $finalCheck")
                                
                                if (finalCheck) {
                                    navController?.navigate("earbuds_detail") {
                                        popUpTo("no_device") { inclusive = true }
                                    }
                                } else {
                                    // 在准备跳转时发现蓝牙已断开
                                    Log.d("检测流程", "⚠️警告: 在准备跳转时发现蓝牙已断开")
                                    isCheckingDevices = false
                                    Toast.makeText(context, context.getString(R.string.device_disconnected), Toast.LENGTH_LONG).show()
                                }
                            }
                        } else {
                            // 蓝牙未连接，不跳转，显示无设备界面
                            Log.d("检测流程", "检查确认蓝牙未连接，显示无设备界面")
                            isCheckingDevices = false
                            Toast.makeText(context, context.getString(R.string.device_not_connected), Toast.LENGTH_LONG).show()
                        }
                    } else {
                        // 无绑定设备，但可能有蓝牙连接
                        Log.d("检测流程", "步骤2结果: 未检测到已绑定设备")
                        deviceCheckComplete = true
                        hasBindedDevice = false
                        
                        if (isBluetoothConnected && !hasNavigatedToBluetooth) {
                            // 蓝牙已连接但未导航，导航到蓝牙页面
                            Log.d("检测流程", "蓝牙已连接，跳转到蓝牙页面")
                            hasNavigatedToBluetooth = true
                            loadingMessage = context.getString(R.string.bluetooth_detected_navigating)
                            delay(300)
                            navController?.navigate("bluetooth")
                        } else if (!isBluetoothConnected) {
                            // 无绑定设备且蓝牙未连接，显示无设备界面
                            Log.d("检测流程", "无绑定设备且蓝牙未连接，显示无设备界面")
                            isCheckingDevices = false
                            loadingMessage = context.getString(R.string.no_device_detected_add_new)
                        } else {
                            // 新增: 捕获情况 - 可能有蓝牙连接但无法导航的情况
                            Log.d("检测流程", "未检测到已绑定设备，且无法导航，强制显示无设备界面")
                            isCheckingDevices = false
                            loadingMessage = context.getString(R.string.no_device_detected_add_new)
                        }
                    }
                }
            }
            is UserViewModel.UiState.Error -> {
                Log.e("检测流程", "步骤2失败: 获取用户信息失败: ${(uiState as UserViewModel.UiState.Error).message}")
                isCheckingDevices = false
                deviceCheckComplete = true  // 即使失败也标记为完成
                hasBindedDevice = false
                
                // 如果蓝牙已连接，仍然可以跳转到蓝牙页面
                if (isBluetoothConnected && !hasNavigatedToBluetooth) {
                    Log.d("检测流程", "获取用户信息失败，但蓝牙已连接，跳转到蓝牙页面")
                    hasNavigatedToBluetooth = true
                    loadingMessage = context.getString(R.string.bluetooth_detected_navigating)
                    delay(300)
                    navController?.navigate("bluetooth")
                }
            }
            is UserViewModel.UiState.Loading -> {
                loadingMessage = context.getString(R.string.loading_user_info)
                
                // 检查是否加载超时（15秒）
                if (System.currentTimeMillis() - loadingStartTime > 15000) {
                    Log.d("检测流程", "加载用户信息超时，显示无设备界面")
                    isCheckingDevices = false
                    deviceCheckComplete = true
                    Toast.makeText(context, "获取用户信息超时，请稍后重试", Toast.LENGTH_LONG).show()
                }
            }
            else -> {
                // 其他状态不处理
            }
        }
    }
    
    // 显示解绑成功提示
    LaunchedEffect(fromDeviceUnbind) {
        if (fromDeviceUnbind) {
            Toast.makeText(context, context.getString(R.string.device_unbind_success), Toast.LENGTH_LONG).show()
        }
    }
    
    // 请求蓝牙权限
    if (showPermissionRequest) {
        BluetoothPermissionHelper.RequestBluetoothPermissions { granted ->
            hasBluetoothPermissions = granted
            showPermissionRequest = false
            if (!granted) {
                Toast.makeText(context, context.getString(R.string.bluetooth_permission_required), Toast.LENGTH_LONG).show()
            }
        }
    }
    
    // 添加生命周期检测，每次恢复时重新检测蓝牙连接
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                MainScope().launch {
                    // 每次恢复时先检查蓝牙连接状态
                    val isConnected = checkBluetoothConnected(context)
                    isBluetoothConnected = isConnected
                    
                    // 简化逻辑判断
                    if (userViewModel.isLoggedIn() && hasBindedDevice && isConnected) {
                        // 已登录、有绑定设备且蓝牙已连接，准备跳转到详情页面
                        // 最后确认蓝牙仍然连接
                        val finalCheck = checkBluetoothConnected(context)
                        Log.d("检测流程", "恢复时跳转前最终确认蓝牙连接: $finalCheck")
                        
                        if (finalCheck) {
                            navController?.navigate("earbuds_detail") {
                                popUpTo("no_device") { inclusive = true }
                            }
                        } else {
                            // 连接状态不一致
                            Log.d("检测流程", "⚠️警告: 蓝牙连接状态不一致，取消跳转")
                            isCheckingDevices = false
                        }
                    } else if (userViewModel.isLoggedIn() && !hasBindedDevice && isConnected && !hasNavigatedToBluetooth) {
                        // 已登录、无绑定设备但蓝牙已连接，跳转到蓝牙页面
                        hasNavigatedToBluetooth = true
                        navController?.navigate("bluetooth") {
                            popUpTo("no_device") { inclusive = true }
                        }
                    } else if (!userViewModel.isLoggedIn() && isConnected && !hasNavigatedToBluetooth) {
                        // 未登录但蓝牙已连接，跳转到蓝牙页面
                        hasNavigatedToBluetooth = true
                        navController?.navigate("bluetooth") {
                            popUpTo("no_device") { inclusive = true }
                        }
                    } else if (!isConnected) {
                        // 蓝牙未连接，显示无设备界面
                        if (!deviceCheckComplete) {
                            // 尚未完成设备检查，获取用户详情
                            userViewModel.getUserDetail()
                        } else {
                            // 已完成设备检查，显示无设备界面
                            isCheckingDevices = false
                        }
                    }
                    
                    // 每次恢复时检查权限状态
                    hasBluetoothPermissions = BluetoothPermissionHelper.hasRequiredPermissions(context)
                    if (!hasBluetoothPermissions) {
                        showPermissionRequest = true
                    }
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    
    DisposableEffect(systemUiController) {
        // 设置状态栏背景为深蓝色，文字为白色
        systemUiController.setSystemBarsColor(
            color = backgroundColor,
            darkIcons = false // false表示使用白色图标
        )
        onDispose {
            // 如果需要恢复默认状态，在这里处理
        }
    }
    
    // 按钮颜色 - 紫色
    val buttonColor = Color(0xFF8771FF)
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        // 加载指示器
        if (isCheckingDevices) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(50.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = loadingMessage,
                    color = Color.White,
                    fontSize = 16.sp
                )
            }
        } else {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 图标容器
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF3E3D5A))
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    // 使用标准蓝牙图标
                    Icon(
                        painter = painterResource(id = R.drawable.bluetooth_icon),
                        contentDescription = stringResource(R.string.bluetooth_icon),
                        tint = Color.White,
                        modifier = Modifier.size(50.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 标题文本
                Text(
                    text = stringResource(R.string.no_available_devices),
                    color = Color.White,
                    fontSize = 22.sp,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 说明文本
                Text(
                    text = if (hasBluetoothPermissions) 
                        stringResource(R.string.no_paired_devices_description) 
                    else 
                        stringResource(R.string.bluetooth_permission_needed_description),
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    lineHeight = 24.sp
                )
                
                Spacer(modifier = Modifier.height(48.dp))
                
                // 添加设备按钮或授权按钮
                Button(
                    onClick = {
                        if (hasBluetoothPermissions) {
                            // 如果有权限，打开蓝牙设置
                            BluetoothPermissionHelper.openBluetoothSettings(context)
                        } else {
                            // 如果没有权限，请求权限
                            showPermissionRequest = true
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = buttonColor
                    ),
                    shape = RoundedCornerShape(percent = 50),
                    modifier = Modifier
                        .padding(16.dp)
                        .height(56.dp)
                        .fillMaxWidth(0.7f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = stringResource(R.string.add),
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.padding(4.dp))
                        Text(
                            text = if (hasBluetoothPermissions) 
                                stringResource(R.string.add_new_device) 
                            else 
                                stringResource(R.string.grant_bluetooth_permission),
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
                
                // 添加直接前往蓝牙界面的按钮
                if (hasBluetoothPermissions) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Button(
                        onClick = {
                            // 直接导航到蓝牙界面
                            navController?.navigate("bluetooth") {
                                popUpTo("no_device") { inclusive = true }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF3E3D5A)
                        ),
                        shape = RoundedCornerShape(percent = 50),
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .height(50.dp)
                            .fillMaxWidth(0.7f)
                    ) {
                        Text(
                            text = stringResource(R.string.go_to_bluetooth_page),
                            color = Color.White,
                            fontSize = 14.sp
                        )
                    }
                }
                
                // 如果没有权限，添加一个额外按钮打开应用设置
                if (!hasBluetoothPermissions) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Button(
                        onClick = {
                            BluetoothPermissionHelper.openAppSettings(context)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF3E3D5A)
                        ),
                        shape = RoundedCornerShape(percent = 50),
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .height(50.dp)
                            .fillMaxWidth(0.7f)
                    ) {
                        Text(
                            text = stringResource(R.string.go_to_app_settings),
                            color = Color.White,
                            fontSize = 14.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

// 优化蓝牙连接状态检测方法
private fun checkBluetoothConnected(context: Context): Boolean {
    val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as? BluetoothManager
    val bluetoothAdapter = bluetoothManager?.adapter
    
    // 检查蓝牙是否开启
    if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
        Log.d("NoDeviceScreen", "蓝牙未启用")
        return false
    }
    
    // 检查是否有权限
    if (!BluetoothPermissionHelper.hasRequiredPermissions(context)) {
        Log.e("NoDeviceScreen", "没有蓝牙权限，无法检查连接状态")
        return false
    }
    
    var anyConnected = false
    
    // 检测方法1：通过配置文件连接状态检查（最快，优先使用）
    try {
        // 检查A2DP配置文件(音频设备)
        val a2dpProfile = bluetoothAdapter.getProfileConnectionState(android.bluetooth.BluetoothProfile.A2DP)
        // 检查耳机配置文件
        val headsetProfile = bluetoothAdapter.getProfileConnectionState(android.bluetooth.BluetoothProfile.HEADSET)
        
        val isConnected = (a2dpProfile == android.bluetooth.BluetoothProfile.STATE_CONNECTED || 
                           headsetProfile == android.bluetooth.BluetoothProfile.STATE_CONNECTED)
        
        // 记录配置文件方式的检测结果
        if (isConnected) {
            Log.d("NoDeviceScreen", "配置文件检测到已连接设备: A2DP=" + 
                  (a2dpProfile == android.bluetooth.BluetoothProfile.STATE_CONNECTED) + 
                  ", HEADSET=" + (headsetProfile == android.bluetooth.BluetoothProfile.STATE_CONNECTED))
            anyConnected = true
        }
    } catch (e: Exception) {
        Log.e("NoDeviceScreen", "配置文件检测方法失败: ${e.message}")
    }
    
    // 检测方法2：检查已配对设备是否有音频设备（如果方法1未检测到）
    if (!anyConnected) {
        try {
            // 获取已配对设备
            val bondedDevices = bluetoothAdapter.bondedDevices
            if (bondedDevices.isNotEmpty()) {
                // 筛选音频设备
                for (device in bondedDevices) {
                    try {
                        val deviceClass = device.bluetoothClass.majorDeviceClass
                        if (deviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO) {
                            // 尝试检查此设备是否真正连接
                            if (device.javaClass.getMethod("isConnected").invoke(device) as Boolean) {
                                Log.d("NoDeviceScreen", "发现已连接的音频设备: ${device.name ?: "未知设备"}")
                                anyConnected = true
                                break
                            }
                        }
                    } catch (e: Exception) {
                        // 单个设备检测失败，继续检查下一个
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("NoDeviceScreen", "已配对设备检测失败: ${e.message}")
        }
    }
    
    // 强制记录最终结果
    Log.d("NoDeviceScreen", "蓝牙连接检测最终结果: $anyConnected")
    return anyConnected
} 