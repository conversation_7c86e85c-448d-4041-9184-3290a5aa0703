package com.example.llya.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.data.HistoryRecord
import com.example.llya.data.RecordType
import com.example.llya.viewmodel.HistoryViewModel
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(
    navController: NavController? = null
) {
    // 深色背景色和强调色
    val darkBgColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8364FD)
    val cardBgColor = Color(0xFF33324A)
    
    val historyViewModel: HistoryViewModel = viewModel()
    val filteredRecords by historyViewModel.filteredRecords.collectAsState()
    val isPlaying by historyViewModel.isPlaying.collectAsState()
    val isLoading by historyViewModel.isLoading.collectAsState()
    val errorMessage by historyViewModel.errorMessage.collectAsState()
    val filterType by historyViewModel.filterType.collectAsState()
    val searchQuery by historyViewModel.searchQuery.collectAsState()
    
    // 选择的记录ID (用于展开/折叠)
    var expandedRecordId by remember { mutableStateOf<String?>(null) }
    
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    
    // 显示错误消息
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            coroutineScope.launch {
                snackbarHostState.showSnackbar(message = it)
                historyViewModel.clearError()
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(darkBgColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    text = stringResource(R.string.history_title),
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = stringResource(R.string.history_back),
                        tint = Color.White
                    )
                }
            },
            actions = {
                // 筛选按钮
                Box {
                    var showFilterMenu by remember { mutableStateOf(false) }
                    
                    IconButton(onClick = { showFilterMenu = true }) {
                        Icon(
                            imageVector = Icons.Default.FilterList,
                            contentDescription = stringResource(R.string.history_filter),
                            tint = Color.White
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showFilterMenu,
                        onDismissRequest = { showFilterMenu = false },
                        modifier = Modifier.background(cardBgColor)
                    ) {
                        DropdownMenuItem(
                            text = { 
                                Text(
                                    stringResource(R.string.history_filter_all),
                                    color = Color.White
                                ) 
                            },
                            onClick = {
                                historyViewModel.setFilterType(null)
                                showFilterMenu = false
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.List,
                                    contentDescription = null,
                                    tint = Color.White
                                )
                            },
                            colors = MenuDefaults.itemColors(
                                textColor = Color.White
                            )
                        )
                        DropdownMenuItem(
                            text = { 
                                Text(
                                    stringResource(R.string.history_filter_speech),
                                    color = Color.White
                                ) 
                            },
                            onClick = {
                                historyViewModel.setFilterType(RecordType.SPEECH_RECOGNITION)
                                showFilterMenu = false
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Mic,
                                    contentDescription = null,
                                    tint = Color.White
                                )
                            },
                            colors = MenuDefaults.itemColors(
                                textColor = Color.White
                            )
                        )
                        DropdownMenuItem(
                            text = { 
                                Text(
                                    stringResource(R.string.history_filter_translation),
                                    color = Color.White
                                ) 
                            },
                            onClick = {
                                historyViewModel.setFilterType(RecordType.TRANSLATION)
                                showFilterMenu = false
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Translate,
                                    contentDescription = null,
                                    tint = Color.White
                                )
                            },
                            colors = MenuDefaults.itemColors(
                                textColor = Color.White
                            )
                        )
                    }
                }
                
                // 清空历史按钮
                var showClearDialog by remember { mutableStateOf(false) }
                
                IconButton(onClick = { showClearDialog = true }) {
                    Icon(
                        imageVector = Icons.Default.DeleteSweep,
                        contentDescription = stringResource(R.string.history_clear),
                        tint = Color.White
                    )
                }
                
                if (showClearDialog) {
                    AlertDialog(
                        onDismissRequest = { showClearDialog = false },
                        title = { Text(stringResource(R.string.history_clear_dialog_title), color = Color.White) },
                        text = { Text(stringResource(R.string.history_clear_dialog_message), color = Color.White) },
                        confirmButton = {
                            val clearSuccessText = stringResource(R.string.history_clear_success)
                            val confirmText = stringResource(R.string.history_confirm)


                            TextButton(
                                onClick = {
                                    historyViewModel.clearAllRecords()
                                    showClearDialog = false
                                    coroutineScope.launch {
                                        snackbarHostState.showSnackbar(clearSuccessText)
                                    }
                                }
                            ) {
                                Text(confirmText, color = purpleColor)
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showClearDialog = false }) {
                                Text(stringResource(R.string.history_cancel), color = Color.White)
                            }
                        },
                        containerColor = cardBgColor,
                        titleContentColor = Color.White,
                        textContentColor = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = darkBgColor,
                titleContentColor = Color.White
            )
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            // 搜索框
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { historyViewModel.setSearchQuery(it) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                placeholder = { 
                    Text(
                        stringResource(R.string.history_search_hint),
                        color = Color.White.copy(alpha = 0.6f)
                    ) 
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null,
                        tint = Color.White.copy(alpha = 0.7f)
                    )
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(onClick = { historyViewModel.setSearchQuery("") }) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = stringResource(R.string.history_clear_search),
                                tint = Color.White.copy(alpha = 0.7f)
                            )
                        }
                    }
                },
                singleLine = true,
                shape = RoundedCornerShape(24.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = purpleColor,
                    unfocusedBorderColor = Color.Gray,
                    focusedTextColor = Color.White,
                    unfocusedTextColor = Color.White,
                    cursorColor = purpleColor,
                    focusedContainerColor = cardBgColor,
                    unfocusedContainerColor = cardBgColor
                )
            )
            
            // 筛选标签
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.history_current_filter),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White.copy(alpha = 0.7f)
                )
                
                val filterTextRes = when (filterType) {
                    null -> R.string.history_filter_all
                    RecordType.SPEECH_RECOGNITION -> R.string.history_filter_speech
                    RecordType.TRANSLATION -> R.string.history_filter_translation
                }
                
                FilterChip(
                    selected = true,
                    onClick = { },
                    enabled = true,
                    label = { Text(stringResource(filterTextRes), color = Color.White) },
                    leadingIcon = {
                        val icon = when (filterType) {
                            null -> Icons.Default.List
                            RecordType.SPEECH_RECOGNITION -> Icons.Default.Mic
                            RecordType.TRANSLATION -> Icons.Default.Translate
                        }
                        Icon(
                            imageVector = icon,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = Color.White
                        )
                    },
                    colors = FilterChipDefaults.filterChipColors(
                        containerColor = purpleColor.copy(alpha = 0.3f),
                        labelColor = Color.White,
                        iconColor = Color.White,
                        selectedContainerColor = purpleColor.copy(alpha = 0.7f),
                        selectedLabelColor = Color.White,
                        selectedLeadingIconColor = Color.White
                    )
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 显示记录数量
                Text(
                    text = stringResource(R.string.history_total_records, filteredRecords.size),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.6f)
                )
            }
            
            if (filteredRecords.isEmpty()) {
                // 空记录提示
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = purpleColor.copy(alpha = 0.5f)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = stringResource(R.string.history_empty_title),
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = stringResource(R.string.history_empty_desc),
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.White.copy(alpha = 0.6f),
                            textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        )
                    }
                }
            } else {
                // 历史记录列表
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredRecords, key = { it.id }) { record ->
                        HistoryRecordItem(
                            record = record,
                            isExpanded = record.id == expandedRecordId,
                            onExpandToggle = {
                                expandedRecordId = if (expandedRecordId == record.id) null else record.id
                            },
                            onPlayTranslation = {
                                historyViewModel.playTranslatedText(record)
                            },
                            onFavoriteToggle = {
                                historyViewModel.toggleFavorite(record.id)
                            },
                            onDelete = {
                                historyViewModel.deleteRecord(record.id)
                            },
                            isPlaying = isPlaying && historyViewModel.selectedRecord.value?.id == record.id,
                            isLoading = isLoading && historyViewModel.selectedRecord.value?.id == record.id,
                            cardBgColor = cardBgColor,
                            purpleColor = purpleColor
                        )
                    }
                }
            }
        }
    }
    
    // SnackBar
    Box(modifier = Modifier.fillMaxSize()) {
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 16.dp),
            snackbar = { data ->
                Snackbar(
                    containerColor = cardBgColor,
                    contentColor = Color.White,
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(text = data.visuals.message)
                }
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryRecordItem(
    record: HistoryRecord,
    isExpanded: Boolean,
    onExpandToggle: () -> Unit,
    onPlayTranslation: () -> Unit,
    onFavoriteToggle: () -> Unit,
    onDelete: () -> Unit,
    isPlaying: Boolean,
    isLoading: Boolean,
    cardBgColor: Color = Color(0xFF33324A),
    purpleColor: Color = Color(0xFF8364FD)
) {
    val recordTypeColor = when (record.type) {
        RecordType.SPEECH_RECOGNITION -> purpleColor
        RecordType.TRANSLATION -> Color(0xFF64ACFD) // 蓝色变种
    }
    
    val recordTypeIcon = when (record.type) {
        RecordType.SPEECH_RECOGNITION -> Icons.Default.Mic
        RecordType.TRANSLATION -> Icons.Default.Translate
    }
    
    val elevation by animateFloatAsState(
        targetValue = if (isExpanded) 4f else 1f,
        label = "ElevationAnimation"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onExpandToggle),
        elevation = CardDefaults.cardElevation(defaultElevation = elevation.dp),
        colors = CardDefaults.cardColors(
            containerColor = cardBgColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            // 记录头部
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 记录类型图标
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(recordTypeColor.copy(alpha = 0.2f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = recordTypeIcon,
                        contentDescription = null,
                        tint = recordTypeColor
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 记录信息
                Column(modifier = Modifier.weight(1f)) {
                    // 记录类型和时间
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val recordTypeStringRes = when (record.type) {
                            RecordType.SPEECH_RECOGNITION -> R.string.history_filter_speech
                            RecordType.TRANSLATION -> R.string.history_filter_translation
                        }
                        
                        Text(
                            text = stringResource(recordTypeStringRes),
                            style = MaterialTheme.typography.labelMedium,
                            color = recordTypeColor,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = record.getShortTime(),
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.White.copy(alpha = 0.6f)
                        )
                    }
                    
                    // 源语言
                    Text(
                        text = record.sourceLanguageName.ifEmpty { record.sourceLanguage },
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
                
                // 收藏按钮
                IconButton(onClick = onFavoriteToggle) {
                    val favoriteText = if (record.isFavorite) 
                        stringResource(R.string.history_item_unfavorite) 
                    else 
                        stringResource(R.string.history_item_favorite)
                    
                    Icon(
                        imageVector = if (record.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                        contentDescription = favoriteText,
                        tint = if (record.isFavorite) Color.Red else Color.White.copy(alpha = 0.6f)
                    )
                }
                
                // 展开/折叠按钮
                IconButton(onClick = onExpandToggle) {
                    val expandCollapseText = if (isExpanded) 
                        stringResource(R.string.history_item_collapse) 
                    else 
                        stringResource(R.string.history_item_expand)
                    
                    Icon(
                        imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = expandCollapseText,
                        tint = Color.White
                    )
                }
            }
            
            // 源文本 (始终显示)
            Text(
                text = record.sourceText,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                maxLines = if (isExpanded) Int.MAX_VALUE else 2,
                overflow = if (isExpanded) TextOverflow.Visible else TextOverflow.Ellipsis,
                color = Color.White
            )
            
            // 展开内容
            AnimatedVisibility(
                visible = isExpanded,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Column {
                    if (record.translatedText.isNotEmpty()) {
                        Divider(
                            modifier = Modifier.padding(vertical = 8.dp),
                            color = Color.White.copy(alpha = 0.1f)
                        )
                        
                        // 翻译目标语言
                        Text(
                            text = record.targetLanguageName.ifEmpty { record.targetLanguage },
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.White.copy(alpha = 0.8f)
                        )
                        
                        // 翻译结果
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(purpleColor.copy(alpha = 0.3f))
                                .padding(12.dp)
                        ) {
                            Text(
                                text = record.translatedText,
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = Color.White
                            )
                        }
                        
                        // 操作按钮
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            // 播放翻译按钮
                            Button(
                                onClick = onPlayTranslation,
                                enabled = !isPlaying && !isLoading,
                                modifier = Modifier.padding(end = 8.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = purpleColor,
                                    contentColor = Color.White,
                                    disabledContainerColor = purpleColor.copy(alpha = 0.4f),
                                    disabledContentColor = Color.White.copy(alpha = 0.4f)
                                )
                            ) {
                                val loadingText = stringResource(R.string.history_item_loading)
                                val stopText = stringResource(R.string.history_item_stop)
                                val playText = stringResource(R.string.history_item_play)
                                
                                when {
                                    isLoading -> {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(20.dp),
                                            strokeWidth = 2.dp,
                                            color = Color.White
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(loadingText)
                                    }
                                    isPlaying -> {
                                        Icon(
                                            imageVector = Icons.Default.Stop,
                                            contentDescription = stopText
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(stopText)
                                    }
                                    else -> {
                                        Icon(
                                            imageVector = Icons.Default.PlayArrow,
                                            contentDescription = playText
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(playText)
                                    }
                                }
                            }
                            
                            // 删除按钮
                            OutlinedButton(
                                onClick = onDelete,
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = Color.Red.copy(alpha = 0.8f)
                                ),
                                border = BorderStroke(
                                    width = 1.dp,
                                    color = Color.Red.copy(alpha = 0.5f)
                                )
                            ) {
                                val deleteText = stringResource(R.string.history_item_delete)
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = deleteText
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(deleteText)
                            }
                        }
                    } else {
                        // 仅有语音识别，没有翻译的情况
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            // 删除按钮
                            OutlinedButton(
                                onClick = onDelete,
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = Color.Red.copy(alpha = 0.8f)
                                ),
                                border = BorderStroke(
                                    width = 1.dp,
                                    color = Color.Red.copy(alpha = 0.5f)
                                )
                            ) {
                                val deleteText = stringResource(R.string.history_item_delete)
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = deleteText
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(deleteText)
                            }
                        }
                    }
                }
            }
        }
    }
} 