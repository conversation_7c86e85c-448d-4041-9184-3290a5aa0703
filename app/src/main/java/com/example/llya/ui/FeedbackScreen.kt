package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.foundation.clickable

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedbackScreen(
    navController: NavController? = null,
    onFeedbackSubmit: (String, String, String, Float) -> Unit = { _, _, _, _ -> }
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val cardBackgroundColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    val scrollState = rememberScrollState()
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    
    var feedbackType by remember { mutableStateOf("功能建议") }
    var email by remember { mutableStateOf("") }
    var feedbackContent by remember { mutableStateOf("") }
    var rating by remember { mutableStateOf(5f) }
    var isSubmitting by remember { mutableStateOf(false) }
    var showSuccessDialog by remember { mutableStateOf(false) }
    
    // 表单验证
    val isEmailValid = email.isEmpty() || email.matches(Regex(".+@.+\\..+"))
    val isContentValid = feedbackContent.length >= 10
    val isFormValid = isEmailValid && isContentValid
    
    if (showSuccessDialog) {
        AlertDialog(
            onDismissRequest = { 
                showSuccessDialog = false
                navController?.popBackStack()
            },
            title = { Text("提交成功") },
            text = { Text("感谢您的反馈，我们将认真阅读并尽快处理！") },
            confirmButton = {
                Button(
                    onClick = {
                        showSuccessDialog = false
                        navController?.popBackStack()
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = purpleColor
                    )
                ) {
                    Text("确定")
                }
            },
            containerColor = cardBackgroundColor,
            titleContentColor = Color.White,
            textContentColor = Color.White
        )
    }
    
    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        containerColor = backgroundColor,
        contentColor = Color.White,
        modifier = Modifier
            .statusBarsPadding()
            .navigationBarsPadding()
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 顶部导航栏
            TopAppBar(
                title = { 
                    Text(
                        text = "意见反馈",
                        color = Color.White
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController?.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = backgroundColor,
                    titleContentColor = Color.White
                )
            )
            
            // 主内容区域
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 反馈说明
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "我们非常重视您的反馈，它将帮助我们不断改进产品。请填写以下表单，分享您的想法和建议。",
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 14.sp,
                            lineHeight = 20.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 反馈类型
                Text(
                    text = "反馈类型",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 反馈类型选择
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        FeedbackTypeOption(
                            title = "功能建议",
                            selected = feedbackType == "功能建议",
                            onClick = { feedbackType = "功能建议" },
                            purpleColor = purpleColor
                        )
                        
                        FeedbackTypeOption(
                            title = "问题反馈",
                            selected = feedbackType == "问题反馈",
                            onClick = { feedbackType = "问题反馈" },
                            purpleColor = purpleColor
                        )
                        
                        FeedbackTypeOption(
                            title = "使用体验",
                            selected = feedbackType == "使用体验",
                            onClick = { feedbackType = "使用体验" },
                            purpleColor = purpleColor
                        )
                        
                        FeedbackTypeOption(
                            title = "其他",
                            selected = feedbackType == "其他",
                            onClick = { feedbackType = "其他" },
                            purpleColor = purpleColor
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 满意度评分
                Text(
                    text = "满意度评分",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 评分滑块
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = when {
                                rating <= 1 -> "非常不满意"
                                rating <= 2 -> "不满意"
                                rating <= 3 -> "一般"
                                rating <= 4 -> "满意"
                                else -> "非常满意"
                            },
                            color = when {
                                rating <= 2 -> Color.Red
                                rating <= 3 -> Color.Yellow
                                else -> Color.Green
                            },
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 星级评分
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.Center
                        ) {
                            for (i in 1..5) {
                                IconButton(
                                    onClick = { rating = i.toFloat() }
                                ) {
                                    Icon(
                                        imageVector = if (i <= rating) Icons.Default.Star else Icons.Default.StarOutline,
                                        contentDescription = "评分 $i",
                                        tint = if (i <= rating) Color.Yellow else Color.Gray,
                                        modifier = Modifier.size(36.dp)
                                    )
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 评分滑块
                        Slider(
                            value = rating,
                            onValueChange = { rating = it },
                            valueRange = 1f..5f,
                            steps = 3,
                            colors = SliderDefaults.colors(
                                thumbColor = purpleColor,
                                activeTrackColor = purpleColor
                            )
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("1", color = Color.White)
                            Text("5", color = Color.White)
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 联系方式（邮箱）
                Text(
                    text = "联系邮箱（选填）",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("请输入您的邮箱地址", color = Color.White.copy(alpha = 0.5f)) },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    singleLine = true,
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        cursorColor = purpleColor,
                        focusedBorderColor = purpleColor,
                        unfocusedBorderColor = Color.Gray,
                        containerColor = cardBackgroundColor
                    ),
                    isError = !isEmailValid,
                    supportingText = {
                        if (!isEmailValid) {
                            Text(
                                text = "请输入有效的邮箱地址",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 反馈内容
                Text(
                    text = "反馈内容",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = feedbackContent,
                    onValueChange = { feedbackContent = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    placeholder = { Text("请详细描述您的问题或建议...", color = Color.White.copy(alpha = 0.5f)) },
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        cursorColor = purpleColor,
                        focusedBorderColor = purpleColor,
                        unfocusedBorderColor = Color.Gray,
                        containerColor = cardBackgroundColor
                    ),
                    isError = !isContentValid && feedbackContent.isNotEmpty(),
                    supportingText = {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            if (!isContentValid && feedbackContent.isNotEmpty()) {
                                Text(
                                    text = "内容至少需要10个字符",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            Text(
                                text = "${feedbackContent.length}/500",
                                color = if (feedbackContent.length > 500) MaterialTheme.colorScheme.error else Color.White.copy(alpha = 0.7f)
                            )
                        }
                    }
                )
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // 提交按钮
                Button(
                    onClick = {
                        isSubmitting = true
                        // 模拟提交过程
                        scope.launch {
                            // 实际应用中，这里应该调用真实的API
                            kotlinx.coroutines.delay(1000)
                            onFeedbackSubmit(feedbackType, email, feedbackContent, rating)
                            isSubmitting = false
                            showSuccessDialog = true
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = isFormValid && !isSubmitting && feedbackContent.isNotEmpty(),
                    shape = RoundedCornerShape(12.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = purpleColor,
                        disabledContainerColor = purpleColor.copy(alpha = 0.5f)
                    )
                ) {
                    if (isSubmitting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "提交反馈",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

@Composable
fun FeedbackTypeOption(
    title: String,
    selected: Boolean,
    onClick: () -> Unit,
    purpleColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = selected,
            onClick = onClick,
            enabled = true,
            colors = RadioButtonDefaults.colors(
                selectedColor = purpleColor,
                unselectedColor = Color.White
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = title,
            color = Color.White,
            fontSize = 16.sp,
            modifier = Modifier.clickable(onClick = onClick)
        )
    }
} 