package com.example.llya.utils

import android.content.Context
import com.example.llya.LlyaApplication
import com.example.llya.data.UserModel
import com.example.llya.network.UserManager

/**
 * 用户会话管理类
 * 
 * 提供简单的静态方法，方便在应用的任何地方获取当前用户信息
 */
object UserSession {
    /**
     * 获取当前登录用户
     */
    fun getCurrentUser(): UserModel? {
        return UserCache.currentUser.value
    }
    
    /**
     * 获取当前登录Token
     */
    fun getToken(): String {
        return UserCache.getToken()
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(): Boolean {
        return UserCache.isLoggedIn()
    }
    
    /**
     * 获取用户ID
     */
    fun getUserId(): Int {
        return UserCache.getUserId()
    }
    
    /**
     * 获取用户名
     */
    fun getUsername(): String {
        return UserCache.getUsername()
    }
    
    /**
     * 获取用户邮箱
     */
    fun getEmail(): String {
        return UserCache.getEmail()
    }
    
    /**
     * 退出登录
     */
    fun logout(context: Context) {
        UserManager.getInstance(context).logout()
    }
} 