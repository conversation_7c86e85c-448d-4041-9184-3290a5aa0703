package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.SystemUpdate
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.utils.AppUpdateHelper
import com.example.llya.viewmodel.AppUpdateViewModel
import androidx.activity.ComponentActivity

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UpdateTestScreen(
    navController: NavController? = null
) {
    val backgroundColor = Color(0xFF1E1D2B)
    val cardColor = Color(0xFF272636)
    val purpleColor = Color(0xFF8364FD)
    
    val context = LocalContext.current
    val activity = context as ComponentActivity
    
    // 获取更新ViewModel
    val appUpdateViewModel: AppUpdateViewModel = viewModel()
    
    // 观察状态
    val updateInfo by appUpdateViewModel.updateInfo.collectAsState()
    val showUpdateDialog by appUpdateViewModel.showUpdateDialog.collectAsState()
    val isLoading by appUpdateViewModel.isLoading.collectAsState()
    val errorMessage by appUpdateViewModel.errorMessage.collectAsState()
    
    // 获取当前版本信息
    val currentVersionInfo = remember { appUpdateViewModel.getCurrentVersionInfo() }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    text = "更新测试",
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor
            )
        )
        
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 当前版本信息卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = cardColor),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "当前版本信息",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "版本名称: ${currentVersionInfo.first}",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 14.sp
                    )
                    Text(
                        text = "版本号: ${currentVersionInfo.second}",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 14.sp
                    )
                }
            }
            
            // 检查更新按钮
            Button(
                onClick = { 
                    AppUpdateHelper.checkForUpdates(activity)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(containerColor = purpleColor),
                enabled = !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("检查中...")
                } else {
                    Icon(
                        imageVector = Icons.Default.SystemUpdate,
                        contentDescription = null,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text("检查更新")
                }
            }
            
            // 手动检查更新按钮
            OutlinedButton(
                onClick = { 
                    AppUpdateHelper.manualCheckUpdate(activity)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = purpleColor
                ),
                enabled = !isLoading
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("手动检查更新")
            }
            
            // 错误信息显示
            errorMessage?.let { error ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF4A2C2A) // 深红色背景
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = error,
                            color = Color(0xFFFFCDD2), // 浅红色文字
                            fontSize = 14.sp,
                            modifier = Modifier.weight(1f)
                        )
                        TextButton(
                            onClick = { appUpdateViewModel.clearErrorMessage() }
                        ) {
                            Text(
                                text = "关闭",
                                color = Color(0xFFFFCDD2)
                            )
                        }
                    }
                }
            }
            
            // API信息说明
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = cardColor),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "API接口信息",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "接口地址: http://8.138.134.96/api/version/checkUpdate",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                    Text(
                        text = "请求方式: POST",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                    Text(
                        text = "平台: android",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                    Text(
                        text = "当前版本号: ${currentVersionInfo.second}",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
    
    // 显示更新对话框
    if (showUpdateDialog && updateInfo != null) {
        AppUpdateDialog(
            updateInfo = updateInfo!!,
            onDownload = {
                appUpdateViewModel.downloadApp()
            },
            onDismiss = {
                appUpdateViewModel.dismissUpdateDialog()
            }
        )
    }
}
