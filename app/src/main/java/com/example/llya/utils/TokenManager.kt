package com.example.llya.utils

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.core.content.ContextCompat

/**
 * Token管理工具类
 * 
 * 用于处理token过期、刷新等相关逻辑
 */
object TokenManager {
    private const val TAG = "TokenManager"
    
    // Token过期状态LiveData，便于观察者模式通知UI
    private val _tokenExpired = MutableLiveData<Boolean>(false)
    val tokenExpired = _tokenExpired
    
    // 默认token有效期为7天（单位：毫秒）
    const val DEFAULT_TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000L
    
    /**
     * 检查token是否过期
     * 
     * @param loginTime 登录时间（毫秒时间戳）
     * @param tokenTtl token有效期（毫秒）
     * @return 是否过期
     */
    fun isTokenExpired(loginTime: Long, tokenTtl: Long = DEFAULT_TOKEN_EXPIRE_TIME): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeElapsed = currentTime - loginTime
        val isExpired = timeElapsed > tokenTtl
        
        Log.d(TAG, "检查Token是否过期: 当前时间=$currentTime, 登录时间=$loginTime, " +
            "已过时间=${timeElapsed/1000/60}分钟, 过期时间=${tokenTtl/1000/60}分钟, 是否过期=$isExpired")
        
        // 保证用户至少可以使用15天，除非非常明确地超过了过期时间
        if (isExpired && timeElapsed < (15 * 24 * 60 * 60 * 1000L)) {
            Log.d(TAG, "Token虽然过期但在安全范围内，仍然视为有效")
            return false
        }
        
        return isExpired
    }
    
    /**
     * 处理token过期
     * 
     * @param context 上下文
     * @param clearData 是否清除用户数据
     */
    fun handleTokenExpired(context: Context, clearData: Boolean = true) {
        Log.d(TAG, "Token已过期，需要重新登录")
        
        // 标记token已过期
        _tokenExpired.value = true
        
        // 清除用户数据
        if (clearData) {
            UserCache.clearUserInfo()
        }
        
        // 跳转到登录页面
        navigateToLogin(context)
    }
    
    /**
     * 重置token过期状态
     */
    fun resetTokenExpiredState() {
        _tokenExpired.value = false
    }
    
    /**
     * 导航到登录页面
     */
    private fun navigateToLogin(context: Context) {
        try {
            // 使用包名动态查找LoginScreen活动
            val packageName = context.packageName
            val loginActivityIntent = Intent("$packageName.LOGIN_ACTION")
            loginActivityIntent.setPackage(packageName)
            loginActivityIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            loginActivityIntent.putExtra("TOKEN_EXPIRED", true)
            
            context.startActivity(loginActivityIntent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转到登录页面失败", e)
        }
    }
} 