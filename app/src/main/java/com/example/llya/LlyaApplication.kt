package com.example.llya

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.example.llya.data.HistoryManager
import com.example.llya.network.LanguageManager
import com.example.llya.network.UserManager
import com.example.llya.utils.LanguageUtils
import com.example.llya.utils.UserCache
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.*

/**
 * 应用程序入口类
 * 用于全局初始化和管理应用级资源
 */
class LlyaApplication : Application() {
    
    companion object {
        private const val TAG = "LlyaApplication"
        
        // 单例Application实例
        private lateinit var instance: LlyaApplication
        
        fun getInstance(): LlyaApplication {
            return instance
        }
    }
    
    // 应用级协程作用域
    val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        Log.d(TAG, "应用程序初始化开始")
        
        // 注册应用生命周期观察者
        registerActivityLifecycleCallbacks(appLifecycleCallbacks)
        
        // 初始化全局资源
        initializeResources()
        
        Log.d(TAG, "应用程序初始化完成")
    }
    
    // 活动生命周期回调
    private val appLifecycleCallbacks = object : ActivityLifecycleCallbacks {
        var activityCounter = 0
        
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            Log.d(TAG, "活动创建: ${activity.javaClass.simpleName}")
            
            try {
                // 确保每个Activity都应用正确的语言设置
                val sharedPrefs = activity.getSharedPreferences(LanguageUtils.LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
                val savedLanguageName = sharedPrefs.getString(LanguageUtils.SELECTED_LANGUAGE_KEY, null)
                
                if (savedLanguageName != null) {
                    // 如果有保存的语言设置，从显示名称获取语言代码
                    val languageCode = LanguageUtils.getLanguageCodeFromDisplayName(savedLanguageName)
                    if (languageCode != null) {
                        Log.d(TAG, "为${activity.javaClass.simpleName}应用语言设置: $savedLanguageName ($languageCode)")
                        val locale = createLocaleFromCode(languageCode)
                        Locale.setDefault(locale) // 先设置默认Locale
                        applyLocaleToActivity(activity, locale)
                    } else {
                        Log.w(TAG, "无法解析保存的语言名称: $savedLanguageName，使用系统默认语言")
                        // 使用当前系统默认语言
                        applyLocaleToActivity(activity, Locale.getDefault())
                    }
                } else {
                    // 如果没有保存的语言设置，使用当前系统设置的语言
                    Log.d(TAG, "没有保存的语言设置，对${activity.javaClass.simpleName}使用系统默认语言")
                    val currentLocale = Locale.getDefault()
                    applyLocaleToActivity(activity, currentLocale)
                }
            } catch (e: Exception) {
                Log.e(TAG, "为Activity应用语言设置时出错", e)
                try {
                    // 出错时使用系统默认语言
                    applyLocaleToActivity(activity, Locale.getDefault())
                } catch (ex: Exception) {
                    Log.e(TAG, "应用默认语言设置时出错", ex)
                }
            }
        }
        
        override fun onActivityStarted(activity: Activity) {
            if (activityCounter == 0) {
                // 应用从后台进入前台
                Log.d(TAG, "应用进入前台，检查登录状态")
                // 重新加载用户信息
                UserCache.initialize(this@LlyaApplication)
            }
            activityCounter++
        }
        
        override fun onActivityResumed(activity: Activity) {}
        
        override fun onActivityPaused(activity: Activity) {}
        
        override fun onActivityStopped(activity: Activity) {
            activityCounter--
            if (activityCounter == 0) {
                // 应用进入后台
                Log.d(TAG, "应用进入后台")
            }
        }
        
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
        
        override fun onActivityDestroyed(activity: Activity) {}
        
        // 帮助方法：创建Locale对象
        private fun createLocaleFromCode(languageCode: String): Locale {
            val parts = languageCode.split("-")
            return when {
                parts.size > 1 -> Locale(parts[0], parts[1])
                else -> Locale(parts[0])
            }
        }
        
        // 帮助方法：将语言设置应用到Activity
        private fun applyLocaleToActivity(activity: Activity, locale: Locale) {
            try {
                Locale.setDefault(locale)
                
                val resources = activity.resources
                val configuration = Configuration(resources.configuration)
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    configuration.setLocale(locale)
                } else {
                    @Suppress("DEPRECATION")
                    configuration.locale = locale
                }
                
                resources.updateConfiguration(configuration, resources.displayMetrics)
                
                Log.d(TAG, "成功为${activity.javaClass.simpleName}应用语言设置: ${locale.language}-${locale.country}")
            } catch (e: Exception) {
                Log.e(TAG, "应用语言设置到Activity时出错", e)
            }
        }
    }
    
    override fun attachBaseContext(base: Context) {
        // 使用语言帮助类包装Context，确保应用正确的语言设置
        val wrappedContext = com.example.llya.utils.LanguageHelper.wrapContext(base)
        super.attachBaseContext(wrappedContext)
        
        // 确保在最早的时候初始化用户缓存
        UserCache.initialize(wrappedContext)
        // 初始化语言工具类
        LanguageUtils.initialize(wrappedContext)
        Log.d(TAG, "应用程序 attachBaseContext 中初始化用户缓存和语言工具类")
    }
    
    /**
     * 初始化应用所需的全局资源
     */
    private fun initializeResources() {
        // 初始化历史记录管理器
        HistoryManager.initialize(this)
        Log.d(TAG, "历史记录管理器初始化完成")
        
        // 再次确保用户缓存初始化（双重保障）
        UserCache.initialize(this)
        Log.d(TAG, "用户缓存初始化完成")
        
        // 初始化用户信息管理工具
        UserManager.getInstance(this)
        Log.d(TAG, "用户信息管理工具初始化完成")
        
        // 初始化语言工具类
        LanguageUtils.initialize(this)
        Log.d(TAG, "语言工具类初始化完成，当前应用语言：${LanguageUtils.getCurrentLanguageDisplayName(this)}")
        
        // 初始化语言管理器并获取语言列表
        val languageManager = LanguageManager.getInstance(this)
        
        // 在后台获取语言列表
        applicationScope.launch {
            try {
                Log.d(TAG, "开始从API获取语言列表")
                val result = languageManager.fetchLanguages()
                
                if (result) {
                    Log.d(TAG, "成功从API获取语言列表并更新LanguageUtils")
                } else {
                    Log.w(TAG, "从API获取语言列表失败，使用默认语言列表")
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取语言列表时发生异常", e)
                // 如果发生异常，确保使用默认语言列表
                LanguageUtils.resetToDefaultLanguages()
            }
        }
        
        // 在这里添加其他必要的全局资源初始化代码
        // 例如：初始化数据库、网络连接、缓存等
    }
    
    override fun onTerminate() {
        // 释放资源
        Log.d(TAG, "应用程序终止")
        super.onTerminate()
    }
} 