package com.example.llya.network

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * 版本更新API接口
 */
interface UpdateApiService {
    
    @POST("api/version/checkUpdate")
    suspend fun checkUpdate(@Body request: CheckUpdateRequest): Response<CheckUpdateResponse>
}

/**
 * 检查更新请求参数
 */
data class CheckUpdateRequest(
    val platform: String,
    val versionCode: String
)

/**
 * 检查更新响应数据
 */
data class CheckUpdateResponse(
    val status: Int,
    val data: UpdateData?,
    val message: String?
)

/**
 * 更新数据
 */
data class UpdateData(
    val hasUpdate: Boolean,
    val currentVersion: String,
    val message: String,
    val latestVersion: String? = null,
    val versionCode: Int? = null,
    val forceUpdate: Boolean? = null,
    val downloadUrl: String? = null,
    val releaseNotes: String? = null,
    val publishTime: String? = null
)
