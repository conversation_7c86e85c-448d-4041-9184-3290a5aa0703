package com.example.llya.asr;

import android.content.Context;
import android.util.Log;
import android.os.Handler;
import android.os.Looper;

import com.microsoft.cognitiveservices.speech.AutoDetectSourceLanguageConfig;
import com.microsoft.cognitiveservices.speech.CancellationDetails;
import com.microsoft.cognitiveservices.speech.CancellationReason;
import com.microsoft.cognitiveservices.speech.PropertyId;
import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SessionEventArgs;
import com.microsoft.cognitiveservices.speech.SourceLanguageConfig;
import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechRecognitionCanceledEventArgs;
import com.microsoft.cognitiveservices.speech.SpeechRecognitionEventArgs;
import com.microsoft.cognitiveservices.speech.SpeechRecognitionResult;
import com.microsoft.cognitiveservices.speech.SpeechRecognizer;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import com.microsoft.cognitiveservices.speech.translation.SpeechTranslationConfig;
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionCanceledEventArgs;
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionEventArgs;
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionResult;
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognizer;
import com.microsoft.cognitiveservices.speech.util.EventHandler;
import com.example.llya.debug.SpeechDebugger;

import java.util.ArrayList;
import java.util.HashMap;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.NetworkCapabilities;
import android.os.Build;

/**
 * 微软语音识别器
 * 使用官方Speech SDK实现语音识别和翻译
 */
public class MicrosoftSpeechRecognizer {
    private static final String TAG = "MicrosoftSpeechRecognizer";

    // 微软语音服务配置
    private final String subscriptionKey;
    private final String region;
    private final Context context;

    // 语音识别器
    private SpeechRecognizer speechRecognizer;
    private TranslationRecognizer translationRecognizer;

    // 自动语言检测配置
    private AutoDetectSourceLanguageConfig autoDetectConfig;

    // 用于等待识别结果的信号量
    private final Semaphore stopRecognitionSemaphore = new Semaphore(0);

    // 识别结果回调
    private OnRecognitionResultListener recognitionResultListener;
    private OnTranslationResultListener translationResultListener;
    private OnLanguageDetectionListener languageDetectionListener;
    private OnErrorListener errorListener;

    // 是否正在识别
    private boolean isRecognizing = false;

    // 性能监测
    private long startRecognitionTime = 0;
    private long firstResultTime = 0;
    private boolean hasReceivedFirstResult = false;
    private long lastRequestTime = 0;
    private long lastResponseTime = 0;
    private long totalResponseTime = 0;
    private int responseCount = 0;

    // 网络状态监测
    private boolean isNetworkIssueDetected = false;
    private boolean isServerIssueDetected = false;

    // 支持的语言映射
    public static final Map<String, String> SUPPORTED_LANGUAGES = new HashMap<String, String>() {{
        put("zh-CN", "中文");
        put("en-US", "英语");
        put("ja-JP", "日语");
        put("ko-KR", "韩语");
        put("fr-FR", "法语");
        put("de-DE", "德语");
        put("es-ES", "西班牙语");
        put("it-IT", "意大利语");
        put("ru-RU", "俄语");
    }};

    /**
     * 构造函数
     *
     * @param context        上下文
     * @param subscriptionKey 订阅密钥
     * @param region         区域
     */
    public MicrosoftSpeechRecognizer(Context context, String subscriptionKey, String region) {
        this.context = context;
        this.subscriptionKey = subscriptionKey;
        this.region = region;
    }

    /**
     * 开始语音识别
     *
     * @param sourceLanguage 源语言代码，例如zh-CN、en-US等，多语言模式下用逗号分隔，如"zh-CN,en-US,ja-JP"
     */
    public void startRecognition(String sourceLanguage) {
        if (isRecognizing) {
            Log.d(TAG, "已经在识别中，忽略请求");
            return;
        }

        try {
            // 清除之前的识别器
            if (speechRecognizer != null) {
                speechRecognizer.close();
                speechRecognizer = null;
            }

            // 检查是否是多语言模式
            boolean isMultiLanguageMode = sourceLanguage.contains(",");
            Log.d(TAG, "源语言: " + sourceLanguage + ", 是否为多语言模式: " + isMultiLanguageMode);

            // 创建语音配置
            SpeechConfig speechConfig;

            if (isMultiLanguageMode) {
                // 对于多语言模式，使用标准配置
                Log.d(TAG, "使用标准配置进行多语言识别");
                speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);

                // 设置语言检测模式
                // 使用Continuous模式，持续检测语言
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdMode", "Continuous");

                // 设置语言检测的优先级为高优先级，确保能够及时检测语言变化
                speechConfig.setProperty("SpeechServiceConnection_LanguageDetectionTaskPriority", "High");
                Log.d(TAG, "设置语言检测模式: Continuous (持续检测语言)");

                // 解析多个语言
                String[] languages = sourceLanguage.split(",");
                StringBuilder languageList = new StringBuilder();

                // 构建语言列表（使用分号分隔，符合微软API要求）
                for (int i = 0; i < languages.length; i++) {
                    String lang = languages[i].trim().toLowerCase();

                    languageList.append(lang);
                    if (i < languages.length - 1) {
                        languageList.append(",");
                    }
                }

                // 设置语言列表属性 - 使用英语、中文和日语（与微软示例一致）
                String supportedLanguages = "en-us;zh-cn;ja-jp";
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", supportedLanguages);
                Log.d(TAG, "设置固定的语言列表: " + supportedLanguages);

                // 设置为平衡模式，兼顾延迟和准确性
                speechConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Latency");

                // 设置语言检测成功后的行为
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize");

                // 设置语言检测未知时的行为
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize");

                // 设置默认识别语言为中文（使用小写区域代码）
                speechConfig.setSpeechRecognitionLanguage("zh-cn");
                speechConfig.setProperty("SpeechServiceConnection_RecoLanguage", "zh-cn");
                Log.d(TAG, "设置默认识别语言: zh-cn");
            } else {
                // 对于单语言模式，使用标准配置
                speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);
                // 设置语音识别语言
                speechConfig.setSpeechRecognitionLanguage(sourceLanguage);
            }

            // 设置详细输出格式
            speechConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText");

            // 创建音频配置（使用默认麦克风）
            AudioConfig audioConfig = AudioConfig.fromDefaultMicrophoneInput();

            // 创建语音识别器
            if (isMultiLanguageMode) {
                // 创建语言配置列表
                String[] languageArray = sourceLanguage.split(",");
                ArrayList<String> languageList = new ArrayList<>();
                for (String lang : languageArray) {
                    languageList.add(lang.trim());
                }

                // 创建SourceLanguageConfig列表，类似于微软官方示例
                Log.d(TAG, "创建SourceLanguageConfig列表，语言列表: " + languageList);

                // 创建SourceLanguageConfig列表
                List<SourceLanguageConfig> sourceLanguageConfigs = new ArrayList<>();

                // 为每种语言创建SourceLanguageConfig
                for (String lang : languageList) {
                    // 这里可以添加自定义模型ID，如果有的话
                    sourceLanguageConfigs.add(SourceLanguageConfig.fromLanguage(lang));
                    Log.d(TAG, "添加语言配置: " + lang);
                }

                // 创建自动检测源语言配置
                AutoDetectSourceLanguageConfig autoDetectSourceLanguageConfig =
                    AutoDetectSourceLanguageConfig.fromSourceLanguageConfigs(sourceLanguageConfigs);

                Log.d(TAG, "已创建AutoDetectSourceLanguageConfig，包含 " + sourceLanguageConfigs.size() + " 种语言");

                // 使用AutoDetectSourceLanguageConfig创建语音识别器
                speechRecognizer = new SpeechRecognizer(speechConfig, autoDetectSourceLanguageConfig, audioConfig);

                // 释放资源
                for (SourceLanguageConfig config : sourceLanguageConfigs) {
                    config.close();
                }

                Log.d(TAG, "已创建带有自动语言检测的语音识别器");
            } else {
                // 对于单语言模式，使用标准配置
                speechRecognizer = new SpeechRecognizer(speechConfig, audioConfig);
            }

            // 注册事件处理程序
            speechRecognizer.recognizing.addEventListener((o, e) -> {
                String text = e.getResult().getText();
                Log.d(TAG, "识别中: " + text);
                
                // 检查回调是否已设置
                if (recognitionResultListener != null) {
                    // 通过UI线程更新，确保回调不会在后台线程上执行
                    new Handler(Looper.getMainLooper()).post(() -> {
                        Log.d(TAG, "触发onRecognizing回调，文本长度：" + text.length());
                        recognitionResultListener.onRecognizing(text);
                    });
                } else {
                    Log.w(TAG, "识别结果回调未设置，无法通知识别中事件");
                }
            });

            speechRecognizer.recognized.addEventListener((o, e) -> {
                SpeechRecognitionResult result = e.getResult();
                
                if (result.getReason() == ResultReason.RecognizedSpeech) {
                    String text = result.getText();
                    Log.d(TAG, "识别完成: " + text);
                    
                    // 检查识别文本是否有内容
                    if (text != null && !text.trim().isEmpty()) {
                        // 检查回调是否已设置
                        if (recognitionResultListener != null) {
                            // 通过UI线程更新，确保回调不会在后台线程上执行
                            new Handler(Looper.getMainLooper()).post(() -> {
                                Log.d(TAG, "触发onRecognized回调，文本长度：" + text.length());
                                recognitionResultListener.onRecognized(text);
                            });
                        } else {
                            Log.w(TAG, "识别结果回调未设置，无法通知识别完成事件");
                        }
                        
                        // 检查是否有语言检测回调
                        if (languageDetectionListener != null) {
                            // 尝试从识别结果中提取语言信息
                            try {
                                String detectedLanguage = null;
                                
                                if (result.getProperties() != null) {
                                    detectedLanguage = result.getProperties().getProperty("RESULT_LANGUAGES");
                                    
                                    // 如果未获取到语言信息，使用备选字段
                                    if (detectedLanguage == null || detectedLanguage.isEmpty()) {
                                        detectedLanguage = result.getProperties().getProperty("SpeechServiceConnection_AutoDetectSourceLanguageResult");
                                    }
                                }
                                
                                // 如果获取到语言代码，触发回调
                                if (detectedLanguage != null && !detectedLanguage.isEmpty()) {
                                    final String finalLang = detectedLanguage;
                                    new Handler(Looper.getMainLooper()).post(() -> {
                                        Log.d(TAG, "触发onLanguageDetected回调，检测到语言：" + finalLang);
                                        languageDetectionListener.onLanguageDetected(finalLang, 0, 0);
                                    });
                                }
                            } catch (Exception ex) {
                                Log.e(TAG, "获取语言信息失败", ex);
                            }
                        }
                    } else {
                        Log.w(TAG, "识别结果为空，不触发回调");
                    }
                } else if (result.getReason() == ResultReason.NoMatch) {
                    Log.d(TAG, "无法识别语音");
                }
                
                // 修改回来：在识别完成后重新启动识别流程，确保正确检测和切换语言
                // 微软语音SDK需要在每句话结束后重启识别会话才能正确切换语言
                if (isRecognizing) {
                    Log.d(TAG, "重启识别流程，确保语言检测和切换正确工作");
                    // 使用非阻塞方式停止并立即重启识别
                    try {
                        // 异步停止识别，但立即重启
                        speechRecognizer.stopContinuousRecognitionAsync();
                        
                        // 更短的延迟重启，减少中断时间
                        new Thread(() -> {
                            try {
                                // 等待较短时间确保停止完成
                                Thread.sleep(300);
                                
                                // 检查是否仍然需要识别
                                if (isRecognizing && speechRecognizer != null) {
                                    // 重新启动识别
                                    speechRecognizer.startContinuousRecognitionAsync();
                                    Log.d(TAG, "识别已快速重启，以保持语言识别灵敏度");
                                }
                            } catch (Exception ex) {
                                Log.e(TAG, "重启识别失败", ex);
                            }
                        }).start();
                    } catch (Exception ex) {
                        Log.e(TAG, "停止识别失败", ex);
                    }
                }
            });

            speechRecognizer.canceled.addEventListener((o, e) -> {
                Log.d(TAG, "识别取消: " + e.getReason());
                if (e.getReason() == CancellationReason.Error) {
                    Log.e(TAG, "错误详情: " + e.getErrorCode() + ", " + e.getErrorDetails());
                    if (errorListener != null) {
                        errorListener.onError("识别错误: " + e.getErrorDetails());
                    }
                }
                stopRecognitionSemaphore.release();
            });

            speechRecognizer.sessionStopped.addEventListener((o, e) -> {
                Log.d(TAG, "识别会话已停止");
                stopRecognitionSemaphore.release();
            });

            // 开始连续识别
            isRecognizing = true;
            speechRecognizer.startContinuousRecognitionAsync();

            Log.d(TAG, "开始语音识别，语言: " + sourceLanguage);
        } catch (Exception e) {
            Log.e(TAG, "启动语音识别失败", e);
            if (errorListener != null) {
                errorListener.onError("启动语音识别失败: " + e.getMessage());
            }
            isRecognizing = false;
        }
    }

    /**
     * 开始语音翻译
     *
     * @param sourceLanguage 源语言代码，例如zh-CN、en-US等
     * @param targetLanguage 目标语言代码，例如zh-CN、en-US等
     */
    public void startTranslation(String sourceLanguage, String targetLanguage) {
        startTranslation(sourceLanguage, targetLanguage, true);
    }

    /**
     * 检查网络状态
     * 返回网络状态信息，用于诊断网络问题
     */
    private String checkNetworkStatus() {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            Log.e(TAG, "无法获取ConnectivityManager");
            return "无法获取网络状态";
        }

        StringBuilder networkInfo = new StringBuilder();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(connectivityManager.getActiveNetwork());
            if (capabilities != null) {
                if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    networkInfo.append("网络类型: WiFi, ");
                } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    networkInfo.append("网络类型: 移动数据, ");
                } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                    networkInfo.append("网络类型: 以太网, ");
                } else {
                    networkInfo.append("网络类型: 其他, ");
                }

                // 获取下行带宽（以Kbps为单位）
                int downSpeed = capabilities.getLinkDownstreamBandwidthKbps();
                int upSpeed = capabilities.getLinkUpstreamBandwidthKbps();
                networkInfo.append("下行速度: ").append(downSpeed).append("Kbps, ");
                networkInfo.append("上行速度: ").append(upSpeed).append("Kbps");

                // 判断网络质量
                if (downSpeed < 500 || upSpeed < 500) {
                    networkInfo.append(" [网络速度较慢]");
                    isNetworkIssueDetected = true;
                } else {
                    isNetworkIssueDetected = false;
                }
            } else {
                networkInfo.append("无活动网络连接");
                isNetworkIssueDetected = true;
            }
        } else {
            NetworkInfo info = connectivityManager.getActiveNetworkInfo();
            if (info != null && info.isConnected()) {
                networkInfo.append("网络类型: ").append(info.getTypeName()).append(", ");
                networkInfo.append("网络状态: 已连接");
                isNetworkIssueDetected = false;
            } else {
                networkInfo.append("无活动网络连接");
                isNetworkIssueDetected = true;
            }
        }

        Log.d(TAG, "网络状态: " + networkInfo.toString());
        return networkInfo.toString();
    }

    /**
     * 开始语音翻译
     *
     * @param sourceLanguage 源语言代码，例如zh-CN、en-US等
     * @param targetLanguage 目标语言代码，例如zh-CN、en-US等
     * @param continuousMode 是否使用连续语言检测模式
     */
    public void startTranslation(String sourceLanguage, String targetLanguage, boolean continuousMode) {
        // 获取调试器实例
        SpeechDebugger debugger = SpeechDebugger.getInstance();

        if (isRecognizing) {
            Log.d(TAG, "已经在识别中，忽略请求");
            debugger.log("初始化", "已经在识别中，忽略请求");
            return;
        }

        try {
            // 记录开始时间，用于性能分析
            startRecognitionTime = System.currentTimeMillis();
            hasReceivedFirstResult = false;
            firstResultTime = 0;
            Log.d(TAG, "开始语音翻译，时间戳: " + startRecognitionTime);
            debugger.log("初始化", "开始语音翻译", "源语言=" + sourceLanguage, "目标语言=" + targetLanguage, "连续模式=" + continuousMode);

            // 检查网络状态
            String networkStatus = checkNetworkStatus();
            Log.d(TAG, "网络状态检查结果: " + networkStatus);
            debugger.log("网络", "网络状态检查结果: " + networkStatus);

            if (isNetworkIssueDetected) {
                Log.w(TAG, "检测到网络问题，可能会影响转写速度");
                debugger.log("网络", "检测到网络问题，可能会影响转写速度");
                if (errorListener != null) {
                    errorListener.onError("网络连接不稳定，可能会影响转写速度");
                }
            }
            // 清除之前的识别器
            if (translationRecognizer != null) {
                try {
                    translationRecognizer.close();
                } catch (Exception e) {
                    Log.e(TAG, "关闭之前的翻译识别器失败", e);
                }
                translationRecognizer = null;
            }

            // 创建翻译配置
            // 注意：根据微软文档，多语言识别需要使用v2端点
            SpeechTranslationConfig translationConfig;

            // 使用已经在方法开始处定义的调试器实例

            // 检查是否是多语言模式
            boolean isMultiLanguageMode = sourceLanguage.contains(",") || sourceLanguage.contains(";");
            Log.d(TAG, "源语言: " + sourceLanguage + ", 是否为多语言模式: " + isMultiLanguageMode);
            debugger.log("配置", "源语言: " + sourceLanguage + ", 是否为多语言模式: " + isMultiLanguageMode);

            if (isMultiLanguageMode) {
                // 对于多语言模式，不使用自定义端点，而是使用标准配置
                Log.d(TAG, "使用标准配置进行多语言识别");
                debugger.log("配置", "使用标准配置进行多语言识别");

                try {
                    // 使用标准配置而不是自定义端点
                    translationConfig = SpeechTranslationConfig.fromSubscription(subscriptionKey, region);
                    debugger.log("配置", "成功创建SpeechTranslationConfig从订阅");
                } catch (Exception e) {
                    debugger.log("错误", "创建SpeechTranslationConfig失败: " + e.getMessage());
                    throw e;
                }

                // 不设置自定义服务属性，使用SDK默认值

                // 设置语言检测相关的其他属性
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageDetection", "true");
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageIdentification", "true");

                // 设置语言检测成功后的行为
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize");
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize");

                // 设置语言检测模式
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMode", "Continuous");

                // 不设置自定义端点URL属性

                // 添加官方示例中的参数
                // 不设置language参数，让API自动检测
                translationConfig.setProperty("format", "detailed");
                translationConfig.setProperty("lidEnabled", "true");
                translationConfig.setProperty("profanity", "masked");
                translationConfig.setProperty("wordLevelTimestamps", "true");

                // 设置说话人分割
                translationConfig.setProperty("diarizationEnabled", "true");

                // 设置语言列表属性 - 使用英语、中文和日语（与微软示例一致）
                // 确保使用小写区域代码和分号分隔，这是Microsoft官方demo的格式
                String supportedLanguages = "en-us;zh-cn;ja-jp";
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", supportedLanguages);
                debugger.log("配置", "设置固定的语言列表: " + supportedLanguages);

                // 再次确认语言列表设置正确
                Log.d(TAG, "设置语言列表: " + supportedLanguages);

                // 设置详细日志记录
                translationConfig.setProperty("SPEECH-Debug", "true");
                translationConfig.setProperty("SPEECH-Debug-Log", "true");
                translationConfig.setProperty("SPEECH-Debug-LogMaxFileSize", "10485760"); // 10MB

                // 设置结果格式
                translationConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText");
                translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Raw");
                translationConfig.setProperty("SpeechServiceResponse_RequestWordLevelTimestamps", "true");

                debugger.log("配置", "使用SDK默认服务连接属性");
            } else {
                // 对于单语言模式，使用标准配置
                translationConfig = SpeechTranslationConfig.fromSubscription(subscriptionKey, region);

                // 设置语言检测相关的其他属性
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageDetection", "true");
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageIdentification", "true");

                // 设置语言检测成功后的行为
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize");
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize");

                // 设置语言检测模式
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMode", "Continuous");

                // 设置详细日志记录
                translationConfig.setProperty("SPEECH-Debug", "true");
                translationConfig.setProperty("SPEECH-Debug-Log", "true");

                // 设置结果格式
                translationConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText");
                translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Raw");

                // 不使用URL参数，而是使用SDK属性
                translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Masked");
                translationConfig.setProperty("SpeechServiceResponse_OutputFormatOption", "Detailed");
                translationConfig.setProperty("SpeechServiceResponse_RequestWordLevelTimestamps", "true");

                // 设置说话人分割
                translationConfig.setProperty("SpeechServiceConnection_EnableDiarization", "true");

                // 设置语言列表属性 - 使用英语、中文和日语（与微软示例一致）
                String supportedLanguages = "en-US;zh-CN;ja-JP";
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", supportedLanguages);
                debugger.log("配置", "设置固定的语言列表: " + supportedLanguages);
            }

            // 设置源语言和目标语言
            Log.d(TAG, "设置源语言: " + sourceLanguage + ", 目标语言: " + targetLanguage);
            if (isMultiLanguageMode) {
                Log.d(TAG, "检测到多语言模式，启用多语言自动检测");

                // 解析多个语言
                String[] languages = sourceLanguage.split(",");
                StringBuilder languageList = new StringBuilder();

                // 构建语言列表（使用分号分隔，符合微软API要求）
                for (int i = 0; i < languages.length; i++) {
                    String lang = languages[i].trim();
                    // 确保日语使用正确的格式
                    if (lang.toLowerCase().startsWith("ja")) {
                        lang = "ja-JP";
                    }
                    // 确保语言代码格式正确（区域代码大写）
                    else if (lang.contains("-")) {
                        String[] parts = lang.split("-");
                        if (parts.length == 2) {
                            lang = parts[0].toLowerCase() + "-" + parts[1].toUpperCase();
                        }
                    }
                    languageList.append(lang);
                    if (i < languages.length - 1) {
                        languageList.append(";");
                    }
                }

                // 记录原始和格式化后的语言列表
                debugger.log("配置", "原始语言列表: " + sourceLanguage);
                debugger.log("配置", "格式化语言列表: " + languageList.toString());

                // 设置多语言检测
                Log.d(TAG, "设置多语言列表: " + languageList.toString());

                // 设置默认识别语言为中文（使用小写区域代码）
                translationConfig.setSpeechRecognitionLanguage("zh-cn");
                translationConfig.setProperty("SpeechServiceConnection_RecoLanguage", "zh-cn");
                debugger.log("配置", "设置默认识别语言: zh-cn");

                // 按照微软官方示例设置参数 - 使用更直接的方式配置多语言识别

                // 设置语言检测优先级 - 使用Latency优先考虑速度，减少延迟
                translationConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Latency");

                // 不使用URL参数，而是使用SDK属性
                translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Masked");
                translationConfig.setProperty("SpeechServiceResponse_OutputFormatOption", "Detailed");
                translationConfig.setProperty("SpeechServiceResponse_RequestWordLevelTimestamps", "true");

                // 设置说话人分割
                translationConfig.setProperty("SpeechServiceConnection_EnableDiarization", "true");

                // 记录配置属性，确保正确设置
                Log.d(TAG, "配置属性 SpeechServiceConnection_ContinuousLanguageIdPriority: " +
                      translationConfig.getProperty("SpeechServiceConnection_ContinuousLanguageIdPriority"));

                // 设置语言检测成功后的行为 - 确保识别继续进行
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize");

                // 设置语言检测未知时的行为 - 继续使用最后检测到的语言
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize");

                // 完全按照Microsoft官方demo的配置设置
                // 设置语言检测模式为连续模式
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMode", "Continuous");

                // 设置语言检测优先级为最高优先级
                translationConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Latency");

                // 设置语言检测的最小持续时间为最小值，使其立即响应语言变化
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMinimumDuration", "0");

                // 设置语言检测的最大持续时间为较小值，加快语言切换速度
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMaximumDuration", "100");
                Log.d(TAG, "语言检测最大持续时间: 100ms");

                // 设置语言检测的置信度阈值为最低值，确保能够检测到任何语言变化
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdConfidenceThreshold", "0.05");
                Log.d(TAG, "语言检测置信度阈值: 0.05");

                // 设置语言检测任务优先级为最高优先级
                translationConfig.setProperty("SpeechServiceConnection_LanguageDetectionTaskPriority", "High");

                // 设置自动语言检测结果的处理方式
                translationConfig.setProperty("SpeechServiceConnection_AutoDetectSourceLanguageResult", "true");

                // 设置语言检测相关的其他属性
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageDetection", "true");
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageIdentification", "true");

                // 不设置自定义端点和服务属性，使用SDK默认值

                // 使用SDK默认端点
                Log.d(TAG, "使用SDK默认端点");
                debugger.log("配置", "使用SDK默认端点");

                // 设置初始静音超时为较短值，提高响应速度
                translationConfig.setProperty("SpeechServiceConnection_InitialSilenceTimeoutMs", "3000");

                // 设置结束静音超时为较长值，避免过早结束识别
                translationConfig.setProperty("SpeechServiceConnection_EndSilenceTimeoutMs", "2000");

                // 记录配置信息
                Log.d(TAG, "多语言配置: 模式=Continuous, 优先级=Latency, 语言列表=" + languageList.toString());

                // 根据参数设置语言检测模式
                // 注意：对于连续语言检测，必须使用"Continuous"值
                String languageIdMode = continuousMode ? "Continuous" : "AtStart";

                // 使用字符串属性名称，避免PropertyId枚举可能不存在的问题
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMode", languageIdMode);

                Log.d(TAG, "设置语言检测模式: " + languageIdMode +
                      (continuousMode ? " (持续检测语言)" : " (只在开始时检测一次语言)"));

                // 记录配置属性，确保正确设置
                String actualMode = translationConfig.getProperty("SpeechServiceConnection_LanguageIdMode");
                Log.d(TAG, "配置属性 SpeechServiceConnection_LanguageIdMode: " + actualMode);

                // 调试日志
                debugger.log("配置", "设置语言检测模式: " + languageIdMode +
                    (continuousMode ? " (持续检测语言)" : " (只在开始时检测一次语言)"));
                debugger.log("配置", "实际语言检测模式: " + actualMode);

                // 验证模式是否设置正确
                if (!languageIdMode.equals(actualMode)) {
                    debugger.log("错误", "语言检测模式设置失败，期望值=" + languageIdMode + "，实际值=" + actualMode);
                }

                // 设置支持的语言列表 - 修复格式问题
                // 微软API要求语言代码格式为"en-us;zh-cn;ja-jp"，不能有方括号或其他字符
                // 注意：使用小写区域代码
                StringBuilder formattedLanguageListBuilder = new StringBuilder();

                // 直接构建格式化的语言列表字符串，确保使用小写区域代码
                for (int i = 0; i < languages.length; i++) {
                    String lang = languages[i].trim();
                    // 确保日语使用正确的格式
                    if (lang.toLowerCase().startsWith("ja")) {
                        lang = "ja-jp";
                    }
                    // 确保语言代码格式正确（区域代码小写）
                    else if (lang.contains("-")) {
                        String[] parts = lang.split("-");
                        if (parts.length == 2) {
                            lang = parts[0].toLowerCase() + "-" + parts[1].toLowerCase();
                        }
                    }
                    formattedLanguageListBuilder.append(lang);
                    if (i < languages.length - 1) {
                        formattedLanguageListBuilder.append(";");
                    }
                }

                String formattedLanguageList = formattedLanguageListBuilder.toString();

                // 记录最终格式化的语言列表
                debugger.log("配置", "最终格式化的语言列表: " + formattedLanguageList);

                // 设置语言列表属性 - 使用英语、中文和日语（与微软示例一致）
                String supportedLanguages = "en-us;zh-cn;ja-jp";
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", supportedLanguages);
                debugger.log("配置", "设置固定的语言列表: " + supportedLanguages);

                // 记录配置属性，确保正确设置
                String actualLanguages = translationConfig.getProperty("SpeechServiceConnection_LanguageIdLanguages");
                Log.d(TAG, "配置属性 SpeechServiceConnection_LanguageIdLanguages: " + actualLanguages);

                // 调试日志
                debugger.log("配置", "设置支持的语言列表(原始): " + languageList.toString());
                debugger.log("配置", "设置支持的语言列表(格式化): " + formattedLanguageList);
                debugger.log("配置", "实际支持的语言列表: " + actualLanguages);

                // 验证语言列表是否设置正确
                if (actualLanguages == null || actualLanguages.isEmpty()) {
                    debugger.log("错误", "语言列表设置失败，期望值=" + formattedLanguageList + "，实际值为空");
                } else if (!actualLanguages.contains(formattedLanguageList)) {
                    debugger.log("错误", "语言列表设置可能不正确，期望包含=" + formattedLanguageList + "，实际值=" + actualLanguages);
                }

                // 设置适当的超时时间，平衡响应速度和准确性
                translationConfig.setProperty("SpeechServiceConnection_InitialSilenceTimeoutMs", "8000");
                translationConfig.setProperty("SpeechServiceConnection_EndSilenceTimeoutMs", "1500");

                // 这部分已经在前面设置过，不需要重复设置
                // 记录设置的默认语言
                Log.d(TAG, "默认识别语言已在前面设置");

                Log.d(TAG, "已启用多语言自动检测模式，默认语言: " + languages[0].trim());
            }
            // 确保源语言格式正确
            else {
                final String lowerSourceLanguage = sourceLanguage.toLowerCase();
                if (lowerSourceLanguage.startsWith("ja")) {
                // 统一转换为小写，确保格式一致性
                // 创建一个新的变量，而不是修改原变量
                final String jaSourceLanguage = lowerSourceLanguage.replace("ja-jp", "ja-jp").replace("ja-JP", "ja-jp");
                // 日语需要特殊处理
                Log.d(TAG, "检测到日语源语言，使用特殊配置");

                // 设置日语识别语言（使用小写的区域代码，与API返回格式一致）
                translationConfig.setSpeechRecognitionLanguage("ja-jp");

                // 设置日语特定的识别模式 - 使用小写区域代码
                translationConfig.setProperty("SpeechServiceConnection_RecoLanguage", "ja-jp");

                // 设置日语特定的识别模式
                translationConfig.setProperty(PropertyId.SpeechServiceConnection_RecoLanguage.toString(), "ja-jp");

                // 设置语言检测相关的其他属性
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageDetection", "true");
                translationConfig.setProperty("SpeechServiceConnection_EnableLanguageIdentification", "true");

                // 配置语言检测
                // 设置为优先考虑准确性的模式，以支持动态语言切换
                translationConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Accuracy");

                // 根据参数设置语言检测模式
                // 注意：对于连续语言检测，必须使用"Continuous"值，而不是"DetectContinuous"
                String languageIdMode = continuousMode ? "Continuous" : "AtStart";
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdMode", languageIdMode);
                Log.d(TAG, "日语设置语言检测模式: " + languageIdMode +
                      (continuousMode ? " (持续检测语言)" : " (只在开始时检测一次语言)"));

                // 调试日志
                debugger.log("配置", "日语设置语言检测模式: " + languageIdMode);

                // 验证模式是否设置正确
                String actualMode = translationConfig.getProperty("SpeechServiceConnection_LanguageIdMode");
                if (!languageIdMode.equals(actualMode)) {
                    debugger.log("错误", "日语语言检测模式设置失败，期望值=" + languageIdMode + "，实际值=" + actualMode);
                }

                // 设置支持的语言列表（包括日语）
                // 注意：语言代码格式为"en-us;ja-jp;zh-cn"，与微软示例一致
                // 确保包含当前语言和其他常用语言
                String supportedLanguages = "en-us;zh-cn;ja-jp";
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", supportedLanguages);
                debugger.log("配置", "日语模式设置支持的语言列表(小写区域代码): " + supportedLanguages);

                // 调试日志
                String actualLanguages = translationConfig.getProperty("SpeechServiceConnection_LanguageIdLanguages");
                debugger.log("配置", "日语模式设置支持的语言列表: " + supportedLanguages);
                debugger.log("配置", "实际支持的语言列表: " + actualLanguages);

                // 设置语言检测成功后的行为
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize");

                // 设置语言检测未知时的行为 - 继续使用当前语言
                translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize");

                // 设置短语输出配置
                translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Masked");
                translationConfig.setProperty("SpeechServiceResponse_OutputFormatOption", "Detailed");

                // 增加超时时间，给日语识别更多处理时间
                translationConfig.setProperty("SpeechServiceConnection_InitialSilenceTimeoutMs", "10000");
                translationConfig.setProperty("SpeechServiceConnection_EndSilenceTimeoutMs", "2000");

                // 设置音频质量参数
                translationConfig.setProperty("SpeechServiceConnection_EnableAudioLogging", "true");

                // 设置更高的采样率
                translationConfig.setProperty("SpeechServiceConnection_AudioInputSamplesPerSecond", "16000");

                // 启用详细日志
                translationConfig.setProperty("SpeechServiceConnection_LogLevel", "Detailed");

                Log.d(TAG, "已应用日语特殊配置");
                } else {
                    // 其他语言正常处理
                    translationConfig.setSpeechRecognitionLanguage(sourceLanguage);
                }
            }

            // 添加目标语言
            String mappedTargetLanguage = mapLanguageCode(targetLanguage);
            translationConfig.addTargetLanguage(mappedTargetLanguage);

            // 设置详细输出格式
            translationConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText");

            // 设置语音识别模式
            // 对于非中英文语言（除日语外，日语已在前面特殊处理），启用连续识别模式
            if (!sourceLanguage.startsWith("ja") && (
                sourceLanguage.startsWith("ko") ||
                sourceLanguage.startsWith("fr") ||
                sourceLanguage.startsWith("de") ||
                sourceLanguage.startsWith("es") ||
                sourceLanguage.startsWith("it") ||
                sourceLanguage.startsWith("ru"))) {
                Log.d(TAG, "为非中英文语言启用连续识别模式");
                translationConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Latency");
            }

            // 设置通用的语音识别参数 - 增加超时时间，提高识别准确性
            translationConfig.setProperty("SpeechServiceConnection_InitialSilenceTimeoutMs", "8000");
            translationConfig.setProperty("SpeechServiceConnection_EndSilenceTimeoutMs", "1500");

            // 创建音频配置（使用默认麦克风）
            AudioConfig audioConfig;

            // 对于日语，使用增强的音频配置
            if (sourceLanguage.startsWith("ja")) {
                Log.d(TAG, "为日语创建增强的音频配置");

                try {
                    // 使用默认音频配置，但添加额外的日志记录
                    audioConfig = AudioConfig.fromDefaultMicrophoneInput();

                    // 记录音频配置信息
                    Log.d(TAG, "已为日语创建音频配置");
                } catch (Exception e) {
                    Log.e(TAG, "创建音频配置失败，使用默认配置", e);
                    audioConfig = AudioConfig.fromDefaultMicrophoneInput();
                }
            } else {
                // 其他语言使用默认配置
                audioConfig = AudioConfig.fromDefaultMicrophoneInput();
            }

            // 记录音频配置信息
            Log.d(TAG, "音频配置已创建: " + (audioConfig != null ? "成功" : "失败"));

            // 创建翻译识别器
            if (isMultiLanguageMode) {
                // 对于多语言模式，使用AutoDetectSourceLanguageConfig
                Log.d(TAG, "使用AutoDetectSourceLanguageConfig创建翻译识别器");

                // 创建语言配置列表
                String[] languageArray = sourceLanguage.split(",");
                ArrayList<String> languageList = new ArrayList<>();
                for (String lang : languageArray) {
                    languageList.add(lang.trim());
                }

                // 创建自动检测源语言配置
                Log.d(TAG, "创建AutoDetectSourceLanguageConfig，语言列表: " + languageList);

                try {
                    // 创建SourceLanguageConfig列表
                    List<SourceLanguageConfig> sourceLanguageConfigs = new ArrayList<>();

                    // 为每种语言创建SourceLanguageConfig
                    for (String lang : languageList) {
                        // 保持原始格式，不转换为小写
                        String formattedLang = lang;
                        // 只对日语特殊处理
                        if (formattedLang.toLowerCase().startsWith("ja")) {
                            formattedLang = "ja-JP";
                        }

                        // 创建语言配置并添加到列表
                        SourceLanguageConfig langConfig = SourceLanguageConfig.fromLanguage(formattedLang);
                        sourceLanguageConfigs.add(langConfig);
                        Log.d(TAG, "添加语言配置: " + formattedLang);
                    }

                        // 创建自动检测源语言配置
                    // 使用fromLanguages方法而不是fromSourceLanguageConfigs
                    // 这是官方文档推荐的方法，可以更好地支持连续语言检测
                    List<String> languagesList = new ArrayList<>();
                    for (String lang : languageList) {
                        // 确保语言代码格式正确
                        String formattedLang = lang.trim();
                        // 特殊处理日语
                        if (formattedLang.toLowerCase().startsWith("ja")) {
                            formattedLang = "ja-jp"; // 使用小写区域代码
                        }
                        // 确保语言代码格式正确（区域代码小写）
                        else if (formattedLang.contains("-")) {
                            String[] parts = formattedLang.split("-");
                            if (parts.length == 2) {
                                formattedLang = parts[0].toLowerCase() + "-" + parts[1].toLowerCase();
                            }
                        }
                        languagesList.add(formattedLang);
                    }

                    // 记录格式化后的语言列表
                    debugger.log("配置", "格式化后的语言列表(小写区域代码): " + languagesList);

                    debugger.log("配置", "准备创建AutoDetectSourceLanguageConfig，语言列表: " + languagesList);

                    // 使用SourceLanguageConfig列表创建AutoDetectSourceLanguageConfig
                    AutoDetectSourceLanguageConfig autoDetectSourceLanguageConfig;
                    try {
                        // 创建SourceLanguageConfig列表 - 使用新的变量名避免重复定义
                        List<SourceLanguageConfig> langConfigs = new ArrayList<>();

                        // 为每种语言创建SourceLanguageConfig
                        for (String lang : languagesList) {
                            // 确保语言代码格式正确 - 使用小写区域代码
                            String formattedLang = lang;
                            if (lang.contains("-")) {
                                String[] parts = lang.split("-");
                                if (parts.length == 2) {
                                    formattedLang = parts[0].toLowerCase() + "-" + parts[1].toLowerCase();
                                }
                            }

                            // 特殊处理日语
                            if (formattedLang.toLowerCase().startsWith("ja")) {
                                formattedLang = "ja-jp";
                            }

                            debugger.log("配置", "格式化语言代码: " + lang + " -> " + formattedLang);

                            // 创建语言配置并添加到列表
                            SourceLanguageConfig langConfig = SourceLanguageConfig.fromLanguage(formattedLang);
                            langConfigs.add(langConfig);
                            debugger.log("配置", "添加语言配置: " + formattedLang);
                        }

                        // 使用SourceLanguageConfig列表创建AutoDetectSourceLanguageConfig
                        debugger.log("配置", "创建AutoDetectSourceLanguageConfig，包含 " + langConfigs.size() + " 种语言");
                        autoDetectSourceLanguageConfig = AutoDetectSourceLanguageConfig.fromSourceLanguageConfigs(langConfigs);
                        debugger.log("配置", "成功创建AutoDetectSourceLanguageConfig");
                    } catch (Exception e) {
                        debugger.log("错误", "创建AutoDetectSourceLanguageConfig失败: " + e.getMessage());
                        throw e;
                    }

                    Log.d(TAG, "已创建AutoDetectSourceLanguageConfig，包含 " + languagesList.size() + " 种语言");

                    // 使用标准方式创建翻译识别器
                    // Microsoft Speech SDK Java版本不支持在TranslationRecognizer构造函数中传递autoDetectSourceLanguageConfig
                    try {
                        // 使用标准构造函数
                        translationRecognizer = new TranslationRecognizer(
                            translationConfig,
                            audioConfig);
                        debugger.log("配置", "成功创建标准TranslationRecognizer");

                        // 记录创建的配置信息
                        Log.d(TAG, "已创建标准TranslationRecognizer，将通过属性设置应用语言检测");
                    } catch (Exception e) {
                        debugger.log("错误", "创建TranslationRecognizer失败: " + e.getMessage());
                        throw e;
                    }

                    // 保存autoDetectSourceLanguageConfig的引用，防止被垃圾回收
                    this.autoDetectConfig = autoDetectSourceLanguageConfig;
                    debugger.log("配置", "保存autoDetectSourceLanguageConfig引用，防止被垃圾回收");

                    Log.d(TAG, "已创建带有自动语言检测配置的翻译识别器");

                    // 记录创建的翻译识别器信息
                    Log.d(TAG, "已创建带有多语言检测配置的翻译识别器");
                    // 只有在识别器关闭时才释放资源

                } catch (Exception e) {
                    Log.e(TAG, "创建多语言配置失败，使用标准方式", e);

                    // 使用标准方式创建翻译识别器
                    translationRecognizer = new TranslationRecognizer(
                        translationConfig,
                        audioConfig);
                }

                // 检查是否成功创建
                if (translationRecognizer != null) {
                    Log.d(TAG, "成功创建TranslationRecognizer");
                } else {
                    Log.e(TAG, "创建TranslationRecognizer失败");
                }

                Log.d(TAG, "已创建带有自动语言检测的翻译识别器");
            } else {
                // 对于单语言模式，使用标准配置
                translationRecognizer = new TranslationRecognizer(translationConfig, audioConfig);
            }

            // 记录配置信息
            boolean isJapanese = sourceLanguage.toLowerCase().startsWith("ja");
            Log.d(TAG, "翻译识别器配置完成，源语言: " + sourceLanguage +
                  ", 目标语言: " + targetLanguage +
                  ", 是否为日语: " + (isJapanese ? "是" : "否") +
                  ", 是否为多语言模式: " + (isMultiLanguageMode ? "是" : "否"));

            // 使用已经在方法开始处定义的调试器实例

            // 注册事件处理程序
            debugger.log("配置", "注册recognizing事件处理程序");
            translationRecognizer.recognizing.addEventListener((o, e) -> {
                TranslationRecognitionResult result = e.getResult();
                String text = result.getText();
                Log.d(TAG, "识别中: " + text);

                // 记录首次获取结果的时间
                if (!hasReceivedFirstResult && !text.isEmpty()) {
                    firstResultTime = System.currentTimeMillis();
                    hasReceivedFirstResult = true;
                    long latency = firstResultTime - startRecognitionTime;
                    Log.d(TAG, "首次识别延迟: " + latency + "ms");
                    debugger.log("识别", "首次识别延迟: " + latency + "ms");

                    // 判断延迟是否过高
                    if (latency > 3000) {
                        Log.w(TAG, "识别延迟较高 (" + latency + "ms)，可能是网络问题");
                        debugger.log("识别", "识别延迟较高 (" + latency + "ms)，可能是网络问题");
                        if (!isNetworkIssueDetected) {
                            // 再次检查网络状态
                            String networkStatusUpdate = checkNetworkStatus();
                            Log.d(TAG, "延迟高，再次检查网络状态: " + networkStatusUpdate);
                            debugger.log("网络", "延迟高，再次检查网络状态: " + networkStatusUpdate);
                        }
                    }
                }

                // 计算服务器响应时间
                long currentTime = System.currentTimeMillis();
                if (lastRequestTime > 0) {
                    long responseTime = currentTime - lastRequestTime;
                    lastResponseTime = responseTime;
                    totalResponseTime += responseTime;
                    responseCount++;

                    // 计算平均响应时间
                    long avgResponseTime = responseCount > 0 ? totalResponseTime / responseCount : 0;

                    // 记录响应时间
                    Log.d(TAG, "服务器响应时间: " + responseTime + "ms, 平均: " + avgResponseTime + "ms");

                    // 判断响应时间是否过高
                    if (responseTime > 1000) {
                        Log.w(TAG, "服务器响应时间较高 (" + responseTime + "ms)，可能是服务器负载问题");
                        debugger.log("网络", "服务器响应时间较高 (" + responseTime + "ms)，可能是服务器负载问题");
                        isServerIssueDetected = true;
                    } else if (responseTime > 500) {
                        Log.w(TAG, "服务器响应时间偏高 (" + responseTime + "ms)");
                        debugger.log("网络", "服务器响应时间偏高 (" + responseTime + "ms)");
                    } else {
                        isServerIssueDetected = false;
                    }
                }

                // 更新请求时间
                lastRequestTime = currentTime;

                // 尝试获取检测到的语言信息 - 使用多种可能的属性名
                try {
                    // 尝试多种可能的属性名
                    String[] languagePropertyNames = {
                        "SpeechServiceConnection_AutoDetectSourceLanguageResult",
                        "AutoDetectedSourceLanguage",
                        "LanguageDetectionResult",
                        "PrimaryLanguage",
                        "DetectedLanguage",
                        "Language",
                        "RecognizedLanguage"
                    };

                    String detectedLanguage = null;
                    String usedPropertyName = null;

                    // 尝试所有可能的属性名
                    for (String propName : languagePropertyNames) {
                        String propValue = result.getProperties().getProperty(propName);
                        if (propValue != null && !propValue.isEmpty()) {
                            detectedLanguage = propValue;
                            usedPropertyName = propName;
                            Log.d(TAG, "中间结果检测到的语言（通过属性名 " + propName + "）: " + propValue);
                            break;
                        }
                    }

                    // 如果找到了语言
                    if (detectedLanguage != null) {
                        debugger.log("语言检测", "中间结果检测到的语言（通过属性名 " + usedPropertyName + "）: " + detectedLanguage);

                        // 通知语言检测监听器
                        if (languageDetectionListener != null) {
                            languageDetectionListener.onLanguageDetected(detectedLanguage);
                            debugger.log("语言检测", "已通知语言检测回调（中间结果）: " + detectedLanguage);
                        }
                    }
                } catch (Exception ex) {
                    // 忽略错误，不影响正常流程
                }

                // 尝试获取检测到的语言信息
                try {
                    // 获取完整的结果JSON
                    String resultJson = result.getProperties().getProperty("result.json");
                    if (resultJson != null && !resultJson.isEmpty()) {
                        // 检查是否包含语言信息
                        if (resultJson.contains("PrimaryLanguage")) {
                            Log.d(TAG, "中间结果检测到语言信息: " + resultJson);

                            // 提取检测到的语言
                            int langStart = resultJson.indexOf("\"Language\":");
                            if (langStart > 0) {
                                int valueStart = langStart + 12; // "Language": 后面的位置
                                int valueEnd = resultJson.indexOf("\"", valueStart);
                                if (valueEnd > valueStart) {
                                    String detectedLanguage = resultJson.substring(valueStart, valueEnd);
                                    Log.d(TAG, "中间结果自动检测到的语言: " + detectedLanguage);

                                    // 通知语言检测回调
                                    if (languageDetectionListener != null) {
                                        languageDetectionListener.onLanguageDetected(detectedLanguage);
                                        Log.d(TAG, "已通知语言检测回调（中间结果）: " + detectedLanguage);
                                    }

                                    // 提取置信度
                                    int confStart = resultJson.indexOf("\"Confidence\":");
                                    if (confStart > 0) {
                                        int confValueStart = confStart + 13; // "Confidence": 后面的位置
                                        int confValueEnd = resultJson.indexOf(",", confValueStart);
                                        if (confValueEnd > confValueStart) {
                                            String confidence = resultJson.substring(confValueStart, confValueEnd);
                                            Log.d(TAG, "中间结果语言检测置信度: " + confidence);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 尝试获取其他属性
                    String[] propertyNames = {
                        "languageDetectionResult",
                        "detectedLanguage",
                        "language",
                        "speechLanguage",
                        "SpeechServiceConnection_AutoDetectSourceLanguageResult",
                        "speech.language"
                    };

                    for (String propName : propertyNames) {
                        try {
                            String propValue = result.getProperties().getProperty(propName);
                            if (propValue != null && !propValue.isEmpty()) {
                                Log.d(TAG, "中间结果属性 " + propName + ": " + propValue);
                            }
                        } catch (Exception propEx) {
                            // 忽略错误，不影响正常流程
                        }
                    }

                    // 特别尝试获取微软文档中提到的自动检测语言结果属性
                    try {
                        String autoDetectResult = result.getProperties().getProperty(
                            "SpeechServiceConnection_AutoDetectSourceLanguageResult");
                        if (autoDetectResult != null && !autoDetectResult.isEmpty()) {
                            Log.d(TAG, "中间结果检测到的语言（通过属性名）: " + autoDetectResult);
                        }
                    } catch (Exception ex) {
                        // 忽略错误，不影响正常流程
                    }
                } catch (Exception ex) {
                    // 忽略错误，不影响正常流程
                }

                if (recognitionResultListener != null) {
                    recognitionResultListener.onRecognizing(text);
                }

                // 获取翻译结果
                String targetLang = mapLanguageCode(targetLanguage);
                Map<String, String> translations = result.getTranslations();
                if (translations.containsKey(targetLang)) {
                    String translatedText = translations.get(targetLang);
                    Log.d(TAG, "翻译中: " + translatedText);
                    if (translationResultListener != null) {
                        translationResultListener.onTranslating(translatedText);
                    }
                }
            });

            // 使用已经在方法开始处定义的调试器实例

            debugger.log("配置", "注册recognized事件处理程序");
            translationRecognizer.recognized.addEventListener((o, e) -> {
                TranslationRecognitionResult result = e.getResult();
                if (result.getReason() == ResultReason.TranslatedSpeech) {
                    String text = result.getText();

                    // 记录更详细的识别信息
                    Log.d(TAG, "识别完成: " + text);
                    Log.d(TAG, "识别结果ID: " + result.getResultId());

                    // 调试日志
                    debugger.log("识别", "识别完成: " + text);
                    debugger.log("识别", "识别结果ID: " + result.getResultId());

                    // 尝试获取检测到的语言信息
                    try {
                        // 获取完整的结果JSON
                        String resultJson = result.getProperties().getProperty("result.json");
                        if (resultJson != null && !resultJson.isEmpty()) {
                            Log.d(TAG, "识别结果JSON: " + resultJson);

                            // 检查是否包含语言信息
                            if (resultJson.contains("PrimaryLanguage")) {
                                Log.d(TAG, "检测到语言信息: " + resultJson);

                                // 提取检测到的语言
                                int langStart = resultJson.indexOf("\"Language\":");
                                if (langStart > 0) {
                                    int valueStart = langStart + 12; // "Language": 后面的位置
                                    int valueEnd = resultJson.indexOf("\"", valueStart);
                                    if (valueEnd > valueStart) {
                                        String detectedLanguage = resultJson.substring(valueStart, valueEnd);
                                        Log.d(TAG, "自动检测到的语言: " + detectedLanguage);

                                        // 调试日志
                                        debugger.log("语言检测", "自动检测到的语言: " + detectedLanguage);

                                        // 通知语言检测回调
                                        if (languageDetectionListener != null) {
                                            languageDetectionListener.onLanguageDetected(detectedLanguage);
                                            Log.d(TAG, "已通知语言检测回调: " + detectedLanguage);
                                            debugger.log("语言检测", "已通知语言检测回调: " + detectedLanguage);
                                        }
                                    }
                                }

                                // 提取置信度
                                int confStart = resultJson.indexOf("\"Confidence\":");
                                if (confStart > 0) {
                                    int valueStart = confStart + 13; // "Confidence": 后面的位置
                                    int valueEnd = resultJson.indexOf(",", valueStart);
                                    if (valueEnd > valueStart) {
                                        String confidence = resultJson.substring(valueStart, valueEnd);
                                        Log.d(TAG, "语言检测置信度: " + confidence);

                                        // 调试日志
                                        debugger.log("语言检测", "语言检测置信度: " + confidence);

                                        // 分析置信度
                                        try {
                                            float confidenceValue = Float.parseFloat(confidence);
                                            if (confidenceValue < 0.5) {
                                                debugger.log("语言检测", "警告：语言检测置信度较低 (" + confidence + ")，可能导致识别不准确");
                                            }
                                        } catch (Exception ex) {
                                            // 忽略解析错误
                                        }
                                    }
                                }
                            } else {
                                Log.d(TAG, "识别结果中没有包含语言信息");
                            }
                        }

                        // 尝试获取其他属性
                        // 直接使用result.getProperties()获取属性
                        String[] propertyNames = {
                            "languageDetectionResult",
                            "detectedLanguage",
                            "language",
                            "speechLanguage",
                            // 添加微软文档中提到的属性
                            "SpeechServiceConnection_AutoDetectSourceLanguageResult",
                            "speech.language"
                        };

                        for (String propName : propertyNames) {
                            try {
                                String propValue = result.getProperties().getProperty(propName);
                                if (propValue != null && !propValue.isEmpty()) {
                                    Log.d(TAG, "属性 " + propName + ": " + propValue);
                                }
                            } catch (Exception propEx) {
                                Log.e(TAG, "获取属性 " + propName + " 失败", propEx);
                            }
                        }

                        // 尝试获取语言检测结果 - 使用多种可能的属性名
                        try {
                            // 尝试多种可能的属性名
                            String[] languagePropertyNames = {
                                "SpeechServiceConnection_AutoDetectSourceLanguageResult",
                                "AutoDetectedSourceLanguage",
                                "LanguageDetectionResult",
                                "PrimaryLanguage",
                                "DetectedLanguage",
                                "Language",
                                "RecognizedLanguage"
                            };

                            String detectedLanguage = null;
                            String usedPropertyName = null;

                            // 尝试所有可能的属性名
                            for (String propName : languagePropertyNames) {
                                String propValue = result.getProperties().getProperty(propName);
                                if (propValue != null && !propValue.isEmpty()) {
                                    detectedLanguage = propValue;
                                    usedPropertyName = propName;
                                    Log.d(TAG, "检测到的语言（通过属性名 " + propName + "）: " + propValue);
                                    break;
                                }
                            }

                            // 如果找到了语言
                            if (detectedLanguage != null) {
                                debugger.log("语言检测", "最终检测到的语言（通过属性名 " + usedPropertyName + "）: " + detectedLanguage);

                                // 通知语言检测监听器
                                if (languageDetectionListener != null) {
                                    languageDetectionListener.onLanguageDetected(detectedLanguage);
                                    debugger.log("语言检测", "已通知语言检测回调（通过属性名）: " + detectedLanguage);
                                }
                            } else {
                                // 如果所有属性都为空，尝试从JSON中提取
                                String resultJsonStr = result.getProperties().getProperty("result.json");
                                if (resultJsonStr != null && !resultJsonStr.isEmpty()) {
                                    debugger.log("语言检测", "尝试从JSON中提取语言信息: " + resultJsonStr.substring(0, Math.min(100, resultJsonStr.length())) + "...");

                                    // 打印完整的JSON字符串，用于调试
                                    Log.d(TAG, "完整的JSON字符串: " + resultJsonStr);

                                    // 尝试从JSON中提取语言信息 - 尝试多种可能的格式
                                    String[] possiblePatterns = {
                                        "\"Language\":",
                                        "\"language\":",
                                        "\"DetectedLanguage\":",
                                        "\"detectedLanguage\":",
                                        "\"PrimaryLanguage\":",
                                        "\"primaryLanguage\":",
                                        "\"RecognizedLanguage\":",
                                        "\"recognizedLanguage\":",
                                        "\"SourceLanguage\":",
                                        "\"sourceLanguage\":",
                                        "\"LanguageId\":",
                                        "\"languageId\":"
                                    };

                                    boolean foundLanguage = false;

                                    for (String pattern : possiblePatterns) {
                                        if (resultJsonStr.contains(pattern)) {
                                            int langStart = resultJsonStr.indexOf(pattern);
                                            if (langStart > 0) {
                                                int valueStart = langStart + pattern.length(); // 模式后面的位置

                                                // 跳过空格和引号
                                                while (valueStart < resultJsonStr.length() &&
                                                      (resultJsonStr.charAt(valueStart) == ' ' ||
                                                       resultJsonStr.charAt(valueStart) == '"')) {
                                                    valueStart++;
                                                }

                                                // 找到值的结束位置
                                                int valueEnd;
                                                if (valueStart > 0 && valueStart < resultJsonStr.length() && resultJsonStr.charAt(valueStart - 1) == '"') {
                                                    // 如果是引号包围的字符串
                                                    valueEnd = resultJsonStr.indexOf("\"", valueStart);
                                                } else {
                                                    // 如果是非引号包围的值
                                                    valueEnd = resultJsonStr.indexOf(",", valueStart);
                                                    if (valueEnd == -1) {
                                                        valueEnd = resultJsonStr.indexOf("}", valueStart);
                                                    }
                                                }

                                                if (valueEnd > valueStart) {
                                                    detectedLanguage = resultJsonStr.substring(valueStart, valueEnd);
                                                    debugger.log("语言检测", "从JSON中提取的语言 (使用模式 " + pattern + "): " + detectedLanguage);

                                                    // 通知语言检测监听器
                                                    if (languageDetectionListener != null) {
                                                        languageDetectionListener.onLanguageDetected(detectedLanguage);
                                                        debugger.log("语言检测", "已通知语言检测回调（从JSON提取）: " + detectedLanguage);
                                                    }

                                                    foundLanguage = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }

                                    if (!foundLanguage) {
                                        debugger.log("语言检测", "JSON中没有找到任何语言字段");
                                        // 尝试直接解析整个JSON字符串
                                        debugger.log("语言检测", "尝试直接解析整个JSON字符串");
                                        Log.d(TAG, "尝试直接解析整个JSON字符串");
                                    }
                                } else {
                                    debugger.log("语言检测", "警告：未能通过属性名获取语言检测结果（所有属性为空）");

                                    // 使用默认语言作为回退
                                    if (isMultiLanguageMode) {
                                        String[] languages = sourceLanguage.split(",");
                                        if (languages.length > 0) {
                                            String defaultLang = languages[0].trim();
                                            debugger.log("语言检测", "使用默认语言作为回退: " + defaultLang);

                                            // 通知语言检测监听器
                                            if (languageDetectionListener != null) {
                                                languageDetectionListener.onLanguageDetected(defaultLang);
                                                debugger.log("语言检测", "已通知语言检测回调（使用默认语言）: " + defaultLang);
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            Log.d(TAG, "通过属性名获取检测到的语言失败: " + ex.getMessage());
                            debugger.log("错误", "通过属性名获取语言检测结果失败: " + ex.getMessage());
                        }
                    } catch (Exception ex) {
                        Log.e(TAG, "获取识别结果属性失败", ex);
                    }

                    if (recognitionResultListener != null) {
                        recognitionResultListener.onRecognized(text);
                    }

                    // 获取翻译结果
                    String targetLang = mapLanguageCode(targetLanguage);
                    Map<String, String> translations = result.getTranslations();
                    if (translations.containsKey(targetLang)) {
                        String translatedText = translations.get(targetLang);
                        Log.d(TAG, "翻译完成: " + translatedText);
                        if (translationResultListener != null) {
                            translationResultListener.onTranslated(translatedText);
                        }
                    }
                } else if (result.getReason() == ResultReason.NoMatch) {
                    // 记录详细的错误信息
                    String resultDetails = "无法识别语音: ResultId:" + result.getResultId() +
                                          " Reason:NoMatch, Recognized text:<" + result.getText() + ">";
                    Log.e(TAG, resultDetails);

                    // 检查音频输入状态
                    Log.d(TAG, "检查音频输入状态...");

                    // 分析NoMatch错误
                    try {
                        // 由于无法使用NoMatchDetails，我们直接分析结果字符串
                        String resultString = result.toString();
                        Log.e(TAG, "NoMatch详细信息: " + resultString);

                        // 根据结果字符串中的关键词判断可能的原因
                        if (resultString.contains("silence") || resultString.contains("quiet")) {
                            Log.e(TAG, "可能是静音超时，没有检测到语音输入");
                            if (errorListener != null) {
                                errorListener.onError("未检测到语音输入，请确保麦克风工作正常并开始说话");
                            }
                        } else if (resultString.contains("noise") || resultString.contains("babble")) {
                            Log.e(TAG, "可能是噪音干扰，检测到噪音但无法识别为语音");
                            if (errorListener != null) {
                                errorListener.onError("检测到噪音但无法识别为语音，请确保环境安静");
                            }
                        } else {
                            Log.e(TAG, "语音无法识别，可能是语音不清晰或背景噪音过大");
                            if (errorListener != null) {
                                errorListener.onError("语音无法识别，请尝试更清晰地说话，并减少背景噪音");
                            }
                        }
                    } catch (Exception ex) {
                        Log.e(TAG, "分析NoMatch错误失败", ex);

                        // 提供一个通用的错误消息
                        if (errorListener != null) {
                            errorListener.onError("语音识别失败，请检查麦克风并尝试更清晰地说话");
                        }
                    }

                    // 检查是否是日语识别
                    final String checkSourceLang = sourceLanguage.toLowerCase();
                    if (checkSourceLang.startsWith("ja")) {
                        Log.e(TAG, "日语识别无匹配，建议：");
                        Log.e(TAG, "1. 确保麦克风工作正常且音量足够");
                        Log.e(TAG, "2. 尝试更清晰、更慢地说日语");
                        Log.e(TAG, "3. 减少背景噪音");
                        Log.e(TAG, "4. 确保网络连接稳定");
                        Log.e(TAG, "5. 尝试使用简单的日语短句进行测试");

                        // 通知UI层显示提示
                        if (errorListener != null) {
                            errorListener.onError("日语识别失败，请确保麦克风正常工作，并尝试更清晰地说话");
                        }
                    }

                    // 尝试重新启动识别（如果是连续模式）
                    if (isRecognizing) {
                        Log.d(TAG, "尝试继续识别...");
                    }
                }
                
                // 在识别完成后，不再重新启动识别流程，保持连续识别状态
                // 原来有停止和重启的代码，现在移除以避免不必要的会话中断
                // 记录当前识别状态即可
                if (isRecognizing) {
                    Log.d(TAG, "识别持续进行中...");
                }
            });

            translationRecognizer.canceled.addEventListener((o, e) -> {
                Log.d(TAG, "翻译取消: " + e.getReason());

                // 确保在所有情况下都释放信号量
                try {
                    if (e.getReason() == CancellationReason.Error) {
                        String errorDetails = "错误详情: 代码=" + e.getErrorCode() +
                                             ", 详情=" + e.getErrorDetails();
                        Log.e(TAG, errorDetails);

                        // 检查是否是超时错误
                        if (e.getErrorDetails().contains("timeout") ||
                            e.getErrorDetails().contains("ServiceTimeout")) {
                            Log.e(TAG, "检测到服务超时错误，这是一个已知问题");
                            Log.e(TAG, "超时错误通常是暂时性的，不会影响应用功能");

                            // 对于超时错误，我们不向用户显示错误消息，因为这通常是暂时性的
                            // 而且通常发生在停止识别时，不会影响用户体验
                        }
                        // 检查是否是网络错误
                        else if (e.getErrorDetails().contains("network") ||
                            e.getErrorDetails().contains("connection")) {
                            Log.e(TAG, "检测到网络错误，请检查网络连接");
                            if (errorListener != null) {
                                errorListener.onError("网络连接错误，请检查您的网络连接并重试");
                            }
                        }
                        // 检查是否是认证错误
                        else if (e.getErrorDetails().contains("auth") ||
                                 e.getErrorDetails().contains("key") ||
                                 e.getErrorDetails().contains("subscription")) {
                            Log.e(TAG, "检测到认证错误，请检查API密钥和区域设置");
                            if (errorListener != null) {
                                errorListener.onError("认证错误，请联系管理员");
                            }
                        }
                        // 检查是否是音频错误
                        else if (e.getErrorDetails().contains("audio") ||
                                 e.getErrorDetails().contains("microphone")) {
                            Log.e(TAG, "检测到音频错误，请检查麦克风设置");
                            if (errorListener != null) {
                                errorListener.onError("麦克风错误，请检查您的麦克风设置");
                            }
                        }
                        // 其他错误
                        else {
                            if (errorListener != null) {
                                errorListener.onError("翻译错误: " + e.getErrorDetails());
                            }
                        }

                        // 如果是日语，提供更多信息
                        final String errorSourceLang = sourceLanguage.toLowerCase();
                        if (errorSourceLang.startsWith("ja")) {
                            Log.e(TAG, "日语翻译错误，可能需要调整配置或检查网络连接");
                        }
                    } else {
                        // 对于非错误取消（如用户主动停止），记录但不显示错误
                        Log.d(TAG, "翻译正常取消，原因: " + e.getReason());
                    }
                } finally {
                    // 确保在所有情况下都释放信号量
                    Log.d(TAG, "释放停止识别信号量");
                    stopRecognitionSemaphore.release(5); // 释放多个许可，确保不会阻塞
                }
            });

            translationRecognizer.sessionStopped.addEventListener((o, e) -> {
                Log.d(TAG, "翻译会话已停止");
                try {
                    Log.d(TAG, "释放停止识别信号量 (sessionStopped)");
                    stopRecognitionSemaphore.release(5); // 释放多个许可，确保不会阻塞
                } catch (Exception ex) {
                    Log.e(TAG, "释放信号量失败", ex);
                }
            });

            // 开始连续翻译
            isRecognizing = true;

            // 记录详细的配置信息
            Log.d(TAG, "======== 开始语音翻译 ========");
            Log.d(TAG, "源语言: " + sourceLanguage);
            Log.d(TAG, "目标语言: " + targetLanguage);
            Log.d(TAG, "是否多语言模式: " + isMultiLanguageMode);
            Log.d(TAG, "连续模式: " + continuousMode);

            // 记录备选语言列表
            if (isMultiLanguageMode) {
                Log.d(TAG, "===== 多语言识别配置 =====");
                Log.d(TAG, "识别模式: " + (continuousMode ? "连续识别 (Continuous)" : "开始时识别 (AtStart)"));

                // 解析备选语言列表
                String[] languageArray = sourceLanguage.split(",");
                Log.d(TAG, "备选语言数量: " + languageArray.length);
                for (int i = 0; i < languageArray.length; i++) {
                    Log.d(TAG, "备选语言 " + (i + 1) + ": " + languageArray[i].trim());
                }

                // 记录语言检测配置
                Log.d(TAG, "语言检测最小持续时间: 0ms");
                Log.d(TAG, "语言检测最大持续时间: 100ms");
                Log.d(TAG, "语言检测置信度阈值: 0.05");
                Log.d(TAG, "语言检测优先级: High");
            }

            // 记录翻译识别器的属性
            try {
                String[] propertyNames = {
                    "SpeechServiceConnection_LanguageIdMode",
                    "SpeechServiceConnection_LanguageIdLanguages",
                    "SpeechServiceConnection_RecoLanguage",
                    "SpeechServiceConnection_ContinuousLanguageIdPriority"
                };

                for (String propName : propertyNames) {
                    try {
                        String propValue = translationRecognizer.getProperties().getProperty(propName);
                        if (propValue != null && !propValue.isEmpty()) {
                            Log.d(TAG, "配置属性 " + propName + ": " + propValue);
                        }
                    } catch (Exception ex) {
                        // 忽略错误
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "获取配置属性失败", e);
            }

            // 启动连续识别
            translationRecognizer.startContinuousRecognitionAsync();
            Log.d(TAG, "已启动连续识别");
        } catch (Exception e) {
            Log.e(TAG, "启动语音翻译失败", e);
            if (errorListener != null) {
                errorListener.onError("启动语音翻译失败: " + e.getMessage());
            }
            isRecognizing = false;
        }
    }

    /**
     * 停止语音识别或翻译
     */
    public void stopRecognition() {
        try {
            // 先设置标志位，避免UI卡顿
            isRecognizing = false;

            Log.d(TAG, "开始停止语音识别/翻译...");

            // 处理语音识别器
            if (speechRecognizer != null) {
                Log.d(TAG, "正在停止语音识别器...");
                stopSpeechRecognizer();
            }

            // 处理翻译识别器
            if (translationRecognizer != null) {
                Log.d(TAG, "正在停止翻译识别器...");
                stopTranslationRecognizer();
            }

            // 释放信号量，确保不会阻塞
            try {
                stopRecognitionSemaphore.drainPermits();
                stopRecognitionSemaphore.release(5);
                Log.d(TAG, "已释放停止识别信号量");
            } catch (Exception e) {
                Log.e(TAG, "释放信号量失败", e);
            }

            // 清空回调
            recognitionResultListener = null;
            translationResultListener = null;
            languageDetectionListener = null;

            // 等待一小段时间，确保所有操作都已完成
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                // 忽略中断异常
            }

            Log.d(TAG, "已完全停止语音识别/翻译");
        } catch (Exception e) {
            Log.e(TAG, "停止语音识别/翻译失败", e);
            if (errorListener != null) {
                errorListener.onError("停止语音识别/翻译失败: " + e.getMessage());
            }
        } finally {
            // 确保状态被重置
            isRecognizing = false;
        }
    }

    /**
     * 停止语音识别器
     */
    private void stopSpeechRecognizer() {
        if (speechRecognizer == null) return;

        try {
            // 设置标志位，防止回调继续处理
            try {
                Log.d(TAG, "设置标志位，防止语音识别器回调继续处理...");
                // Microsoft Speech SDK没有提供移除所有事件监听器的方法
                // 我们使用isRecognizing标志位来控制回调处理
                isRecognizing = false;
                Log.d(TAG, "已设置标志位，防止语音识别器回调继续处理");
            } catch (Exception e) {
                Log.e(TAG, "设置标志位失败", e);
            }

            // 尝试优雅地停止
            try {
                Log.d(TAG, "尝试停止语音识别...");

                // 创建一个Future来跟踪异步操作
                java.util.concurrent.Future<Void> stopFuture = speechRecognizer.stopContinuousRecognitionAsync();

                // 等待操作完成，但设置超时
                try {
                    stopFuture.get(2, java.util.concurrent.TimeUnit.SECONDS);
                    Log.d(TAG, "语音识别已正常停止");
                } catch (java.util.concurrent.TimeoutException te) {
                    Log.w(TAG, "停止语音识别超时，将强制关闭");
                } catch (Exception e) {
                    Log.e(TAG, "等待停止语音识别时出错", e);
                }
            } catch (Exception e) {
                Log.e(TAG, "停止语音识别失败", e);
            }

            // 等待一小段时间，确保所有操作都已完成
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                // 忽略中断异常
            }
        } finally {
            // 无论如何都要关闭识别器
            try {
                Log.d(TAG, "正在关闭语音识别器...");
                speechRecognizer.close();
                Log.d(TAG, "语音识别器已关闭");
            } catch (Exception e) {
                Log.e(TAG, "关闭语音识别器失败", e);
            }
            speechRecognizer = null;
        }
    }

    /**
     * 停止翻译识别器
     */
    private void stopTranslationRecognizer() {
        if (translationRecognizer == null) return;

        try {
            // 设置标志位，防止回调继续处理
            try {
                Log.d(TAG, "设置标志位，防止翻译识别器回调继续处理...");
                // Microsoft Speech SDK没有提供移除所有事件监听器的方法
                // 我们使用isRecognizing标志位来控制回调处理
                isRecognizing = false;
                Log.d(TAG, "已设置标志位，防止翻译识别器回调继续处理");
            } catch (Exception e) {
                Log.e(TAG, "设置标志位失败", e);
            }

            // 尝试优雅地停止
            try {
                Log.d(TAG, "尝试停止翻译识别...");

                // 创建一个Future来跟踪异步操作
                java.util.concurrent.Future<Void> stopFuture = translationRecognizer.stopContinuousRecognitionAsync();

                // 等待操作完成，但设置超时
                try {
                    stopFuture.get(2, java.util.concurrent.TimeUnit.SECONDS);
                    Log.d(TAG, "翻译识别已正常停止");
                } catch (java.util.concurrent.TimeoutException te) {
                    Log.w(TAG, "停止翻译识别超时，将强制关闭");
                } catch (Exception e) {
                    Log.e(TAG, "等待停止翻译识别时出错", e);
                }
            } catch (Exception e) {
                Log.e(TAG, "停止翻译识别失败", e);
            }

            // 等待一小段时间，确保所有操作都已完成
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                // 忽略中断异常
            }
        } finally {
            // 无论如何都要关闭识别器
            try {
                Log.d(TAG, "正在关闭翻译识别器...");
                translationRecognizer.close();
                Log.d(TAG, "翻译识别器已关闭");
            } catch (Exception e) {
                Log.e(TAG, "关闭翻译识别器失败", e);
            }

            // 释放自动语言检测配置
            if (autoDetectConfig != null) {
                try {
                    autoDetectConfig.close();
                    Log.d(TAG, "自动语言检测配置已关闭");
                } catch (Exception e) {
                    Log.e(TAG, "关闭自动语言检测配置失败", e);
                }
                autoDetectConfig = null;
            }

            // 清空回调
            recognitionResultListener = null;
            translationResultListener = null;
            languageDetectionListener = null;

            translationRecognizer = null;
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        stopRecognition();
    }

    /**
     * 是否正在识别
     */
    public boolean isRecognizing() {
        return isRecognizing;
    }

    /**
     * 设置识别结果回调
     */
    public void setRecognitionResultListener(OnRecognitionResultListener listener) {
        this.recognitionResultListener = listener;
    }

    /**
     * 设置翻译结果回调
     */
    public void setTranslationResultListener(OnTranslationResultListener listener) {
        this.translationResultListener = listener;
    }

    /**
     * 设置错误回调
     */
    public void setErrorListener(OnErrorListener listener) {
        this.errorListener = listener;
    }

    /**
     * 设置语言检测回调
     */
    public void setLanguageDetectionListener(OnLanguageDetectionListener languageDetectionListener) {
        this.languageDetectionListener = languageDetectionListener;
    }

    /**
     * 将语言代码映射为微软翻译API支持的格式
     */
    private String mapLanguageCode(String languageCode) {
        // 记录原始语言代码和映射结果，便于调试
        String mappedCode;

        // 微软翻译API使用的是ISO 639-1格式的语言代码
        if (languageCode.startsWith("zh-CN")) {
            mappedCode = "zh-Hans";
        } else if (languageCode.startsWith("zh-TW")) {
            mappedCode = "zh-Hant";
        } else if (languageCode.startsWith("en")) {
            mappedCode = "en";
        } else if (languageCode.startsWith("ja")) {
            // 日语特殊处理
            // 注意：微软翻译API需要使用"ja"，但语音识别API需要使用"ja-jp"（小写）
            // 这里返回"ja"是因为这个方法主要用于翻译目标语言的映射
            mappedCode = "ja";
        } else if (languageCode.startsWith("ko")) {
            mappedCode = "ko";
        } else if (languageCode.startsWith("fr")) {
            mappedCode = "fr";
        } else if (languageCode.startsWith("es")) {
            mappedCode = "es";
        } else if (languageCode.startsWith("it")) {
            mappedCode = "it";
        } else if (languageCode.startsWith("de")) {
            mappedCode = "de";
        } else if (languageCode.startsWith("ru")) {
            mappedCode = "ru";
        } else {
            // 默认取第一部分
            mappedCode = languageCode.split("-")[0];
        }

        Log.d(TAG, "语言代码映射: " + languageCode + " -> " + mappedCode);
        return mappedCode;
    }

    /**
     * 识别结果回调接口
     */
    public interface OnRecognitionResultListener {
        void onRecognizing(String text);
        void onRecognized(String text);
    }

    /**
     * 翻译结果回调接口
     */
    public interface OnTranslationResultListener {
        void onTranslating(String text);
        void onTranslated(String text);
    }

    /**
     * 错误回调接口
     */
    public interface OnErrorListener {
        void onError(String errorMessage);
    }

    /**
     * 语言检测回调接口
     */
    public interface OnLanguageDetectionListener {
        /**
         * 检测到语言时的回调
         * @param languageCode 语言代码
         */
        default void onLanguageDetected(String languageCode) {
            onLanguageDetected(languageCode, 0, 0);
        }

        /**
         * 检测到语言时的回调（带时间信息）
         * @param languageCode 语言代码
         * @param offset 偏移量（毫秒）
         * @param duration 持续时间（毫秒）
         */
        void onLanguageDetected(String languageCode, long offset, long duration);
    }

    /**
     * 将分号分隔的语言列表转换为JSON数组格式
     * 例如："en-us;ja-jp;zh-cn" -> "\"en-us\",\"ja-jp\",\"zh-cn\""
     */
    private String convertLanguageListToJson(String languageList) {
        if (languageList == null || languageList.isEmpty()) {
            return "\"en-us\"";
        }

        // 记录原始输入，便于调试
        Log.d(TAG, "转换语言列表为JSON，输入: " + languageList);

        String[] languages = languageList.split(";");
        StringBuilder jsonArray = new StringBuilder();

        for (int i = 0; i < languages.length; i++) {
            // 确保语言代码是小写的
            String lang = languages[i].trim().toLowerCase();

            // 确保日语使用正确的格式
            if (lang.startsWith("ja")) {
                lang = "ja-jp";
            }

            jsonArray.append("\"").append(lang).append("\"");
            if (i < languages.length - 1) {
                jsonArray.append(",");
            }
        }

        String result = jsonArray.toString();
        Log.d(TAG, "转换语言列表为JSON，输出: " + result);
        return result;
    }

    /**
     * 检查麦克风状态
     */
    private void checkMicrophoneStatus() {
        try {
            // 记录麦克风检查开始
            Log.d(TAG, "开始检查麦克风状态...");

            // 在实际应用中，这里可以添加更多麦克风状态检查的代码
            // 例如检查麦克风权限、音量级别等

            // 记录麦克风检查完成
            Log.d(TAG, "麦克风状态检查完成");
        } catch (Exception e) {
            Log.e(TAG, "检查麦克风状态失败", e);
        }
    }

    /**
     * 获取诊断信息
     * 返回当前的性能和网络状态信息，用于诊断问题
     */
    public String getDiagnosticInfo() {
        StringBuilder info = new StringBuilder();

        // 添加网络状态信息
        info.append("网络状态: ").append(checkNetworkStatus()).append("\n");

        // 添加性能信息
        if (startRecognitionTime > 0) {
            info.append("识别开始时间: ").append(startRecognitionTime).append("\n");

            if (hasReceivedFirstResult) {
                long latency = firstResultTime - startRecognitionTime;
                info.append("首次识别延迟: ").append(latency).append("ms\n");
            } else {
                info.append("尚未收到首次识别结果\n");
            }

            if (responseCount > 0) {
                long avgResponseTime = totalResponseTime / responseCount;
                info.append("平均服务器响应时间: ").append(avgResponseTime).append("ms\n");
                info.append("最近一次响应时间: ").append(lastResponseTime).append("ms\n");
                info.append("响应次数: ").append(responseCount).append("\n");
            } else {
                info.append("尚未收到服务器响应\n");
            }
        } else {
            info.append("尚未开始识别\n");
        }

        // 添加问题诊断
        if (isNetworkIssueDetected) {
            info.append("诊断结果: 检测到网络问题，建议检查网络连接\n");
        } else if (isServerIssueDetected) {
            info.append("诊断结果: 检测到服务器响应较慢，可能是服务器负载问题\n");
        } else if (hasReceivedFirstResult && (firstResultTime - startRecognitionTime) > 3000) {
            info.append("诊断结果: 首次识别延迟较高，可能是网络延迟或服务器处理慢\n");
        } else {
            info.append("诊断结果: 未检测到明显问题\n");
        }

        return info.toString();
    }

    /**
     * 使用音频配置开始语音识别
     *
     * @param sourceLanguage 源语言代码，例如zh-CN、en-US等，多语言模式下用逗号分隔，如"zh-CN,en-US,ja-JP"
     * @param audioConfig 音频配置
     * @param waitForResult 是否等待识别结果
     * @param resultCallback 识别结果回调
     */
    public void startRecognitionWithAudioConfig(String sourceLanguage, AudioConfig audioConfig, boolean waitForResult, RecognitionResultCallback resultCallback) {
        if (isRecognizing) {
            Log.d(TAG, "已经在识别中，忽略请求");
            return;
        }

        try {
            // 清除之前的识别器
            if (speechRecognizer != null) {
                speechRecognizer.close();
                speechRecognizer = null;
            }

            // 检查是否是多语言模式
            boolean isMultiLanguageMode = sourceLanguage.contains(",");
            Log.d(TAG, "源语言: " + sourceLanguage + ", 是否为多语言模式: " + isMultiLanguageMode);

            // 创建语音配置
            SpeechConfig speechConfig;

            if (isMultiLanguageMode) {
                // 对于多语言模式，使用标准配置
                Log.d(TAG, "使用标准配置进行多语言识别");
                speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);

                // 设置语言检测模式
                // 使用Continuous模式，持续检测语言
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdMode", "Continuous");
                Log.d(TAG, "设置语言检测模式: Continuous (持续检测语言)");

                // 记录详细的语言检测配置
                Log.d(TAG, "===== 语音识别详细配置 =====");
                Log.d(TAG, "识别模式: 连续识别模式 (Continuous)");
                Log.d(TAG, "等待结果: " + waitForResult);

                // 设置语言检测的最小持续时间为最小值，使其立即响应语言变化
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdMinimumDuration", "0");
                Log.d(TAG, "语言检测最小持续时间: 0ms");

                // 设置语言检测的最大持续时间为较小值，加快语言切换速度
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdMaximumDuration", "100");
                Log.d(TAG, "语言检测最大持续时间: 100ms");

                // 设置语言检测的置信度阈值为最低值，确保能够检测到任何语言变化
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdConfidenceThreshold", "0.05");
                Log.d(TAG, "语言检测置信度阈值: 0.05");

                // 设置语言检测任务优先级为最高优先级
                speechConfig.setProperty("SpeechServiceConnection_LanguageDetectionTaskPriority", "High");
                Log.d(TAG, "语言检测任务优先级: High");

                // 解析多个语言
                String[] languages = sourceLanguage.split(",");
                StringBuilder languageList = new StringBuilder();

                // 构建语言列表（使用分号分隔，符合微软API要求）
                for (int i = 0; i < languages.length; i++) {
                    String lang = languages[i].trim();
                    // 确保语言代码格式正确（区域代码大写）
                    if (lang.contains("-")) {
                        String[] parts = lang.split("-");
                        if (parts.length == 2) {
                            lang = parts[0].toLowerCase() + "-" + parts[1].toUpperCase();
                        }
                    }

                    languageList.append(lang);
                    if (i < languages.length - 1) {
                        languageList.append(";");
                    }
                }

                // 设置语言列表属性 - 使用我们构建的语言列表
                // 注意：Microsoft官方文档要求使用正确的BCP-47格式，区域代码应该大写
                String supportedLanguages = languageList.toString();
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", supportedLanguages);
                Log.d(TAG, "设置语言列表: " + supportedLanguages);

                // 确保日志输出与实际设置一致
                Log.d(TAG, "实际设置的语言列表: " + supportedLanguages);

                // 记录备选语言详情
                Log.d(TAG, "===== 备选语言详情 =====");
                String[] supportedLanguageArray = supportedLanguages.split(";");
                for (int i = 0; i < supportedLanguageArray.length; i++) {
                    Log.d(TAG, "备选语言 " + (i + 1) + ": " + supportedLanguageArray[i]);
                }

                // 记录Microsoft官方文档链接
                Log.d(TAG, "参考文档: https://learn.microsoft.com/zh-cn/azure/ai-services/Speech-Service/language-identification?tabs=continuous&pivots=programming-language-java#candidate-languages");

                // 设置为优先考虑延迟的模式（与微软示例一致）
                speechConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Latency");

                // 设置语言检测成功后的行为
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize");

                // 设置语言检测未知时的行为
                speechConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize");

                // 设置默认识别语言为中文（使用正确的BCP-47格式，区域代码大写）
                speechConfig.setSpeechRecognitionLanguage("zh-CN");
                speechConfig.setProperty("SpeechServiceConnection_RecoLanguage", "zh-CN");
                Log.d(TAG, "设置默认识别语言: zh-CN");

                // 确保日志输出与实际设置一致
                Log.d(TAG, "实际设置的默认识别语言: " + speechConfig.getSpeechRecognitionLanguage());
            } else {
                // 对于单语言模式，使用标准配置
                speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);
                // 设置语音识别语言
                speechConfig.setSpeechRecognitionLanguage(sourceLanguage);
            }

            // 设置详细输出格式
            speechConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText");

            // 设置说话人分割
            speechConfig.setProperty("diarizationEnabled", "true");

            // 设置时间戳
            speechConfig.setProperty("wordLevelTimestamps", "true");

            // 设置详细输出
            speechConfig.setProperty("format", "detailed");

            // 设置沉默检测
            // 设置沉默超时时间为1秒（1000毫秒）- 恢复到原始值
            speechConfig.setProperty("SpeechServiceConnection_EndSilenceTimeoutMs", "1000");

            // 设置初始沉默超时时间为3秒（3000毫秒）- 恢复到原始值
            speechConfig.setProperty("SpeechServiceConnection_InitialSilenceTimeoutMs", "3000");

            // 创建语音识别器
            if (isMultiLanguageMode) {
                // 根据Microsoft官方文档创建语言配置
                // 参考: https://learn.microsoft.com/zh-cn/azure/ai-services/Speech-Service/language-identification?tabs=continuous&pivots=programming-language-java#candidate-languages
                Log.d(TAG, "根据Microsoft官方文档创建语言配置");

                // 使用分号分隔的语言列表
                String[] languageArray = sourceLanguage.split(",");
                List<String> languageList = new ArrayList<>();

                // 将逗号分隔的语言列表转换为标准格式
                for (String lang : languageArray) {
                    String formattedLang = lang.trim();
                    // 确保语言代码格式正确（区域代码大写）
                    if (formattedLang.contains("-")) {
                        String[] parts = formattedLang.split("-");
                        if (parts.length == 2) {
                            formattedLang = parts[0].toLowerCase() + "-" + parts[1].toUpperCase();
                        }
                    }
                    languageList.add(formattedLang);
                    Log.d(TAG, "添加候选语言: " + formattedLang);
                }

                // 使用官方推荐的方法创建自动检测源语言配置
                Log.d(TAG, "使用AutoDetectSourceLanguageConfig.fromLanguages()方法创建语言配置");
                AutoDetectSourceLanguageConfig autoDetectSourceLanguageConfig =
                    AutoDetectSourceLanguageConfig.fromLanguages(languageList);

                Log.d(TAG, "已创建AutoDetectSourceLanguageConfig，包含 " + languageList.size() + " 种语言");

                // 使用AutoDetectSourceLanguageConfig创建语音识别器
                speechRecognizer = new SpeechRecognizer(speechConfig, autoDetectSourceLanguageConfig, audioConfig);

                Log.d(TAG, "已创建带有自动语言检测的语音识别器");
            } else {
                // 对于单语言模式，使用标准配置
                speechRecognizer = new SpeechRecognizer(speechConfig, audioConfig);
            }

            // 注册事件处理程序
            speechRecognizer.recognizing.addEventListener((o, e) -> {
                String text = e.getResult().getText();
                Log.d(TAG, "识别中: " + text);
                
                // 检查回调是否已设置
                if (recognitionResultListener != null) {
                    // 通过UI线程更新，确保回调不会在后台线程上执行
                    new Handler(Looper.getMainLooper()).post(() -> {
                        Log.d(TAG, "触发onRecognizing回调，文本长度：" + text.length());
                        recognitionResultListener.onRecognizing(text);
                    });
                } else {
                    Log.w(TAG, "识别结果回调未设置，无法通知识别中事件");
                }
            });

            speechRecognizer.recognized.addEventListener((o, e) -> {
                SpeechRecognitionResult result = e.getResult();
                
                if (result.getReason() == ResultReason.RecognizedSpeech) {
                    String text = result.getText();
                    Log.d(TAG, "识别完成: " + text);
                    
                    // 检查识别文本是否有内容
                    if (text != null && !text.trim().isEmpty()) {
                        // 检查回调是否已设置
                        if (recognitionResultListener != null) {
                            // 通过UI线程更新，确保回调不会在后台线程上执行
                            new Handler(Looper.getMainLooper()).post(() -> {
                                Log.d(TAG, "触发onRecognized回调，文本长度：" + text.length());
                                recognitionResultListener.onRecognized(text);
                            });
                        } else {
                            Log.w(TAG, "识别结果回调未设置，无法通知识别完成事件");
                        }
                        
                        // 检查是否有语言检测回调
                        if (languageDetectionListener != null) {
                            // 尝试从识别结果中提取语言信息
                            try {
                                String detectedLanguage = null;
                                
                                if (result.getProperties() != null) {
                                    detectedLanguage = result.getProperties().getProperty("RESULT_LANGUAGES");
                                    
                                    // 如果未获取到语言信息，使用备选字段
                                    if (detectedLanguage == null || detectedLanguage.isEmpty()) {
                                        detectedLanguage = result.getProperties().getProperty("SpeechServiceConnection_AutoDetectSourceLanguageResult");
                                    }
                                }
                                
                                // 如果获取到语言代码，触发回调
                                if (detectedLanguage != null && !detectedLanguage.isEmpty()) {
                                    final String finalLang = detectedLanguage;
                                    new Handler(Looper.getMainLooper()).post(() -> {
                                        Log.d(TAG, "触发onLanguageDetected回调，检测到语言：" + finalLang);
                                        languageDetectionListener.onLanguageDetected(finalLang, 0, 0);
                                    });
                                }
                            } catch (Exception ex) {
                                Log.e(TAG, "获取语言信息失败", ex);
                            }
                        }
                    } else {
                        Log.w(TAG, "识别结果为空，不触发回调");
                    }
                } else if (result.getReason() == ResultReason.NoMatch) {
                    Log.d(TAG, "无法识别语音");
                }
                
                // 在识别完成后，不再重新启动识别流程，保持连续识别状态
                // 原来有停止和重启的代码，现在移除以避免不必要的会话中断
                // 记录当前识别状态即可
                if (isRecognizing) {
                    Log.d(TAG, "识别持续进行中...");
                }
            });

            speechRecognizer.canceled.addEventListener((o, e) -> {
            });

            speechRecognizer.canceled.addEventListener((o, e) -> {
                Log.d(TAG, "识别取消: " + e.getReason());
                if (e.getReason() == CancellationReason.Error) {
                    Log.e(TAG, "错误详情: " + e.getErrorCode() + ", " + e.getErrorDetails());
                    if (errorListener != null) {
                        errorListener.onError("识别错误: " + e.getErrorDetails());
                    }
                }
                stopRecognitionSemaphore.release();
            });

            speechRecognizer.sessionStopped.addEventListener((o, e) -> {
                Log.d(TAG, "识别会话已停止");
                stopRecognitionSemaphore.release();
            });

            // 开始识别
            isRecognizing = true;

            if (waitForResult) {
                // 使用单次识别
                Log.d(TAG, "开始单次语音识别，语言: " + sourceLanguage);
                speechRecognizer.recognizeOnceAsync();
            } else {
                // 使用连续识别
                Log.d(TAG, "开始连续语音识别，语言: " + sourceLanguage);
                speechRecognizer.startContinuousRecognitionAsync();
            }
        } catch (Exception e) {
            Log.e(TAG, "启动语音识别失败", e);
            if (errorListener != null) {
                errorListener.onError("启动语音识别失败: " + e.getMessage());
            }
            isRecognizing = false;
        }
    }

    /**
     * 识别结果回调接口
     */
    public interface RecognitionResultCallback {
        void onResult(SpeechRecognitionResult result);
    }
}
