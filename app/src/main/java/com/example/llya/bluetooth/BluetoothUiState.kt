package com.example.llya.bluetooth

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattService

// 连接状态枚举
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED
}

// 统一UI状态
data class BluetoothUiState(
    val isScanning: Boolean = false,
    val scanResults: List<BluetoothDevice> = emptyList(),
    val connectionState: ConnectionState = ConnectionState.DISCONNECTED,
    val connectedDevice: BluetoothDevice? = null,
    val discoveredServices: List<BluetoothGattService> = emptyList(),
    val deviceInfo: Map<String, String> = emptyMap(),
    val statusMessage: String? = null
) 