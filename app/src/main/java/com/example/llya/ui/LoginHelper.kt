package com.example.llya.ui

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.network.UserManager
import com.example.llya.viewmodel.UserViewModel
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch

/**
 * 登录帮助工具类
 * 
 * 用于简化登录功能的调用，提供快捷方法
 */
object LoginHelper {
    
    /**
     * 检查用户是否已登录
     * 
     * @param context 上下文
     * @return 是否已登录
     */
    fun isUserLoggedIn(context: Context): Boolean {
        return UserManager.getInstance(context).isLoggedIn()
    }
    
    /**
     * 退出登录
     * 
     * @param context 上下文
     */
    fun logout(context: Context) {
        UserManager.getInstance(context).logout()
    }
    
    /**
     * 在Compose中获取当前用户登录状态
     * 
     * @return 是否已登录
     */
    @Composable
    fun isLoggedIn(): Boolean {
        val userViewModel: UserViewModel = viewModel()
        return userViewModel.isLoggedIn()
    }
    
    /**
     * 发送手机验证码
     * 
     * @param context 上下文
     * @param mobile 手机号
     * @param callback 结果回调
     */
    fun sendSmsVerifyCode(context: Context, mobile: String, callback: (Boolean, String, String?) -> Unit) {
        val scope = MainScope()
        
        scope.launch {
            UserManager.getInstance(context).sendSms(mobile)
                .catch { e ->
                    callback(false, e.message ?: "发送验证码失败", null)
                }
                .collect { response ->
                    if (response.status == 200) {
                        callback(true, "验证码发送成功", response.key)
                    } else {
                        callback(false, response.msg ?: "发送验证码失败", null)
                    }
                }
        }
    }
    
    /**
     * 发送邮箱验证码
     * 
     * @param context 上下文
     * @param email 邮箱地址
     * @param callback 结果回调
     */
    fun sendEmailVerifyCode(context: Context, email: String, callback: (Boolean, String, String?) -> Unit) {
        val scope = MainScope()
        
        scope.launch {
            UserManager.getInstance(context).sendEmailCode(email)
                .catch { e ->
                    callback(false, e.message ?: "发送验证码失败", null)
                }
                .collect { response ->
                    if (response.status == 200) {
                        callback(true, "验证码发送成功", response.key)
                    } else {
                        callback(false, response.msg ?: "发送验证码失败", null)
                    }
                }
        }
    }
    
    /**
     * 手机登录
     * 
     * @param context 上下文
     * @param mobile 手机号
     * @param verifyId 验证ID
     * @param verify 验证码
     * @param callback 结果回调
     */
    fun phoneLogin(
        context: Context,
        mobile: String,
        verifyId: String,
        verify: String,
        callback: (Boolean, String) -> Unit
    ) {
        val scope = MainScope()
        
        scope.launch {
            UserManager.getInstance(context).login(mobile, verifyId, verify)
                .catch { e ->
                    callback(false, e.message ?: "登录失败")
                }
                .collect { response ->
                    if (response.status == 200 && response.data != null) {
                        callback(true, "登录成功")
                    } else {
                        callback(false, response.msg ?: "登录失败")
                    }
                }
        }
    }
    
    /**
     * 邮箱登录
     * 
     * @param context 上下文
     * @param email 邮箱
     * @param verifyId 验证ID
     * @param verify 验证码
     * @param callback 结果回调
     */
    fun emailLogin(
        context: Context,
        email: String,
        verifyId: String,
        verify: String,
        callback: (Boolean, String) -> Unit
    ) {
        val scope = MainScope()
        
        scope.launch {
            UserManager.getInstance(context).emailLogin(email, verifyId, verify)
                .catch { e ->
                    callback(false, e.message ?: "登录失败")
                }
                .collect { response ->
                    if (response.status == 200 && response.data != null) {
                        callback(true, "登录成功")
                    } else {
                        callback(false, response.msg ?: "登录失败")
                    }
                }
        }
    }
} 