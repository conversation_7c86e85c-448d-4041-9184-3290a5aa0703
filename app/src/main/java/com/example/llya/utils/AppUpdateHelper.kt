package com.example.llya.utils

import android.content.Intent
import android.net.Uri
import androidx.activity.ComponentActivity
import androidx.lifecycle.ViewModelProvider
import com.example.llya.viewmodel.AppUpdateViewModel

/**
 * APP更新帮助类，用于在应用的任何地方快速调用更新功能
 */
object AppUpdateHelper {
    
    /**
     * 显示更新弹窗
     * @param activity 当前活动
     * @param forceUpdate 是否是强制更新
     * @param versionName 版本名称
     * @param updateContent 更新内容
     * @param downloadUrl 下载地址
     */
    fun showUpdateDialog(
        activity: ComponentActivity,
        forceUpdate: Boolean = true,
        versionName: String = "2.0.0",
        updateContent: String = "1. 修复了一些已知问题\n2. 优化了语音识别功能\n3. 增加了新的翻译语言支持\n4. 改进了用户界面体验",
        downloadUrl: String = "https://example.com/download/llya.apk"
    ) {
        // 获取AppUpdateViewModel实例
        val viewModel = ViewModelProvider(activity.viewModelStore, ViewModelProvider.AndroidViewModelFactory.getInstance(activity.application))
            .get(AppUpdateViewModel::class.java)
        
        // 设置更新信息
        viewModel.setUpdateInfo(versionName, 20, updateContent, downloadUrl, forceUpdate)
        
        // 显示更新弹窗
        viewModel.showUpdateDialog()
    }
    
    /**
     * 直接打开浏览器下载APP
     * @param activity 当前活动
     * @param downloadUrl 下载地址
     */
    fun directDownload(activity: ComponentActivity, downloadUrl: String = "https://example.com/download/llya.apk") {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(downloadUrl))
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        activity.startActivity(intent)
    }
} 