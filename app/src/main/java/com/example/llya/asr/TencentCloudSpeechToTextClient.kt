package com.example.llya.asr

import android.util.Log
import com.example.llya.utils.SignatureUtil
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import okhttp3.*
import okio.ByteString
import java.util.concurrent.TimeUnit

class TencentCloudSpeechToTextClient(
    private val appId: String,
    private val secretId: String,
    private val secretKey: String,
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "TencentCloudSpeechToTextClient"
        private const val BASE_URL = "asr.cloud.tencent.com/asr/v2"
        private const val ASR_URL = "wss://$BASE_URL"
        private const val SILENCE_THRESHOLD = 1000L // 设置为1秒的适中静音阈值
    }

    private var lastUpdateTime: Long = System.currentTimeMillis()
    private val _recognitionResult = MutableStateFlow<String>("")
    val recognitionResult: StateFlow<String> = _recognitionResult

    // 添加句子是否完成的标志
    private val _isFinalResult = MutableStateFlow<Boolean>(false)
    val isFinalResult: StateFlow<Boolean> = _isFinalResult

    // 存储当前语句的识别结果
    private var currentSentence: String = ""

    // 存储累积的所有语句
    private val _accumulatedText = MutableStateFlow<String>("")
    val accumulatedText: StateFlow<String> = _accumulatedText

    // 保存最后一次有效结果
    private var lastValidResult: String = ""

    // 移除自动语言检测相关代码

    // 记录已经通过slice_type=2处理过的文本，避免重复
    private var hasProcessedSliceType2 = false
    private var lastProcessedText = ""

    // 存储最近处理过的文本列表，用于去重
    private val recentlyProcessedTexts = mutableListOf<String>()

    private var webSocket: WebSocket? = null
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()

    // Moshi适配器，用于JSON序列化和反序列化
    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()

    private val asrResponseAdapter: JsonAdapter<AsrResponse> = moshi.adapter(AsrResponse::class.java)
    private val endSignalAdapter: JsonAdapter<EndSignal> = moshi.adapter(EndSignal::class.java)

    // 标记是否是第一帧音频数据
    private var isFirstAudioFrame = true

    // 连接WebSocket
    fun connect(engineModelType: String = "16k_multi_lang") {
        // 清除临时状态但保留累积文本
        clearExceptAccumulatedText()

        // 重置第一帧标记
        isFirstAudioFrame = true

        coroutineScope.launch(Dispatchers.IO) {
            try {
                // 生成参数和签名
                val voiceId = SignatureUtil.generateVoiceId()
                val timestamp = (System.currentTimeMillis() / 1000).toString()
                val expireTime = (System.currentTimeMillis() / 1000 + 86400).toString()
                val nonce = SignatureUtil.generateNonce()

                val params = mutableMapOf(
                    "engine_model_type" to engineModelType,
                    "voice_id" to voiceId,
                    "timestamp" to timestamp,
                    "expired" to expireTime,
                    "nonce" to nonce,
                    "voice_format" to "1", // 1: PCM
                    "secretid" to secretId,
                    "needvad" to "1", // 1: 需要VAD
                    "hotword_id" to "08003a00000000000000000000000000", // 启用热词
                    "vad_silence_time" to "1000" // 设置VAD静音检测时间为1000ms
                )

                // 生成签名
                val signature = SignatureUtil.generateSignature(
                    secretId,
                    secretKey,
                    "asr.cloud.tencent.com/asr/v2/$appId",
                    params
                )

                // 构建请求URL
                val urlBuilder = StringBuilder()
                urlBuilder.append("wss://asr.cloud.tencent.com/asr/v2/")
                urlBuilder.append(appId)
                urlBuilder.append("?")

                params.forEach { (key, value) ->
                    urlBuilder.append("$key=$value&")
                }
                urlBuilder.append("signature=$signature")
                val url = urlBuilder.toString()

                Log.d(TAG, "连接URL: $url")

                // 创建请求
                val request = Request.Builder()
                    .url(url)
                    .build()

                // 连接WebSocket
                webSocket = client.newWebSocket(request, createWebSocketListener())

                Log.d(TAG, "已发起WebSocket连接")
            } catch (e: Exception) {
                Log.e(TAG, "连接失败: ${e.message}", e)
                _recognitionResult.value = "连接失败: ${e.message}"
            }
        }
    }

    // 发送音频数据
    fun sendAudioData(data: ByteArray) {
        // 如果是第一帧音频数据，只重置识别状态，但保留累积文本
        if (isFirstAudioFrame) {
            Log.d(TAG, "收到第一帧音频数据，保留累积文本但重置识别状态")
            clearExceptAccumulatedText()
            isFirstAudioFrame = false
        }

        // 发送音频数据到WebSocket
        webSocket?.send(ByteString.of(*data))

        // 检查静音
        checkSilence()
    }

    /**
     * 结束语音识别
     * 优化版本：确保在结束识别时正确处理最后一个句子，并为下一次会话做好准备
     */
    fun endRecognition() {
        try {
            // 标记下次为第一帧，以便下次发送音频时清除历史
            isFirstAudioFrame = true

            // 保存当前句子的副本和累积文本副本
            val lastSentence = currentSentence
            val existingAccumulatedText = _accumulatedText.value

            // 如果当前句子不为空且有意义，考虑将其添加到累积文本中
            if (lastSentence.isNotEmpty() && lastSentence.length > 2) {
                Log.d(TAG, "结束识别时考虑添加最后一个句子到累积文本: '$lastSentence'")

                // 进行更严格的重复检查，避免添加重复内容
                // 检查累积文本是否已经包含最后一个句子
                if (!existingAccumulatedText.contains(lastSentence)) {
                    // 检查最后一个句子是否包含累积文本的大部分内容
                    if (lastSentence.contains(existingAccumulatedText) &&
                        existingAccumulatedText.length > 10 &&
                        lastSentence.length > existingAccumulatedText.length * 1.2) {
                        // 最后一个句子包含了累积文本的大部分内容，且明显更长，使用它替换
                        Log.d(TAG, "最后一个句子包含累积文本且明显更长，使用它替换")
                        _accumulatedText.value = lastSentence
                    } else {
                        // 检查是否有显著重叠
                        val overlap = findOverlap(existingAccumulatedText, lastSentence)
                        if (overlap.length > 5) {
                            // 有显著重叠，只添加非重叠部分
                            val nonOverlappingPart = lastSentence.substring(overlap.length)
                            if (nonOverlappingPart.isNotEmpty()) {
                                _accumulatedText.value = existingAccumulatedText + nonOverlappingPart
                                Log.d(TAG, "添加最后一个句子的非重叠部分: '$nonOverlappingPart'")
                            } else {
                                Log.d(TAG, "最后一个句子与累积文本完全重叠，已跳过")
                            }
                        } else {
                            // 没有显著重叠，根据标点符号添加适当的分隔符
                            val separator = if (existingAccumulatedText.endsWith(".") ||
                                               existingAccumulatedText.endsWith("。") ||
                                               existingAccumulatedText.endsWith("!") ||
                                               existingAccumulatedText.endsWith("！") ||
                                               existingAccumulatedText.endsWith("?") ||
                                               existingAccumulatedText.endsWith("？")) {
                                "\n" // 句子结束，添加换行
                            } else if (existingAccumulatedText.endsWith(",") ||
                                       existingAccumulatedText.endsWith("，") ||
                                       existingAccumulatedText.endsWith(";") ||
                                       existingAccumulatedText.endsWith("；") ||
                                       existingAccumulatedText.endsWith(":") ||
                                       existingAccumulatedText.endsWith("：")) {
                                " " // 句内停顿，添加空格
                            } else {
                                " " // 默认添加空格
                            }
                            _accumulatedText.value = existingAccumulatedText + separator + lastSentence
                            Log.d(TAG, "添加最后一个句子，使用分隔符: '$separator'")
                        }
                    }

                    // 添加到已处理文本列表
                    addToProcessedTexts(lastSentence)

                    // 标记为最终结果
                    _isFinalResult.value = true

                    // 更新UI上显示的最终结果
                    _recognitionResult.value = _accumulatedText.value
                } else {
                    Log.d(TAG, "最后一个句子已包含在累积文本中，已跳过: '$lastSentence'")
                }
            }

            // 清空当前句子，但不清除累积文本
            currentSentence = ""

            // 记录当前累积文本状态
            val finalAccumulatedText = _accumulatedText.value
            Log.d(TAG, "结束识别，最终累积文本: '${finalAccumulatedText.take(100)}${if (finalAccumulatedText.length > 100) "..." else ""}'")

            // 发送结束信号
            val endSignal = EndSignal(type = "end")
            val endSignalJson = endSignalAdapter.toJson(endSignal)
            webSocket?.send(endSignalJson)

            Log.d(TAG, "已发送结束信号")

            // 关闭WebSocket
            webSocket?.close(1000, "正常关闭")
            webSocket = null

            // 确保在结束后累积文本不会丢失
            if (_accumulatedText.value.isEmpty() && existingAccumulatedText.isNotEmpty()) {
                Log.d(TAG, "检测到累积文本被清空，正在恢复...")
                _accumulatedText.value = existingAccumulatedText
            }

            // 为下一次会话做好准备
            // 清空临时状态，但保留累积文本和最后处理的文本
            // 这样在下一次会话开始时，我们有足够的上下文来判断新文本是否重复
            _recognitionResult.value = ""
            _isFinalResult.value = false
            hasProcessedSliceType2 = false

            // 清空recentlyProcessedTexts列表，但保留最后一个处理的文本
            // 这样在新会话开始时，我们只有一个参考点来判断重复
            if (recentlyProcessedTexts.isNotEmpty()) {
                val lastProcessed = recentlyProcessedTexts.last()
                recentlyProcessedTexts.clear()
                recentlyProcessedTexts.add(lastProcessed)
            }

            Log.d(TAG, "已为下一次会话做好准备，保留累积文本和最后处理的文本")
        } catch (e: Exception) {
            Log.e(TAG, "结束识别时出错: ${e.message}", e)
        }
    }

    // 创建WebSocket监听器
    private fun createWebSocketListener(): WebSocketListener {
        return object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接已建立")
                _recognitionResult.value = "连接已建立，请开始说话..."
                // 注意：状态已在connect方法中通过clearAll完全重置
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到消息: $text")
                try {
                    val response = asrResponseAdapter.fromJson(text)
                    response?.let {
                        if (it.code == 0) {
                            // 检测声音活动开始 - 通过slice_type=0判断
                            if (it.result?.slice_type == 0) {
                                Log.d(TAG, "检测到新的语音输入开始(slice_type=0)，保留累积文本但重置识别状态")
                                clearExceptAccumulatedText()
                                return@let
                            }

                            // 成功接收到识别结果
                            it.result?.voice_text_str?.let { newText ->
                                if (newText.isNotEmpty()) {
                                    // 重置句子完成标志
                                    _isFinalResult.value = false

                                    // 更新临时显示识别结果
                                    _recognitionResult.value = newText

                                    // 处理最终结果(slice_type=2)
                                    if (it.result.slice_type == 2) {
                                        Log.d(TAG, "收到最终结果(slice_type=2): '$newText'")

                                        // 检查是否有意义的文本且不是重复
                                        if (newText.length > 2 && !isDuplicateText(newText)) {
                                            // 更新当前句子
                                            currentSentence = newText

                                            // 添加到已处理文本列表
                                            addToProcessedTexts(newText)

                                            // 添加到累积文本
                                            addToAccumulatedText(newText)

                                            // 记录最新处理的文本
                                            lastProcessedText = newText

                                            // 移除自动语言检测逻辑

                                            // 标记为最终结果
                                            _isFinalResult.value = true

                                            // 更新UI上显示的识别结果为完整累积文本
                                            _recognitionResult.value = _accumulatedText.value
                                        } else {
                                            Log.d(TAG, "跳过无意义或重复的最终结果: '$newText'")
                                        }
                                    }
                                    // 处理中间结果
                                    else {
                                        // 仅当文本长度大于3才考虑作为有效中间结果
                                        if (newText.length > 3) {
                                            // 更新当前句子
                                            currentSentence = newText

                                            // 检测句子结束标志
                                            val containsEndMarks = newText.contains("。") ||
                                                                 newText.contains("！") ||
                                                                 newText.contains("？") ||
                                                                 newText.contains(".") ||
                                                                 newText.contains("!") ||
                                                                 newText.contains("?")

                                            // 仅当句子包含结束标志且文本长度足够时，才添加到累积文本
                                            if (containsEndMarks && newText.length >= 5 && !isDuplicateText(newText)) {
                                                Log.d(TAG, "中间结果检测到句子结束标志，添加到累积文本: '$newText'")

                                                // 添加到已处理文本
                                                addToProcessedTexts(newText)

                                                // 添加到累积文本
                                                addToAccumulatedText(newText)

                                                // 更新最后处理的文本
                                                lastProcessedText = newText

                                                // 标记为最终结果
                                                _isFinalResult.value = true

                                                // 更新UI上显示的识别结果为完整累积文本
                                                _recognitionResult.value = _accumulatedText.value
                                            } else {
                                                // 中间结果，仅更新显示
                                                _recognitionResult.value = if (_accumulatedText.value.isEmpty()) {
                                                    newText
                                                } else {
                                                    // 如果已有累积文本，显示累积文本+当前正在识别的内容
                                                    "${_accumulatedText.value} + [${newText}]"
                                                }
                                            }
                                        }
                                    }

                                    // 保存最后一次有效结果
                                    lastValidResult = newText

                                    // 更新上次活动时间
                                    lastUpdateTime = System.currentTimeMillis()
                                }
                            }
                        } else {
                            Log.e(TAG, "识别错误: ${it.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析响应失败: ${e.message}", e)
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败: ${t.message}", t)
                _recognitionResult.value = "连接失败: ${t.message}"
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket正在关闭, code: $code, reason: $reason")

                // 保存关闭前的累积文本
                val existingAccumulatedText = _accumulatedText.value

                // 确保将当前句子添加到累积文本，但避免重复添加
                if (currentSentence.isNotEmpty() && !existingAccumulatedText.contains(currentSentence)) {
                    Log.d(TAG, "WebSocket关闭时添加当前句子到累积文本: '$currentSentence'")

                    // 检查是否有显著重叠
                    val overlap = findOverlap(existingAccumulatedText, currentSentence)
                    if (overlap.length > 5) {
                        // 有显著重叠，只添加非重叠部分
                        val nonOverlappingPart = currentSentence.substring(overlap.length)
                        if (nonOverlappingPart.isNotEmpty()) {
                            _accumulatedText.value = existingAccumulatedText + nonOverlappingPart
                            Log.d(TAG, "WebSocket关闭时添加当前句子的非重叠部分: '$nonOverlappingPart'")
                        }
                    } else {
                        // 没有显著重叠，使用适当的分隔符
                        val separator = if (existingAccumulatedText.endsWith(".") ||
                                           existingAccumulatedText.endsWith("。") ||
                                           existingAccumulatedText.endsWith("!") ||
                                           existingAccumulatedText.endsWith("！") ||
                                           existingAccumulatedText.endsWith("?") ||
                                           existingAccumulatedText.endsWith("？")) {
                            "\n" // 句子结束，添加换行
                        } else {
                            " " // 默认添加空格
                        }
                        _accumulatedText.value = existingAccumulatedText + separator + currentSentence
                    }
                } else if (currentSentence.isNotEmpty()) {
                    Log.d(TAG, "WebSocket关闭时当前句子已包含在累积文本中，已跳过: '$currentSentence'")
                }

                // 清空当前句子
                currentSentence = ""

                // 确保关闭后累积文本不被清空
                if (_accumulatedText.value.isEmpty() && existingAccumulatedText.isNotEmpty()) {
                    Log.d(TAG, "WebSocket关闭过程中累积文本被清空，正在恢复...")
                    _accumulatedText.value = existingAccumulatedText
                }
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket已关闭, code: $code, reason: $reason")
            }
        }
    }

    /**
     * 检查文本是否重复
     * 修改后的版本：更宽松地接受新文本，特别是在新的录音会话开始时
     */
    private fun isDuplicateText(text: String): Boolean {
        if (text.isEmpty()) return true

        // 检查是否与当前句子完全相同
        if (text == currentSentence && currentSentence.isNotEmpty()) {
            Log.d(TAG, "与当前句子完全相同，已跳过: '$text'")
            return true
        }

        // 检查是否与上次处理的文本完全相同
        if (text == lastProcessedText && lastProcessedText.isNotEmpty()) {
            Log.d(TAG, "与上次处理文本完全相同，已跳过: '$text'")
            return true
        }

        // 主要检查：与累积文本的关系
        val currentAccumulated = _accumulatedText.value
        if (currentAccumulated.isNotEmpty()) {
            // 检查新文本是否已经完全包含在累积文本中
            // 只有当新文本长度超过5个字符时才进行这个检查
            if (text.length > 5 && currentAccumulated.contains(text)) {
                // 检查新文本是否是累积文本的最后部分
                // 如果是最后部分，可能是新会话的开始，应该允许
                val isLastPart = currentAccumulated.endsWith(text)
                if (!isLastPart) {
                    Log.d(TAG, "新文本已完全包含在累积文本中且不是最后部分，已跳过: '$text'")
                    return true
                }
            }

            // 检查累积文本是否完全包含在新文本中，且新文本不显著大于累积文本
            // 这种情况下，新文本可能是累积文本的轻微变体
            // 但如果新文本显著大于累积文本，应该接受它
            if (text.contains(currentAccumulated) && text.length < currentAccumulated.length * 1.5) {
                // 如果新文本只是在累积文本后面添加了一些内容，应该接受它
                if (!text.startsWith(currentAccumulated)) {
                    Log.d(TAG, "累积文本包含在新文本中，但新文本不显著更大且不是简单追加，已跳过: '$text'")
                    return true
                }
            }

            // 检查是否与累积文本有显著重叠
            // 只有当文本长度足够时才进行这个检查
            // 在新会话开始时，这个检查应该更宽松
            if (text.length > 15) {  // 增加长度阈值，只对较长文本进行检查
                // 计算新文本与累积文本的重叠程度
                val commonPrefix = findCommonPrefix(currentAccumulated, text)
                val commonSuffix = findCommonSuffix(currentAccumulated, text)

                // 降低重叠阈值，更宽松地接受新文本
                val overlapLength = maxOf(commonPrefix.length, commonSuffix.length)
                if (overlapLength > text.length * 0.8) {  // 提高阈值，只有极高重叠才跳过
                    Log.d(TAG, "与累积文本重叠度极高，已跳过: '$text', 重叠率: ${overlapLength.toFloat() / text.length}")
                    return true
                }
            }
        }

        // 检查是否完全相同的文本已存在于最近处理列表中
        // 这个检查不再是主要判断依据，因为在新会话中recentlyProcessedTexts会被清空
        if (recentlyProcessedTexts.contains(text)) {
            // 如果是新会话的第一个文本，即使在列表中也接受它
            if (recentlyProcessedTexts.size <= 1) {
                return false
            }
            Log.d(TAG, "完全匹配的重复文本，已跳过: '$text'")
            return true
        }

        // 检查文本是否是之前文本的子串（仅在recentlyProcessedTexts不为空时检查）
        // 在新会话开始时，这个检查应该被忽略
        if (recentlyProcessedTexts.size > 1) {
            for (existingText in recentlyProcessedTexts) {
                // 检查是否新文本完全包含在已有文本中
                if (existingText.contains(text) && existingText.length > text.length * 1.5) {
                    Log.d(TAG, "新文本是已有文本的子串，已跳过: 新='$text', 已有='${existingText.take(50)}'")
                    return true
                }
            }
        }

        return false
    }

    /**
     * 查找两个字符串的共同前缀
     */
    private fun findCommonPrefix(str1: String, str2: String): String {
        val minLength = minOf(str1.length, str2.length)
        var i = 0
        while (i < minLength && str1[i] == str2[i]) {
            i++
        }
        return str1.substring(0, i)
    }

    /**
     * 查找两个字符串的共同后缀
     */
    private fun findCommonSuffix(str1: String, str2: String): String {
        val minLength = minOf(str1.length, str2.length)
        var i = 0
        while (i < minLength &&
               str1[str1.length - 1 - i] == str2[str2.length - 1 - i]) {
            i++
        }
        return if (i > 0) str1.substring(str1.length - i) else ""
    }

    // 添加到最近处理过的文本列表
    private fun addToProcessedTexts(text: String) {
        // 限制列表大小，避免内存占用过多
        if (recentlyProcessedTexts.size > 20) {
            recentlyProcessedTexts.removeAt(0)
        }
        recentlyProcessedTexts.add(text)
    }

    /**
     * 添加文本到累积文本
     * 优化版本：更好地处理新会话中的文本追加
     */
    private fun addToAccumulatedText(text: String) {
        if (text.isEmpty()) return

        val current = _accumulatedText.value

        // 如果当前累积文本为空，直接设置
        if (current.isEmpty()) {
            _accumulatedText.value = text
            Log.d(TAG, "累积文本为空，直接设置: '$text'")
            return
        }

        // 检查新文本是否已经完全包含在累积文本中
        // 只有当新文本不是累积文本的最后部分时才跳过
        if (current.contains(text) && !current.endsWith(text)) {
            // 如果新文本是累积文本的一部分，但不是最后部分，可能是新会话的开始
            // 检查新文本是否足够长，如果足够长，可能是新的有意义内容
            if (text.length < 10) {
                Log.d(TAG, "新文本已包含在累积文本中且不是最后部分，跳过添加: '$text'")
                return
            }
        }

        // 检查是否累积文本是新文本的一部分（完全被新文本包含）
        if (text.contains(current)) {
            // 如果新文本包含累积文本，并且新文本更长，使用新文本替换
            // 降低替换阈值，更容易接受新的更完整的文本
            if (text.length > current.length * 1.1) {
                Log.d(TAG, "累积文本是新文本的子串，使用更完整的新文本: 旧='$current', 新='$text'")
                _accumulatedText.value = text
                return
            }
        }

        // 查找累积文本和新文本的重叠部分
        val overlap = findOverlap(current, text)

        // 如果有显著重叠（超过5个字符）
        if (overlap.length > 5) {
            // 计算需要添加的部分（新文本中不重叠的部分）
            val newPart = text.substring(overlap.length)
            if (newPart.isNotEmpty()) {
                _accumulatedText.value = current + newPart
                Log.d(TAG, "找到重叠(${overlap.length}字符)，只添加新部分: '${newPart.take(50)}'")
            } else {
                Log.d(TAG, "完全重叠，无需添加")
            }
            return
        }

        // 检查新文本是否可能是新会话的开始
        // 如果新文本长度适中且不与当前文本有明显重叠，可能是新会话的开始
        if (text.length > 5 && text.length < 30 && recentlyProcessedTexts.size <= 1) {
            // 添加分隔符，表示这是新会话的开始
            val updated = if (current.endsWith("\n")) {
                "$current$text"  // 已经有换行，直接添加
            } else {
                "$current\n\n$text"  // 添加两个换行，明确分隔
            }
            _accumulatedText.value = updated
            Log.d(TAG, "可能是新会话的开始，添加明确分隔: '$text'")
            return
        }

        // 根据句子结束标点符号添加适当的分隔符
        // 检查文本格式，处理特殊情况如"下面有请。领导讲话。领导讲话。"
        val updated = if (current.endsWith(".") || current.endsWith("。") ||
                          current.endsWith("!") || current.endsWith("！") ||
                          current.endsWith("?") || current.endsWith("？")) {
            // 检查新文本是否以大写字母或中文开头，这通常表示新的句子开始
            if (text.isNotEmpty() && (text[0].isUpperCase() || text[0].toString().matches(Regex("[\\u4e00-\\u9fa5]")))) {
                "$current\n$text"  // 句子结束且新文本是新句子，添加换行
            } else {
                "$current $text"  // 句子结束但新文本可能是句子的继续，添加空格
            }
        } else if (current.endsWith(",") || current.endsWith("，") ||
                   current.endsWith(";") || current.endsWith("；") ||
                   current.endsWith(":") || current.endsWith("：")) {
            "$current $text"  // 句内停顿，添加空格
        } else if (current.endsWith("\n")) {
            // 如果当前文本已经以换行结束，直接添加新文本，不需要额外的分隔符
            "$current$text"
        } else {
            // 检查当前文本的最后一个非空白字符
            val lastNonWhitespace = current.trim().lastOrNull()
            if (lastNonWhitespace != null && !lastNonWhitespace.isLetterOrDigit() && !lastNonWhitespace.toString().matches(Regex("[\\u4e00-\\u9fa5]"))) {
                // 如果最后一个非空白字符是非字母数字且非中文字符（可能是某种标点），添加空格
                "$current $text"
            } else {
                // 无标点，直接连接
                "$current$text"
            }
        }

        _accumulatedText.value = updated
        Log.d(TAG, "标准添加到累积文本: '${text.take(50)}', 结果: '${updated.takeLast(100)}'")
    }

    /**
     * 查找两个字符串的最大重叠部分
     * 比如："今天天气真好" 和 "天气真好晴空万里"
     * 重叠部分是 "天气真好"
     */
    private fun findOverlap(end: String, start: String): String {
        // 从最长的可能重叠开始检查
        val maxOverlap = minOf(end.length, start.length)

        for (overlapSize in maxOverlap downTo 1) {
            val endSuffix = end.substring(end.length - overlapSize)
            val startPrefix = start.substring(0, overlapSize)

            if (endSuffix == startPrefix) {
                return endSuffix
            }
        }

        return ""
    }

    // 清空当前识别结果
    fun clearCurrentResult() {
        _recognitionResult.value = ""
        _isFinalResult.value = false
    }

    /**
     * 清除所有文本和状态，包括累积文本
     * 注意：此方法会清除累积文本，只在用户明确要求清空所有内容时使用
     */
    fun clearAll() {
        Log.d(TAG, "清除所有文本和状态，包括累积文本")

        // 清除临时显示的识别结果
        _recognitionResult.value = ""

        // 清除是否为最终结果标志
        _isFinalResult.value = false

        // 清除累积文本
        _accumulatedText.value = ""

        // 清除当前句子
        currentSentence = ""

        // 清除已处理的文本列表
        recentlyProcessedTexts.clear()

        // 清除相关状态标志
        hasProcessedSliceType2 = false
        lastProcessedText = ""
        lastValidResult = ""

        // 重置最后更新时间
        lastUpdateTime = System.currentTimeMillis()

        Log.d(TAG, "所有文本和状态已清除，包括累积文本")
    }

    /**
     * 清除临时状态但保留累积文本
     * 用于新的识别会话开始时，保留之前的累积文本
     */
    fun clearExceptAccumulatedText() {
        Log.d(TAG, "清除临时状态但保留累积文本")

        // 保存当前累积文本
        val savedAccumulatedText = _accumulatedText.value

        // 清除临时显示的识别结果
        _recognitionResult.value = ""

        // 清除是否为最终结果标志
        _isFinalResult.value = false

        // 清除当前句子
        currentSentence = ""

        // 清除已处理的文本列表
        recentlyProcessedTexts.clear()

        // 清除相关状态标志
        hasProcessedSliceType2 = false
        lastProcessedText = ""
        lastValidResult = ""

        // 重置最后更新时间
        lastUpdateTime = System.currentTimeMillis()

        // 恢复累积文本
        _accumulatedText.value = savedAccumulatedText

        Log.d(TAG, "临时状态已清除，累积文本已保留: '${savedAccumulatedText.take(50)}${if (savedAccumulatedText.length > 50) "..." else ""}'")
    }

    // 释放资源
    fun release() {
        webSocket?.close(1000, "正常关闭")
        webSocket = null
        client.dispatcher.executorService.shutdown()
    }

    private fun checkSilence() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastUpdateTime > SILENCE_THRESHOLD) {
            lastUpdateTime = currentTime
        }
    }

    // 移除语言检测方法
}

// 响应数据类
data class AsrResponse(
    val code: Int,
    val message: String,
    val voice_id: String? = null,
    val message_id: String? = null,
    val result: AsrResult? = null,
    val final: Int? = null
)

data class AsrResult(
    val slice_type: Int? = null,
    val index: Int? = null,
    val start_time: Int? = null,
    val end_time: Int? = null,
    val voice_text_str: String? = null,
    val word_size: Int? = null,
    val word_list: List<Any>? = null
)

// 结束信号数据类
data class EndSignal(val type: String = "end")