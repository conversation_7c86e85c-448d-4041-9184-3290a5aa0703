package com.example.llya.network

import android.content.Context
import android.util.Log
import com.google.gson.GsonBuilder
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Retrofit客户端单例类
 */
object RetrofitClient {
    private const val BASE_URL = "http://8.138.134.96"
    private const val TIME_OUT = 30L
    private var token: String = ""
    private const val TAG = "RetrofitClient"

    private val gson = GsonBuilder()
        .setLenient()
        .create()

    /**
     * 设置用户Token
     */
    fun setToken(token: String) {
        this.token = token
    }

    /**
     * 获取当前Token
     */
    fun getToken(): String {
        return token
    }

    /**
     * 创建OkHttpClient实例
     */
    private fun createOkHttpClient(): OkHttpClient {
        // 认证拦截器
        val authInterceptor = object : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val original = chain.request()
                val requestBuilder = original.newBuilder()

                if (token.isNotEmpty()) {
                    // 直接使用token作为Authorization头的值，不添加Bearer前缀
                    requestBuilder.header("Authorization", token)
                    Log.d(TAG, "添加认证头: Authorization=${if (token.length > 10) token.substring(0, 5) + "..." else token}")
                }

                val request = requestBuilder.build()
                return chain.proceed(request)
            }
        }

        // 日志拦截器
        val loggingInterceptor = object : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val request = chain.request()
                val requestTime = System.nanoTime()

                Log.d(TAG, "发送请求: ${request.url}")
                Log.d(TAG, "请求头: ${request.headers}")

                val response = chain.proceed(request)
                val responseTime = System.nanoTime()

                Log.d(TAG, "接收响应: ${response.request.url}")
                Log.d(TAG, "响应耗时: ${(responseTime - requestTime) / 1e6}ms")
                Log.d(TAG, "响应码: ${response.code}")

                // 检查是否是401未授权状态码（token过期）
                if (response.code == 401) {
                    Log.e(TAG, "检测到401未授权状态码，token可能已过期")
                    // 使用应用上下文处理token过期
                    val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                    // 在主线程中处理token过期
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                    }
                }

                // 检查响应体中是否包含token过期信息
                try {
                    val responseBody = response.peekBody(Long.MAX_VALUE)
                    val responseBodyString = responseBody.string()

                    // 尝试解析JSON响应
                    try {
                        val jsonObj = org.json.JSONObject(responseBodyString)
                        val status = jsonObj.optInt("status")
                        val msg = jsonObj.optString("msg", "")

                        // 检查是否是token过期（状态码101或消息包含"token过期"）
                        if (status == 101 || msg.contains("token过期")) {
                            Log.e(TAG, "检测到API响应中的token过期信息: status=$status, msg=$msg")
                            // 使用应用上下文处理token过期
                            val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                            // 在主线程中处理token过期
                            android.os.Handler(android.os.Looper.getMainLooper()).post {
                                com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                            }
                        }
                    } catch (e: Exception) {
                        // JSON解析失败，忽略
                    }
                } catch (e: Exception) {
                    // 获取响应体失败，忽略
                }

                // 打印响应内容，针对语言列表接口进行详细记录
                try {
                    val responseBody = response.peekBody(Long.MAX_VALUE)
                    val responseBodyString = responseBody.string()

                    // 检查是否是语言接口请求
                    if (request.url.toString().contains("language/index")) {
                        Log.d(TAG, "========== 语言列表API响应开始 ==========")
                        Log.d(TAG, "响应内容(完整): $responseBodyString")
                        Log.d(TAG, "========== 语言列表API响应结束 ==========")

                        // 尝试解析JSON结构
                        try {
                            val jsonObj = org.json.JSONObject(responseBodyString)
                            Log.d(TAG, "语言接口JSON解析：status=${jsonObj.optInt("status")}, msg=${jsonObj.optString("msg")}")

                            // 检查并记录data字段的类型
                            if (jsonObj.has("data")) {
                                val data = jsonObj.get("data")
                                when (data) {
                                    is org.json.JSONArray -> {
                                        Log.d(TAG, "data是数组，长度: ${data.length()}")
                                        if (data.length() > 0) {
                                            val firstItem = data.get(0)
                                            Log.d(TAG, "第一个元素类型: ${firstItem::class.java.simpleName}, 值: $firstItem")

                                            // 如果是对象，打印其键
                                            if (firstItem is org.json.JSONObject) {
                                                Log.d(TAG, "第一个元素键：${firstItem.keys().asSequence().toList()}")
                                            }
                                        }
                                    }
                                    is org.json.JSONObject -> {
                                        Log.d(TAG, "data是对象，键: ${data.keys().asSequence().toList()}")

                                        // 检查是否有分页结构
                                        val hasPageInfo = data.has("total") || data.has("per_page") || data.has("current_page") || data.has("last_page")
                                        if (hasPageInfo) {
                                            Log.d(TAG, "发现分页结构: total=${data.optInt("total")}, current_page=${data.optInt("current_page")}")

                                            // 检查data中是否嵌套有data数组
                                            if (data.has("data") && data.get("data") is org.json.JSONArray) {
                                                val nestedData = data.getJSONArray("data")
                                                Log.d(TAG, "嵌套data数组长度: ${nestedData.length()}")

                                                if (nestedData.length() > 0) {
                                                    val firstItem = nestedData.get(0)
                                                    Log.d(TAG, "嵌套data第一个元素类型: ${firstItem::class.java.simpleName}, 值: $firstItem")

                                                    // 如果是对象，打印其键
                                                    if (firstItem is org.json.JSONObject) {
                                                        Log.d(TAG, "嵌套data第一个元素键：${firstItem.keys().asSequence().toList()}")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else -> {
                                        Log.d(TAG, "data是其他类型: ${data::class.java.simpleName}, 值: $data")
                                    }
                                }
                            } else {
                                Log.d(TAG, "响应中没有data字段")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析语言接口JSON失败: ${e.message}", e)
                        }
                    } else {
                        // 其他接口只打印简短日志
                        val shortResponseBody = if (responseBodyString.length > 500)
                            responseBodyString.substring(0, 500) + "..."
                        else
                            responseBodyString
                        Log.d(TAG, "响应内容: $shortResponseBody")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "获取响应内容失败: ${e.message}", e)
                }

                return response
            }
        }

        // 创建OkHttpClient
        return OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(TIME_OUT, TimeUnit.SECONDS)
            .readTimeout(TIME_OUT, TimeUnit.SECONDS)
            .writeTimeout(TIME_OUT, TimeUnit.SECONDS)
            .build()
    }

    /**
     * 获取Retrofit实例
     */
    private fun getRetrofit(): Retrofit {
        val contentType = "application/json".toMediaType()

        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(createOkHttpClient())
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

    /**
     * 创建API服务实例
     */
    fun createApiService(): ApiService {
        return getRetrofit().create(ApiService::class.java)
    }
}