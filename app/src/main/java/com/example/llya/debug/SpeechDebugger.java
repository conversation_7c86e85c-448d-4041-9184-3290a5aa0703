package com.example.llya.debug;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 语音识别调试工具
 * 用于记录和分析语音识别过程中的各种状态和事件
 */
public class SpeechDebugger {
    private static final String TAG = "SpeechDebugger";
    
    // 单例实例
    private static SpeechDebugger instance;
    
    // 调试日志列表
    private final CopyOnWriteArrayList<DebugEntry> debugEntries = new CopyOnWriteArrayList<>();
    
    // 调试监听器列表
    private final List<DebugListener> listeners = new ArrayList<>();
    
    // 是否启用调试
    private boolean debugEnabled = true;
    
    // 上下文
    private Context context;
    
    // 日志文件
    private File logFile;
    
    /**
     * 获取单例实例
     */
    public static synchronized SpeechDebugger getInstance() {
        if (instance == null) {
            instance = new SpeechDebugger();
        }
        return instance;
    }
    
    /**
     * 初始化调试器
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        createLogFile();
    }
    
    /**
     * 创建日志文件
     */
    private void createLogFile() {
        try {
            File directory = context.getExternalFilesDir("speech_debug_logs");
            if (directory != null) {
                if (!directory.exists()) {
                    boolean created = directory.mkdirs();
                    if (!created) {
                        Log.e(TAG, "无法创建日志目录");
                    }
                }
                
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
                String fileName = "speech_debug_" + sdf.format(new Date()) + ".log";
                logFile = new File(directory, fileName);
                
                // 写入日志头
                FileWriter writer = new FileWriter(logFile, true);
                writer.append("=== 语音识别调试日志 ===\n");
                writer.append("开始时间: ").append(new Date().toString()).append("\n\n");
                writer.flush();
                writer.close();
                
                Log.d(TAG, "创建日志文件: " + logFile.getAbsolutePath());
            }
        } catch (IOException e) {
            Log.e(TAG, "创建日志文件失败", e);
        }
    }
    
    /**
     * 添加调试条目
     */
    public void log(String category, String message) {
        if (!debugEnabled) return;
        
        DebugEntry entry = new DebugEntry(category, message);
        debugEntries.add(entry);
        
        // 写入日志文件
        writeToLogFile(entry);
        
        // 通知监听器
        notifyListeners(entry);
        
        // 打印到Logcat
        Log.d(TAG, "[" + category + "] " + message);
    }
    
    /**
     * 添加调试条目（带参数）
     */
    public void log(String category, String message, Object... params) {
        if (!debugEnabled) return;
        
        StringBuilder sb = new StringBuilder(message);
        if (params.length > 0) {
            sb.append(" | 参数: ");
            for (int i = 0; i < params.length; i++) {
                sb.append(params[i]);
                if (i < params.length - 1) {
                    sb.append(", ");
                }
            }
        }
        
        log(category, sb.toString());
    }
    
    /**
     * 写入日志文件
     */
    private void writeToLogFile(DebugEntry entry) {
        if (logFile == null) return;
        
        try {
            FileWriter writer = new FileWriter(logFile, true);
            writer.append(entry.toString()).append("\n");
            writer.flush();
            writer.close();
        } catch (IOException e) {
            Log.e(TAG, "写入日志文件失败", e);
        }
    }
    
    /**
     * 通知监听器
     */
    private void notifyListeners(DebugEntry entry) {
        for (DebugListener listener : listeners) {
            listener.onDebugEntryAdded(entry);
        }
    }
    
    /**
     * 添加调试监听器
     */
    public void addListener(DebugListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除调试监听器
     */
    public void removeListener(DebugListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 获取所有调试条目
     */
    public List<DebugEntry> getDebugEntries() {
        return new ArrayList<>(debugEntries);
    }
    
    /**
     * 清除所有调试条目
     */
    public void clearDebugEntries() {
        debugEntries.clear();
        
        // 通知监听器
        for (DebugListener listener : listeners) {
            listener.onDebugEntriesCleared();
        }
    }
    
    /**
     * 设置是否启用调试
     */
    public void setDebugEnabled(boolean enabled) {
        this.debugEnabled = enabled;
    }
    
    /**
     * 调试条目类
     */
    public static class DebugEntry {
        private final String category;
        private final String message;
        private final long timestamp;
        
        public DebugEntry(String category, String message) {
            this.category = category;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
        
        public String getCategory() {
            return category;
        }
        
        public String getMessage() {
            return message;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        @Override
        public String toString() {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault());
            return sdf.format(new Date(timestamp)) + " [" + category + "] " + message;
        }
    }
    
    /**
     * 调试监听器接口
     */
    public interface DebugListener {
        void onDebugEntryAdded(DebugEntry entry);
        void onDebugEntriesCleared();
    }
}
