package com.example.llya.aliyunasi

import android.util.Base64
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.net.URLEncoder
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

/**
 * 阿里云语音识别Token获取工具类
 */
class AliyunTokenUtil {
    companion object {
        private const val TAG = "AliyunTokenUtil"
        // 正确的Token获取URL
        private const val TOKEN_URL = "http://nls-meta.cn-shanghai.aliyuncs.com/"
        
        /**
         * 获取阿里云语音识别的访问令牌
         * @param accessKeyId 阿里云AccessKeyId
         * @param accessKeySecret 阿里云AccessKeySecret
         * @return 访问令牌，如果获取失败则返回null
         */
        suspend fun getToken(accessKeyId: String, accessKeySecret: String): String? = withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始获取阿里云Token...")
                
                val client = OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(10, TimeUnit.SECONDS)
                    .build()

                // 构建阿里云POP协议所需参数
                val params = mutableMapOf<String, String>()
                params["AccessKeyId"] = accessKeyId
                params["Action"] = "CreateToken"
                params["Version"] = "2019-02-28"
                params["Format"] = "JSON"
                params["RegionId"] = "cn-shanghai"
                params["SignatureMethod"] = "HMAC-SHA1"
                params["SignatureVersion"] = "1.0"
                
                // 生成UTC时间戳，格式：YYYY-MM-DDThh:mm:ssZ
                val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US)
                dateFormat.timeZone = TimeZone.getTimeZone("UTC")
                params["Timestamp"] = dateFormat.format(Date())
                
                // 生成唯一请求编号SignatureNonce
                params["SignatureNonce"] = UUID.randomUUID().toString()
                
                // 1. 构造规范化的请求字符串
                val sortedParams = params.entries.sortedBy { it.key }
                val canonicalizedQueryString = StringBuilder()
                for ((key, value) in sortedParams) {
                    canonicalizedQueryString
                        .append("&")
                        .append(percentEncode(key))
                        .append("=")
                        .append(percentEncode(value))
                }
                // 删除第一个&
                val queryString = canonicalizedQueryString.substring(1)
                
                // 2. 构造签名字符串
                val stringToSign = "GET" + "&" + 
                    percentEncode("/") + "&" + 
                    percentEncode(queryString)
                
                // 3. 计算签名
                val signature = sign(stringToSign, accessKeySecret + "&")
                
                // 4. 构建最终URL
                val finalUrl = "$TOKEN_URL?Signature=$signature&$queryString"
                Log.d(TAG, "生成的请求URL: $finalUrl")
                
                // 构建请求并发送
                val request = Request.Builder()
                    .url(finalUrl)
                    .get()
                    .build()
                
                val response = client.newCall(request).execute()
                val responseBody = response.body?.string()

                // 记录响应信息
                Log.d(TAG, "收到响应状态码: ${response.code}")
                responseBody?.let {
                    if (it.length > 500) {
                        Log.d(TAG, "响应内容(部分): ${it.substring(0, 500)}...")
                    } else {
                        Log.d(TAG, "响应内容: $it")
                    }
                }

                // 解析响应
                if (response.isSuccessful && responseBody != null) {
                    try {
                        val jsonObject = JSONObject(responseBody)
                        Log.d(TAG, "解析JSON成功，开始提取Token")
                        
                        // 根据文档，Token结构应该包含在Token对象中
                        if (jsonObject.has("Token")) {
                            val tokenObj = jsonObject.getJSONObject("Token")
                            val id = tokenObj.getString("Id")
                            Log.d(TAG, "获取阿里云Token成功: $id")
                            return@withContext id
                        } else {
                            // 尝试其他可能的结构
                            Log.e(TAG, "找不到Token字段，尝试读取其他可能的字段")
                            Log.d(TAG, "JSON字段: ${jsonObject.keys().asSequence().toList()}")
                            return@withContext null
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解析JSON失败", e)
                        return@withContext null
                    }
                } else {
                    Log.e(TAG, "获取阿里云Token失败: ${response.code} ${responseBody}")
                    return@withContext null
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取阿里云Token异常", e)
                return@withContext null
            }
        }
        
        /**
         * POP协议URL编码
         * 除了字母、数字和-、_、.、~以外，都要编码，空格需要编码为%20，不是+
         */
        private fun percentEncode(value: String): String {
            val encoded = URLEncoder.encode(value, "UTF-8")
            return encoded.replace("+", "%20")
                .replace("*", "%2A")
                .replace("%7E", "~")
        }
        
        /**
         * 使用HMAC-SHA1算法计算签名
         */
        private fun sign(content: String, key: String): String {
            try {
                val algorithm = "HmacSHA1"
                val mac = Mac.getInstance(algorithm)
                mac.init(SecretKeySpec(key.toByteArray(), algorithm))
                val signData = mac.doFinal(content.toByteArray())
                
                // Base64编码
                val encodedSignature = Base64.encodeToString(signData, Base64.NO_WRAP)
                
                // URL编码签名结果
                return percentEncode(encodedSignature)
            } catch (e: Exception) {
                Log.e(TAG, "计算签名失败", e)
                throw e
            }
        }
    }
} 