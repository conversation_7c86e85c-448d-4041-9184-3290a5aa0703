package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.DayOfWeek
import androidx.compose.foundation.clickable
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import kotlinx.coroutines.launch
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import android.widget.Toast

@Composable
fun MeetingRecordScreen(
    navController: NavController? = null
) {
    val darkBgColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8675E6)
    val accentColor = Color(0xFF8364FD)
    
    val speechViewModel: SpeechRecognitionViewModel = viewModel()
    val isRecording by speechViewModel.isRecording.collectAsState()
    val recognitionResult by speechViewModel.recognitionResult.collectAsState()
    val cumulativeContent by speechViewModel.cumulativeContent.collectAsState()
    val currentDisplayContent by speechViewModel.currentDisplayContent.collectAsState()
    val errorMessage by speechViewModel.errorMessage.collectAsState()
    val currentAsrServiceName by speechViewModel.currentAsrServiceName.collectAsState()
    val selectedLanguage by speechViewModel.selectedLanguage.collectAsState()
    
    // 创建一个本地状态用于显示实时内容
    var displayContent by remember { mutableStateOf("") }
    
    // 滚动状态
    val scrollState = rememberScrollState()
    
    // 语言选择菜单状态
    var showLanguageSelector by remember { mutableStateOf(false) }
    
    // 计算当前日期和星期
    val currentDate = remember {
        val formatter = DateTimeFormatter.ofPattern("yyyy年M月d日")
        val today = LocalDate.now()
        val dateStr = today.format(formatter)
        val dayOfWeek = when (today.dayOfWeek) {
            DayOfWeek.MONDAY -> "星期一"
            DayOfWeek.TUESDAY -> "星期二"
            DayOfWeek.WEDNESDAY -> "星期三"
            DayOfWeek.THURSDAY -> "星期四"
            DayOfWeek.FRIDAY -> "星期五"
            DayOfWeek.SATURDAY -> "星期六"
            DayOfWeek.SUNDAY -> "星期日"
            else -> ""
        }
        "$dateStr $dayOfWeek"
    }
    
    // 字数统计
    var wordCount by remember { mutableStateOf(0) }
    
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()
    
    // 监听识别结果和累积内容的变化
    LaunchedEffect(cumulativeContent, isRecording) {
        // 只使用累积文本，不显示实时转写内容
        displayContent = cumulativeContent
        
        // 更新字数统计
        wordCount = displayContent.length
    }
    
    // 自动滚动到底部
    LaunchedEffect(displayContent) {
        scrollState.animateScrollTo(scrollState.maxValue)
    }
    
    // 默认提示文本
    val defaultPrompt = "连接已建立，请开始说话..."
    
    // 设置为会议记录模式
    LaunchedEffect(Unit) {
        speechViewModel.toggleTranslation(false) // 关闭翻译功能
        speechViewModel.toggleMeetingMode(true)  // 开启会议模式
        // 默认选择中文 - 如果不是中文则设置为中文
        if (selectedLanguage != "zh-CN") {
            speechViewModel.setLanguage("zh-CN")
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF222131)) // 更深的背景色，与截图匹配
    ) {
        // 主要内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 24.dp, start = 24.dp, end = 24.dp)
        ) {
            // 顶部标题和清空按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "会议记录",
                    color = Color.White,
                    fontSize = 22.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row {
                    // 历史记录按钮
                    IconButton(
                        onClick = {
                            navController?.navigate("meeting_list")
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = "历史记录",
                            tint = Color.White
                        )
                    }
                    
                    // 清空按钮
                    IconButton(
                        onClick = {
                            // 清空所有会议记录
                            speechViewModel.clearContent()
                            displayContent = ""
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "清空会议记录",
                            tint = Color.White
                        )
                    }
                }
            }
            
            // ASR服务信息显示
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color(0xFF2D2C3E))
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .clickable { showLanguageSelector = !showLanguageSelector },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "语音识别引擎:",
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 14.sp
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = currentAsrServiceName,
                        color = accentColor,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Icon(
                        imageVector = Icons.Default.ArrowDropDown,
                        contentDescription = "选择语言",
                        tint = Color.White
                    )
                }
            }
            
            // 语言选择下拉菜单
            AnimatedVisibility(visible = showLanguageSelector) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    shape = RoundedCornerShape(8.dp),
                    color = Color(0xFF33324A)
                ) {
                    LazyColumn(
                        modifier = Modifier.heightIn(max = 200.dp)
                    ) {
                        items(speechViewModel.availableLanguages) { langPair ->
                            val (langCode, langName) = langPair
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        // 设置新的语言
                                        speechViewModel.setLanguage(langCode)
                                        // 关闭语言选择器
                                        showLanguageSelector = false
                                    }
                                    .padding(horizontal = 16.dp, vertical = 12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = langName,
                                    color = Color.White,
                                    fontSize = 16.sp
                                )
                                
                                if (langCode == selectedLanguage) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选择",
                                        tint = accentColor,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 日期和字数显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = currentDate,
                    color = Color.White,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = "字数: $wordCount",
                    color = purpleColor,
                    fontSize = 16.sp
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 会议记录区域 - 显示实时内容
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clip(RoundedCornerShape(16.dp)),
                color = Color(0xFF2D2C3E)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    if (displayContent.isNotEmpty()) {
                        Text(
                            text = displayContent,
                            color = Color.White,
                            fontSize = 18.sp,
                            lineHeight = 28.sp,
                            overflow = TextOverflow.Visible,
                            modifier = Modifier
                                .fillMaxWidth()
                                .verticalScroll(scrollState)
                        )
                    } else {
                        // 显示默认提示
                        Text(
                            text = if (isRecording) defaultPrompt else "点击下方按钮开始录音...",
                            color = Color.Gray,
                            fontSize = 18.sp,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 录音状态提示
            Text(
                text = if (isRecording) "正在录音..." else "点击按钮开始录音",
                color = if (isRecording) Color(0xFFFF4A4A) else Color(0xFF4CAF50),
                fontSize = 16.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 底部录音按钮
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 48.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(72.dp)
                    .clip(CircleShape)
                    .background(
                        // 根据录音状态切换颜色：录音中为红色，未录音为绿色
                        if (isRecording) Color(0xFFFF4A4A) else Color(0xFF4CAF50)
                    )
                    .clickable {
                        if (isRecording) {
                            speechViewModel.stopRecognition()
                        } else {
                            coroutineScope.launch {
                                speechViewModel.toggleTranslation(false) // 关闭翻译功能
                                speechViewModel.toggleMeetingMode(true)  // 开启会议模式
                                
                                // 确保在开始语音识别前使用当前选择的语言
                                // 这确保了服务提供商的选择是基于最新的语言设置
                                speechViewModel.setLanguage(selectedLanguage)
                                
                                // 开始语音识别
                                speechViewModel.startRecognition()
                            }
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Mic,
                    contentDescription = if (isRecording) "停止录音" else "开始录音",
                    tint = Color.White,
                    modifier = Modifier.size(30.dp)
                )
            }
        }
    }
} 