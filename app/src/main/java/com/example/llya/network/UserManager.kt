package com.example.llya.network

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.example.llya.data.ApiResponse
import com.example.llya.data.DeviceModel
import com.example.llya.data.UserModel
import com.example.llya.utils.UserCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import android.util.Log

/**
 * 用户信息管理工具类
 * 
 * 统一管理用户相关的API调用和本地用户信息存储
 */
class UserManager private constructor(context: Context) {
    private val TAG = "UserManager"
    private val apiService = RetrofitClient.createApiService()
    
    // 当前登录用户信息来自UserCache
    val currentUser: LiveData<UserModel?> = UserCache.currentUser
    
    companion object {
        private var instance: UserManager? = null
        
        @Synchronized
        fun getInstance(context: Context): UserManager {
            if (instance == null) {
                instance = UserManager(context.applicationContext)
                // 确保UserCache已初始化
                UserCache.initialize(context.applicationContext)
            }
            return instance!!
        }
    }
    
    init {
        // 从缓存中获取token并设置到RetrofitClient
        val token = UserCache.getToken()
        RetrofitClient.setToken(token)
        Log.d(TAG, "UserManager初始化，设置token: ${if (token.isEmpty()) "空" else "长度为${token.length}"}")
    }
    
    /**
     * 清除用户信息和Token
     */
    fun logout() {
        UserCache.clearUserInfo()
        RetrofitClient.setToken("")
    }
    
    /**
     * 是否已登录
     */
    fun isLoggedIn(): Boolean {
        return UserCache.isLoggedIn()
    }
    
    /**
     * 获取当前用户详细信息
     */
    fun getUserDetail(): Flow<ApiResponse<UserModel>> = flow {
        // 检查是否有token
        val token = UserCache.getToken()
        Log.d(TAG, "getUserDetail - 获取到token: ${if (token.isEmpty()) "空" else "长度为${token.length}"}")
        
        if (token.isEmpty()) {
            Log.d(TAG, "getUserDetail - Token为空，无法获取用户信息")
            emit(ApiResponse<UserModel>(status = -1, msg = "未登录，无法获取用户信息"))
            return@flow
        }
        
        // 确保RetrofitClient使用最新的token
        RetrofitClient.setToken(token)
        Log.d(TAG, "getUserDetail - 已更新RetrofitClient中的token并发起请求")
        
        val response = apiService.getUserDetail()
        Log.d(TAG, "getUserDetail - 收到响应: status=${response.status}, message=${response.msg ?: "无消息"}")
        
        if (response.status == 200 && response.data != null) {
            Log.d(TAG, "getUserDetail - 获取成功，保存用户信息")
            UserCache.saveLoginInfo(response.data, UserCache.getToken())
        } else {
            Log.d(TAG, "getUserDetail - 获取失败: ${response.msg ?: "未知错误"}")
        }
        
        emit(response)
    }.flowOn(Dispatchers.IO)
    
    /**
     * 更新用户信息
     */
    fun updateUserInfo(
        username: String,
        email: String,
        userType: Int,
        status: Int? = null,
        vipExpireTime: String? = null
    ): Flow<ApiResponse<Any>> = flow {
        val response = apiService.updateUserInfo(username, email, userType, status, vipExpireTime)
        if (response.status == 200) {
            // 更新成功后，重新获取用户详情
            getUserDetail().collect {}
        }
        emit(response)
    }.flowOn(Dispatchers.IO)
    
    /**
     * 用户账号注销
     */
    fun accountCancellation(password: String): Flow<ApiResponse<Any>> = flow {
        val response = apiService.accountCancellation(password)
        if (response.status == 200) {
            logout()
        }
        emit(response)
    }.flowOn(Dispatchers.IO)
    
    /**
     * 发送手机验证码
     */
    fun sendSms(mobile: String): Flow<ApiResponse<Any>> = flow {
        emit(apiService.sendSms(mobile))
    }.flowOn(Dispatchers.IO)
    
    /**
     * 发送邮箱验证码
     */
    fun sendEmailCode(email: String): Flow<ApiResponse<Any>> = flow {
        emit(apiService.sendEmailCode(email))
    }.flowOn(Dispatchers.IO)
    
    /**
     * 验证邮箱验证码
     */
    fun verifyEmailCode(email: String, code: String, key: String): Flow<ApiResponse<Any>> = flow {
        emit(apiService.verifyEmailCode(email, code, key))
    }.flowOn(Dispatchers.IO)
    
    /**
     * 用户登录(手机)
     */
    fun login(mobile: String, verifyId: String, verify: String): Flow<ApiResponse<UserModel>> = flow {
        Log.d(TAG, "login - 开始登录请求: mobile=$mobile")
        
        val response = apiService.login(mobile, verifyId, verify)
        Log.d(TAG, "login - 收到响应: status=${response.status}")
        
        if (response.status == 200 && response.data != null) {
            // 记录token信息
            val responseToken = response.token ?: ""
            Log.d(TAG, "login - 登录成功，收到token: ${if (responseToken.isEmpty()) "空" else "长度为${responseToken.length}"}")
            
            // 保存到UserCache
            UserCache.saveLoginInfo(response.data, responseToken)
            // 设置token到RetrofitClient
            RetrofitClient.setToken(responseToken)
            
            Log.d(TAG, "login - 用户信息和token已保存")
        } else {
            Log.d(TAG, "login - 登录失败: ${response.msg ?: "未知错误"}")
        }
        
        emit(response)
    }.flowOn(Dispatchers.IO)
    
    /**
     * 邮箱登录
     */
    fun emailLogin(
        email: String, 
        verifyId: String, 
        verify: String
    ): Flow<ApiResponse<UserModel>> = flow {
        val response = apiService.emailLogin(email, verifyId, verify)
        if (response.status == 200 && response.data != null) {
            // 保存到UserCache
            UserCache.saveLoginInfo(response.data, response.token ?: "")
            // 设置token到RetrofitClient
            RetrofitClient.setToken(response.token ?: "")
        }
        emit(response)
    }.flowOn(Dispatchers.IO)
    
    /**
     * 获取用户绑定设备
     */
    fun getUserDevices(): Flow<ApiResponse<List<DeviceModel>>> = flow {
        emit(apiService.getUserDevices())
    }.flowOn(Dispatchers.IO)
} 