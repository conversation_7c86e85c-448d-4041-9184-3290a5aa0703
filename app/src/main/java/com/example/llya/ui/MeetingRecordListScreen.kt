package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.R
import com.example.llya.utils.TextPersistenceManager.MeetingRecord
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MeetingRecordListScreen(
    navController: NavController,
    speechViewModel: SpeechRecognitionViewModel = viewModel()
) {
    val darkBgColor = Color(0xFF222131)
    val itemBgColor = Color(0xFF2D2C3E)
    val purpleColor = Color(0xFF8675E6)
    
    // 加载会议记录列表
    val records by speechViewModel.meetingRecords.collectAsState()
    
    // 删除确认对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showDeleteAllDialog by remember { mutableStateOf(false) }
    var recordToDelete by remember { mutableStateOf<MeetingRecord?>(null) }
    
    // 首次进入页面时加载会议记录
    LaunchedEffect(Unit) {
        speechViewModel.loadMeetingRecords()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.meeting_history_title), color = Color.White) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back),
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    if (records.isNotEmpty()) {
                        IconButton(onClick = { showDeleteAllDialog = true }) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = stringResource(R.string.meeting_delete_all),
                                tint = Color.Red
                            )
                        }
                    }
                }
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(darkBgColor)
                .padding(padding)
        ) {
            if (records.isEmpty()) {
                // 如果没有会议记录，显示空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无会议记录",
                        color = Color.White,
                        fontSize = 18.sp
                    )
                }
            } else {
                // 显示会议记录列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(records) { record ->
                        MeetingRecordItem(
                            record = record,
                            onClick = {
                                // 导航到会议记录详情页，并传递会议记录ID
                                navController.navigate("meeting_detail/${record.id}")
                            },
                            onDelete = {
                                // 显示删除确认对话框
                                recordToDelete = record
                                showDeleteDialog = true
                            },
                            backgroundColor = itemBgColor,
                            accentColor = purpleColor
                        )
                    }
                }
            }
            
            // 删除确认对话框
            if (showDeleteDialog && recordToDelete != null) {
                AlertDialog(
                    containerColor = Color(0xFF363547),
                    textContentColor = Color.White,
                    titleContentColor = Color.White,
                    onDismissRequest = {
                        showDeleteDialog = false
                        recordToDelete = null
                    },
                    title = { Text("删除会议记录") },
                    text = { Text("确定要删除会议记录\"${recordToDelete?.title}\"吗？此操作不可撤销。") },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                recordToDelete?.id?.let { id ->
                                    speechViewModel.deleteMeetingRecord(id)
                                }
                                showDeleteDialog = false
                                recordToDelete = null
                            }
                        ) {
                            Text("删除", color = Color.Red)
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = {
                                showDeleteDialog = false
                                recordToDelete = null
                            }
                        ) {
                            Text("取消", color = Color.White)
                        }
                    }
                )
            }
            
            // 删除全部记录确认对话框
            if (showDeleteAllDialog) {
                AlertDialog(
                    containerColor = Color(0xFF363547),
                    textContentColor = Color.White,
                    titleContentColor = Color.White,
                    onDismissRequest = { showDeleteAllDialog = false },
                    title = { Text(stringResource(R.string.meeting_delete_all)) },
                    text = { Text("确定要删除所有会议记录吗？此操作不可撤销。") },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                // 删除所有会议记录
                                speechViewModel.meetingRecords.value.forEach { record ->
                                    record.id?.let { id ->
                                        speechViewModel.deleteMeetingRecord(id)
                                    }
                                }
                                showDeleteAllDialog = false
                            }
                        ) {
                            Text("删除全部", color = Color.Red)
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showDeleteAllDialog = false }
                        ) {
                            Text("取消", color = Color.White)
                        }
                    }
                )
            }
        }
    }
}

@Composable
fun MeetingRecordItem(
    record: MeetingRecord,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    backgroundColor: Color,
    accentColor: Color
) {
    // 格式化日期
    val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    val dateString = dateFormatter.format(Date(record.timestamp))
    
    // 格式化持续时间
    val durationMinutes = record.duration / 60000
    val durationString = if (durationMinutes > 0) "$durationMinutes 分钟" else "不到1分钟"
    
    // 提取文本预览
    val previewText = if (record.content.length > 50) {
        record.content.substring(0, 50) + "..."
    } else {
        record.content
    }
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.title,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                IconButton(
                    onClick = onDelete,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = previewText,
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 14.sp,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = dateString,
                    color = accentColor,
                    fontSize = 12.sp
                )
                
                Text(
                    text = "时长: $durationString",
                    color = accentColor,
                    fontSize = 12.sp
                )
            }
        }
    }
} 