package com.example.llya.viewmodel

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class AppUpdateInfo(
    val versionName: String,
    val versionCode: Int,
    val updateContent: String,
    val downloadUrl: String,
    val forceUpdate: Boolean
)

class AppUpdateViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "AppUpdateViewModel"
    
    // 更新信息状态
    private val _updateInfo = MutableStateFlow<AppUpdateInfo?>(null)
    val updateInfo: StateFlow<AppUpdateInfo?> = _updateInfo.asStateFlow()
    
    // 显示更新弹窗的状态
    private val _showUpdateDialog = MutableStateFlow(false)
    val showUpdateDialog: StateFlow<Boolean> = _showUpdateDialog.asStateFlow()
    
    // 初始化时不再自动检查更新
    init {
        // 初始化时不执行任何操作
    }
    
    // 检查更新
    fun checkForUpdates() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "检查应用更新...")
                
                // 模拟从服务器获取更新信息
                // 在实际开发中，这里应该是网络请求，获取服务器上最新的版本信息
                val mockUpdateInfo = AppUpdateInfo(
                    versionName = "2.0.0",
                    versionCode = 20,
                    updateContent = "1. 修复了一些已知问题\n2. 优化了语音识别功能\n3. 增加了新的翻译语言支持\n4. 改进了用户界面体验",
                    downloadUrl = "https://example.com/download/llya.apk",
                    forceUpdate = true
                )
                
                // 获取当前应用版本
                val packageInfo = getApplication<Application>().packageManager.getPackageInfo(
                    getApplication<Application>().packageName, 0
                )
                val currentVersionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode.toInt()
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode
                }
                
                Log.d(TAG, "当前版本: $currentVersionCode, 服务器版本: ${mockUpdateInfo.versionCode}")
                
                // 判断是否需要更新
                // 为了演示，我们假设需要更新（服务器版本号>当前版本号）
                if (true) { // 在实际使用中，应使用 mockUpdateInfo.versionCode > currentVersionCode
                    _updateInfo.value = mockUpdateInfo
                    _showUpdateDialog.value = true
                    Log.d(TAG, "需要更新应用")
                } else {
                    Log.d(TAG, "应用已是最新版本")
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查更新出错: ${e.message}")
            }
        }
    }
    
    // 手动设置更新信息
    fun setUpdateInfo(
        versionName: String,
        versionCode: Int,
        updateContent: String,
        downloadUrl: String,
        forceUpdate: Boolean
    ) {
        _updateInfo.value = AppUpdateInfo(
            versionName = versionName,
            versionCode = versionCode,
            updateContent = updateContent,
            downloadUrl = downloadUrl,
            forceUpdate = forceUpdate
        )
        Log.d(TAG, "手动设置更新信息: 版本 $versionName, 强制更新: $forceUpdate")
    }
    
    // 手动显示更新弹窗（用于测试或特定场景）
    fun showUpdateDialog() {
        if (_updateInfo.value == null) {
            // 如果还没有更新信息，先获取一个模拟的更新信息
            _updateInfo.value = AppUpdateInfo(
                versionName = "2.0.0",
                versionCode = 20,
                updateContent = "1. 修复了一些已知问题\n2. 优化了语音识别功能\n3. 增加了新的翻译语言支持\n4. 改进了用户界面体验",
                downloadUrl = "https://example.com/download/llya.apk",
                forceUpdate = true
            )
        }
        _showUpdateDialog.value = true
    }
    
    // 下载APP
    fun downloadApp() {
        viewModelScope.launch {
            try {
                _updateInfo.value?.let { updateInfo ->
                    Log.d(TAG, "准备下载应用: ${updateInfo.downloadUrl}")
                    
                    // 打开浏览器下载
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(updateInfo.downloadUrl))
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    getApplication<Application>().startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "下载应用出错: ${e.message}")
            }
        }
    }
    
    // 关闭更新弹窗
    fun dismissUpdateDialog() {
        _showUpdateDialog.value = false
    }
}
