package com.example.llya.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material.icons.filled.Language
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.viewmodel.TencentCloudAsrViewModel

// 支持的语言列表
data class LanguageOption(
    val code: String,
    val name: String
)

val supportedLanguages = listOf(
    LanguageOption("16k_zh", "中文普通话"),
    LanguageOption("16k_en", "英语"),
    LanguageOption("16k_ja", "日语"),
    LanguageOption("16k_ko", "韩语"),
    LanguageOption("16k_fr", "法语"),
    LanguageOption("16k_es", "西班牙语"),
    LanguageOption("16k_ru", "俄语"),
    LanguageOption("16k_de", "德语"),
    LanguageOption("16k_it", "意大利语"),
    LanguageOption("16k_th", "泰语"),
    LanguageOption("16k_pt", "葡萄牙语"),
    LanguageOption("16k_vi", "越南语"),
    LanguageOption("16k_id", "印尼语"),
    LanguageOption("16k_ms", "马来语"),
    LanguageOption("16k_hi", "印地语")
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TencentCloudAsrScreen(
    navController: NavController,
    viewModel: TencentCloudAsrViewModel = viewModel()
) {
    val isRecording by viewModel.isRecording.collectAsState()
    val recognitionResult by viewModel.recognitionResult.collectAsState()
    val accumulatedText by viewModel.accumulatedText.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val isConnected by viewModel.isConnected.collectAsState()
    
    // 语言选择相关状态
    var showLanguageDialog by remember { mutableStateOf(false) }
    var selectedLanguage by remember { mutableStateOf(supportedLanguages[0]) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("腾讯云语音识别") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 语言选择按钮
                    IconButton(onClick = { showLanguageDialog = true }) {
                        Icon(Icons.Default.Language, contentDescription = "选择语言")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 当前选择的语言
            Text(
                text = "当前语言：${selectedLanguage.name}",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.primary
            )

            // 连接状态
            Text(
                text = if (isConnected) "已连接" else "未连接",
                style = MaterialTheme.typography.bodyLarge,
                color = if (isConnected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
            )

            // 错误信息
            errorMessage?.let { error ->
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                    textAlign = TextAlign.Center
                )
            }

            // 当前识别结果
            Card(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "当前识别结果",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = recognitionResult,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }

            // 累积文本
            Card(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "累积文本",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = accumulatedText,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            // 录音按钮
            Button(
                onClick = {
                    if (isRecording) {
                        viewModel.stopRecording()
                    } else {
                        viewModel.startRecording(selectedLanguage.code)
                    }
                },
                modifier = Modifier
                    .size(72.dp)
                    .padding(8.dp)
            ) {
                Icon(
                    imageVector = if (isRecording) Icons.Default.MicOff else Icons.Default.Mic,
                    contentDescription = if (isRecording) "停止录音" else "开始录音",
                    modifier = Modifier.size(32.dp)
                )
            }

            // 清除按钮
            TextButton(
                onClick = { viewModel.clearResults() },
                modifier = Modifier.padding(8.dp)
            ) {
                Text("清除结果")
            }
        }
    }

    // 语言选择对话框
    if (showLanguageDialog) {
        AlertDialog(
            onDismissRequest = { showLanguageDialog = false },
            title = { Text("选择识别语言") },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState())
                ) {
                    supportedLanguages.forEach { language ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = language.code == selectedLanguage.code,
                                onClick = {
                                    selectedLanguage = language
                                    showLanguageDialog = false
                                }
                            )
                            Text(
                                text = language.name,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showLanguageDialog = false }) {
                    Text("关闭")
                }
            }
        )
    }
} 