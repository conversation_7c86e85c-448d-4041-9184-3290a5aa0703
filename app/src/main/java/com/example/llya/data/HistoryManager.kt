package com.example.llya.data

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 * 历史记录管理器
 * 使用SharedPreferences本地存储管理历史记录，不依赖数据库
 */
class HistoryManager private constructor() {
    
    companion object {
        private const val TAG = "HistoryManager"
        private const val PREF_NAME = "translation_history"
        private const val KEY_HISTORY_RECORDS = "history_records"
        
        @Volatile
        private var instance: HistoryManager? = null
        private var applicationContext: Context? = null
        
        // 获取单例实例
        fun getInstance(): HistoryManager {
            return instance ?: synchronized(this) {
                instance ?: HistoryManager().also { instance = it }
            }
        }
        
        // 初始化方法，需要在应用启动时调用
        fun initialize(context: Context) {
            applicationContext = context.applicationContext
            // 确保单例实例已创建，并加载历史记录
            getInstance().loadHistoryRecords()
        }
    }
    
    // 历史记录列表，使用StateFlow以便UI观察变化
    private val _historyRecords = MutableStateFlow<List<HistoryRecord>>(emptyList())
    val historyRecords: StateFlow<List<HistoryRecord>> = _historyRecords.asStateFlow()
    
    // 最大记录数量
    private val maxRecordCount = 100
    
    // 获取SharedPreferences
    private val prefs: SharedPreferences?
        get() = applicationContext?.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    // Gson用于JSON序列化和反序列化
    private val gson = Gson()
    
    /**
     * 从SharedPreferences加载历史记录
     */
    private fun loadHistoryRecords() {
        try {
            val jsonString = prefs?.getString(KEY_HISTORY_RECORDS, null)
            if (!jsonString.isNullOrEmpty()) {
                val type = object : TypeToken<List<HistoryRecord>>() {}.type
                val loadedRecords = gson.fromJson<List<HistoryRecord>>(jsonString, type)
                _historyRecords.value = loadedRecords
                Log.d(TAG, "成功从本地存储加载${loadedRecords.size}条历史记录")
            } else {
                Log.d(TAG, "本地存储中没有历史记录")
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载历史记录失败", e)
            _historyRecords.value = emptyList()
        }
    }
    
    /**
     * 保存历史记录到SharedPreferences
     */
    private fun saveHistoryRecords() {
        try {
            val jsonString = gson.toJson(_historyRecords.value)
            prefs?.edit()?.putString(KEY_HISTORY_RECORDS, jsonString)?.apply()
            Log.d(TAG, "历史记录已保存到本地存储，共${_historyRecords.value.size}条")
        } catch (e: Exception) {
            Log.e(TAG, "保存历史记录失败", e)
        }
    }
    
    /**
     * 添加历史记录
     */
    fun addRecord(record: HistoryRecord) {
        Log.d(TAG, "添加历史记录: $record")
        
        // 检查是否已存在相同内容的记录（避免重复）
        val existingSimilarRecord = _historyRecords.value.firstOrNull { 
            it.sourceText == record.sourceText && 
            it.translatedText == record.translatedText &&
            it.sourceLanguage == record.sourceLanguage &&
            it.targetLanguage == record.targetLanguage &&
            System.currentTimeMillis() - it.timestamp < 10000 // 10秒内的记录视为可能重复
        }
        
        // 如果存在类似记录，不添加新记录
        if (existingSimilarRecord != null) {
            Log.d(TAG, "忽略相似记录")
            return
        }
        
        // 添加到列表头部
        _historyRecords.update { currentList ->
            val newList = currentList.toMutableList()
            newList.add(0, record)
            
            // 如果超过最大记录数，删除旧记录
            if (newList.size > maxRecordCount) {
                newList.subList(0, maxRecordCount)
            } else {
                newList
            }
        }
        
        // 保存到本地存储
        saveHistoryRecords()
    }
    
    /**
     * 删除历史记录
     */
    fun deleteRecord(id: String) {
        _historyRecords.update { currentList ->
            currentList.filterNot { it.id == id }
        }
        // 保存到本地存储
        saveHistoryRecords()
    }
    
    /**
     * 清空所有历史记录
     */
    fun clearAll() {
        _historyRecords.value = emptyList()
        // 保存到本地存储
        saveHistoryRecords()
    }
    
    /**
     * 将记录标记为收藏/取消收藏
     */
    fun toggleFavorite(id: String) {
        _historyRecords.update { currentList ->
            currentList.map { 
                if (it.id == id) it.copy(isFavorite = !it.isFavorite) else it 
            }
        }
        // 保存到本地存储
        saveHistoryRecords()
    }
    
    /**
     * 获取收藏记录
     */
    fun getFavorites(): List<HistoryRecord> {
        return historyRecords.value.filter { it.isFavorite }
    }
    
    /**
     * 根据类型筛选记录
     */
    fun getRecordsByType(type: RecordType): List<HistoryRecord> {
        return historyRecords.value.filter { it.type == type }
    }
    
    /**
     * 搜索历史记录
     */
    fun searchRecords(query: String): List<HistoryRecord> {
        if (query.isEmpty()) return historyRecords.value
        
        return historyRecords.value.filter {
            it.sourceText.contains(query, ignoreCase = true) || 
            it.translatedText.contains(query, ignoreCase = true)
        }
    }
} 