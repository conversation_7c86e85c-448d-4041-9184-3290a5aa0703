package com.example.llya.aliyunasi

import android.Manifest
import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState

@OptIn(ExperimentalPermissionsApi::class, ExperimentalMaterial3Api::class)
@Composable
fun AliyunAsrScreen(
    onNavigateBack: () -> Unit,
    accessKeyId: String,
    accessKeySecret: String,
    appKey: String
) {
    val context = LocalContext.current
    val viewModel: AliyunAsrViewModel = viewModel(
        factory = AliyunAsrViewModel.Factory(
            context = context,
            accessKeyId = accessKeyId,
            accessKeySecret = accessKeySecret,
            appKey = appKey
        )
    )
    
    val recognitionResult by viewModel.recognitionResult.collectAsState()
    val connectionStatus by viewModel.connectionStatus.collectAsState()
    val isRecording by viewModel.isRecording.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    
    val micPermissionState = rememberPermissionState(Manifest.permission.RECORD_AUDIO)
    val micPermissionGranted = micPermissionState.status.isGranted
    
    // 显示麦克风权限状态
    LaunchedEffect(micPermissionGranted) {
        if (!micPermissionGranted) {
            Log.d("AliyunAsrScreen", "麦克风权限未授予")
        } else {
            Log.d("AliyunAsrScreen", "麦克风权限已授予")
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("阿里云实时语音识别") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 连接状态
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "连接状态",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = connectionStatus,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
            
            // 识别结果
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        if (recognitionResult.isEmpty()) {
                            Text(
                                text = "语音识别结果将显示在这里...",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                            )
                            
                            // 如果处于录音状态但没有识别结果，显示提示
                            if (isRecording) {
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = "请对着麦克风说话...\n如果长时间没有识别结果，请检查麦克风是否正常工作",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.primary,
                                    textAlign = TextAlign.Center
                                )
                            }
                        } else {
                            Text(
                                text = recognitionResult,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }
                }
            }
            
            // 错误信息
            if (errorMessage.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = errorMessage,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onErrorContainer,
                                modifier = Modifier.weight(1f)
                            )
                            IconButton(onClick = { viewModel.clearError() }) {
                                Icon(
                                    Icons.Default.Close,
                                    contentDescription = "清除错误",
                                    tint = MaterialTheme.colorScheme.onErrorContainer
                                )
                            }
                        }
                        
                        // 添加重试按钮（仅在出现特定错误时显示）
                        if (errorMessage.contains("超时") || errorMessage.contains("连接失败")) {
                            Button(
                                onClick = { viewModel.retryConnection() },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.primary
                                )
                            ) {
                                Text("重试连接")
                            }
                        }
                    }
                }
            }
            
            // 录音按钮
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Button(
                    onClick = {
                        if (!micPermissionState.status.isGranted) {
                            Log.d("AliyunAsrScreen", "请求麦克风权限")
                            micPermissionState.launchPermissionRequest()
                        } else {
                            if (isRecording) {
                                Log.d("AliyunAsrScreen", "停止录音")
                                viewModel.stopRecognition()
                            } else {
                                Log.d("AliyunAsrScreen", "开始录音")
                                viewModel.startRecognition()
                            }
                        }
                    },
                    modifier = Modifier
                        .size(80.dp),
                    shape = RoundedCornerShape(40.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isRecording) MaterialTheme.colorScheme.errorContainer else MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Call,
                        contentDescription = if (isRecording) "停止录音" else "开始录音",
                        modifier = Modifier.size(32.dp),
                        tint = if (isRecording) MaterialTheme.colorScheme.onErrorContainer else MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                
                Text(
                    text = if (isRecording) "点击停止录音" else "点击开始录音",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp, bottom = 8.dp)
                )
                
                if (!isRecording) {
                    Text(
                        text = "提示：按下按钮后，请对着麦克风清晰地说话",
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }
            }
        }
    }
    
    // 在组件卸载时释放资源
    DisposableEffect(viewModel) {
        onDispose {
            viewModel.stopRecognition()
        }
    }
} 