package com.example.llya.network

import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.location.Location
import android.location.LocationManager
import android.os.Build
import android.os.Build.VERSION_CODES
import android.util.Log
import android.annotation.SuppressLint
import com.example.llya.data.ApiResponse
import com.example.llya.data.DeviceModel
import com.example.llya.utils.UserCache
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import retrofit2.http.Path
import okhttp3.OkHttpClient
import okhttp3.Interceptor
import okhttp3.Response
import java.util.concurrent.TimeUnit
import okio.Buffer

/**
 * 设备绑定API接口
 */
interface DeviceBindApi {
    /**
     * 绑定蓝牙设备
     */
    @POST("/api/device/bind")
    suspend fun bindDevice(@Body request: DeviceBindRequest): ApiResponse<DeviceBindResponse>

    /**
     * 获取设备详细信息
     */
    @POST("/api/device/info/{deviceId}")
    suspend fun getDeviceInfo(@Path("deviceId") deviceId: String): ApiResponse<DeviceInfoResponse>

    /**
     * 获取设备详情（新接口）
     */
    @FormUrlEncoded
    @POST("/api/device/detail")
    suspend fun getDeviceDetail(@Field("macaddress") macAddress: String): ApiResponse<DeviceInfoResponse>

    /**
     * 解绑设备
     */
    @FormUrlEncoded
    @POST("/api/device/unbind")
    suspend fun unbindDevice(@Field("macaddress") macAddress: String): ApiResponse<Any>
}

/**
 * 设备绑定请求参数
 */
data class DeviceBindRequest(
    @SerializedName("deviceName") val deviceName: String,
    @SerializedName("macaddress") val macAddress: String,
    @SerializedName("location") val location: LocationInfo? = null
)

/**
 * 位置信息
 */
data class LocationInfo(
    val longitude: Double,
    val latitude: Double
)

/**
 * 设备绑定响应结果
 */
data class DeviceBindResponse(
    @SerializedName("deviceId") val deviceId: String,
    @SerializedName("bindTime") val bindTime: String
)

/**
 * 设备详细信息响应
 */
data class DeviceInfoResponse(
    @SerializedName("deviceId") val deviceId: String? = null,
    @SerializedName("deviceName") val deviceName: String? = null,
    @SerializedName("macAddress") val macAddress: String? = null,
    @SerializedName("status") val status: Int? = null,
    @SerializedName("bindTime") val bindTime: String? = null,
    @SerializedName("battery") val battery: Int? = null,
    @SerializedName("firmwareVersion") val firmwareVersion: String? = null,
    @SerializedName("location") val location: String? = null,
    @SerializedName("image") val image: String? = null
)

/**
 * 设备绑定管理工具类
 *
 * 提供设备绑定、解绑和设备信息查询等功能
 */
class DeviceBindManager private constructor(private val context: Context) {
    private val TAG = "DeviceBindManager"
    private val apiService = RetrofitClient.createApiService()

    // 为了适配项目现有结构，我们需要自定义一个Retrofit实例
    private val deviceApi: DeviceBindApi by lazy {
        // 创建适用于设备API的Retrofit实例
        val BASE_URL = "http://************"  // 从API文档中获取

        // 创建认证拦截器
        val authInterceptor = object : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val original = chain.request()
                val requestBuilder = original.newBuilder()

                // 使用UserCache获取token
                val token = UserCache.getToken()

                if (token.isNotEmpty()) {
                    // 根据服务器要求格式化认证头
                    requestBuilder.header("Authorization", token)
                    Log.d(TAG, "添加认证头: $token")
                } else {
                    Log.d(TAG, "未找到有效token")
                }

                val request = requestBuilder.build()
                return chain.proceed(request)
            }
        }

        // 日志拦截器
        val loggingInterceptor = object : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val request = chain.request()
                Log.d(TAG, "发送请求: ${request.url}")
                Log.d(TAG, "请求头: ${request.headers}")

                // 尝试记录请求体
                val requestBody = request.body
                if (requestBody != null) {
                    Log.d(TAG, "请求体类型: ${requestBody.contentType()}")
                    // 尝试打印请求体内容 - 注意这可能会消费请求体
                    try {
                        val buffer = Buffer()
                        requestBody.writeTo(buffer)
                        val requestBodyString = buffer.readUtf8()
                        Log.d(TAG, "请求体内容: $requestBodyString")
                    } catch (e: Exception) {
                        Log.e(TAG, "无法读取请求体: ${e.message}")
                    }
                }

                val startTime = System.nanoTime()
                val response = chain.proceed(request)
                val endTime = System.nanoTime()

                Log.d(TAG, "接收响应: ${response.code}")
                Log.d(TAG, "响应耗时: ${(endTime - startTime) / 1e6}ms")
                Log.d(TAG, "响应头: ${response.headers}")

                // 检查是否是401未授权状态码（token过期）
                if (response.code == 401) {
                    Log.e(TAG, "检测到401未授权状态码，token可能已过期")
                    // 使用应用上下文处理token过期
                    val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                    // 在主线程中处理token过期
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                    }
                }

                // 尝试记录响应体，但不消费它
                val responseBody = response.body
                if (responseBody != null) {
                    Log.d(TAG, "响应体类型: ${responseBody.contentType()}, 长度: ${responseBody.contentLength()}")
                }

                return response
            }
        }

        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30L, TimeUnit.SECONDS)
            .readTimeout(30L, TimeUnit.SECONDS)
            .writeTimeout(30L, TimeUnit.SECONDS)
            .build()

        val retrofit = retrofit2.Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(retrofit2.converter.gson.GsonConverterFactory.create())
            .build()

        retrofit.create(DeviceBindApi::class.java)
    }

    companion object {
        private var instance: DeviceBindManager? = null

        @Synchronized
        fun getInstance(context: Context): DeviceBindManager {
            if (instance == null) {
                instance = DeviceBindManager(context.applicationContext)
            }
            return instance!!
        }
    }

    /**
     * 绑定蓝牙设备
     *
     * @param deviceName 蓝牙设备名称
     * @param macAddress 蓝牙设备MAC地址
     * @param includeLocation 是否包含位置信息
     * @return Flow<ApiResponse<DeviceBindResponse>> 绑定结果
     */
    fun bindDevice(deviceName: String, macAddress: String, includeLocation: Boolean = false): Flow<ApiResponse<DeviceBindResponse>> = flow {
        try {
            // 创建请求参数
            val request = if (includeLocation) {
                val locationInfo = getCurrentLocation()
                DeviceBindRequest(deviceName, macAddress, locationInfo)
            } else {
                DeviceBindRequest(deviceName, macAddress)
            }

            // 记录请求详情便于调试
            Log.d(TAG, "设备绑定请求参数: deviceName=${request.deviceName}, macAddress=${request.macAddress}")

            // 使用Gson转换请求对象，查看实际JSON
            val gson = com.google.gson.Gson()
            val requestJson = gson.toJson(request)
            Log.d(TAG, "请求JSON: $requestJson")

            // 调用API
            val response = deviceApi.bindDevice(request)

            // 检查是否是token过期 (HTTP 401或API状态码101)
            if (response.status == 401 || response.status == 101 || response.msg?.contains("token过期") == true) {
                Log.e(TAG, "设备绑定失败: token过期 (状态码: ${response.status})")
                // 使用应用上下文处理token过期
                val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                // 在主线程中处理token过期
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                }
                // 发送token过期响应
                emit(ApiResponse<DeviceBindResponse>(status = 401, msg = "设备绑定失败: token过期"))
            } else {
                // 记录日志
                Log.d(TAG, "设备绑定结果: ${response.msg}, 设备ID: ${response.data?.deviceId}")
                emit(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "设备绑定异常: ${e.message}")
            // 发送错误响应
            emit(ApiResponse<DeviceBindResponse>(status = -1, msg = "设备绑定失败: ${e.message}"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取设备详细信息
     *
     * @param deviceId 设备ID
     * @return Flow<ApiResponse<DeviceInfoResponse>> 设备信息
     */
    fun getDeviceInfo(deviceId: String): Flow<ApiResponse<DeviceInfoResponse>> = flow {
        try {
            val response = deviceApi.getDeviceInfo(deviceId)
            emit(response)
        } catch (e: Exception) {
            Log.e(TAG, "获取设备信息异常: ${e.message}")
            emit(ApiResponse<DeviceInfoResponse>(status = -1, msg = "获取设备信息失败: ${e.message}"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取设备详情（新接口）
     *
     * @param macAddress 设备MAC地址
     * @return Flow<ApiResponse<DeviceInfoResponse>> 设备详情
     */
    fun getDeviceDetail(macAddress: String): Flow<ApiResponse<DeviceInfoResponse>> = flow {
        try {
            Log.d(TAG, "开始调用获取设备详情API - MAC地址: $macAddress")

            // 记录当前token状态
            val token = UserCache.getToken()
            if (token.isEmpty()) {
                Log.w(TAG, "警告: 当前Token为空，API请求可能会失败")
            } else {
                Log.d(TAG, "当前Token长度: ${token.length}, 前10个字符: ${token.take(10)}...")
            }

            // 调用API
            val response = deviceApi.getDeviceDetail(macAddress)

            // 详细记录响应信息
            Log.d(TAG, "获取设备详情API响应 - 状态码: ${response.status}, 消息: ${response.msg}")

            // 检查是否是token过期 (HTTP 401或API状态码101)
            if (response.status == 401 || response.status == 101 || response.msg?.contains("token过期") == true) {
                Log.e(TAG, "获取设备详情失败: token过期 (状态码: ${response.status})")
                // 使用应用上下文处理token过期
                val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                // 在主线程中处理token过期
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                }
                // 发送token过期响应
                emit(ApiResponse<DeviceInfoResponse>(status = 401, msg = "获取设备详情失败: token过期"))
                return@flow
            }

            // 修改判断逻辑：status为0或200都视为成功
            if ((response.status == 0 || response.status == 200) && response.data != null) {
                // 添加空值检查
                val deviceId = response.data.deviceId ?: "未知ID"
                val deviceName = response.data.deviceName ?: "未知设备"
                val battery = response.data.battery ?: 0
                val firmwareVersion = response.data.firmwareVersion ?: "未知版本"
                val macAddr = response.data.macAddress ?: macAddress
                val location = response.data.location ?: ""
                val image = response.data.image ?: ""

                Log.d(TAG, "设备详情获取成功 - 设备ID: $deviceId, 名称: $deviceName")
                Log.d(TAG, "设备详情 - MAC地址: $macAddr, 电池: $battery%, 固件版本: $firmwareVersion")
                if (location.isNotEmpty()) {
                    Log.d(TAG, "设备位置: $location")
                }
                if (image.isNotEmpty()) {
                    Log.d(TAG, "设备图片URL: $image")
                }
            } else {
                Log.e(TAG, "获取设备详情失败 - 错误码: ${response.status}, 消息: ${response.msg}")
            }

            // 发送响应
            emit(response)
        } catch (e: Exception) {
            // 详细记录异常信息，包括堆栈跟踪
            Log.e(TAG, "获取设备详情API调用异常 - MAC地址: $macAddress, 错误: ${e.message}", e)

            // 根据异常类型提供更具体的错误信息
            val errorMsg = when (e) {
                is java.net.UnknownHostException -> "网络错误: 无法连接到服务器"
                is java.net.SocketTimeoutException -> "网络超时: 服务器响应超时"
                is retrofit2.HttpException -> {
                    // 检查是否是401错误
                    if (e.code() == 401) {
                        // 使用应用上下文处理token过期
                        val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                        // 在主线程中处理token过期
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                        }
                        "Token已过期，请重新登录"
                    } else {
                        "HTTP错误: 状态码 ${e.code()}"
                    }
                }
                else -> "获取设备详情失败: ${e.message}"
            }

            // 发送错误响应
            emit(ApiResponse<DeviceInfoResponse>(status = -1, msg = errorMsg))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 解绑设备
     *
     * @param macAddress 设备MAC地址
     * @return Flow<ApiResponse<Any>> 解绑结果
     */
    fun unbindDevice(macAddress: String): Flow<ApiResponse<Any>> = flow {
        try {
            Log.d(TAG, "开始调用解绑设备API - MAC地址: $macAddress")

            // 记录当前token状态
            val token = UserCache.getToken()
            if (token.isEmpty()) {
                Log.w(TAG, "警告: 当前Token为空，API请求可能会失败")
            } else {
                Log.d(TAG, "当前Token长度: ${token.length}, 前10个字符: ${token.take(10)}...")
            }

            // 调用API
            val response = deviceApi.unbindDevice(macAddress)

            // 详细记录响应信息
            Log.d(TAG, "解绑设备API响应 - 状态码: ${response.status}, 消息: ${response.msg}")

            // 根据响应状态记录不同级别的日志
            when (response.status) {
                0, 200 -> Log.d(TAG, "设备解绑成功 - MAC地址: $macAddress")
                1002 -> Log.e(TAG, "设备解绑失败 - 设备不存在: $macAddress")
                1003 -> Log.e(TAG, "设备解绑失败 - 设备未绑定: $macAddress")
                401, 101 -> {
                    Log.e(TAG, "设备解绑失败 - 未授权，Token可能无效 (状态码: ${response.status})")
                    // 使用应用上下文处理token过期
                    val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                    // 在主线程中处理token过期
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                    }
                }
                else -> {
                    // 检查消息是否包含token过期
                    if (response.msg?.contains("token过期") == true) {
                        Log.e(TAG, "设备解绑失败 - Token过期 (消息: ${response.msg})")
                        // 使用应用上下文处理token过期
                        val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                        // 在主线程中处理token过期
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                        }
                    } else {
                        Log.e(TAG, "设备解绑失败 - 未知错误码: ${response.status}")
                    }
                }
            }

            // 发送响应
            emit(response)
        } catch (e: Exception) {
            // 详细记录异常信息，包括堆栈跟踪
            Log.e(TAG, "解绑设备API调用异常 - MAC地址: $macAddress, 错误: ${e.message}", e)

            // 根据异常类型提供更具体的错误信息
            val errorMsg = when (e) {
                is java.net.UnknownHostException -> "网络错误: 无法连接到服务器"
                is java.net.SocketTimeoutException -> "网络超时: 服务器响应超时"
                is retrofit2.HttpException -> {
                    // 检查是否是401错误
                    if (e.code() == 401) {
                        // 使用应用上下文处理token过期
                        val appContext = com.example.llya.LlyaApplication.getInstance().applicationContext
                        // 在主线程中处理token过期
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            com.example.llya.utils.TokenManager.handleTokenExpired(appContext)
                        }
                        "Token已过期，请重新登录"
                    } else {
                        "HTTP错误: 状态码 ${e.code()}"
                    }
                }
                else -> "解绑设备失败: ${e.message}"
            }

            // 发送错误响应
            emit(ApiResponse<Any>(status = -1, msg = errorMsg))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 智能解绑设备（尝试多种标识符）
     *
     * 该方法无论传入的是设备ID还是MAC地址，都尝试使用现有API进行解绑
     *
     * @param id 设备标识符（无论是MAC地址还是设备ID都将尝试解绑）
     * @return Flow<ApiResponse<Any>> 解绑结果
     */
    fun smartUnbindDevice(id: String): Flow<ApiResponse<Any>> {
        Log.d(TAG, "尝试智能解绑设备，标识符: $id")
        return unbindDevice(id)
    }

    /**
     * 将本地设备同步到服务器
     *
     * @param devices 蓝牙设备列表
     * @return Flow<ApiResponse<Any>> 同步结果
     */
    fun syncLocalDevices(devices: List<Pair<String, String>>): Flow<ApiResponse<Any>> = flow {
        try {
            var successCount = 0
            var failCount = 0

            // 遍历本地设备列表，依次绑定
            for ((deviceName, macAddress) in devices) {
                val request = DeviceBindRequest(deviceName, macAddress)
                try {
                    val response = deviceApi.bindDevice(request)
                    if (response.status == 0) {
                        successCount++
                    } else {
                        failCount++
                    }
                } catch (e: Exception) {
                    failCount++
                    Log.e(TAG, "同步设备[${deviceName}]失败: ${e.message}")
                }
            }

            // 返回同步结果
            if (failCount == 0) {
                emit(ApiResponse<Any>(status = 0, msg = "成功同步 $successCount 个设备"))
            } else {
                emit(ApiResponse<Any>(status = 1, msg = "同步完成，成功: $successCount, 失败: $failCount"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步设备异常: ${e.message}")
            emit(ApiResponse<Any>(status = -1, msg = "同步设备失败: ${e.message}"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取当前位置信息
     */
    private fun getCurrentLocation(): LocationInfo? {
        try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            val providers = locationManager.getProviders(true)

            if (providers.isEmpty()) {
                return null
            }

            for (provider in providers) {
                try {
                    val location: Location? = locationManager.getLastKnownLocation(provider)
                    if (location != null) {
                        return LocationInfo(
                            longitude = location.longitude,
                            latitude = location.latitude
                        )
                    }
                } catch (e: SecurityException) {
                    Log.e(TAG, "获取位置信息权限不足: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取位置信息异常: ${e.message}")
        }

        return null
    }

    /**
     * 获取当前连接的蓝牙设备MAC地址
     *
     * @return 当前连接的蓝牙设备MAC地址，如果没有连接设备则返回空字符串
     */
    @SuppressLint("MissingPermission")
    fun getCurrentDeviceMacAddress(): String {
        try {
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return ""

            // 获取已配对设备
            val bondedDevices = bluetoothAdapter.bondedDevices

            if (bondedDevices.isEmpty()) {
                Log.d(TAG, "没有已配对的蓝牙设备")
                return ""
            }

            // 先尝试查找音频设备
            val audioDevice = bondedDevices.firstOrNull {
                it.bluetoothClass?.majorDeviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO
            }

            // 如果没找到音频设备但有其他设备，使用第一个绑定设备
            val finalDevice = when {
                audioDevice != null -> audioDevice
                bondedDevices.isNotEmpty() -> bondedDevices.first()
                else -> null
            }

            if (finalDevice != null) {
                val macAddress = finalDevice.address
                Log.d(TAG, "获取到当前连接的蓝牙设备MAC地址: $macAddress")
                return macAddress
            } else {
                Log.d(TAG, "未找到连接的蓝牙设备")
                return ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取当前连接的蓝牙设备MAC地址失败: ${e.message}")
            return ""
        }
    }

    /**
     * 获取设备详情（使用当前连接的设备MAC地址）
     *
     * @return Flow<ApiResponse<DeviceInfoResponse>> 设备详情
     */
    fun getCurrentDeviceDetail(): Flow<ApiResponse<DeviceInfoResponse>> {
        val macAddress = getCurrentDeviceMacAddress()
        if (macAddress.isEmpty()) {
            return flow {
                emit(ApiResponse<DeviceInfoResponse>(status = -1, msg = "未找到连接的蓝牙设备"))
            }.flowOn(Dispatchers.IO)
        }

        return getDeviceDetail(macAddress)
    }
}