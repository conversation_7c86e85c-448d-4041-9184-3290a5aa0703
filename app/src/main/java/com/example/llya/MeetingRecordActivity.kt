package com.example.llya

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.example.llya.ui.theme.LlyaTheme
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

class MeetingRecordActivity : ComponentActivity() {
    private val TAG = "MeetingRecordActivity"

    // 微软语音服务配置
    private val speechSubscriptionKey = "7lJ1e4p4LppCs62hMBvzyzxYv5vCOauJTzIKqnY3U0NVw7hFXiGaJQQJ99BEACYeBjFXJ3w3AAAYACOGuXCg"
    private val speechRegion = "eastus"

    // 语音识别器
    private var speechRecognizer: SpeechRecognizer? = null

    // 会议记录状态
    private var isRecording = mutableStateOf(false)
    private var currentMeetingContent = mutableStateOf("")
    private var selectedLanguage = mutableStateOf("zh-CN")
    private var statusMessage = mutableStateOf("准备就绪")
    private var wordCount = mutableStateOf(0)
    private var enableSpeakerRecognition = mutableStateOf(true)
    private var currentSpeaker = mutableStateOf("说话人1")

    // 说话人识别相关
    private var lastRecognitionTime = 0L
    private var lastTextLength = 0
    private var speakerChangeThreshold = 3000L // 3秒停顿阈值

    // 会议记录历史
    private val meetingHistory = mutableStateListOf<MeetingRecord>()

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            Log.d(TAG, "麦克风权限已授予")
        } else {
            Log.e(TAG, "麦克风权限被拒绝")
            Toast.makeText(this, "需要麦克风权限才能进行语音识别", Toast.LENGTH_LONG).show()
        }
    }

    // 支持的语言
    private val supportedLanguages = mapOf(
        "zh-CN" to "中文(简体)",
        "zh-TW" to "中文(繁体)",
        "zh-HK" to "中文(香港)",
        "en-US" to "英语(美国)",
        "en-GB" to "英语(英国)",
        "ja-JP" to "日语",
        "ko-KR" to "韩语",
        "fr-FR" to "法语",
        "de-DE" to "德语",
        "es-ES" to "西班牙语",
        "it-IT" to "意大利语",
        "ru-RU" to "俄语",
        "pt-BR" to "葡萄牙语",
        "ar-SA" to "阿拉伯语",
        "hi-IN" to "印地语",
        "th-TH" to "泰语",
        "vi-VN" to "越南语",
        "id-ID" to "印尼语",
        "ms-MY" to "马来语",
        "nl-NL" to "荷兰语",
        "sv-SE" to "瑞典语",
        "da-DK" to "丹麦语",
        "no-NO" to "挪威语",
        "fi-FI" to "芬兰语",
        "pl-PL" to "波兰语",
        "cs-CZ" to "捷克语",
        "hu-HU" to "匈牙利语",
        "tr-TR" to "土耳其语",
        "he-IL" to "希伯来语"
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 请求麦克风权限
        requestMicrophonePermission()

        // 加载历史记录
        loadMeetingHistory()

        Log.d(TAG, "会议记录应用启动")

        setContent {
            LlyaTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MeetingRecordScreen()
                }
            }
        }
    }

    private fun requestMicrophonePermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED -> {
                Log.d(TAG, "麦克风权限已预先授予")
            }
            else -> {
                Log.d(TAG, "请求麦克风权限")
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

    private fun loadMeetingHistory() {
        // 从SharedPreferences加载历史记录
        val prefs = getSharedPreferences("meeting_records", Context.MODE_PRIVATE)
        val historyJson = prefs.getString("history", "[]")
        // 这里可以添加JSON解析逻辑来加载历史记录
        Log.d(TAG, "加载会议记录历史")
    }

    private fun saveMeetingHistory() {
        // 保存到SharedPreferences
        val prefs = getSharedPreferences("meeting_records", Context.MODE_PRIVATE)
        // 这里可以添加JSON序列化逻辑来保存历史记录
        Log.d(TAG, "保存会议记录历史")
    }

    // 会议记录数据类
    data class MeetingRecord(
        val id: String = UUID.randomUUID().toString(),
        val title: String,
        val content: String,
        val language: String,
        val wordCount: Int,
        val duration: String,
        val timestamp: Long = System.currentTimeMillis()
    )

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun MeetingRecordScreen() {
        val context = LocalContext.current
        var showLanguageMenu by remember { mutableStateOf(false) }
        var showHistoryDialog by remember { mutableStateOf(false) }
        var showSaveDialog by remember { mutableStateOf(false) }
        var showSpeakerDialog by remember { mutableStateOf(false) }
        var meetingTitle by remember { mutableStateOf("") }

        // 深色主题颜色
        val darkBgColor = Color(0xFF1E1D2B)
        val purpleColor = Color(0xFF8364FD)
        val cardBgColor = Color(0xFF33324A)

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(darkBgColor)
        ) {
            // 顶部标题栏
            TopAppBar(
                title = {
                    Text(
                        text = "会议记录",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { finish() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 历史记录按钮
                    IconButton(onClick = { showHistoryDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = "历史记录",
                            tint = Color.White
                        )
                    }

                    // 保存按钮
                    if (currentMeetingContent.value.isNotEmpty()) {
                        IconButton(onClick = { showSaveDialog = true }) {
                            Icon(
                                imageVector = Icons.Default.Save,
                                contentDescription = "保存",
                                tint = Color.White
                            )
                        }
                    }

                    // 清空按钮
                    if (currentMeetingContent.value.isNotEmpty()) {
                        IconButton(onClick = {
                            currentMeetingContent.value = ""
                            wordCount.value = 0
                        }) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清空",
                                tint = Color.White
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = darkBgColor,
                    titleContentColor = Color.White
                )
            )

            // 语言选择和状态信息
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                color = cardBgColor
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 语言选择
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { showLanguageMenu = true }
                        ) {
                            Text(
                                text = "识别语言",
                                color = Color.Gray,
                                fontSize = 12.sp
                            )
                            Text(
                                text = supportedLanguages[selectedLanguage.value] ?: "选择语言",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold
                            )

                            DropdownMenu(
                                expanded = showLanguageMenu,
                                onDismissRequest = { showLanguageMenu = false },
                                modifier = Modifier.background(cardBgColor)
                            ) {
                                supportedLanguages.forEach { (code, name) ->
                                    DropdownMenuItem(
                                        text = { Text(name, color = Color.White) },
                                        onClick = {
                                            selectedLanguage.value = code
                                            showLanguageMenu = false
                                        }
                                    )
                                }
                            }
                        }

                        // 说话人识别开关
                        Column(
                            horizontalAlignment = Alignment.End
                        ) {
                            Text(
                                text = "说话人识别",
                                color = Color.Gray,
                                fontSize = 12.sp
                            )
                            Switch(
                                checked = enableSpeakerRecognition.value,
                                onCheckedChange = { enableSpeakerRecognition.value = it },
                                colors = SwitchDefaults.colors(
                                    checkedThumbColor = purpleColor,
                                    checkedTrackColor = purpleColor.copy(alpha = 0.5f)
                                )
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    // 状态信息和说话人控制
                    Column {
                        Text(
                            text = "状态: ${statusMessage.value}",
                            color = Color.White,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )

                        if (enableSpeakerRecognition.value) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "当前说话人: ${currentSpeaker.value}",
                                    color = purpleColor,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    modifier = Modifier.clickable { showSpeakerDialog = true }
                                )

                                // 手动切换说话人按钮
                                TextButton(
                                    onClick = { switchToNextSpeaker() },
                                    colors = ButtonDefaults.textButtonColors(
                                        contentColor = purpleColor
                                    )
                                ) {
                                    Text(
                                        text = "切换说话人",
                                        fontSize = 12.sp
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // 会议内容显示区域
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                item {
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(min = 200.dp),
                        shape = RoundedCornerShape(16.dp),
                        color = cardBgColor
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            // 会议内容标题和字数统计
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "会议内容",
                                    color = Color.Gray,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold
                                )

                                // 字数统计
                                Text(
                                    text = "${wordCount.value} 字",
                                    color = purpleColor,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            if (currentMeetingContent.value.isEmpty()) {
                                Text(
                                    text = "点击录音按钮开始记录会议内容...",
                                    color = Color.Gray,
                                    fontSize = 14.sp,
                                    style = androidx.compose.ui.text.TextStyle(
                                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                    ),
                                    modifier = Modifier.fillMaxWidth(),
                                    textAlign = TextAlign.Center
                                )
                            } else {
                                Text(
                                    text = currentMeetingContent.value,
                                    color = Color.White,
                                    fontSize = 16.sp,
                                    lineHeight = 24.sp
                                )
                            }
                        }
                    }
                }
            }

            // 底部录音控制
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                color = cardBgColor
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (isRecording.value) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.Red.copy(alpha = 0.1f))
                                .padding(vertical = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "正在录音中...",
                                color = Color.Red,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }

                    // 录音按钮
                    Box(
                        modifier = Modifier
                            .size(80.dp)
                            .padding(16.dp)
                            .clip(CircleShape)
                            .background(if (isRecording.value) Color.Red else purpleColor)
                            .clickable {
                                if (isRecording.value) {
                                    stopRecording()
                                } else {
                                    startRecording()
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = if (isRecording.value) Icons.Default.Stop else Icons.Default.Mic,
                            contentDescription = if (isRecording.value) "停止录音" else "开始录音",
                            tint = Color.White,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                }
            }
        }

        // 保存对话框
        if (showSaveDialog) {
            SaveMeetingDialog(
                onSave = { title ->
                    saveMeetingRecord(title)
                    showSaveDialog = false
                },
                onDismiss = { showSaveDialog = false }
            )
        }

        // 历史记录对话框
        if (showHistoryDialog) {
            MeetingHistoryDialog(
                meetings = meetingHistory,
                onDismiss = { showHistoryDialog = false },
                onDeleteMeeting = { meeting ->
                    meetingHistory.remove(meeting)
                    saveMeetingHistory()
                }
            )
        }

        // 说话人设置对话框
        if (showSpeakerDialog) {
            SpeakerSettingsDialog(
                currentSpeaker = currentSpeaker.value,
                onSpeakerChange = { newSpeaker ->
                    currentSpeaker.value = newSpeaker
                },
                onDismiss = { showSpeakerDialog = false }
            )
        }
    }

    // 开始录音
    private fun startRecording() {
        try {
            Log.d(TAG, "开始录音，语言: ${selectedLanguage.value}")
            statusMessage.value = "初始化语音识别..."

            val speechConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, speechRegion)
            speechConfig.speechRecognitionLanguage = selectedLanguage.value

            val audioConfig: AudioConfig = AudioConfig.fromDefaultMicrophoneInput()
            speechRecognizer = SpeechRecognizer(speechConfig, audioConfig as AudioConfig)

            // 注册事件处理器
            speechRecognizer?.let { recognizer ->
                // 识别中事件
                recognizer.recognizing.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    if (recognizedText.isNotEmpty()) {
                        Log.d(TAG, "识别中: $recognizedText")
                        statusMessage.value = "识别中: $recognizedText"
                    }
                }

                // 识别完成事件
                recognizer.recognized.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    if (recognizedText.isNotEmpty() && recognizedText.length > 2) {
                        Log.d(TAG, "识别完成: $recognizedText")

                        // 添加说话人识别和内容
                        val speakerLabel = if (enableSpeakerRecognition.value) {
                            detectSpeakerChange(recognizedText)
                            "${currentSpeaker.value}: "
                        } else {
                            ""
                        }

                        val newContent = if (currentMeetingContent.value.isEmpty()) {
                            "$speakerLabel$recognizedText"
                        } else {
                            "${currentMeetingContent.value}\n\n$speakerLabel$recognizedText"
                        }

                        currentMeetingContent.value = newContent
                        wordCount.value = countWords(newContent)
                        statusMessage.value = "识别完成，继续录音中..."
                    }
                }

                // 取消事件
                recognizer.canceled.addEventListener { _, event ->
                    Log.e(TAG, "识别被取消: ${event.reason}, 详情: ${event.errorDetails}")
                    statusMessage.value = "识别被取消: ${event.errorDetails}"
                    isRecording.value = false
                }

                // 会话停止事件
                recognizer.sessionStopped.addEventListener { _, _ ->
                    Log.d(TAG, "识别会话结束")
                    isRecording.value = false
                    statusMessage.value = "录音已停止"
                }

                // 启动连续识别
                recognizer.startContinuousRecognitionAsync().get()
                isRecording.value = true
                statusMessage.value = "录音中，请开始说话..."

                Toast.makeText(this, "开始录音", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            Log.e(TAG, "启动录音失败: ${e.message}", e)
            statusMessage.value = "启动失败: ${e.message}"
            Toast.makeText(this, "启动录音失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    // 停止录音
    private fun stopRecording() {
        try {
            Log.d(TAG, "停止录音")
            statusMessage.value = "正在停止录音..."

            speechRecognizer?.let { recognizer ->
                recognizer.stopContinuousRecognitionAsync().get()
                recognizer.close()
            }
            speechRecognizer = null

            isRecording.value = false
            statusMessage.value = "录音已停止"
            Toast.makeText(this, "录音已停止", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败: ${e.message}", e)
            statusMessage.value = "停止失败: ${e.message}"
            isRecording.value = false
        }
    }

    // 字数统计
    private fun countWords(text: String): Int {
        if (text.isEmpty()) return 0

        // 移除说话人标签（如："说话人1: "）
        val cleanText = text.replace(Regex("说话人\\d+:\\s*"), "")

        // 中文字符统计
        val chineseChars = cleanText.count { it.toString().matches(Regex("[\\u4e00-\\u9fa5]")) }

        // 英文单词统计
        val englishWords = cleanText.split(Regex("\\s+"))
            .filter { it.matches(Regex("[a-zA-Z]+")) }
            .size

        return chineseChars + englishWords
    }

    // 说话人变化检测（智能实现）
    private fun detectSpeakerChange(newText: String) {
        if (!enableSpeakerRecognition.value) return

        val currentTime = System.currentTimeMillis()

        // 检测停顿时间
        val timeSinceLastRecognition = currentTime - lastRecognitionTime

        // 检测文本长度变化（可能表示语速变化）
        val currentTextLength = newText.length
        val lengthDifference = kotlin.math.abs(currentTextLength - lastTextLength)

        // 多重检测条件
        var shouldSwitchSpeaker = false

        // 1. 长时间停顿检测
        if (timeSinceLastRecognition > speakerChangeThreshold) {
            Log.d(TAG, "检测到长时间停顿: ${timeSinceLastRecognition}ms")
            shouldSwitchSpeaker = true
        }

        // 2. 语速变化检测（文本长度差异较大）
        if (lastTextLength > 0 && lengthDifference > 20) {
            Log.d(TAG, "检测到语速变化: 长度差异 $lengthDifference")
            shouldSwitchSpeaker = true
        }

        // 3. 关键词检测（如"我来说"、"让我说"等）
        val speakerChangeKeywords = listOf("我来说", "让我说", "我说一下", "我补充", "我觉得", "换我说")
        if (speakerChangeKeywords.any { newText.contains(it) }) {
            Log.d(TAG, "检测到说话人切换关键词")
            shouldSwitchSpeaker = true
        }

        if (shouldSwitchSpeaker) {
            switchToNextSpeaker()
        }

        // 更新记录
        lastRecognitionTime = currentTime
        lastTextLength = currentTextLength
    }

    // 切换到下一个说话人
    private fun switchToNextSpeaker() {
        val currentSpeakerNum = currentSpeaker.value.replace("说话人", "").toIntOrNull() ?: 1
        val nextSpeakerNum = if (currentSpeakerNum >= 4) 1 else currentSpeakerNum + 1
        currentSpeaker.value = "说话人$nextSpeakerNum"
        Log.d(TAG, "切换到${currentSpeaker.value}")
    }

    // 保存会议记录
    private fun saveMeetingRecord(title: String) {
        if (currentMeetingContent.value.isEmpty()) {
            Toast.makeText(this, "没有内容可保存", Toast.LENGTH_SHORT).show()
            return
        }

        val meeting = MeetingRecord(
            title = title.ifEmpty { "会议记录_${SimpleDateFormat("yyyy-MM-dd_HH-mm", Locale.getDefault()).format(Date())}" },
            content = currentMeetingContent.value,
            language = supportedLanguages[selectedLanguage.value] ?: selectedLanguage.value,
            wordCount = wordCount.value,
            duration = "未知", // 可以添加计时功能
            timestamp = System.currentTimeMillis()
        )

        meetingHistory.add(0, meeting) // 添加到列表开头
        saveMeetingHistory()

        Toast.makeText(this, "会议记录已保存", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "保存会议记录: ${meeting.title}")
    }

    @Composable
    fun SaveMeetingDialog(
        onSave: (String) -> Unit,
        onDismiss: () -> Unit
    ) {
        var title by remember { mutableStateOf("") }

        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = "保存会议记录",
                    color = Color.White
                )
            },
            text = {
                Column {
                    Text(
                        text = "请输入会议标题:",
                        color = Color.White,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = title,
                        onValueChange = { title = it },
                        placeholder = {
                            Text(
                                "会议记录_${SimpleDateFormat("yyyy-MM-dd_HH-mm", Locale.getDefault()).format(Date())}",
                                color = Color.Gray
                            )
                        },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedTextColor = Color.White,
                            unfocusedTextColor = Color.White,
                            focusedBorderColor = Color(0xFF8364FD),
                            unfocusedBorderColor = Color.Gray
                        )
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { onSave(title) }
                ) {
                    Text("保存", color = Color(0xFF8364FD))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = onDismiss
                ) {
                    Text("取消", color = Color.Gray)
                }
            },
            containerColor = Color(0xFF33324A)
        )
    }

    @Composable
    fun MeetingHistoryDialog(
        meetings: List<MeetingRecord>,
        onDismiss: () -> Unit,
        onDeleteMeeting: (MeetingRecord) -> Unit
    ) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = "会议记录历史",
                    color = Color.White
                )
            },
            text = {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                ) {
                    if (meetings.isEmpty()) {
                        item {
                            Text(
                                text = "暂无会议记录",
                                color = Color.Gray,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    } else {
                        items(meetings) { meeting ->
                            MeetingHistoryItem(
                                meeting = meeting,
                                onDelete = { onDeleteMeeting(meeting) }
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onDismiss
                ) {
                    Text("关闭", color = Color(0xFF8364FD))
                }
            },
            containerColor = Color(0xFF33324A)
        )
    }

    @Composable
    fun MeetingHistoryItem(
        meeting: MeetingRecord,
        onDelete: () -> Unit
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            shape = RoundedCornerShape(8.dp),
            color = Color(0xFF1E1D2B)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = meeting.title,
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                                .format(Date(meeting.timestamp)),
                            color = Color.Gray,
                            fontSize = 12.sp
                        )
                        Text(
                            text = "${meeting.language} • ${meeting.wordCount}字",
                            color = Color.Gray,
                            fontSize = 12.sp
                        )
                    }

                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color.Red,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }

                if (meeting.content.length > 100) {
                    Text(
                        text = meeting.content.take(100) + "...",
                        color = Color.Gray,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                } else {
                    Text(
                        text = meeting.content,
                        color = Color.Gray,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
    }

    @Composable
    fun SpeakerSettingsDialog(
        currentSpeaker: String,
        onSpeakerChange: (String) -> Unit,
        onDismiss: () -> Unit
    ) {
        var customSpeakerName by remember { mutableStateOf("") }
        val predefinedSpeakers = listOf("说话人1", "说话人2", "说话人3", "说话人4", "主持人", "发言人", "嘉宾")

        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = "说话人设置",
                    color = Color.White
                )
            },
            text = {
                Column {
                    Text(
                        text = "选择或自定义说话人:",
                        color = Color.White,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )

                    // 预定义说话人选项
                    LazyColumn(
                        modifier = Modifier.heightIn(max = 200.dp)
                    ) {
                        items(predefinedSpeakers) { speaker ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        onSpeakerChange(speaker)
                                        onDismiss()
                                    }
                                    .padding(vertical = 8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = currentSpeaker == speaker,
                                    onClick = {
                                        onSpeakerChange(speaker)
                                        onDismiss()
                                    },
                                    colors = RadioButtonDefaults.colors(
                                        selectedColor = Color(0xFF8364FD)
                                    )
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = speaker,
                                    color = Color.White,
                                    fontSize = 14.sp
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 自定义说话人输入
                    Text(
                        text = "自定义说话人名称:",
                        color = Color.White,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = customSpeakerName,
                        onValueChange = { customSpeakerName = it },
                        placeholder = {
                            Text("输入自定义名称", color = Color.Gray)
                        },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedTextColor = Color.White,
                            unfocusedTextColor = Color.White,
                            focusedBorderColor = Color(0xFF8364FD),
                            unfocusedBorderColor = Color.Gray
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                if (customSpeakerName.isNotEmpty()) {
                    TextButton(
                        onClick = {
                            onSpeakerChange(customSpeakerName)
                            onDismiss()
                        }
                    ) {
                        Text("使用自定义", color = Color(0xFF8364FD))
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = onDismiss
                ) {
                    Text("取消", color = Color.Gray)
                }
            },
            containerColor = Color(0xFF33324A)
        )
    }

    override fun onDestroy() {
        Log.d(TAG, "Activity销毁，清理资源")

        // 停止录音
        if (isRecording.value) {
            stopRecording()
        }

        // 清理语音识别器
        speechRecognizer?.close()
        speechRecognizer = null

        super.onDestroy()
    }
}