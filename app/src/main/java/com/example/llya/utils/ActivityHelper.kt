package com.example.llya.utils

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import java.util.Locale

/**
 * Activity帮助类
 * 用于统一处理Activity的语言设置
 */
object ActivityHelper {
    private const val TAG = "ActivityHelper"
    
    /**
     * 为Activity应用当前的语言设置
     * 应在Activity的onCreate方法中调用，以确保Activity使用正确的语言
     */
    fun applyLanguageSettings(activity: Activity) {
        try {
            // 从SharedPreferences读取保存的语言设置
            val sharedPrefs = activity.getSharedPreferences(LanguageUtils.LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
            val savedLanguageName = sharedPrefs.getString(LanguageUtils.SELECTED_LANGUAGE_KEY, null)
            
            if (savedLanguageName != null) {
                // 如果有保存的语言设置，从显示名称获取语言代码
                val languageCode = LanguageUtils.getLanguageCodeFromDisplayName(savedLanguageName)
                if (languageCode != null) {
                    Log.d(TAG, "为${activity.javaClass.simpleName}应用语言设置: $savedLanguageName ($languageCode)")
                    
                    // 创建Locale对象
                    val locale = createLocaleFromCode(languageCode)
                    
                    // 应用Locale到Activity
                    applyLocaleToActivity(activity, locale)
                } else {
                    Log.w(TAG, "无法解析保存的语言名称: $savedLanguageName，使用系统默认语言")
                    // 使用当前系统默认语言
                    applyLocaleToActivity(activity, Locale.getDefault())
                }
            } else {
                Log.d(TAG, "没有保存的语言设置，使用系统默认语言")
                // 使用当前系统默认语言
                applyLocaleToActivity(activity, Locale.getDefault())
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用语言设置时出错", e)
            // 出错时使用系统默认语言
            try {
                applyLocaleToActivity(activity, Locale.getDefault())
            } catch (ex: Exception) {
                Log.e(TAG, "应用默认语言设置时出错", ex)
            }
        }
    }
    
    /**
     * 根据语言代码创建Locale对象
     */
    private fun createLocaleFromCode(languageCode: String): Locale {
        val parts = languageCode.split("-")
        return when {
            parts.size > 1 -> Locale(parts[0], parts[1])
            else -> Locale(parts[0])
        }
    }
    
    /**
     * 将Locale应用到Activity
     */
    private fun applyLocaleToActivity(activity: Activity, locale: Locale) {
        try {
            // 设置默认语言环境
            Locale.setDefault(locale)
            
            val resources = activity.resources
            val configuration = Configuration(resources.configuration)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                configuration.setLocale(locale)
                activity.createConfigurationContext(configuration)
            } else {
                @Suppress("DEPRECATION")
                configuration.locale = locale
            }
            
            // 更新资源配置
            resources.updateConfiguration(configuration, resources.displayMetrics)
            
            Log.d(TAG, "成功为${activity.javaClass.simpleName}应用语言设置: ${locale.language}-${locale.country}")
        } catch (e: Exception) {
            Log.e(TAG, "应用Locale到Activity时出错", e)
        }
    }
} 