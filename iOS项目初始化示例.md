# LLya iOS项目初始化示例

本文档提供了LLya iOS应用程序初始化的步骤和基础代码示例，帮助您快速开始iOS版本的开发。

## 项目创建步骤

1. 打开Xcode（推荐使用Xcode 14或更高版本）
2. 选择"Create a new Xcode project"
3. 选择iOS平台，应用类型选择"App"
4. 填写项目基本信息：
   - Product Name: LLya
   - Organization Identifier: com.example.llya（或您自己的标识符）
   - Interface: SwiftUI
   - Life Cycle: SwiftUI App
   - Language: Swift
   - 取消选中"Include Tests"（如不需要）
5. 选择项目保存位置并创建

## 基础架构设置

### 1. 文件夹结构创建

在Xcode中创建以下文件夹结构：

- App (存放应用入口相关文件)
- Features (存放各功能模块)
- Core (存放核心组件)
- UI (存放UI组件)
- Config (存放配置文件)

### 2. 安装依赖包

通过Swift Package Manager添加基础依赖：

1. 在Xcode中选择"File" > "Add Packages..."
2. 搜索并添加以下包：
   - https://github.com/Alamofire/Alamofire.git
   - https://github.com/SwiftyJSON/SwiftyJSON.git
   - https://github.com/kishikawakatsumi/KeychainAccess.git
   - https://github.com/daltoniam/Starscream.git (WebSocket库)

## 基础代码示例

### 应用入口 (LLyaApp.swift)

```swift
import SwiftUI

@main
struct LLyaApp: App {
    // 应用全局状态管理
    @StateObject private var appState = AppState()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
        }
    }
}

// 应用全局状态
class AppState: ObservableObject {
    @Published var isAuthenticated: Bool = false
    @Published var currentLanguage: String = "zh-CN"
    @Published var isDarkMode: Bool = false
    
    // 其他全局状态...
}
```

### 主内容视图 (ContentView.swift)

```swift
import SwiftUI

struct ContentView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        if appState.isAuthenticated {
            MainTabView()
        } else {
            LoginView()
        }
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(AppState())
    }
}
```

### 主标签视图 (MainTabView.swift)

```swift
import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Label("首页", systemImage: "house")
                }
                .tag(0)
            
            TranslationView()
                .tabItem {
                    Label("翻译", systemImage: "globe")
                }
                .tag(1)
            
            AIChatView()
                .tabItem {
                    Label("AI聊天", systemImage: "message")
                }
                .tag(2)
            
            ProfileView()
                .tabItem {
                    Label("我的", systemImage: "person")
                }
                .tag(3)
        }
    }
}

struct MainTabView_Previews: PreviewProvider {
    static var previews: some View {
        MainTabView()
    }
}
```

### 首页视图 (HomeView.swift)

```swift
import SwiftUI

struct HomeView: View {
    @StateObject private var viewModel = HomeViewModel()
    
    var body: some View {
        NavigationView {
            VStack {
                // 语言选择器
                LanguageSelectorView(
                    sourceLanguage: $viewModel.sourceLanguage,
                    targetLanguage: $viewModel.targetLanguage,
                    onSwapLanguages: viewModel.swapLanguages
                )
                
                Spacer()
                
                // 语音识别结果显示
                SpeechRecognitionResultView(
                    originalText: viewModel.recognizedText,
                    translatedText: viewModel.translatedText
                )
                
                Spacer()
                
                // 语音录制按钮
                RecordingButton(
                    isRecording: viewModel.isRecording,
                    onTap: viewModel.toggleRecording
                )
                
                Spacer()
            }
            .padding()
            .navigationTitle("实时翻译")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: viewModel.showSettings) {
                        Image(systemName: "gear")
                    }
                }
            }
        }
    }
}

// MARK: - 子视图组件

struct LanguageSelectorView: View {
    @Binding var sourceLanguage: String
    @Binding var targetLanguage: String
    var onSwapLanguages: () -> Void
    
    var body: some View {
        HStack {
            LanguageDropdown(
                selectedLanguage: $sourceLanguage,
                title: "源语言"
            )
            
            Button(action: onSwapLanguages) {
                Image(systemName: "arrow.left.arrow.right")
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .clipShape(Circle())
            }
            
            LanguageDropdown(
                selectedLanguage: $targetLanguage,
                title: "目标语言"
            )
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

struct LanguageDropdown: View {
    @Binding var selectedLanguage: String
    var title: String
    @State private var isExpanded = false
    
    let languages = [
        "zh-CN": "中文",
        "en-US": "英语",
        "ja-JP": "日语",
        "ko-KR": "韩语",
        "fr-FR": "法语"
        // 其他语言...
    ]
    
    var body: some View {
        Menu {
            ForEach(languages.sorted(by: { $0.value < $1.value }), id: \.key) { key, value in
                Button(action: {
                    selectedLanguage = key
                }) {
                    Text(value)
                    if selectedLanguage == key {
                        Image(systemName: "checkmark")
                    }
                }
            }
        } label: {
            VStack(alignment: .leading) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(languages[selectedLanguage] ?? selectedLanguage)
                    .font(.headline)
            }
            .frame(width: 100)
        }
    }
}

struct SpeechRecognitionResultView: View {
    var originalText: String
    var translatedText: String
    
    var body: some View {
        VStack(spacing: 20) {
            Text(originalText)
                .font(.body)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
                .frame(height: originalText.isEmpty ? 100 : nil)
                .multilineTextAlignment(.leading)
            
            if !translatedText.isEmpty {
                Text(translatedText)
                    .font(.body)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                    )
                    .multilineTextAlignment(.leading)
            }
        }
    }
}

struct RecordingButton: View {
    var isRecording: Bool
    var onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                Circle()
                    .fill(isRecording ? Color.red : Color.blue)
                    .frame(width: 70, height: 70)
                
                if isRecording {
                    Circle()
                        .stroke(Color.red.opacity(0.3), lineWidth: 4)
                        .frame(width: 85, height: 85)
                }
                
                Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                    .font(.system(size: 30))
                    .foregroundColor(.white)
            }
        }
    }
}

// MARK: - ViewModel

class HomeViewModel: ObservableObject {
    @Published var sourceLanguage: String = "zh-CN"
    @Published var targetLanguage: String = "en-US"
    @Published var recognizedText: String = ""
    @Published var translatedText: String = ""
    @Published var isRecording: Bool = false
    
    // 语音识别服务管理器
    private var speechRecognizer: SpeechRecognitionManager?
    
    init() {
        speechRecognizer = SpeechRecognitionManager(
            onRecognitionResult: { [weak self] text, isFinal in
                DispatchQueue.main.async {
                    self?.recognizedText = text
                    
                    // 如果是最终结果，执行翻译
                    if isFinal {
                        self?.translate(text)
                    }
                }
            },
            onError: { [weak self] error in
                DispatchQueue.main.async {
                    print("语音识别错误: \(error)")
                    self?.isRecording = false
                }
            }
        )
    }
    
    func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        // 检查和请求麦克风权限
        SpeechRecognitionManager.requestMicrophonePermission { [weak self] granted in
            guard let self = self else { return }
            
            if granted {
                // 启动语音识别
                self.speechRecognizer?.startRecognition(languageCode: self.sourceLanguage)
                
                DispatchQueue.main.async {
                    self.isRecording = true
                    self.recognizedText = ""
                    self.translatedText = ""
                }
            } else {
                print("麦克风权限被拒绝")
            }
        }
    }
    
    private func stopRecording() {
        speechRecognizer?.stopRecognition()
        
        DispatchQueue.main.async {
            self.isRecording = false
        }
    }
    
    func swapLanguages() {
        let temp = sourceLanguage
        sourceLanguage = targetLanguage
        targetLanguage = temp
        
        // 清空结果
        recognizedText = ""
        translatedText = ""
    }
    
    private func translate(_ text: String) {
        if text.isEmpty { return }
        
        // 调用翻译服务
        TranslationService.shared.translate(
            text: text,
            sourceLanguage: sourceLanguage,
            targetLanguage: targetLanguage
        ) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let translatedText):
                    self?.translatedText = translatedText
                case .failure(let error):
                    print("翻译错误: \(error)")
                    self?.translatedText = "翻译失败"
                }
            }
        }
    }
    
    func showSettings() {
        // 显示设置页面
        print("显示设置页面")
    }
}
```

## 核心服务示例

### 语音识别管理器 (SpeechRecognitionManager.swift)

```swift
import Foundation
import Speech
import AVFoundation

class SpeechRecognitionManager {
    // 回调
    private let onRecognitionResult: (String, Bool) -> Void
    private let onError: (Error) -> Void
    
    // 语音识别
    private let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    
    // 音频引擎
    private let audioEngine = AVAudioEngine()
    
    // 是否正在录音
    private var isRecording = false
    
    init(onRecognitionResult: @escaping (String, Bool) -> Void,
         onError: @escaping (Error) -> Void) {
        self.onRecognitionResult = onRecognitionResult
        self.onError = onError
    }
    
    // 请求麦克风权限
    static func requestMicrophonePermission(completion: @escaping (Bool) -> Void) {
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            completion(granted)
        }
    }
    
    // 请求语音识别权限
    static func requestSpeechRecognitionPermission(completion: @escaping (Bool) -> Void) {
        SFSpeechRecognizer.requestAuthorization { status in
            let granted = (status == .authorized)
            completion(granted)
        }
    }
    
    // 开始语音识别
    func startRecognition(languageCode: String = "zh-CN") {
        // 如果已经在录音，先停止
        if isRecording {
            stopRecognition()
        }
        
        // 设置语音识别器的语言
        guard let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: languageCode)) else {
            onError(NSError(domain: "SpeechRecognitionManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "语音识别不支持该语言：\(languageCode)"]))
            return
        }
        
        // 确保语音识别可用
        guard speechRecognizer.isAvailable else {
            onError(NSError(domain: "SpeechRecognitionManager", code: 2, userInfo: [NSLocalizedDescriptionKey: "语音识别暂时不可用"]))
            return
        }
        
        // 配置音频会话
        do {
            try AVAudioSession.sharedInstance().setCategory(.record, mode: .measurement, options: .duckOthers)
            try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            onError(error)
            return
        }
        
        // 创建语音识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        
        guard let recognitionRequest = recognitionRequest else {
            onError(NSError(domain: "SpeechRecognitionManager", code: 3, userInfo: [NSLocalizedDescriptionKey: "创建语音识别请求失败"]))
            return
        }
        
        // 配置语音识别请求
        recognitionRequest.shouldReportPartialResults = true
        
        // 创建音频输入节点
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        // 安装音频输入回调
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { [weak self] buffer, _ in
            self?.recognitionRequest?.append(buffer)
        }
        
        // 准备并启动音频引擎
        audioEngine.prepare()
        
        do {
            try audioEngine.start()
            isRecording = true
        } catch {
            onError(error)
            return
        }
        
        // 开始语音识别任务
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }
            
            var isFinal = false
            
            if let result = result {
                // 获取识别结果
                let recognizedText = result.bestTranscription.formattedString
                isFinal = result.isFinal
                
                // 调用回调，传递识别结果
                self.onRecognitionResult(recognizedText, isFinal)
            }
            
            if error != nil || isFinal {
                // 停止音频引擎
                self.audioEngine.stop()
                inputNode.removeTap(onBus: 0)
                
                // 重置语音识别
                self.recognitionRequest = nil
                self.recognitionTask = nil
                
                self.isRecording = false
            }
        }
    }
    
    // 停止语音识别
    func stopRecognition() {
        if isRecording {
            audioEngine.stop()
            recognitionRequest?.endAudio()
            audioEngine.inputNode.removeTap(onBus: 0)
            
            recognitionTask?.cancel()
            
            recognitionRequest = nil
            recognitionTask = nil
            
            isRecording = false
            
            do {
                try AVAudioSession.sharedInstance().setActive(false)
            } catch {
                print("停止音频会话失败: \(error)")
            }
        }
    }
}
```

### 翻译服务 (TranslationService.swift)

```swift
import Foundation
import Alamofire
import SwiftyJSON

class TranslationService {
    static let shared = TranslationService()
    
    private let baseURL = "https://gogoapi.sdltws.com"
    
    enum TranslationError: Error {
        case networkError
        case serverError(String)
        case parsingError
    }
    
    // 翻译文本
    func translate(text: String, sourceLanguage: String, targetLanguage: String, completion: @escaping (Result<String, Error>) -> Void) {
        // 构建请求参数
        let parameters: [String: Any] = [
            "text": text,
            "source": sourceLanguage,
            "target": targetLanguage
        ]
        
        // 发送请求
        AF.request("\(baseURL)/api/translate", 
                   method: .post, 
                   parameters: parameters, 
                   encoding: JSONEncoding.default)
            .validate()
            .responseJSON { response in
                switch response.result {
                case .success(let value):
                    do {
                        let json = try JSON(data: response.data!)
                        
                        if let translatedText = json["translatedText"].string {
                            completion(.success(translatedText))
                        } else if let errorMessage = json["error"].string {
                            completion(.failure(TranslationError.serverError(errorMessage)))
                        } else {
                            completion(.failure(TranslationError.parsingError))
                        }
                    } catch {
                        completion(.failure(TranslationError.parsingError))
                    }
                    
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
    
    // 获取支持的语言列表
    func fetchSupportedLanguages(completion: @escaping (Result<[String: String], Error>) -> Void) {
        AF.request("\(baseURL)/api/languages", method: .get)
            .validate()
            .responseJSON { response in
                switch response.result {
                case .success:
                    do {
                        let json = try JSON(data: response.data!)
                        
                        var languages = [String: String]()
                        
                        if let languagesArray = json["languages"].array {
                            for language in languagesArray {
                                if let code = language["code"].string,
                                   let name = language["name"].string {
                                    languages[code] = name
                                }
                            }
                            completion(.success(languages))
                        } else {
                            completion(.failure(TranslationError.parsingError))
                        }
                    } catch {
                        completion(.failure(TranslationError.parsingError))
                    }
                    
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
}
```

### WebSocket服务 (WebSocketService.swift)

```swift
import Foundation
import Starscream

class WebSocketService {
    // 单例
    static let shared = WebSocketService()
    
    // WebSocket客户端
    private var socket: WebSocket?
    
    // 回调闭包
    private var onConnected: (() -> Void)?
    private var onDisconnected: ((Error?) -> Void)?
    private var onTextReceived: ((String) -> Void)?
    private var onBinaryReceived: ((Data) -> Void)?
    
    // 连接状态
    private(set) var isConnected = false
    
    // 连接到WebSocket服务器
    func connect(to url: URL,
                 onConnected: (() -> Void)? = nil,
                 onDisconnected: ((Error?) -> Void)? = nil,
                 onTextReceived: ((String) -> Void)? = nil,
                 onBinaryReceived: ((Data) -> Void)? = nil) {
        
        // 保存回调
        self.onConnected = onConnected
        self.onDisconnected = onDisconnected
        self.onTextReceived = onTextReceived
        self.onBinaryReceived = onBinaryReceived
        
        // 如果已经连接，先断开
        if socket != nil {
            disconnect()
        }
        
        // 配置WebSocket连接
        var request = URLRequest(url: url)
        request.timeoutInterval = 10
        
        socket = WebSocket(request: request)
        socket?.delegate = self
        
        // 连接
        socket?.connect()
    }
    
    // 断开连接
    func disconnect() {
        socket?.disconnect()
        socket = nil
        isConnected = false
    }
    
    // 发送文本消息
    func send(text: String, completion: ((Error?) -> Void)? = nil) {
        guard let socket = socket, isConnected else {
            completion?(NSError(domain: "WebSocketService", code: 1, userInfo: [NSLocalizedDescriptionKey: "未连接到WebSocket服务器"]))
            return
        }
        
        socket.write(string: text) {
            completion?(nil)
        }
    }
    
    // 发送二进制数据
    func send(data: Data, completion: ((Error?) -> Void)? = nil) {
        guard let socket = socket, isConnected else {
            completion?(NSError(domain: "WebSocketService", code: 1, userInfo: [NSLocalizedDescriptionKey: "未连接到WebSocket服务器"]))
            return
        }
        
        socket.write(data: data) {
            completion?(nil)
        }
    }
}

// MARK: - WebSocketDelegate
extension WebSocketService: WebSocketDelegate {
    func didReceive(event: WebSocketEvent, client: WebSocket) {
        switch event {
        case .connected(_):
            isConnected = true
            onConnected?()
            
        case .disconnected(let reason, let code):
            isConnected = false
            print("WebSocket断开连接: \(reason) with code: \(code)")
            onDisconnected?(nil)
            
        case .text(let string):
            onTextReceived?(string)
            
        case .binary(let data):
            onBinaryReceived?(data)
            
        case .ping(_):
            break
            
        case .pong(_):
            break
            
        case .viabilityChanged(_):
            break
            
        case .reconnectSuggested(_):
            break
            
        case .cancelled:
            isConnected = false
            onDisconnected?(nil)
            
        case .error(let error):
            isConnected = false
            print("WebSocket错误: \(String(describing: error))")
            onDisconnected?(error)
            
        default:
            break
        }
    }
}
```

## 项目配置文件

### Info.plist 关键配置

下面是一些需要添加到Info.plist文件中的关键配置：

```xml
<!-- 麦克风权限 -->
<key>NSMicrophoneUsageDescription</key>
<string>LLya需要访问麦克风以进行语音识别和翻译</string>

<!-- 语音识别权限 -->
<key>NSSpeechRecognitionUsageDescription</key>
<string>LLya需要使用语音识别功能来实现实时翻译</string>

<!-- 蓝牙权限 -->
<key>NSBluetoothAlwaysUsageDescription</key>
<string>LLya需要使用蓝牙功能来连接耳机和其他音频设备</string>

<!-- 本地网络权限 -->
<key>NSLocalNetworkUsageDescription</key>
<string>LLya需要访问本地网络以连接到翻译服务</string>
```

## 下一步开发建议

1. 完成上述基础架构搭建后，首先实现核心的语音识别和翻译功能
2. 逐步添加用户界面和交互体验
3. 实现蓝牙设备连接和管理功能
4. 添加本地数据存储和历史记录功能
5. 集成AI聊天功能
6. 最后完善设置和个性化选项

使用本文档中的代码示例，您可以快速搭建LLya iOS应用的基础框架，并开始核心功能的开发。根据您的需求，可以进一步扩展和完善各个模块。 