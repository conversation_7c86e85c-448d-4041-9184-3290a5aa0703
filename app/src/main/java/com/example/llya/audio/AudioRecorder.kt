package com.example.llya.audio

import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.BufferUnderflowException
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.min
import kotlin.ExperimentalStdlibApi

/**
 * 音频录制错误枚举
 */
enum class AudioRecorderError {
    INIT_FAILED,         // 初始化失败
    PERMISSION_DENIED,   // 权限被拒绝
    START_FAILED,        // 开始录音失败
    RECORDING_ERROR,     // 录音过程中发生错误
    NEED_REINITIALIZE,   // 需要重新初始化
    BUFFER_ERROR,        // 缓冲区错误
    NO_AUDIO_DATA,       // 没有音频数据
    ZERO_AUDIO_DATA,     // 全零音频数据
    UNKNOWN_ERROR        // 未知错误
}

// 启用break/continue在内联lambda中的实验性特性
@OptIn(ExperimentalStdlibApi::class)
class AudioRecorder(
    private val context: Context? = null,
    private val onAudioBufferReady: (ByteArray) -> Unit = {},
    private val onError: (AudioRecorderError) -> Unit = {},
    private val sampleRate: Int = SAMPLE_RATE,
    private val channelConfig: Int = CHANNEL_CONFIG,
    private val encoding: Int = AUDIO_FORMAT,
    private val bufferSizeMultiplier: Int = BUFFER_SIZE_FACTOR
) {
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingJob: Job? = null
    private var _recordingCoroutineScope: CoroutineScope? = null
    private val recordingCoroutineScope: CoroutineScope
        get() {
            if (_recordingCoroutineScope == null || _recordingCoroutineScope?.isActive == false) {
                _recordingCoroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
            }
            return _recordingCoroutineScope!!
        }

    // 使用专门的CoroutineExceptionHandler处理协程异常
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        Log.e(TAG, "录音协程发生未捕获异常", exception)
        onError(AudioRecorderError.UNKNOWN_ERROR)
        stopRecordingSafely()
    }

    // 使用SupervisorJob和专用异常处理器创建协程作用域
    private val recordingScope = CoroutineScope(
        SupervisorJob() + Dispatchers.IO + exceptionHandler
    )

    private var audioManager: AudioManager? = null
    private val mutex = Mutex() // 用于同步操作的互斥锁

    // 音频配置参数
    companion object {
        private const val TAG = "AudioRecorder"
        const val SAMPLE_RATE = 16000 // 16kHz
        const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        const val BUFFER_SIZE_FACTOR = 4  // 增大缓冲区以提高稳定性

        // 根据上述参数，计算所需的 buffer size
        val BUFFER_SIZE = AudioRecord.getMinBufferSize(
            SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT
        ) * BUFFER_SIZE_FACTOR

        // 每20ms一个数据包的大小 (16000 * 2 * 0.02 = 640 bytes)
        val BYTES_PER_FRAME = 640  // 20ms at 16kHz with 16bit
    }

    // 计算当前实例的实际缓冲区大小
    private val actualBufferSize: Int
        get() = AudioRecord.getMinBufferSize(sampleRate, channelConfig, encoding) * bufferSizeMultiplier

    init {
        // 尝试获取 AudioManager
        context?.let {
            try {
                audioManager = it.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
                Log.d(TAG, "AudioManager 初始化${if (audioManager != null) "成功" else "失败"}")
            } catch (e: Exception) {
                Log.e(TAG, "初始化AudioManager失败", e)
            }
        }
    }

    /**
     * 初始化录音器
     */
    private fun initialize(): Boolean {
        Log.d(TAG, "开始初始化录音器")

        // 设置 AudioManager 的模式和麦克风状态
        setupAudioManager()

        // 先检查并释放可能存在的旧实例
        try {
            if (audioRecord != null) {
                try {
                    if (audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                        audioRecord?.stop()
                    }
                    audioRecord?.release()
                    Log.d(TAG, "释放旧的AudioRecord实例")
                } catch (e: Exception) {
                    Log.e(TAG, "释放旧的AudioRecord实例失败", e)
                }
                audioRecord = null
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理旧AudioRecord实例时发生异常", e)
        }

        try {
            // 检查缓冲区大小是否有效
            val minBufferSize = AudioRecord.getMinBufferSize(
                sampleRate, channelConfig, encoding
            )

            if (minBufferSize == AudioRecord.ERROR || minBufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Log.e(TAG, "获取最小缓冲区大小失败，错误码: $minBufferSize")
                onError(AudioRecorderError.BUFFER_ERROR)
                return false
            }

            val bufferSize = minBufferSize * bufferSizeMultiplier
            Log.d(TAG, "开始初始化 AudioRecord，SAMPLE_RATE: $sampleRate, 最小缓冲区: $minBufferSize, 实际缓冲区: $bufferSize")

            try {
                // 尝试使用通信音频源，可能对语音识别有更好的效果
                audioRecord = try {
                    AudioRecord(
                        MediaRecorder.AudioSource.VOICE_COMMUNICATION,
                        sampleRate,
                        channelConfig,
                        encoding,
                        bufferSize
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "使用VOICE_COMMUNICATION音频源失败，回退到MIC", e)
                    // 如果失败，回退到标准麦克风
                    AudioRecord(
                        MediaRecorder.AudioSource.MIC,
                        sampleRate,
                        channelConfig,
                        encoding,
                        bufferSize
                    )
                }
            } catch (e: IllegalArgumentException) {
                Log.e(TAG, "AudioRecord初始化参数错误", e)
                onError(AudioRecorderError.INIT_FAILED)
                return false
            } catch (e: SecurityException) {
                Log.e(TAG, "AudioRecord初始化权限错误", e)
                onError(AudioRecorderError.PERMISSION_DENIED)
                return false
            } catch (e: Exception) {
                Log.e(TAG, "AudioRecord初始化未知错误", e)
                onError(AudioRecorderError.UNKNOWN_ERROR)
                return false
            }

            // 验证AudioRecord状态
            val state = audioRecord?.state ?: AudioRecord.STATE_UNINITIALIZED
            val isInitialized = state == AudioRecord.STATE_INITIALIZED

            Log.d(TAG, "AudioRecord 初始化状态: $state (${if(isInitialized) "已初始化" else "初始化失败"})")

            return isInitialized
        } catch (e: Exception) {
            Log.e(TAG, "初始化AudioRecord时发生异常", e)
            onError(AudioRecorderError.INIT_FAILED)
            return false
        }
    }

    // 将initializeSafely修改为suspend函数
    suspend fun initializeSafely(): Boolean {
        try {
            return mutex.withLock {
                initialize()
            }
        } catch (e: Exception) {
            Log.e(TAG, "安全初始化录音器时发生异常", e)
            onError(AudioRecorderError.UNKNOWN_ERROR)
            return false
        }
    }

    // 设置音频管理器
    private fun setupAudioManager() {
        try {
            audioManager?.let { manager ->
                // 设置为通话模式，可以激活降噪和回声消除
                Log.d(TAG, "设置音频模式为 MODE_IN_COMMUNICATION")
                manager.mode = AudioManager.MODE_IN_COMMUNICATION

                // 启用降噪（如果设备支持）
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    try {
                        if (manager.getParameters("noise_suppression").isEmpty()) {
                            Log.d(TAG, "尝试开启降噪")
                            manager.setParameters("noise_suppression=on")
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "设置降噪参数失败", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置音频管理器失败", e)
        }
    }

    /**
     * 开始录音
     */
    fun startRecording(): Boolean {
        return try {
            if (isRecording.get()) {
                Log.d(TAG, "录音已经在进行中")
                return true
            }

            recordingScope.launch {
                Log.d(TAG, "开始录音，先尝试初始化AudioRecord")

                // 初始化录音器
                if (!initializeSafely()) {
                    Log.e(TAG, "初始化录音器失败，无法开始录音")
                    stopRecordingSafely()
                    isRecording.set(false)
                    onError(AudioRecorderError.INIT_FAILED)
                    return@launch
                }

                try {
                    audioRecord?.startRecording()
                    if ((audioRecord?.recordingState ?: AudioRecord.RECORDSTATE_STOPPED) != AudioRecord.RECORDSTATE_RECORDING) {
                        Log.e(TAG, "启动录音失败，当前录音状态: ${audioRecord?.recordingState}")
                        stopRecordingSafely()
                        isRecording.set(false)
                        onError(AudioRecorderError.START_FAILED)
                        return@launch
                    }

                    // 录音状态标志设置为 true
                    isRecording.set(true)
                    Log.d(TAG, "录音成功启动，开始读取音频数据")

                    // 初始化连续静音检测
                    var consecutiveZeroFrames = 0
                    val maxConsecutiveZeroFrames = 50 // 连续50帧静音（约1秒）就发出警告

                    // 创建音频数据缓冲区
                    val bufferSize = BUFFER_SIZE
                    val buffer = ByteArray(bufferSize)

                    // 统计信息
                    var totalBytesRead = 0L
                    var totalFramesRead = 0
                    var startTime = System.currentTimeMillis()

                    // 记录上次的有效音频包时间
                    var lastNonZeroAudioTime = System.currentTimeMillis()

                    // 开始读取数据循环
                    while (isRecording.get()) {
                        // 读取音频数据
                        val bytesRead = audioRecord?.read(buffer, 0, bufferSize) ?: -1

                        // 更新统计信息
                        if (bytesRead > 0) {
                            totalBytesRead += bytesRead
                            totalFramesRead++

                            // 每50帧记录一次状态
                            if (totalFramesRead % 50 == 0) {
                                val currentTime = System.currentTimeMillis()
                                val elapsedSec = (currentTime - startTime) / 1000.0
                                val bytesPerSec = totalBytesRead / elapsedSec

                                Log.d(TAG, String.format(
                                    "已录制 %.1f 秒，%.1f KB/秒，共 %d 帧",
                                    elapsedSec,
                                    bytesPerSec / 1024,
                                    totalFramesRead
                                ))
                            }

                            // 检查音频质量 - 是否全零或音量太低（静音）
                            var isZeroBuffer = true
                            var rms = 0.0

                            // 将字节数据转换为短整型以计算音量
                            try {
                                val shorts = ShortArray(bytesRead / 2)
                                ByteBuffer.wrap(buffer, 0, bytesRead)
                                    .order(ByteOrder.LITTLE_ENDIAN)
                                    .asShortBuffer()
                                    .get(shorts)

                                // 计算RMS音量
                                var sum = 0.0
                                for (sample in shorts) {
                                    sum += sample * sample
                                    if (sample != 0.toShort()) {
                                        isZeroBuffer = false
                                    }
                                }
                                rms = Math.sqrt(sum / shorts.size)

                                // 如果音量太低，应用增益处理
                                if (rms < 500 && !isZeroBuffer) {
                                    val gainFactor = Math.min(2.5, 1000 / Math.max(rms, 1.0))

                                    // 应用增益，但避免溢出
                                    for (i in shorts.indices) {
                                        val amplified = shorts[i] * gainFactor
                                        shorts[i] = when {
                                            amplified > Short.MAX_VALUE -> Short.MAX_VALUE
                                            amplified < Short.MIN_VALUE -> Short.MIN_VALUE
                                            else -> amplified.toInt().toShort()
                                        }
                                    }

                                    // 将处理后的短整型数组转换回字节数组
                                    val processedBuffer = ByteBuffer.allocate(shorts.size * 2)
                                        .order(ByteOrder.LITTLE_ENDIAN)

                                    for (value in shorts) {
                                        processedBuffer.putShort(value)
                                    }

                                    // 复制处理后的数据回原始缓冲区
                                    val processedData = processedBuffer.array()
                                    System.arraycopy(processedData, 0, buffer, 0, Math.min(processedData.size, bytesRead))

                                    Log.d(TAG, "应用音量增益: $gainFactor, 原始RMS: $rms")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "音频处理失败", e)
                                // 如果处理失败，继续使用原始缓冲区
                            }

                            // 使用RMS值判断是否为静音，更准确
                            val isSilence = rms < 50

                            if (isZeroBuffer || isSilence) {
                                consecutiveZeroFrames++

                                // 如果连续检测到太多静音帧，发出警告
                                if (consecutiveZeroFrames == maxConsecutiveZeroFrames) {
                                    Log.w(TAG, "检测到连续静音帧，麦克风可能未正常工作或周围环境太安静")
                                    onError(AudioRecorderError.ZERO_AUDIO_DATA)
                                }
                            } else {
                                // 重置计数器并更新最后有效音频时间
                                consecutiveZeroFrames = 0
                                lastNonZeroAudioTime = System.currentTimeMillis()
                            }

                            // 检查是否太久没有收到有效音频
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastNonZeroAudioTime > 5000) { // 5秒无有效音频
                                Log.w(TAG, "5秒内未检测到有效音频，请检查麦克风")
                                lastNonZeroAudioTime = currentTime // 重置以避免连续警告
                            }

                            // 将读取的数据发送给回调函数
                            if (bytesRead > 0) {
                                // 复制数据，避免缓冲区被后续的读取操作覆盖
                                val data = ByteArray(bytesRead)
                                System.arraycopy(buffer, 0, data, 0, bytesRead)
                                onAudioBufferReady(data)
                            }
                        } else if (bytesRead < 0) {
                            // 读取错误
                            Log.e(TAG, "读取音频数据错误: $bytesRead")
                            if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                                Log.e(TAG, "无效操作错误，AudioRecord可能未正确初始化")
                                onError(AudioRecorderError.NEED_REINITIALIZE)
                                break
                            } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                                Log.e(TAG, "参数错误")
                                onError(AudioRecorderError.BUFFER_ERROR)
                                break
                            } else {
                                Log.e(TAG, "未知录音错误: $bytesRead")
                                onError(AudioRecorderError.RECORDING_ERROR)
                                break
                            }
                        }

                        // 短暂休眠，避免CPU占用过高
                        delay(1)
                    }

                    // 录音结束，记录统计信息
                    val endTime = System.currentTimeMillis()
                    val totalDuration = (endTime - startTime) / 1000.0

                    Log.d(TAG, String.format(
                        "录音已结束。总时长: %.1f秒，总数据: %.2f KB，总帧数: %d",
                        totalDuration,
                        totalBytesRead / 1024.0,
                        totalFramesRead
                    ))

                    // 如果没有读取到任何数据，报告错误
                    if (totalBytesRead == 0L) {
                        Log.e(TAG, "整个录音过程没有读取到任何音频数据")
                        onError(AudioRecorderError.NO_AUDIO_DATA)
                    }
                    // 如果数据量异常少，也报告错误
                    else if (totalBytesRead < 1000 && totalDuration > 1.0) {
                        Log.e(TAG, "录音数据量异常少: ${totalBytesRead}字节/${totalDuration}秒")
                        onError(AudioRecorderError.NO_AUDIO_DATA)
                    }

                } catch (e: Exception) {
                    when (e) {
                        is SecurityException -> {
                            Log.e(TAG, "录音权限错误", e)
                            onError(AudioRecorderError.PERMISSION_DENIED)
                        }
                        is IllegalStateException -> {
                            Log.e(TAG, "AudioRecord状态错误", e)
                            onError(AudioRecorderError.NEED_REINITIALIZE)
                        }
                        else -> {
                            Log.e(TAG, "录音过程中发生未知错误", e)
                            onError(AudioRecorderError.UNKNOWN_ERROR)
                        }
                    }
                } finally {
                    // 停止录音并释放资源
                    stopRecordingSafely()
                    isRecording.set(false)
                }
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "启动录音失败", e)
            isRecording.set(false)
            onError(AudioRecorderError.START_FAILED)
            false
        }
    }

    // 开始录音并返回音频数据流（兼容旧版本）
    @OptIn(ExperimentalStdlibApi::class)
    fun startRecordingAsFlow(): Flow<ByteArray> = flow {
        // 使用变量控制循环
        var continueRecording = true

        try {
            mutex.withLock {
                // 确保AudioRecord已正确初始化
                if (audioRecord == null || audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                    Log.d(TAG, "音频流录制前初始化AudioRecord")
                    if (!initialize()) {
                        throw IllegalStateException("AudioRecord 初始化失败")
                    }
                }

                // 确保前一个录音状态已清理
                if (isRecording.get()) {
                    Log.w(TAG, "在调用startRecordingAsFlow前，已存在录音状态，现将其停止")
                    stopRecordingSafely()
                    delay(100) // 给停止过程一些时间
                }

                // 开始新的录音会话
                isRecording.set(true)

                try {
                    audioRecord?.startRecording()
                    Log.d(TAG, "音频流录制已开始")
                } catch (e: Exception) {
                    Log.e(TAG, "启动音频流录制失败", e)
                    isRecording.set(false)
                    throw IllegalStateException("启动音频录制失败: ${e.message}", e)
                }
            }

            // 创建缓冲区
            val buffer = ByteArray(BYTES_PER_FRAME)
            // 使用直接缓冲区减少数据复制
            val directBuffer = ByteBuffer.allocateDirect(BYTES_PER_FRAME)
            directBuffer.order(ByteOrder.nativeOrder())

            while (isRecording.get() && continueRecording) {
                try {
                    // 执行录音操作
                    val readResult = withContext(Dispatchers.IO) {
                        ensureActive() // 确保协程未被取消

                        // 重置DirectBuffer
                        directBuffer.clear()

                        // 读取音频数据到DirectBuffer
                        val result = audioRecord?.read(directBuffer, BYTES_PER_FRAME) ?: -1

                        // 如果成功读取，复制到ByteArray以便发送
                        if (result > 0) {
                            directBuffer.flip()
                            val bytesToCopy = result.coerceAtMost(buffer.size)
                            if (bytesToCopy > 0) {
                                directBuffer.get(buffer, 0, bytesToCopy)
                                return@withContext result
                            }
                        }

                        result
                    }

                    if (readResult > 0) {
                        // 确保数据有效后再发送
                        val validData = buffer.copyOf(readResult.coerceAtMost(buffer.size))

                        // 发送录制的音频数据
                        emit(validData)

                        // 控制发送速率，40ms一个包
                        delay(40)
                    } else if (readResult < 0) {
                        // 处理错误情况
                        @OptIn(ExperimentalStdlibApi::class)
                        when (readResult) {
                            AudioRecord.ERROR -> Log.e(TAG, "录音时发生一般性错误")
                            AudioRecord.ERROR_BAD_VALUE -> Log.e(TAG, "录音参数错误")
                            AudioRecord.ERROR_DEAD_OBJECT -> {
                                Log.e(TAG, "录音对象不再有效，尝试重新初始化")
                                // 使用标志变量而不是continue/break
                                var shouldContinue = false
                                var shouldBreak = false

                                // 尝试恢复
                                mutex.withLock {
                                    isRecording.set(false)

                                    // 安全停止并重新初始化
                                    try {
                                        audioRecord?.stop()
                                    } catch (e: Exception) {
                                        Log.e(TAG, "停止无效的AudioRecord失败", e)
                                    }

                                    val initialized = initialize() // 直接调用非挂起的initialize()
                                    if (initialized) {
                                        isRecording.set(true)
                                        try {
                                            audioRecord?.startRecording()
                                            Log.d(TAG, "重新初始化后恢复录音")
                                            shouldContinue = true
                                        } catch (e: Exception) {
                                            Log.e(TAG, "恢复录音失败", e)
                                            shouldBreak = true
                                        }
                                    } else {
                                        Log.e(TAG, "重新初始化AudioRecord失败，退出录音")
                                        shouldBreak = true
                                    }
                                }

                                if (shouldBreak) {
                                    continueRecording = false
                                    continue
                                }
                                if (shouldContinue) {
                                    continue
                                }
                            }
                            AudioRecord.ERROR_INVALID_OPERATION -> {
                                Log.e(TAG, "录音无效操作")
                                continueRecording = false
                                continue
                            }
                            else -> Log.e(TAG, "读取音频数据失败，未知错误码: $readResult")
                        }

                        delay(100) // 给系统一些恢复时间
                    }
                } catch (e: CancellationException) {
                    Log.d(TAG, "音频流录制协程被取消")
                    continueRecording = false // 使用状态变量控制循环退出
                } catch (e: BufferUnderflowException) {
                    Log.e(TAG, "音频流录制缓冲区下溢异常", e)
                    delay(50)  // 短暂延迟后继续尝试
                } catch (e: Exception) {
                    Log.e(TAG, "音频流录制数据读取异常: ${e.javaClass.simpleName}", e)
                    delay(100)  // 短暂延迟后继续尝试
                }
            }
        } catch (e: CancellationException) {
            Log.d(TAG, "音频流录制被取消", e)
            throw e // 重新抛出取消异常
        } catch (e: Exception) {
            Log.e(TAG, "音频流录制过程中发生异常", e)
            isRecording.set(false)
            onError(AudioRecorderError.RECORDING_ERROR)
            throw e
        } finally {
            // 确保在流结束时停止录音
            if (isRecording.getAndSet(false)) {
                try {
                    if (audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                        audioRecord?.stop()
                        Log.d(TAG, "音频流录制已停止")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止音频流录制失败", e)
                }
            }
        }
    }.flowOn(Dispatchers.IO)  // 确保在IO线程上执行

    // 安全停止录音的内部方法
    private fun stopRecordingSafely() {
        recordingScope.launch {
            stopRecording()
        }
    }

    // 停止录音
    fun stopRecording() {
        if (!isRecording.get()) {
            return
        }

        recordingScope.launch {
            mutex.withLock {
                try {
                    Log.d(TAG, "停止录音中...")
                    // 首先更新标志位，这样协程会停止读取
                    isRecording.set(false)

                    // 取消录音作业
                    recordingJob?.cancelAndJoin() // 使用cancelAndJoin确保完全取消
                    recordingJob = null

                    // 停止AudioRecord
                    try {
                        audioRecord?.stop()
                    } catch (e: Exception) {
                        Log.e(TAG, "停止AudioRecord失败", e)
                    }

                    // 恢复 AudioManager 设置
                    restoreAudioManager()
                    Log.d(TAG, "录音已停止")
                } catch (e: Exception) {
                    Log.e(TAG, "停止录音失败", e)
                }
            }
        }
    }

    // 恢复音频管理器
    private fun restoreAudioManager() {
        try {
            audioManager?.let { manager ->
                // 恢复到普通模式
                Log.d(TAG, "恢复音频模式到 MODE_NORMAL")
                manager.mode = AudioManager.MODE_NORMAL
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复音频管理器设置失败", e)
        }
    }

    /**
     * 释放录音机资源
     * 此方法应在不再需要录音功能时调用，例如在ViewModel的onCleared方法中
     */
    fun release() {
        Log.i(TAG, "释放AudioRecorder资源")

        try {
            // 立即设置停止标志，确保所有协程循环能立即退出
            isRecording.set(false)

            // 使用mutex确保线程安全
            runBlocking {
                mutex.withLock {
                    // 1. 首先停止录音并取消所有协程
                    try {
                        // 安全停止AudioRecord
                        if (audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                            try {
                                audioRecord?.stop()
                                Log.d(TAG, "AudioRecord已停止")
                            } catch (e: Exception) {
                                Log.e(TAG, "停止AudioRecord失败", e)
                            }
                        }

                        // 取消录音作业并等待其完成
                        recordingJob?.let { job ->
                            if (job.isActive) {
                                job.cancel("AudioRecorder销毁")
                                // 使用超时版本的join，避免永久阻塞
                                withTimeoutOrNull(500) {
                                    job.join()
                                }
                                Log.d(TAG, "录音作业已取消")
                            }
                            recordingJob = null
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "取消录音作业时发生异常", e)
                    }

                    // 2. 释放AudioRecord资源
                    try {
                        audioRecord?.apply {
                            if (state == AudioRecord.STATE_INITIALIZED) {
                                try {
                                    release()
                                    Log.d(TAG, "AudioRecord资源已释放")
                                } catch (e: Exception) {
                                    Log.e(TAG, "释放AudioRecord资源失败", e)
                                }
                            }
                        }
                        audioRecord = null
                    } catch (e: Exception) {
                        Log.e(TAG, "处理AudioRecord时发生异常", e)
                    }

                    // 3. 恢复AudioManager设置
                    try {
                        restoreAudioManager()
                    } catch (e: Exception) {
                        Log.e(TAG, "恢复AudioManager设置失败", e)
                    }
                }
            }

            // 4. 清理协程作用域 - 在mutex锁外执行避免死锁
            try {
                _recordingCoroutineScope?.let { scope ->
                    scope.cancel("AudioRecorder销毁")
                    // 使用非阻塞方式等待取消完成
                    runBlocking {
                        withTimeoutOrNull(500) {
                            scope.coroutineContext[Job]?.join()
                        }
                    }
                }
                _recordingCoroutineScope = null
                Log.d(TAG, "录音协程作用域已取消")
            } catch (e: Exception) {
                Log.e(TAG, "取消录音协程作用域时发生异常", e)
            }

            // 5. 清理recordingScope
            try {
                if (recordingScope.isActive) {
                    recordingScope.cancel("AudioRecorder销毁")
                    Log.d(TAG, "主录音协程作用域已取消")
                }
            } catch (e: Exception) {
                Log.e(TAG, "取消主录音协程作用域时发生异常", e)
            }

            Log.i(TAG, "AudioRecorder所有资源已释放完毕")
        } catch (e: Exception) {
            Log.e(TAG, "释放AudioRecorder资源时发生未预期的异常", e)
        } finally {
            // 确保关键资源被清理，即使发生异常
            try {
                audioRecord?.release()
            } catch (e: Exception) {
                Log.e(TAG, "在finally块中释放AudioRecord失败", e)
            }
            audioRecord = null
            recordingJob = null
            isRecording.set(false)
        }
    }

    // 在应用程序退出时完全取消所有协程
    fun shutdown() {
        try {
            // 设置状态
            isRecording.set(false)

            // 取消所有工作
            recordingJob?.cancel()
            recordingJob = null

            // 释放音频记录器
            try {
                audioRecord?.release()
            } catch (e: Exception) {
                Log.e(TAG, "释放AudioRecord失败", e)
            }
            audioRecord = null

            // 恢复AudioManager
            try {
                restoreAudioManager()
            } catch (e: Exception) {
                Log.e(TAG, "恢复AudioManager失败", e)
            }

            // 取消整个协程作用域
            recordingScope.cancel()

            Log.d(TAG, "AudioRecorder完全关闭")
        } catch (e: Exception) {
            Log.e(TAG, "关闭AudioRecorder失败", e)
        }
    }

    // 在对象被垃圾回收时确保释放资源
    protected fun finalize() {
        try {
            if (audioRecord != null) {
                try {
                    if (isRecording.get()) {
                        audioRecord?.stop()
                    }
                    audioRecord?.release()
                } catch (e: Exception) {
                    Log.e(TAG, "在finalize中释放AudioRecord失败", e)
                }
                audioRecord = null
            }
        } catch (e: Exception) {
            Log.e(TAG, "finalize方法执行失败", e)
        }
    }
}