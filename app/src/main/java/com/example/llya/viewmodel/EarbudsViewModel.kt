package com.example.llya.viewmodel

import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.data.ApiResponse
import com.example.llya.network.DeviceBindManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import android.annotation.SuppressLint
import java.lang.reflect.Method

/**
 * 耳机设备UI状态
 */
data class EarbudsUiState(
    val deviceName: String = "未知设备",
    val volume: Float = 0f,
    val isDeviceBound: Boolean = false,
    val deviceId: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String = "",
    val battery: Int = 0,
    val firmwareVersion: String = "",
    val location: String = "",
    val image: String = "",
    val macAddress: String = ""
)

class EarbudsViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "EarbudsViewModel"
    
    private val audioManager = application.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private val bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    private val deviceBindManager = DeviceBindManager.getInstance(application)
    
    // UI 状态
    private val _uiState = MutableStateFlow(EarbudsUiState())
    val uiState: StateFlow<EarbudsUiState> = _uiState
    
    // 音量相关状态
    private val _volume = MutableStateFlow(0f)
    val volume: StateFlow<Float> = _volume.asStateFlow()
    
    // 设备名称状态
    private val _deviceName = MutableStateFlow("OWS1002")
    val deviceName: StateFlow<String> = _deviceName.asStateFlow()
    
    // 初始化
    init {
        // 加载当前音量
        refreshVolume()
        
        // 尝试获取当前连接设备的名称
        getCurrentDeviceName()
        
        Log.d(TAG, "EarbudsViewModel初始化完成")
    }
    
    // 获取当前连接的蓝牙设备名称
    @SuppressLint("MissingPermission")
    private fun getCurrentDeviceName() {
        viewModelScope.launch {
            try {
                val previousName = _deviceName.value
                
                bluetoothAdapter?.let { adapter ->
                    // 清除缓存，确保获取最新数据
                    adapter.cancelDiscovery()
                    
                    // 获取已绑定的设备
                    val bondedDevices = adapter.bondedDevices
                    
                    // 先尝试查找音频设备
                    var audioDevice = bondedDevices.firstOrNull { 
                        it.bluetoothClass?.majorDeviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO 
                    }
                    
                    // 如果没找到音频设备但有其他设备，使用第一个绑定设备
                    val finalDevice: BluetoothDevice? = when {
                        audioDevice != null -> audioDevice
                        bondedDevices.isNotEmpty() -> bondedDevices.first()
                        else -> null
                    }
                    
                    if (finalDevice != null) {
                        // 强制重新获取名称
                        val newName = finalDevice.name
                        if (newName != null && newName.isNotEmpty()) {
                            _deviceName.value = newName
                            // 更新UI状态
                            _uiState.update { it.copy(deviceName = newName) }
                            
                            Log.d(TAG, "当前蓝牙设备名称: $newName (之前: $previousName)")
                            
                            // 如果名称改变了，显示Toast通知
                            if (previousName != newName && previousName != "OWS1002") {
                                Toast.makeText(
                                    getApplication(),
                                    "设备名称已更新: $newName", 
                                    Toast.LENGTH_SHORT
                                ).show()
                            } else {
                                // 名称未变化或初始名称为OWS1002，不显示通知
                            }
                        } else {
                            Log.d(TAG, "设备名称为空，保持原名称: $previousName")
                        }
                    } else {
                        Log.d(TAG, "未找到已配对的蓝牙设备")
                    }
                } ?: run {
                    Log.d(TAG, "蓝牙适配器不可用")
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取设备名称出错: ${e.message}")
            }
        }
    }
    
    // 刷新当前音量
    fun refreshVolume() {
        viewModelScope.launch {
            try {
                // 获取当前媒体音量
                val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                
                // 计算音量百分比
                val volumePercent = currentVolume.toFloat() / maxVolume.toFloat()
                _volume.value = volumePercent
                
                Log.d(TAG, "当前音量: $currentVolume/$maxVolume = $volumePercent")
            } catch (e: Exception) {
                Log.e(TAG, "获取音量失败: ${e.message}")
            }
        }
    }
    
    // 设置系统音量
    fun setVolume(volumePercent: Float) {
        viewModelScope.launch {
            try {
                // 获取最大音量
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                
                // 计算实际音量值
                val volumeValue = (volumePercent * maxVolume).toInt()
                
                // 设置音量
                audioManager.setStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    volumeValue,
                    AudioManager.FLAG_PLAY_SOUND // 播放声音反馈
                )
                
                // 更新状态流
                _volume.value = volumePercent
                
                Log.d(TAG, "音量已设置为: $volumeValue/$maxVolume = $volumePercent")
            } catch (e: Exception) {
                Log.e(TAG, "设置音量失败: ${e.message}")
            }
        }
    }
    
    // 增加音量
    fun increaseVolume() {
        val currentVolume = _volume.value
        val newVolume = minOf(1f, currentVolume + 0.1f)
        setVolume(newVolume)
    }
    
    // 减少音量
    fun decreaseVolume() {
        val currentVolume = _volume.value
        val newVolume = maxOf(0f, currentVolume - 0.1f)
        setVolume(newVolume)
    }
    
    // 打开系统蓝牙设置页面
    fun openBluetoothSettings() {
        try {
            // 显示提示信息
            Toast.makeText(
                getApplication(),
                "正在打开蓝牙设置",
                Toast.LENGTH_SHORT
            ).show()
            
            // 打开设置页面
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            getApplication<Application>().startActivity(intent)
            Log.d(TAG, "打开系统蓝牙设置页面")
        } catch (e: Exception) {
            Log.e(TAG, "打开系统蓝牙设置页面失败: ${e.message}")
        }
    }
    
    // 公开方法：刷新设备名称（在从系统设置返回时调用）
    fun refreshDeviceName() {
        Log.d(TAG, "开始强制刷新蓝牙设备名称...")
        
        // 使用更强力的刷新方法
        forceRefreshBluetoothName()
    }
    
    // 强制刷新蓝牙名称 - 使用多种方法尝试获取最新名称
    @SuppressLint("MissingPermission")
    private fun forceRefreshBluetoothName() {
        viewModelScope.launch {
            try {
                val previousName = _deviceName.value
                Log.d(TAG, "强制刷新前设备名称: $previousName")
                
                // 1. 首先重新获取BluetoothAdapter实例
                val newAdapter = BluetoothAdapter.getDefaultAdapter()
                
                // 2. 取消所有发现活动
                newAdapter?.cancelDiscovery()
                
                // 3. 添加更长延迟，确保系统完成更新
                kotlinx.coroutines.delay(1000)
                
                // 4. 获取所有已配对设备
                val pairedDevices = newAdapter?.bondedDevices ?: emptySet()
                
                if (pairedDevices.isEmpty()) {
                    Log.d(TAG, "没有发现已配对设备")
                    return@launch
                }
                
                // 记录所有设备名称，用于调试
                pairedDevices.forEach { device ->
                    Log.d(TAG, "已配对设备: ${device.name} (${device.address}), 类型: ${device.type}, 类别: ${device.bluetoothClass?.majorDeviceClass}")
                }
                
                // 5. 查找音频设备
                val targetDevice = pairedDevices.firstOrNull { 
                    it.bluetoothClass?.majorDeviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO 
                }
                
                // 6. 确定最终使用的设备
                val finalDevice: BluetoothDevice? = when {
                    targetDevice != null -> targetDevice
                    pairedDevices.isNotEmpty() -> {
                        pairedDevices.first().also {
                            Log.d(TAG, "未找到音频设备，使用第一个已配对设备: ${it.name}")
                        }
                    }
                    else -> null
                }
                
                // 7. 尝试从选定设备获取名称
                if (finalDevice != null) {
                    // 直接从设备获取名称属性
                    val directName = finalDevice.name
                    Log.d(TAG, "从设备直接获取的名称: $directName")
                    
                    // 使用反射尝试刷新设备信息
                    try {
                        val refreshMethod = finalDevice.javaClass.getMethod("fetchName")
                        refreshMethod.invoke(finalDevice)
                        Log.d(TAG, "已尝试通过反射刷新设备名称")
                    } catch (e: Exception) {
                        Log.d(TAG, "反射调用失败: ${e.message}")
                    }
                    
                    // 再次获取名称，看是否刷新
                    val newName = finalDevice.name
                    
                    // 检查名称是否有效
                    if (newName != null && newName.isNotEmpty()) {
                        _deviceName.value = newName
                        Log.d(TAG, "更新后的设备名称: $newName (之前: $previousName)")
                        
                        // 显示名称变化通知
                        if (previousName != newName) {
                            Toast.makeText(
                                getApplication(),
                                "蓝牙设备名称已更新: $newName", 
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    } else {
                        Log.d(TAG, "获取的设备名称为空，保持原名称")
                    }
                } else {
                    Log.d(TAG, "未找到可用的蓝牙设备")
                }
            } catch (e: Exception) {
                Log.e(TAG, "强制刷新蓝牙名称时出错: ${e.message}")
            }
        }
    }
    
    // 解绑蓝牙设备
    @SuppressLint("MissingPermission")
    fun unbindBluetoothDevice() {
        viewModelScope.launch {
            try {
                // 获取当前连接的设备
                bluetoothAdapter?.let { adapter ->
                    // 停止发现过程
                    adapter.cancelDiscovery()
                    
                    // 获取已配对设备
                    val bondedDevices = adapter.bondedDevices
                    
                    // 查找音频设备
                    val audioDevice = bondedDevices.firstOrNull { 
                        it.bluetoothClass?.majorDeviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO 
                    } 
                    
                    // 确定要解绑的设备
                    val deviceToUnbind: BluetoothDevice? = when {
                        audioDevice != null -> audioDevice
                        bondedDevices.isNotEmpty() -> bondedDevices.first()
                        else -> null
                    }
                    
                    if (deviceToUnbind != null) {
                        // 使用反射调用removeBond方法解除配对
                        try {
                            val removeBondMethod = deviceToUnbind.javaClass.getMethod("removeBond")
                            val success = removeBondMethod.invoke(deviceToUnbind) as? Boolean ?: false
                            
                            if (success) {
                                Log.d(TAG, "设备解绑成功: ${deviceToUnbind.name}")
                                Toast.makeText(
                                    getApplication(),
                                    "设备\"${deviceToUnbind.name}\"已解绑", 
                                    Toast.LENGTH_SHORT
                                ).show()
                                
                                // 不再重置设备名称，因为我们将跳转到NoDeviceScreen
                            } else {
                                Log.d(TAG, "设备解绑失败")
                                Toast.makeText(
                                    getApplication(),
                                    "设备解绑失败，请在系统设置中手动解绑", 
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解绑设备出错: ${e.message}")
                            // 反射失败时，打开系统蓝牙设置页面
                            Toast.makeText(
                                getApplication(),
                                "无法直接解绑设备，请在蓝牙设置中手动操作", 
                                Toast.LENGTH_LONG
                            ).show()
                            openBluetoothSettings()
                        }
                    } else {
                        Log.d(TAG, "未找到可解绑的蓝牙设备")
                        Toast.makeText(
                            getApplication(),
                            "未找到已连接的蓝牙设备", 
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                } ?: run {
                    Log.d(TAG, "蓝牙适配器不可用")
                    Toast.makeText(
                        getApplication(),
                        "蓝牙不可用", 
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "解绑设备过程中出错: ${e.message}")
                Toast.makeText(
                    getApplication(),
                    "解绑设备出错: ${e.message}", 
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
    
    /**
     * 绑定当前连接的蓝牙设备到服务器
     * 
     * @param includeLocation 是否包含位置信息
     */
    @SuppressLint("MissingPermission")
    fun bindCurrentDevice(includeLocation: Boolean = false) {
        viewModelScope.launch {
            try {
                // 更新UI状态为加载中
                _uiState.update { it.copy(
                    isLoading = true,
                    errorMessage = ""
                )}
                
                bluetoothAdapter?.let { adapter ->
                    // 停止发现过程
                    adapter.cancelDiscovery()
                    
                    // 获取已配对设备
                    val bondedDevices = adapter.bondedDevices
                    
                    // 查找音频设备
                    val audioDevice = bondedDevices.firstOrNull { 
                        it.bluetoothClass?.majorDeviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO 
                    } 
                    
                    // 确定要绑定的设备
                    val deviceToBind: BluetoothDevice? = when {
                        audioDevice != null -> audioDevice
                        bondedDevices.isNotEmpty() -> bondedDevices.first()
                        else -> null
                    }
                    
                    if (deviceToBind != null) {
                        val deviceName = deviceToBind.name ?: "未知设备"
                        val macAddress = deviceToBind.address
                        
                        Log.d(TAG, "准备绑定设备: $deviceName, MAC: $macAddress")
                        
                        // 调用设备绑定API
                        deviceBindManager.bindDevice(deviceName, macAddress, includeLocation)
                            .collect { response ->
                                if (response.status == 0 && response.data != null) {
                                    // 绑定成功
                                    val deviceId = response.data.deviceId
                                    val bindTime = response.data.bindTime
                                    
                                    // 更新UI状态
                                    _uiState.update { it.copy(
                                        isLoading = false,
                                        isDeviceBound = true,
                                        deviceId = deviceId,
                                        errorMessage = ""
                                    )}
                                    
                                    Log.d(TAG, "设备绑定成功, ID: $deviceId, 时间: $bindTime")
                                    
                                    // 显示成功提示
                                    Toast.makeText(
                                        getApplication(),
                                        "设备\"$deviceName\"绑定成功", 
                                        Toast.LENGTH_SHORT
                                    ).show()
                                } else {
                                    // 绑定失败
                                    _uiState.update { it.copy(
                                        isLoading = false,
                                        errorMessage = "设备绑定失败: ${response.msg}"
                                    )}
                                    
                                    Log.e(TAG, "设备绑定失败: ${response.msg}")
                                    
                                    // 显示错误提示
                                    Toast.makeText(
                                        getApplication(),
                                        "设备绑定失败: ${response.msg}", 
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                    } else {
                        _uiState.update { it.copy(
                            isLoading = false,
                            errorMessage = "未找到可绑定的蓝牙设备"
                        )}
                        
                        Log.d(TAG, "未找到可绑定的蓝牙设备")
                        
                        Toast.makeText(
                            getApplication(),
                            "未找到可绑定的蓝牙设备", 
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                } ?: run {
                    _uiState.update { it.copy(
                        isLoading = false,
                        errorMessage = "蓝牙不可用"
                    )}
                    
                    Log.d(TAG, "蓝牙适配器不可用")
                    
                    Toast.makeText(
                        getApplication(),
                        "蓝牙不可用", 
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(
                    isLoading = false,
                    errorMessage = "绑定设备过程中出错: ${e.message}"
                )}
                
                Log.e(TAG, "绑定设备过程中出错: ${e.message}")
                
                Toast.makeText(
                    getApplication(),
                    "绑定设备出错: ${e.message}", 
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
    
    /**
     * 解绑服务器上的设备
     * 
     * @param deviceId 要解绑的设备ID
     */
    fun unbindServerDevice(deviceId: String) {
        viewModelScope.launch {
            try {
                // 更新UI状态为加载中
                _uiState.update { it.copy(
                    isLoading = true,
                    errorMessage = ""
                )}
                
                // 调用设备解绑API
                deviceBindManager.unbindDevice(deviceId)
                    .collect { response ->
                        if (response.status == 0) {
                            // 解绑成功
                            _uiState.update { it.copy(
                                isLoading = false,
                                isDeviceBound = false,
                                deviceId = "",
                                errorMessage = ""
                            )}
                            
                            Log.d(TAG, "服务器设备解绑成功")
                            
                            // 显示成功提示
                            Toast.makeText(
                                getApplication(),
                                "设备已从服务器解绑", 
                                Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            // 解绑失败
                            _uiState.update { it.copy(
                                isLoading = false,
                                errorMessage = "服务器设备解绑失败: ${response.msg}"
                            )}
                            
                            Log.e(TAG, "服务器设备解绑失败: ${response.msg}")
                            
                            // 显示错误提示
                            Toast.makeText(
                                getApplication(),
                                "服务器设备解绑失败: ${response.msg}", 
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
            } catch (e: Exception) {
                _uiState.update { it.copy(
                    isLoading = false,
                    errorMessage = "服务器设备解绑过程中出错: ${e.message}"
                )}
                
                Log.e(TAG, "服务器设备解绑过程中出错: ${e.message}")
                
                Toast.makeText(
                    getApplication(),
                    "服务器设备解绑出错: ${e.message}", 
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
    
    /**
     * 获取设备详情
     */
    fun getDeviceDetail() {
        viewModelScope.launch {
            try {
                // 更新UI状态为加载中
                _uiState.update { it.copy(
                    isLoading = true,
                    errorMessage = ""
                )}
                
                // 调用获取设备详情API
                deviceBindManager.getCurrentDeviceDetail()
                    .collect { response ->
                        // 修改判断逻辑：status为0或200都视为成功
                        if ((response.status == 0 || response.status == 200) && response.data != null) {
                            // 获取成功
                            val deviceInfo = response.data
                            
                            // 添加空值检查，使用默认值处理可能的null值
                            val deviceId = deviceInfo.deviceId ?: ""
                            val deviceName = deviceInfo.deviceName ?: "未知设备"
                            val battery = deviceInfo.battery ?: 0
                            val firmwareVersion = deviceInfo.firmwareVersion ?: ""
                            val location = deviceInfo.location ?: ""
                            val image = deviceInfo.image ?: ""
                            val macAddress = deviceInfo.macAddress ?: ""
                            
                            _uiState.update { it.copy(
                                isLoading = false,
                                isDeviceBound = true,
                                deviceId = deviceId,
                                deviceName = deviceName,
                                battery = battery,
                                firmwareVersion = firmwareVersion,
                                location = location,
                                image = image,
                                macAddress = macAddress,
                                errorMessage = ""
                            )}
                            
                            Log.d(TAG, "获取设备详情成功 - 设备ID: $deviceId, 名称: $deviceName, MAC: $macAddress")
                            Log.d(TAG, "设备电池: $battery%, 固件版本: $firmwareVersion")
                            if (location.isNotEmpty()) {
                                Log.d(TAG, "设备位置: $location")
                            }
                            if (image.isNotEmpty()) {
                                Log.d(TAG, "设备图片: $image")
                            }
                        } else {
                            // 获取失败
                            _uiState.update { it.copy(
                                isLoading = false,
                                errorMessage = "获取设备详情失败: ${response.msg}"
                            )}
                            
                            Log.e(TAG, "获取设备详情失败: ${response.msg}")
                        }
                    }
            } catch (e: Exception) {
                _uiState.update { it.copy(
                    isLoading = false,
                    errorMessage = "获取设备详情过程中出错: ${e.message}"
                )}
                
                Log.e(TAG, "获取设备详情过程中出错: ${e.message}", e)
            }
        }
    }
    
    /**
     * 全面解绑设备（本地蓝牙和服务器）
     * 
     * 该方法执行以下步骤：
     * 1. 无论设备绑定状态如何，都尝试从服务器解绑：
     *    a. 如果MAC地址不为空，直接使用MAC地址解绑
     *    b. 如果MAC地址为空，尝试从当前蓝牙连接获取MAC地址
     *    c. 如果仍无法获取MAC地址，尝试解绑所有已配对设备
     * 2. 解绑本地蓝牙连接
     * 
     * 注意：即使绑定状态为false，也会尝试服务器解绑，以确保完全解绑
     */
    fun fullUnbindDevice(forceServerUnbind: Boolean = true) {
        // 立即显示开始解绑的提示
        Toast.makeText(
            getApplication(),
            "开始解绑设备...",
            Toast.LENGTH_SHORT
        ).show()
        
        Log.d(TAG, "===== 开始执行设备全面解绑流程 =====")
        
        viewModelScope.launch {
            try {
                // 更新UI状态为加载中
                _uiState.update { it.copy(
                    isLoading = true,
                    errorMessage = ""
                )}
                
                // 获取当前设备MAC地址和绑定状态
                val macAddress = _uiState.value.macAddress
                val isDeviceBound = _uiState.value.isDeviceBound
                Log.d(TAG, "当前MAC地址: $macAddress, 绑定状态: $isDeviceBound")
                
                // 记录操作结果
                var serverUnbindSuccess = true
                var localUnbindSuccess = false
                
                // 步骤1: 尝试服务器解绑（即使绑定状态为false也尝试）
                if (isDeviceBound || forceServerUnbind) {
                    // 尝试获取MAC地址
                    val deviceMacAddress = if (macAddress.isNotEmpty()) {
                        macAddress
                    } else {
                        deviceBindManager.getCurrentDeviceMacAddress()
                    }
                    
                    if (deviceMacAddress.isNotEmpty()) {
                        Log.d(TAG, "第一步：开始从服务器解绑设备 MAC: $deviceMacAddress")
                        Toast.makeText(
                            getApplication(),
                            "正在从服务器解绑设备...",
                            Toast.LENGTH_SHORT
                        ).show()
                        
                        try {
                            deviceBindManager.smartUnbindDevice(deviceMacAddress).collect { response ->
                                Log.d(TAG, "服务器解绑响应: status=${response.status}, msg=${response.msg}")
                                
                                if (response.status == 0 || response.status == 200) {
                                    // 服务器解绑成功
                                    _uiState.update { it.copy(
                                        isDeviceBound = false,
                                        deviceId = "",
                                        macAddress = ""
                                    )}
                                    Log.d(TAG, "服务器设备解绑成功")
                                    serverUnbindSuccess = true
                                    
                                    Toast.makeText(
                                        getApplication(),
                                        "服务器解绑成功，开始解绑本地蓝牙...",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                } else {
                                    // 服务器解绑失败
                                    Log.e(TAG, "服务器设备解绑失败: ${response.msg}")
                                    serverUnbindSuccess = false
                                    _uiState.update { it.copy(
                                        errorMessage = "服务器设备解绑失败: ${response.msg}"
                                    )}
                                    
                                    Toast.makeText(
                                        getApplication(),
                                        "服务器解绑失败: ${response.msg}",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "服务器设备解绑过程中出错: ${e.message}", e)
                            serverUnbindSuccess = false
                            _uiState.update { it.copy(
                                errorMessage = "服务器设备解绑过程中出错: ${e.message}"
                            )}
                            
                            Toast.makeText(
                                getApplication(),
                                "服务器解绑出错: ${e.message}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    } else {
                        // 尝试使用设备ID解绑
                        val deviceId = _uiState.value.deviceId
                        if (deviceId.isNotEmpty()) {
                            Log.d(TAG, "MAC地址为空，尝试使用设备ID解绑: $deviceId")
                            
                            Toast.makeText(
                                getApplication(),
                                "正在使用设备ID尝试解绑...",
                                Toast.LENGTH_SHORT
                            ).show()
                            
                            try {
                                deviceBindManager.smartUnbindDevice(deviceId).collect { response ->
                                    Log.d(TAG, "使用设备ID解绑响应: status=${response.status}, msg=${response.msg}")
                                    
                                    if (response.status == 0 || response.status == 200) {
                                        // 服务器解绑成功
                                        _uiState.update { it.copy(
                                            isDeviceBound = false,
                                            deviceId = "",
                                            macAddress = ""
                                        )}
                                        Log.d(TAG, "使用设备ID解绑成功")
                                        serverUnbindSuccess = true
                                        
                                        Toast.makeText(
                                            getApplication(),
                                            "服务器解绑成功，开始解绑本地蓝牙...",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    } else {
                                        tryUnbindAllDevices()
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "使用设备ID解绑过程中出错: ${e.message}", e)
                                // 继续尝试解绑所有设备
                                tryUnbindAllDevices()
                            }
                        } else {
                            // 尝试解绑所有配对设备
                            Log.d(TAG, "MAC地址和设备ID均为空，尝试解绑所有配对设备")
                            tryUnbindAllDevices()
                        }
                    }
                } else {
                    Log.d(TAG, "设备未绑定到服务器，但forceServerUnbind为false，跳过服务器解绑步骤")
                    serverUnbindSuccess = true // 如果未绑定且不强制解绑，视为服务器解绑成功
                }
                
                // 步骤2: 解绑本地蓝牙
                Log.d(TAG, "第二步：开始解绑本地蓝牙设备")
                
                Toast.makeText(
                    getApplication(),
                    "正在解绑本地蓝牙设备...",
                    Toast.LENGTH_SHORT
                ).show()
                
                bluetoothAdapter?.let { adapter ->
                    // 停止发现过程
                    adapter.cancelDiscovery()
                    
                    // 获取已配对设备
                    val bondedDevices = adapter.bondedDevices
                    Log.d(TAG, "获取到${bondedDevices.size}个已配对设备")
                    
                    // 记录所有设备信息用于调试
                    bondedDevices.forEach { device ->
                        Log.d(TAG, "已配对设备: ${device.name ?: "未知名称"} (${device.address}), " +
                                "类型: ${device.type}, 类别: ${device.bluetoothClass?.majorDeviceClass}")
                    }
                    
                    // 查找音频设备
                    val audioDevice = bondedDevices.firstOrNull { 
                        it.bluetoothClass?.majorDeviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO 
                    } 
                    
                    // 确定要解绑的设备
                    val deviceToUnbind: BluetoothDevice? = when {
                        audioDevice != null -> audioDevice
                        bondedDevices.isNotEmpty() -> bondedDevices.first()
                        else -> null
                    }
                    
                    if (deviceToUnbind != null) {
                        val deviceName = deviceToUnbind.name ?: "未知设备"
                        val deviceAddress = deviceToUnbind.address
                        
                        Log.d(TAG, "准备解绑设备: $deviceName, MAC: $deviceAddress")
                        
                        // 使用反射调用removeBond方法解除配对
                        try {
                            val removeBondMethod = deviceToUnbind.javaClass.getMethod("removeBond")
                            Log.d(TAG, "调用反射方法removeBond()")
                            val success = removeBondMethod.invoke(deviceToUnbind) as? Boolean ?: false
                            
                            Log.d(TAG, "removeBond()方法返回结果: $success")
                            
                            if (success) {
                                Log.d(TAG, "本地蓝牙设备解绑成功: $deviceName")
                                localUnbindSuccess = true
                                
                                Toast.makeText(
                                    getApplication(),
                                    "蓝牙设备\"$deviceName\"解绑成功",
                                    Toast.LENGTH_SHORT
                                ).show()
                            } else {
                                Log.d(TAG, "本地蓝牙设备解绑失败，方法返回false")
                                localUnbindSuccess = false
                                
                                // 尝试打开系统蓝牙设置
                                Log.d(TAG, "尝试打开系统蓝牙设置来手动解绑")
                                openBluetoothSettings()
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解绑本地蓝牙设备出错: ${e.message}", e)
                            localUnbindSuccess = false
                            
                            // 更新错误信息
                            _uiState.update { it.copy(
                                errorMessage = "${_uiState.value.errorMessage} 本地蓝牙设备解绑失败: ${e.message}".trim()
                            )}
                            
                            // 尝试打开系统蓝牙设置
                            Log.d(TAG, "解绑失败，尝试打开系统蓝牙设置来手动解绑")
                            openBluetoothSettings()
                        }
                    } else {
                        Log.d(TAG, "未找到可解绑的本地蓝牙设备")
                        localUnbindSuccess = true // 如果没有设备可解绑，视为本地解绑成功
                        
                        Toast.makeText(
                            getApplication(),
                            "未找到可解绑的蓝牙设备",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                } ?: run {
                    Log.d(TAG, "蓝牙适配器不可用")
                    localUnbindSuccess = false
                    _uiState.update { it.copy(
                        errorMessage = "${_uiState.value.errorMessage} 蓝牙不可用".trim()
                    )}
                    
                    Toast.makeText(
                        getApplication(),
                        "蓝牙不可用，无法完成解绑",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                
                // 更新完成状态
                _uiState.update { it.copy(isLoading = false) }
                
                Log.d(TAG, "解绑完成状态 - 服务器: $serverUnbindSuccess, 本地: $localUnbindSuccess")
                
                // 最终结果提示
                if (serverUnbindSuccess && localUnbindSuccess) {
                    // 全部解绑成功
                    Log.d(TAG, "设备已完全解绑成功 (服务器和本地蓝牙)")
                    Toast.makeText(
                        getApplication(),
                        "设备已完全解绑",
                        Toast.LENGTH_SHORT
                    ).show()
                } else if (!serverUnbindSuccess && !localUnbindSuccess) {
                    // 全部解绑失败
                    Log.e(TAG, "设备解绑完全失败 (服务器和本地蓝牙均解绑失败)")
                    Toast.makeText(
                        getApplication(),
                        "设备解绑失败：服务器和本地蓝牙均解绑失败",
                        Toast.LENGTH_LONG
                    ).show()
                } else if (!serverUnbindSuccess) {
                    // 服务器解绑失败
                    Log.e(TAG, "服务器设备解绑失败，本地蓝牙已解绑")
                    Toast.makeText(
                        getApplication(),
                        "服务器设备解绑失败，本地蓝牙已解绑",
                        Toast.LENGTH_LONG
                    ).show()
                } else {
                    // 本地解绑失败
                    Log.e(TAG, "本地蓝牙解绑失败，服务器设备已解绑")
                    Toast.makeText(
                        getApplication(),
                        "本地蓝牙解绑失败，服务器设备已解绑",
                        Toast.LENGTH_LONG
                    ).show()
                }
                
                Log.d(TAG, "===== 设备全面解绑流程结束 =====")
                
                // 最终重置UI状态，确保解绑后状态一致
                if (serverUnbindSuccess) {
                    _uiState.update { it.copy(
                        isDeviceBound = false,
                        deviceId = "",
                        macAddress = ""
                    )}
                    Log.d(TAG, "已重置设备绑定状态")
                }
            } catch (e: Exception) {
                // 处理意外异常
                Log.e(TAG, "设备解绑过程中发生意外错误: ${e.message}", e)
                _uiState.update { it.copy(
                    isLoading = false,
                    errorMessage = "设备解绑过程中发生意外错误: ${e.message}"
                )}
                
                Toast.makeText(
                    getApplication(),
                    "设备解绑过程中发生错误: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
    
    /**
     * 尝试解绑服务器上的所有配对设备
     * 作为MAC地址和设备ID都无法使用时的备选方案
     * 
     * @return 是否有任何设备解绑成功
     */
    private suspend fun tryUnbindAllDevices(): Boolean {
        var anyDeviceUnbindSuccess = false
        
        bluetoothAdapter?.let { adapter ->
            val bondedDevices = adapter.bondedDevices
            
            if (bondedDevices.isNotEmpty()) {
                Log.d(TAG, "尝试解绑所有配对设备，共 ${bondedDevices.size} 个")
                
                // 创建一个Toast显示正在尝试解绑多个设备
                Toast.makeText(
                    getApplication(),
                    "正在尝试解绑所有配对设备...",
                    Toast.LENGTH_SHORT
                ).show()
                
                for (device in bondedDevices) {
                    val deviceAddress = device.address
                    val deviceName = device.name ?: "未知设备"
                    Log.d(TAG, "尝试从服务器解绑设备: $deviceName, MAC: $deviceAddress")
                    
                    try {
                        deviceBindManager.smartUnbindDevice(deviceAddress).collect { response ->
                            if (response.status == 0 || response.status == 200) {
                                Log.d(TAG, "成功解绑MAC地址为 $deviceAddress 的设备")
                                anyDeviceUnbindSuccess = true
                            } else {
                                Log.d(TAG, "设备 $deviceName (MAC: $deviceAddress) 解绑结果: ${response.msg}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解绑MAC地址为 $deviceAddress 的设备时出错: ${e.message}")
                    }
                }
                
                // 显示解绑结果
                if (anyDeviceUnbindSuccess) {
                    _uiState.update { it.copy(isDeviceBound = false) }
                    
                    Toast.makeText(
                        getApplication(),
                        "已成功解绑服务器上的设备",
                        Toast.LENGTH_SHORT
                    ).show()
                } else {
                    Toast.makeText(
                        getApplication(),
                        "所有服务器设备解绑尝试均失败",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } else {
                Log.d(TAG, "没有已配对的设备可供解绑")
            }
        } ?: run {
            Log.e(TAG, "蓝牙适配器不可用，无法获取已配对设备")
        }
        
        return anyDeviceUnbindSuccess
    }
} 