package com.example.llya.utils

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Process
import android.util.Log
import com.example.llya.MainActivity
import java.util.Locale
import android.graphics.Color
import com.example.llya.utils.findActivity

/**
 * 语言帮助类
 * 用于提供更深入的语言设置功能
 */
object LanguageHelper {
    private const val TAG = "LanguageHelper"
    
    /**
     * 重启应用以应用新的语言设置
     * 这是最彻底的应用语言设置的方法，会完全重启应用
     */
    fun restartApp(context: Context) {
        try {
            // 读取保存的系统栏颜色设置
            val prefs = context.getSharedPreferences("ui_settings", Context.MODE_PRIVATE)
            
            // 强制保存深色主题颜色
            try {
                // 默认深色主题颜色
                val defaultColor = Color.parseColor("#1E1D2B")
                
                // 保存深色主题的系统栏颜色设置 - 使用apply()改为commit()确保立即保存
                prefs.edit()
                    .putInt("status_bar_color", defaultColor)
                    .putInt("navigation_bar_color", defaultColor)
                    .putBoolean("is_dark_theme", true) // 强制使用深色主题设置
                    .commit() // 使用commit替代apply确保同步保存
                
                // 在重启前应用系统栏颜色到当前Activity
                val activity = context.findActivity()
                activity?.let {
                    try {
                        // 设置系统栏颜色
                        it.window.statusBarColor = defaultColor
                        it.window.navigationBarColor = defaultColor
                        
                        // 强制使用浅色图标
                        androidx.core.view.WindowCompat.getInsetsController(it.window, it.window.decorView).apply {
                            isAppearanceLightStatusBars = false
                            isAppearanceLightNavigationBars = false
                        }
                        
                        // 确保设置生效
                        it.window.decorView.systemUiVisibility = 0
                        it.window.addFlags(android.view.WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                        it.window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                        
                        Log.d(TAG, "在重启前成功应用系统栏颜色")
                    } catch (e: Exception) {
                        Log.e(TAG, "在重启前应用系统栏颜色失败", e)
                    }
                }
                
                Log.d(TAG, "已保存深色系统栏颜色设置：#1E1D2B")
            } catch (e: Exception) {
                Log.e(TAG, "保存系统栏颜色设置失败", e)
            }
            
            // 获取主Activity的Intent
            val intent = Intent(context, MainActivity::class.java)
            
            // 清除任务栈并开启新任务
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            
            // 添加标识，表示这是语言切换导致的重启
            intent.putExtra("from_language_change", true)
            
            // 启动新的Activity栈
            context.startActivity(intent)
            
            // 记录重启应用
            Log.d(TAG, "重启应用以应用新的语言设置")
        } catch (e: Exception) {
            Log.e(TAG, "重启应用失败", e)
        }
    }
    
    /**
     * 为任何Context包装应用当前的语言设置
     * 适用于attachBaseContext方法
     * 优先级: 用户设置的语言 > 系统语言
     */
    fun wrapContext(context: Context): Context {
        try {
            // 从SharedPreferences读取保存的语言设置
            val sharedPrefs = context.getSharedPreferences(LanguageUtils.LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
            val savedLanguageName = sharedPrefs.getString(LanguageUtils.SELECTED_LANGUAGE_KEY, null)
            val languageSource = sharedPrefs.getInt(LanguageUtils.LANGUAGE_SOURCE_KEY, 0) // 0=系统默认，1=用户选择
            
            val languageCode = if (savedLanguageName != null && languageSource == 1) {
                // 如果有用户设置的语言，从显示名称获取语言代码
                val code = LanguageUtils.getLanguageCodeFromDisplayName(savedLanguageName)
                if (code != null) {
                    Log.d(TAG, "使用用户设置的语言: $savedLanguageName ($code)")
                    code
                } else {
                    // 如果无法解析保存的语言名称，使用系统默认
                    Log.w(TAG, "无法解析用户设置的语言名称: $savedLanguageName，使用系统语言")
                    getSystemLanguageCode(context)
                }
            } else if (savedLanguageName != null && languageSource == 0) {
                // 检查系统语言是否已变更
                val systemCode = getSystemLanguageCode(context)
                val systemName = LanguageUtils.getLanguageDisplayName(systemCode)
                
                if (systemName != savedLanguageName) {
                    // 系统语言已变更，更新设置
                    Log.d(TAG, "检测到系统语言已变更，更新设置: $savedLanguageName -> $systemName")
                    updateLanguageSetting(context, systemName, systemCode, false)
                    systemCode
                } else {
                    // 系统语言未变更，使用保存的设置
                    val code = LanguageUtils.getLanguageCodeFromDisplayName(savedLanguageName)
                    if (code != null) {
                        code
                    } else {
                        systemCode
                    }
                }
            } else {
                // 如果没有保存的语言设置，使用系统语言
                val code = getSystemLanguageCode(context)
                Log.d(TAG, "没有保存的语言设置，使用系统语言: $code")
                
                // 保存系统语言设置
                val name = LanguageUtils.getLanguageDisplayName(code)
                updateLanguageSetting(context, name, code, false)
                
                code
            }
            
            // 创建并应用Locale
            val locale = createLocaleFromCode(languageCode)
            Locale.setDefault(locale)
            
            // 创建新的配置
            val configuration = Configuration(context.resources.configuration)
            
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                // 应用Locale到配置
                configuration.setLocale(locale)
                // 创建新的Context
                val newContext = context.createConfigurationContext(configuration)
                Log.d(TAG, "已创建新的Context并应用语言设置: $languageCode")
                newContext
            } else {
                @Suppress("DEPRECATION")
                configuration.locale = locale
                context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
                Log.d(TAG, "已更新资源配置并应用语言设置: $languageCode")
                context
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用语言设置失败，使用原始Context", e)
            return context
        }
    }
    
    /**
     * 根据语言代码创建Locale
     */
    private fun createLocaleFromCode(languageCode: String): Locale {
        val parts = languageCode.split("-")
        return when {
            parts.size > 1 -> Locale(parts[0], parts[1])
            else -> Locale(parts[0])
        }
    }
    
    /**
     * 获取系统当前语言代码
     */
    private fun getSystemLanguageCode(context: Context): String {
        val systemLocale = Locale.getDefault()
        val systemLanguage = systemLocale.language
        val systemCountry = systemLocale.country
        
        return if (systemCountry.isNotEmpty()) {
            LanguageUtils.standardizeLanguageCode("$systemLanguage-$systemCountry")
        } else {
            LanguageUtils.standardizeLanguageCode(systemLanguage)
        }
    }
    
    /**
     * 更新语言设置
     */
    private fun updateLanguageSetting(context: Context, languageName: String, languageCode: String, isUserSelected: Boolean) {
        try {
            val sharedPrefs = context.getSharedPreferences(LanguageUtils.LANGUAGE_PREFERENCE, Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putString(LanguageUtils.SELECTED_LANGUAGE_KEY, languageName)
                .putInt(LanguageUtils.LANGUAGE_SOURCE_KEY, if (isUserSelected) 1 else 0)
                .apply()
            
            Log.d(TAG, "更新语言设置: $languageName ($languageCode), 来源: ${if (isUserSelected) "用户选择" else "系统默认"}")
        } catch (e: Exception) {
            Log.e(TAG, "更新语言设置时出错", e)
        }
    }
} 