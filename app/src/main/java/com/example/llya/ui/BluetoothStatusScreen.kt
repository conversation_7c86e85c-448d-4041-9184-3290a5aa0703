package com.example.llya.ui

import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.bluetooth.*
import com.example.llya.utils.BluetoothPermissionFix
import com.example.llya.utils.BluetoothPermissionHelper
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect

/**
 * 蓝牙状态显示屏幕
 * 增强版本，包含针对低版本Android的权限兼容处理
 */
@Composable
fun BluetoothStatusScreen() {
    val context = LocalContext.current
    val viewModel: BluetoothViewModel = viewModel()
    val scope = rememberCoroutineScope()
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // 状态
    val state by viewModel.state.collectAsState()
    val statusMessage by viewModel.statusMessage.collectAsState()
    val shouldOpenSystemSettings by viewModel.shouldOpenSystemSettings.collectAsState()
    
    // 权限状态
    var hasBluetoothPermissions by remember { mutableStateOf(false) }
    var showPermissionRequest by remember { mutableStateOf(false) }
    var connectionErrorDetected by remember { mutableStateOf(false) }
    
    // 检查权限
    LaunchedEffect(Unit) {
        hasBluetoothPermissions = BluetoothPermissionHelper.hasRequiredPermissions(context)
        if (!hasBluetoothPermissions) {
            showPermissionRequest = true
        }
        
        // 注册生命周期观察者，以便在恢复时检查连接状态
        viewModel.registerLifecycleObserver(lifecycleOwner)
    }
    
    // 定期检查设备连接状态（每30秒）
    LaunchedEffect(Unit) {
        while (isActive) {
            viewModel.refreshOnResume()
            delay(30000) // 30秒检查一次
        }
    }
    
    // 处理系统设置跳转
    LaunchedEffect(shouldOpenSystemSettings) {
        if (shouldOpenSystemSettings) {
            BluetoothPermissionFix.showBluetoothPermissionSetting(context)
            viewModel.systemSettingsOpened()
        }
    }
    
    // 当状态消息显示断开连接提示时，自动尝试修复连接
    LaunchedEffect(statusMessage, state.connectionState) {
        if (statusMessage?.contains("断开连接") == true || 
            state.connectionState == ConnectionState.DISCONNECTED) {
            
            if (!connectionErrorDetected) {
                connectionErrorDetected = true
                
                Log.d("BluetoothStatusScreen", "检测到连接错误，尝试自动修复")
                // 尝试修复连接
                BluetoothPermissionFix.autoFixConnection(context, scope)
                
                delay(5000) // 5秒后重置错误检测状态
                connectionErrorDetected = false
            }
        }
    }
    
    // 请求蓝牙权限
    if (showPermissionRequest) {
        BluetoothPermissionHelper.RequestBluetoothPermissions { granted ->
            hasBluetoothPermissions = granted
            showPermissionRequest = false
            
            if (granted) {
                viewModel.refreshOnResume()
            } else {
                Toast.makeText(
                    context,
                    "蓝牙权限不足，可能影响设备连接功能",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }
    
    // UI界面
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1E1D2B))
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标题
        Text(
            text = "蓝牙设备状态",
            color = Color.White,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 连接状态
        val statusText = when (state.connectionState) {
            ConnectionState.CONNECTED -> "已连接"
            ConnectionState.CONNECTING -> "正在连接..."
            ConnectionState.DISCONNECTED -> "未连接"
        }
        
        val statusColor = when (state.connectionState) {
            ConnectionState.CONNECTED -> Color.Green
            ConnectionState.CONNECTING -> Color.Yellow
            ConnectionState.DISCONNECTED -> Color.Red
        }
        
        Text(
            text = "连接状态: $statusText",
            color = statusColor,
            fontSize = 18.sp
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 设备信息
        if (state.connectedDevice != null) {
            Text(
                text = "设备名称: ${state.deviceInfo["name"] ?: "未知设备"}",
                color = Color.White,
                fontSize = 16.sp
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "设备地址: ${state.deviceInfo["address"] ?: "未知"}",
                color = Color.White,
                fontSize = 16.sp
            )
        } else {
            Text(
                text = "未检测到已连接的设备",
                color = Color.Gray,
                fontSize = 16.sp
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 权限状态
        if (!hasBluetoothPermissions) {
            Text(
                text = "缺少蓝牙权限，可能影响设备连接",
                color = Color.Red,
                fontSize = 14.sp
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = { showPermissionRequest = true },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF8364FD)
                )
            ) {
                Text("请求权限")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 刷新按钮
        Button(
            onClick = { viewModel.refreshOnResume() },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF8364FD)
            )
        ) {
            Text("刷新设备状态")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 打开系统设置按钮
        Button(
            onClick = {
                BluetoothPermissionFix.showBluetoothPermissionSetting(context)
            },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF475896)
            )
        ) {
            Text("打开蓝牙设置")
        }
    }
    
    // 状态消息显示
    statusMessage?.let { message ->
        LaunchedEffect(message) {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }
} 