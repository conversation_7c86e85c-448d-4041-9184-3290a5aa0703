package com.example.llya.data

import com.google.gson.annotations.SerializedName

/**
 * 语言信息模型
 * 用于表示从API接收的语言数据
 */
data class LanguageModel(
    /**
     * 语言ID
     */
    @SerializedName("id")
    val id: Int = 0,
    
    /**
     * 语言代码，例如：zh-CN、en-US
     */
    @SerializedName("language_code")
    val languageCode: String = "",
    
    /**
     * 语言显示名称，例如：简体中文、English
     */
    @SerializedName("language_name")
    val languageName: String = "",
    
    /**
     * 语言本地名称，例如：中文、English
     */
    @SerializedName("native_name")
    val nativeName: String = "",
    
    /**
     * 语言地区，例如：China、United States
     */
    @SerializedName("region")
    val region: String = "",
    
    /**
     * 是否为默认语言
     */
    @SerializedName("is_default")
    val isDefault: Int = 0,
    
    /**
     * 语言排序
     */
    @SerializedName("sort")
    val sort: Int = 0,
    
    /**
     * 语言状态，1: 启用，0: 禁用
     */
    @SerializedName("status")
    val status: Int = 1,
    
    /**
     * 创建时间
     */
    @SerializedName("create_time")
    val createTime: Long = 0,
    
    /**
     * 更新时间
     */
    @SerializedName("update_time")
    val updateTime: Long = 0
) 