# 阿里云实时语音识别模块

## 模块概述

这是一个基于阿里云实时语音识别服务的Android测试模块，使用WebSocket协议实现实时语音转文字功能。该模块实现了完整的阿里云实时语音识别流程，包括Token获取、WebSocket连接、发送语音数据、接收识别结果等功能。

## 核心文件

- **AliyunAsrClient.kt**：阿里云实时语音识别客户端，负责WebSocket连接和数据交互
- **AliyunTokenUtil.kt**：阿里云Token获取工具类，用于获取访问令牌
- **AliyunAsrViewModel.kt**：阿里云语音识别ViewModel，管理语音识别状态和操作
- **AliyunAsrScreen.kt**：阿里云语音识别UI界面，使用Jetpack Compose实现

## 使用流程

1. 获取阿里云accessKeyId和accessKeySecret，以及appKey
2. 使用AliyunTokenUtil获取临时访问令牌
3. 使用AliyunAsrClient建立WebSocket连接
4. 开始录音并实时发送音频数据
5. 接收并处理识别结果
6. 结束识别并关闭连接

## 协议说明

本模块基于阿里云语音交互服务的WebSocket协议实现。具体协议细节参考：
[阿里云实时语音识别WebSocket协议](https://help.aliyun.com/zh/isi/developer-reference/websocket)

## 功能特点

1. **实时识别**：边说边出文字，支持长语音识别
2. **多种音频格式**：支持PCM、WAV等常见音频格式
3. **状态管理**：使用Kotlin Flow管理识别状态和结果
4. **错误处理**：完善的错误处理和状态反馈机制
5. **UI界面**：简洁美观的Material Design 3界面

## 使用示例

```kotlin
// 获取Token
val token = AliyunTokenUtil.getToken(accessKeyId, accessKeySecret)

// 创建客户端
val asrClient = AliyunAsrClient(token, appKey)

// 连接服务
asrClient.connect()

// 开始录音并发送音频数据
audioRecorder.startRecording { audioData ->
    asrClient.sendAudioData(audioData)
}

// 接收识别结果
asrClient.recognitionResultFlow.collect { result ->
    // 处理识别结果
}

// 停止识别
asrClient.sendStopTranscriptionCommand()
asrClient.close()
```

## 注意事项

1. 使用前需确保已经开通阿里云语音交互服务并创建项目
2. accessKeyId和accessKeySecret建议通过安全方式存储，不要硬编码在代码中
3. 识别过程中需保持良好的网络连接
4. 音频采样率推荐使用16000Hz，采样位数16bit，单声道
5. 发送音频数据时建议按40ms为单位分包发送，避免数据过大 