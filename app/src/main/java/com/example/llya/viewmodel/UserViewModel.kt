package com.example.llya.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.llya.data.ApiResponse
import com.example.llya.data.DeviceModel
import com.example.llya.data.UserModel
import com.example.llya.network.UserManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import android.util.Log

/**
 * 用户信息ViewModel
 * 
 * 负责处理用户信息相关的业务逻辑，并提供UI需要的状态数据
 */
class UserViewModel(application: Application) : AndroidViewModel(application) {
    private val userManager = UserManager.getInstance(application)
    
    // 当前登录用户信息
    val currentUser: LiveData<UserModel?> = userManager.currentUser
    
    // UI状态
    private val _uiState = MutableStateFlow<UiState>(UiState.Idle)
    val uiState: StateFlow<UiState> = _uiState
    
    // 登录状态流
    private val _isLoggedInState = MutableStateFlow(userManager.isLoggedIn())
    val isLoggedInState = _isLoggedInState.asStateFlow()
    
    // 防止重复请求标志
    private var _isLoadingUserDetail = false
    
    // 用户设备列表
    private val _userDevices = MutableLiveData<List<DeviceModel>>(emptyList())
    val userDevices: LiveData<List<DeviceModel>> = _userDevices
    
    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        // 更新登录状态流
        val loginStatus = userManager.isLoggedIn()
        _isLoggedInState.value = loginStatus
        Log.d("UserViewModel", "检查登录状态: $loginStatus")
        return loginStatus
    }
    
    /**
     * 退出登录
     */
    fun logout() {
        userManager.logout()
        _uiState.value = UiState.LogoutSuccess
        // 更新登录状态
        _isLoggedInState.value = false
        Log.d("UserViewModel", "用户已登出")
    }
    
    /**
     * 获取用户详情
     */
    fun getUserDetail() {
        // 检查是否已经在请求中，避免重复请求
        if (_isLoadingUserDetail) {
            Log.d("UserViewModel", "已有获取用户详情的请求正在进行中，忽略此次调用")
            return
        }
        
        viewModelScope.launch {
            _isLoadingUserDetail = true
            _uiState.value = UiState.Loading
            
            // 先检查用户是否已登录
            if (!isLoggedIn()) {
                _uiState.value = UiState.Error("用户未登录，无法获取用户信息")
                _isLoadingUserDetail = false
                return@launch
            }
            
            try {
                userManager.getUserDetail()
                    .catch { e ->
                        _uiState.value = UiState.Error(e.message ?: "获取用户信息失败")
                        _isLoadingUserDetail = false
                    }
                    .collect { response ->
                        if (response.status == 200) {
                            _uiState.value = UiState.Success("获取用户信息成功")
                        } else {
                            _uiState.value = UiState.Error(response.msg ?: "获取用户信息失败")
                        }
                        _isLoadingUserDetail = false
                    }
            } catch (e: Exception) {
                Log.e("UserViewModel", "获取用户详情异常: ${e.message}")
                _uiState.value = UiState.Error("获取用户信息异常: ${e.message}")
                _isLoadingUserDetail = false
            }
        }
    }
    
    /**
     * 更新用户信息
     */
    fun updateUserInfo(
        username: String,
        email: String,
        userType: Int,
        status: Int? = null,
        vipExpireTime: String? = null
    ) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.updateUserInfo(username, email, userType, status, vipExpireTime)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "更新用户信息失败")
                }
                .collect { response ->
                    if (response.status == 200) {
                        _uiState.value = UiState.Success("更新用户信息成功")
                    } else {
                        _uiState.value = UiState.Error(response.msg ?: "更新用户信息失败")
                    }
                }
        }
    }
    
    /**
     * 账号注销
     */
    fun accountCancellation(password: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.accountCancellation(password)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "账号注销失败")
                }
                .collect { response ->
                    if (response.status == 200) {
                        _uiState.value = UiState.Success("账号注销成功")
                    } else {
                        _uiState.value = UiState.Error(response.msg ?: "账号注销失败")
                    }
                }
        }
    }
    
    /**
     * 发送手机验证码
     */
    fun sendSms(mobile: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.sendSms(mobile)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "发送验证码失败")
                }
                .collect { response ->
                    if (response.status == 200) {
                        _uiState.value = UiState.VerifyCodeSent(response.key ?: "")
                    } else {
                        _uiState.value = UiState.Error(response.msg ?: "发送验证码失败")
                    }
                }
        }
    }
    
    /**
     * 发送邮箱验证码
     */
    fun sendEmailCode(email: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.sendEmailCode(email)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "发送验证码失败")
                }
                .collect { response ->
                    if (response.status == 200) {
                        _uiState.value = UiState.VerifyCodeSent(response.key ?: "")
                    } else {
                        _uiState.value = UiState.Error(response.msg ?: "发送验证码失败")
                    }
                }
        }
    }
    
    /**
     * 验证邮箱验证码
     */
    fun verifyEmailCode(email: String, code: String, key: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.verifyEmailCode(email, code, key)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "验证码验证失败")
                }
                .collect { response ->
                    if (response.status == 200) {
                        _uiState.value = UiState.Success("验证码验证成功")
                    } else {
                        _uiState.value = UiState.Error(response.msg ?: "验证码验证失败")
                    }
                }
        }
    }
    
    /**
     * 手机号登录
     */
    fun login(mobile: String, verifyId: String, verify: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.login(mobile, verifyId, verify)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "登录失败")
                    // 确保登录状态为false
                    _isLoggedInState.value = false
                }
                .collect { response ->
                    if (response.status == 200 && response.data != null) {
                        // 更新登录状态
                        _isLoggedInState.value = true
                        Log.d("UserViewModel", "用户登录成功")
                        _uiState.value = UiState.LoginSuccess(response.data)
                    } else {
                        _isLoggedInState.value = false
                        _uiState.value = UiState.Error(response.msg ?: "登录失败")
                    }
                }
        }
    }
    
    /**
     * 邮箱登录
     */
    fun emailLogin(email: String, verifyId: String, verify: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.emailLogin(email, verifyId, verify)
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "登录失败")
                    // 确保登录状态为false
                    _isLoggedInState.value = false
                }
                .collect { response ->
                    if (response.status == 200 && response.data != null) {
                        // 更新登录状态
                        _isLoggedInState.value = true
                        Log.d("UserViewModel", "用户邮箱登录成功")
                        _uiState.value = UiState.LoginSuccess(response.data)
                    } else {
                        _isLoggedInState.value = false
                        _uiState.value = UiState.Error(response.msg ?: "登录失败")
                    }
                }
        }
    }
    
    /**
     * 获取用户绑定设备
     */
    fun getUserDevices() {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            userManager.getUserDevices()
                .catch { e ->
                    _uiState.value = UiState.Error(e.message ?: "获取设备信息失败")
                }
                .collect { response ->
                    if (response.status == 200 && response.data != null) {
                        _userDevices.value = response.data
                        _uiState.value = UiState.Success("获取设备信息成功")
                    } else {
                        _uiState.value = UiState.Error(response.msg ?: "获取设备信息失败")
                    }
                }
        }
    }
    
    /**
     * UI状态密封类
     */
    sealed class UiState {
        object Idle : UiState()
        object Loading : UiState()
        data class Success(val message: String) : UiState()
        data class Error(val message: String) : UiState()
        data class LoginSuccess(val user: UserModel) : UiState()
        object LogoutSuccess : UiState()
        data class VerifyCodeSent(val key: String) : UiState()
    }
} 