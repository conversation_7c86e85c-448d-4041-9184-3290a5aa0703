package com.example.llya.network

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okio.ByteString
import org.json.JSONObject
import java.util.concurrent.TimeUnit
import java.io.IOException
import java.nio.ByteBuffer
import java.nio.ByteOrder
import com.example.llya.R
import com.example.llya.LlyaApplication

/**
 * 谷歌云语音服务
 * 专门用于普通语音转写功能，与会议记录功能分离
 */
class GoogleCloudSpeechService {
    private val TAG = "GoogleCloudSpeechService"

    // 基础URL和WebSocket URL
    private val BASE_URL = "https://lyapi.xlbcloud.com"
    private val WS_URL = "wss://lyapi.xlbcloud.com/ws/speech-to-text"

    private val client: OkHttpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    private var webSocket: WebSocket? = null
    private var speechRecognitionListener: SpeechRecognitionListener? = null

    companion object {
        @Volatile
        private var instance: GoogleCloudSpeechService? = null

        fun getInstance(): GoogleCloudSpeechService {
            return instance ?: synchronized(this) {
                instance ?: GoogleCloudSpeechService().also { instance = it }
            }
        }
    }

    /**
     * 开始实时语音识别
     */
    fun startRealTimeSpeechRecognition(
        languageCode: String = "zh-CN",
        model: String = "default", // 使用更精确的模型
        listener: SpeechRecognitionListener
    ) {
        // 标准化语言代码，确保与服务器兼容
        val standardizedLanguageCode = standardizeSpeechLanguageCode(languageCode)

        Log.d(
            TAG,
            "启动语音识别，原始语言: $languageCode, 标准化语言: $standardizedLanguageCode, 模型: $model"
        )

        // 关闭现有WebSocket连接
        webSocket?.close(1000, "切换语言")
        webSocket = null

        this.speechRecognitionListener = listener

        // 构建WebSocket请求URL，添加更多优化参数
        val wsUrl = "$WS_URL?languageCode=$standardizedLanguageCode&model=$model" +
                "&single_utterance=true" +  // 每次沉默后自动结束当前识别会话
                "&enableAutomaticPunctuation=true" + // 启用自动标点
                "&useEnhanced=true" + // 使用增强模型
                "&enableWordTimeOffsets=false" + // 禁用词时间偏移以减少数据量
                "&maxAlternatives=1" // 只返回最佳结果

        val request = Request.Builder()
            .url(wsUrl)
            .addHeader("Connection", "Upgrade")
            .addHeader("Upgrade", "websocket")
            .addHeader("User-Agent", "SpeechServiceAdapter/1.0")
            .build()

        Log.d(TAG, "创建WebSocket连接: $wsUrl")

        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接已打开，使用语言: $standardizedLanguageCode, 模型: $model")

                // 发送语言变更消息给服务器，添加更多高级配置
                val languageName = getLanguageDisplayName(standardizedLanguageCode)
                val languageChangeMessage = JSONObject().apply {
                    put("type", "changeLanguage")
                    put("languageCode", standardizedLanguageCode)
                    put("model", model)
                    put("name", languageName)
                    // 基本配置
                    put("singleUtterance", true)
                    put("interimResults", true)
                    // 高级配置
                    put("enableAutomaticPunctuation", true)
                    put("useEnhanced", true)
                    put("maxAlternatives", 1)
                    put("profanityFilter", false) // 不过滤脏话，保持原始识别结果
                    put("enableWordConfidence", false) // 禁用词置信度以减少数据量
                    put("enableWordTimeOffsets", false) // 禁用词时间偏移以减少数据量
                    put("enableSpeakerDiarization", false) // 禁用说话人分离
                    // 音频配置
                    put("sampleRateHertz", 16000) // 明确指定采样率
                    put("audioChannelCount", 1) // 单声道
                    put("encoding", "LINEAR16") // 16位PCM编码
                }.toString()

                // 发送消息
                webSocket.send(languageChangeMessage)
                Log.d(TAG, "已发送语言变更消息: $languageChangeMessage")

                // 发送增强配置消息
                val configMessage = JSONObject().apply {
                    put("type", "config")
                    put("singleUtterance", true)
                    put("interimResults", true)
                    put("enableAutomaticPunctuation", true)
                    put("useEnhanced", true)
                    put("maxAlternatives", 1)
                    // 添加强制重置标志，确保后端完全清除之前的识别状态
                    put("forceReset", true)
                    // 添加音频质量参数
                    put("audioQuality", "high")
                }.toString()
                webSocket.send(configMessage)
                Log.d(TAG, "已发送配置消息: $configMessage")

                listener.onConnectionEstablished()
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                try {
                    val json = JSONObject(text)
                    if (json.has("error")) {
                        listener.onError(json.getString("error"))
                    } else if (json.has("text")) {
                        val recognizedText = json.getString("text")
                        val isFinal = json.optBoolean("isFinal", false)

                        // 处理识别结果
                        val processedText = processRecognitionResult(recognizedText)

                        // 回调处理后的结果
                        listener.onRecognitionResult(processedText, isFinal)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析WebSocket消息失败", e)
                    listener.onError("解析识别结果失败: ${e.message}")
                }
            }

            override fun onFailure(
                webSocket: WebSocket,
                t: Throwable,
                response: Response?
            ) {
                Log.e(TAG, "WebSocket连接错误", t)
                listener.onError("语音识别连接错误: ${t.message}")
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket即将关闭: $code, $reason")
                webSocket.close(1000, null)
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket已关闭: $code, $reason")
                listener.onConnectionClosed()
            }
        })
    }

    /**
     * 标准化语音识别语言代码
     * 确保语言代码格式符合服务器期望的格式
     */
    private fun standardizeSpeechLanguageCode(languageCode: String): String {
        // 将常见语言代码转换为语音识别服务支持的标准格式
        return when {
            languageCode.startsWith("zh") -> "zh-CN" // 中文统一使用zh-CN
            languageCode.startsWith("en") -> "en-US" // 英文统一使用en-US
            languageCode.startsWith("ja") -> "ja-JP" // 日语
            languageCode.startsWith("ko") -> "ko-KR" // 韩语
            languageCode.startsWith("fr") -> "fr-FR" // 法语
            languageCode.startsWith("de") -> "de-DE" // 德语
            languageCode.startsWith("ru") -> "ru-RU" // 俄语
            languageCode.startsWith("es") -> "es-ES" // 西班牙语
            languageCode.startsWith("it") -> "it-IT" // 意大利语
            languageCode.startsWith("pt") -> "pt-BR" // 葡萄牙语
            languageCode.startsWith("vi") -> "vi-VN" // 越南语
            languageCode.startsWith("id") -> "id-ID" // 印尼语
            languageCode.startsWith("th") -> "th-TH" // 泰语
            languageCode.startsWith("tr") -> "tr-TR" // 土耳其语
            languageCode.startsWith("nl") -> "nl-NL" // 荷兰语
            languageCode.startsWith("pl") -> "pl-PL" // 波兰语
            else -> languageCode // 其他语言保持不变
        }
    }

    /**
     * 获取语言的显示名称
     */
    private fun getLanguageDisplayName(languageCode: String): String {
        val context = LlyaApplication.getInstance().applicationContext
        return when (languageCode) {
            "zh-CN" -> context.getString(R.string.lang_zh_cn)
            "en-US" -> context.getString(R.string.lang_en_us)
            "ja-JP" -> context.getString(R.string.lang_ja_jp)
            "ko-KR" -> context.getString(R.string.lang_ko_kr)
            "fr-FR" -> context.getString(R.string.lang_fr_fr)
            "de-DE" -> context.getString(R.string.lang_de_de)
            "ru-RU" -> context.getString(R.string.lang_ru_ru)
            "es-ES" -> context.getString(R.string.lang_es_es)
            "it-IT" -> context.getString(R.string.lang_it_it)
            "pt-BR" -> context.getString(R.string.lang_pt_br)
            "vi-VN" -> context.getString(R.string.lang_vi_vn)
            "id-ID" -> context.getString(R.string.lang_id_id)
            "th-TH" -> context.getString(R.string.lang_th_th)
            "tr-TR" -> context.getString(R.string.lang_tr_tr)
            "nl-NL" -> context.getString(R.string.lang_nl_nl)
            "pl-PL" -> context.getString(R.string.lang_pl_pl)
            else -> "$languageCode" // 未知语言直接返回代码
        }
    }

    /**
     * 处理识别结果
     * 清理和格式化识别文本
     */
    private fun processRecognitionResult(text: String): String {
        // 如果文本为空，直接返回
        if (text.isBlank()) return ""

        // 移除多余的空格
        var processedText = text.trim()

        // 移除特殊字符和控制字符
        processedText = processedText.replace(Regex("[\\p{Cntrl}]"), "")

        // 修复常见的识别错误
        processedText = fixCommonRecognitionErrors(processedText)

        // 修复重复文本问题
        processedText = removeRepeatedPhrases(processedText)

        // 返回处理后的文本
        return processedText
    }

    /**
     * 修复常见的语音识别错误
     */
    private fun fixCommonRecognitionErrors(text: String): String {
        var result = text

        // 修复标点符号间隔问题
        result = result.replace(Regex("([,.!?，。！？]) "), "$1")

        // 修复中文标点后不应有空格
        result = result.replace(Regex("([，。！？]) "), "$1")

        // 修复英文标点后应有空格
        result = result.replace(Regex("([,.!?])([a-zA-Z])"), "$1 $2")

        // 修复数字格式
        result = result.replace(Regex("(\\d) (\\d)"), "$1$2")

        // 修复常见的错误识别词
        val commonErrors = mapOf(
            "嗯嗯" to "嗯",
            "啊啊" to "啊",
            "那那" to "那",
            "这这" to "这",
            "的的" to "的",
            "是是" to "是",
            "我我" to "我",
            "你你" to "你",
            "他他" to "他",
            "她她" to "她",
            "它它" to "它"
        )

        // 应用常见错误修复
        for ((error, correction) in commonErrors) {
            result = result.replace(error, correction)
        }

        return result
    }

    /**
     * 移除重复的短语
     */
    private fun removeRepeatedPhrases(text: String): String {
        if (text.length < 10) return text

        var result = text

        // 检测并移除相邻重复的短语（3个字符以上）
        val regex = Regex("(\\S{3,})(\\s+\\1)+")
        result = result.replace(regex, "$1")

        // 检测并移除句子内部的重复短语
        val words = result.split(" ", "，", "。", "！", "？", ",", ".", "!", "?")
        for (word in words) {
            if (word.length >= 3) {
                // 检查同一个词是否在句子中连续出现多次
                val repeatedWordRegex = Regex("($word)([^\\S]\\1){2,}")
                result = result.replace(repeatedWordRegex, "$1")
            }
        }

        return result
    }

    /**
     * 发送音频数据到语音识别服务器
     */
    fun sendAudioData(audioData: ByteArray) {
        // 检查WebSocket是否正常连接
        if (webSocket == null) {
            Log.e(TAG, "WebSocket未连接，无法发送音频数据")
            return
        }

        // 检查音频数据是否有效
        if (audioData.isEmpty()) {
            Log.w(TAG, "音频数据为空，跳过发送")
            return
        }

        // 预处理音频数据，提高质量
        val processedAudio = preprocessAudio(audioData)

        // 发送处理后的音频数据
        try {
            webSocket?.send(ByteString.of(*processedAudio))
        } catch (e: Exception) {
            Log.e(TAG, "发送音频数据失败: ${e.message}")
            // 如果发送失败，尝试重新建立连接
            speechRecognitionListener?.let { listener ->
                listener.onError("音频数据发送失败，正在尝试重连")
                // 关闭当前连接并重新连接
                reconnectWebSocket(listener)
            }
        }
    }

    /**
     * 预处理音频数据
     * 应用噪音过滤和音量增强
     */
    private fun preprocessAudio(audioData: ByteArray): ByteArray {
        // 如果数据为空，直接返回
        if (audioData.isEmpty()) return audioData

        try {
            // 将字节数据转换为短整型数组以便处理
            val shortBuffer = ByteBuffer.wrap(audioData)
                .order(ByteOrder.LITTLE_ENDIAN)
                .asShortBuffer()
            val shorts = ShortArray(shortBuffer.remaining())
            shortBuffer.get(shorts)
            val shortArray = shorts

            // 1. 检测是否是静音数据
            var isSilence = true
            for (sample in shortArray) {
                if (Math.abs(sample.toInt()) > 150) { // 提高阈值，减少背景噪音干扰（从50提高到150）
                    isSilence = false
                    break
                }
            }

            // 如果是静音数据，直接返回原始数据
            if (isSilence) return audioData

            // 2. 计算音频能量
            var sum = 0.0
            for (sample in shortArray) {
                sum += sample * sample
            }
            val rms = Math.sqrt(sum / shortArray.size)

            // 3. 应用音量增强 - 如果音量太低
            if (rms < 700) { // 提高低音量阈值（从500提高到700）
                val gainFactor = Math.min(2.0, 800 / Math.max(rms, 1.0)) // 降低最大增益（从3.0降低到2.0）和增益系数（从1000降低到800）

                // 应用增益，但避免溢出
                for (i in shortArray.indices) {
                    val amplified = shortArray[i] * gainFactor
                    shortArray[i] = when {
                        amplified > Short.MAX_VALUE -> Short.MAX_VALUE
                        amplified < Short.MIN_VALUE -> Short.MIN_VALUE
                        else -> amplified.toInt().toShort()
                    }
                }

                Log.d(TAG, "应用音量增益: $gainFactor, 原始RMS: $rms")
            }

            // 4. 将处理后的短整型数组转换回字节数组
            val byteBuffer = ByteBuffer.allocate(shortArray.size * 2)
                .order(ByteOrder.LITTLE_ENDIAN)

            // 将短整型数据写入ByteBuffer
            for (value in shortArray) {
                byteBuffer.putShort(value)
            }

            // 获取字节数组
            val processedData = byteBuffer.array()

            return processedData
        } catch (e: Exception) {
            Log.e(TAG, "音频预处理失败，使用原始数据", e)
            return audioData
        }
    }

    /**
     * 重连WebSocket
     */
    private fun reconnectWebSocket(listener: SpeechRecognitionListener) {
        webSocket?.close(1000, "重新连接")
        webSocket = null

        // 获取用户最后使用的语言代码
        val languageCode = listener.lastLanguageCode ?: "zh-CN"
        val standardizedLanguageCode = standardizeSpeechLanguageCode(languageCode)
        val model = "default"

        // 重新连接
        val request = Request.Builder()
            .url("$WS_URL?languageCode=$standardizedLanguageCode&model=$model&single_utterance=true")
            .build()

        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket重连成功，使用语言: $standardizedLanguageCode")

                // 发送语言变更消息给服务器
                val languageName = getLanguageDisplayName(standardizedLanguageCode)
                val languageChangeMessage = JSONObject().apply {
                    put("type", "changeLanguage")
                    put("languageCode", standardizedLanguageCode)
                    put("model", model)
                    put("name", languageName)
                    // 添加single_utterance参数到初始化消息中
                    put("singleUtterance", true)
                    // 添加interimResults参数，允许接收中间结果
                    put("interimResults", true)
                }.toString()

                // 发送消息
                webSocket.send(languageChangeMessage)
                Log.d(TAG, "已发送语言变更消息: $languageChangeMessage")

                // 发送配置消息，确保single_utterance参数生效
                val configMessage = JSONObject().apply {
                    put("type", "config")
                    put("singleUtterance", true)
                    put("interimResults", true)
                }.toString()
                webSocket.send(configMessage)
                Log.d(TAG, "已发送配置消息: $configMessage")

                listener.onConnectionEstablished()
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                try {
                    val json = JSONObject(text)
                    if (json.has("error")) {
                        listener.onError(json.getString("error"))
                    } else if (json.has("text")) {
                        val recognizedText = json.getString("text")
                        val isFinal = json.optBoolean("isFinal", false)

                        // 处理识别结果
                        val processedText = processRecognitionResult(recognizedText)

                        // 回调处理后的结果
                        listener.onRecognitionResult(processedText, isFinal)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析WebSocket消息失败", e)
                    listener.onError("解析识别结果失败: ${e.message}")
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket重连失败", t)
                listener.onError("语音识别重连失败: ${t.message}")
            }
        })
    }

    /**
     * 停止语音识别
     */
    fun stopSpeechRecognition() {
        Log.d(TAG, "停止WebSocket连接")

        try {
            webSocket?.close(1000, "正常关闭")
        } catch (e: Exception) {
            Log.e(TAG, "关闭WebSocket连接时出错", e)
        } finally {
            webSocket = null
            speechRecognitionListener = null
        }
    }

    /**
     * 重置识别状态
     * 通知服务器重置当前识别会话，但不关闭连接
     * 这样可以在检测到沉默后清空内部缓冲区，避免下次开始说话时累积之前的内容
     */
    fun resetRecognitionState() {
        try {
            // 发送重置消息 - 使用更强的重置命令
            val resetMessage = JSONObject().apply {
                put("type", "resetRecognition")
                // 添加single_utterance参数，确保后端知道需要重置并启用单句模式
                put("singleUtterance", true)
                // 添加强制重置标志，确保后端完全清除之前的识别状态
                put("forceReset", true)
                // 添加完全重置标志
                put("fullReset", true)
                // 添加清除历史标志
                put("clearHistory", true)
                // 添加重置时间戳
                put("resetTimestamp", System.currentTimeMillis())
            }.toString()
            webSocket?.send(resetMessage)
            Log.d(TAG, "已发送增强版重置识别状态请求: $resetMessage")

            // 发送清除缓冲区消息
            val clearMessage = JSONObject().apply {
                put("type", "clearBuffer")
                put("clearAll", true)
                put("immediate", true)
            }.toString()
            webSocket?.send(clearMessage)
            Log.d(TAG, "已发送清除缓冲区消息: $clearMessage")

            // 再次发送增强配置消息，确保所有参数生效
            val configMessage = JSONObject().apply {
                put("type", "config")
                put("singleUtterance", true)
                put("interimResults", true)
                // 添加强制重置标志，确保后端完全清除之前的识别状态
                put("forceReset", true)
                // 高级配置
                put("enableAutomaticPunctuation", true)
                put("useEnhanced", true)
                put("maxAlternatives", 1)
                // 添加音频质量参数
                put("audioQuality", "high")
                // 添加重置后的配置标志
                put("afterReset", true)
                // 添加时间戳确保消息不被缓存
                put("timestamp", System.currentTimeMillis())
            }.toString()
            webSocket?.send(configMessage)
            Log.d(TAG, "重置后发送增强配置消息: $configMessage")

            // 发送特殊的刷新命令
            val flushMessage = JSONObject().apply {
                put("type", "flush")
                put("flushAll", true)
            }.toString()
            webSocket?.send(flushMessage)
            Log.d(TAG, "已发送刷新命令: $flushMessage")

            // 短暂延迟，确保所有命令都被处理
            Thread.sleep(50)
        } catch (e: Exception) {
            Log.e(TAG, "重置识别状态失败", e)

            // 如果重置失败，尝试重新连接WebSocket
            try {
                Log.d(TAG, "重置失败，尝试重新连接WebSocket")
                speechRecognitionListener?.let { listener ->
                    // 关闭当前连接
                    webSocket?.close(1000, "重置失败，重新连接")
                    webSocket = null

                    // 短暂延迟后重新连接
                    Thread.sleep(100)

                    // 获取当前语言
                    val languageCode = listener.lastLanguageCode ?: "zh-CN"

                    // 重新启动识别
                    startRealTimeSpeechRecognition(
                        languageCode = languageCode,
                        model = "default",
                        listener = listener
                    )
                }
            } catch (reconnectError: Exception) {
                Log.e(TAG, "重新连接WebSocket失败", reconnectError)
            }
        }
    }

    /**
     * 语音识别监听器接口
     */
    interface SpeechRecognitionListener {
        // 连接已建立
        fun onConnectionEstablished()

        // 识别结果回调
        fun onRecognitionResult(text: String, isFinal: Boolean)

        // 错误回调
        fun onError(errorMessage: String)

        // 连接关闭回调
        fun onConnectionClosed()

        // 最后使用的语言代码，用于重连时继承
        val lastLanguageCode: String?
            get() = "zh-CN" // 默认提供中文
    }
}