package com.example.llya.utils

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper

/**
 * Context扩展函数
 */

/**
 * 从Context中查找关联的Activity
 * 
 * @return 找到的Activity，如果没有找到则返回null
 */
fun Context.findActivity(): Activity? {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) {
            return context
        }
        context = context.baseContext
    }
    return null
} 