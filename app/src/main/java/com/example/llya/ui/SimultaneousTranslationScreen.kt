package com.example.llya.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.llya.viewmodel.SpeechRecognitionViewModel
import com.example.llya.R
import androidx.compose.ui.res.painterResource
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.filled.SpeakerPhone
import androidx.compose.material.icons.filled.TextFields
import androidx.compose.ui.text.style.TextOverflow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.animation.core.*
import androidx.compose.ui.draw.alpha
import androidx.compose.foundation.layout.width
import androidx.compose.ui.draw.scale
import androidx.compose.ui.res.stringResource

data class TranslationMessage(
    val content: String,
    val isSourceLanguage: Boolean,
    val timestamp: String = SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimultaneousTranslationScreen(
    navController: NavController? = null
) {
    val darkBgColor = Color(0xFF1E1D2B)
    val purpleColor = Color(0xFF8364FD)
    val speechViewModel: SpeechRecognitionViewModel = viewModel()
    val googleCloudViewModel = viewModel<com.example.llya.viewmodel.GoogleCloudServiceViewModel>()
    val isRecording by speechViewModel.isRecording.collectAsState()
    val recognitionResult by speechViewModel.recognitionResult.collectAsState()
    val aiResponse by speechViewModel.aiResponse.collectAsState()
    val currentAsrServiceName by speechViewModel.currentAsrServiceName.collectAsState()

    // 在页面销毁时关闭同声传译模式
    DisposableEffect(Unit) {
        onDispose {
            googleCloudViewModel.setSimultaneousTranslationMode(false)
        }
    }

    // 获取当前选择的语言代码和目标语言代码
    val selectedSourceLanguage by speechViewModel.selectedLanguage.collectAsState()
    val selectedTargetLanguage by speechViewModel.targetLanguage.collectAsState()

    // 用于实时显示的临时文字状态
    var currentSourceText by remember { mutableStateOf("") }
    var currentTargetText by remember { mutableStateOf("") }

    // 确保翻译功能开启并设置为同声传译模式
    LaunchedEffect(Unit) {
        // 使用已经在Composable函数顶部声明的googleCloudViewModel
        googleCloudViewModel.setSimultaneousTranslationMode(true)

        speechViewModel.toggleTranslation(true)
        // 默认设置源语言为中文，目标语言为英文
        if (selectedSourceLanguage != "zh-CN") {
            speechViewModel.setLanguage("zh-CN")
        }
        if (selectedTargetLanguage != "en-US") {
            speechViewModel.setTargetLanguage("en-US")
        }
    }

    // 监听识别结果变化
    LaunchedEffect(recognitionResult) {
        if (recognitionResult.isNotEmpty() && !recognitionResult.contains("失败") && !recognitionResult.contains("null")) {
            currentSourceText = recognitionResult

            // 当有新的识别结果时，自动发送给AI进行翻译
            if (recognitionResult.trim().length > 3) {
                speechViewModel.sendMessageToAI("请将以下文本翻译成英文: $recognitionResult")
            }
        }
    }

    // 监听AI回复（作为翻译结果）变化
    LaunchedEffect(aiResponse) {
        if (aiResponse.isNotEmpty() && !aiResponse.contains("失败")) {
            currentTargetText = aiResponse
        }
    }

    // 示例消息列表
    var messages by remember { mutableStateOf(listOf(
        TranslationMessage("Hello Hello.", false),
        TranslationMessage("Hello Hello.", false),
        TranslationMessage("你好。", true),
        TranslationMessage("Hello.", false),
        TranslationMessage("你在哪里?", true),
        TranslationMessage("Where are you?", false)
    )) }

    val coroutineScope = rememberCoroutineScope()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(darkBgColor)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    text = stringResource(R.string.simultaneous_translation_title),
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController?.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = stringResource(R.string.back),
                        tint = Color.White
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = {
                        // 清空消息
                        messages = emptyList()
                        // 清空AI历史记录
                        speechViewModel.clearAIHistory()
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = stringResource(R.string.translation_clear_messages),
                        tint = Color.Red
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = darkBgColor,
                titleContentColor = Color.White
            )
        )

        // 在工具栏下面添加ASR服务提供商信息
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(darkBgColor)
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "当前语音识别服务: $currentAsrServiceName",
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium
            )
        }

        // 语言选择面板
        LanguageSelectionPanel(
            sourceLanguage = speechViewModel.availableLanguages.find { it.first == selectedSourceLanguage }?.second ?: "中文普通话",
            targetLanguage = speechViewModel.availableLanguages.find { it.first == selectedTargetLanguage }?.second ?: "英语",
            onSwitchLanguages = {
                // 交换源语言和目标语言
                val tempSourceLang = selectedSourceLanguage
                speechViewModel.setLanguage(selectedTargetLanguage)
                speechViewModel.setTargetLanguage(tempSourceLang)
            },
            availableLanguages = speechViewModel.availableLanguages,
            onSourceLanguageSelected = { langCode ->
                speechViewModel.setLanguage(langCode)
            },
            onTargetLanguageSelected = { langCode ->
                speechViewModel.setTargetLanguage(langCode)
            }
        )

        // 今天日期
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "${stringResource(R.string.translation_today)} ${SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())}",
                color = Color.Gray,
                fontSize = 14.sp
            )
        }

        // 消息列表
        val listState = rememberLazyListState()
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(messages) { message ->
                TranslationMessageItem(message = message)
            }

            // 当正在录音时，显示实时识别的文字
            if (isRecording && currentSourceText.isNotEmpty()) {
                item {
                    // 源语言实时显示
                    TranslationMessageItem(
                        message = TranslationMessage(currentSourceText, true),
                        isTyping = true
                    )
                }

                // 如果有翻译结果，实时显示
                if (currentTargetText.isNotEmpty()) {
                    item {
                        TranslationMessageItem(
                            message = TranslationMessage(currentTargetText, false),
                            isTyping = true
                        )
                    }
                }
            }
        }

        // 自动滚动到底部 - 考虑实时输入
        LaunchedEffect(messages.size, currentSourceText, currentTargetText) {
            if (isRecording || messages.isNotEmpty()) {
                listState.animateScrollToItem(listState.layoutInfo.totalItemsCount - 1)
            }
        }

        // 底部控制栏
        BottomControlBar(
            isRecording = isRecording,
            onMicClick = {
                if (isRecording) {
                    speechViewModel.stopRecognition(saveToHistory = false)

                    // 确保清除谷歌云服务的缓存内容
                    googleCloudViewModel.clearRecognizedText()

                    // 保存当前识别和翻译结果到消息列表
                    if (currentSourceText.isNotEmpty()) {
                        coroutineScope.launch {
                            messages = messages + TranslationMessage(currentSourceText, true)
                            if (currentTargetText.isNotEmpty()) {
                                messages = messages + TranslationMessage(currentTargetText, false)
                            }
                            // 清空临时显示的文字
                            currentSourceText = ""
                            currentTargetText = ""
                            // 清空AI历史记录，确保下次翻译不受影响
                            speechViewModel.clearAIHistory()
                        }
                    }
                } else {
                    // 开始新的录音前确保使用当前选择的语言
                    speechViewModel.setLanguage(selectedSourceLanguage)

                    // 开始新的录音
                    currentSourceText = ""
                    currentTargetText = ""
                    speechViewModel.startRecognition()
                }
            }
        )
    }
}

@Composable
fun LanguageSelectionPanel(
    sourceLanguage: String,
    targetLanguage: String,
    onSwitchLanguages: () -> Unit,
    availableLanguages: List<Pair<String, String>>,
    onSourceLanguageSelected: (String) -> Unit,
    onTargetLanguageSelected: (String) -> Unit
) {
    // 语言菜单展开状态
    var showSourceLanguageMenu by remember { mutableStateOf(false) }
    var showTargetLanguageMenu by remember { mutableStateOf(false) }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        color = Color(0xFF33324A)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 源语言
            Column(
                modifier = Modifier
                    .weight(1f)
                    .clickable { showSourceLanguageMenu = true },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = sourceLanguage,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "源语言",
                    color = Color.Gray,
                    fontSize = 12.sp
                )

                // 源语言下拉菜单
                DropdownMenu(
                    expanded = showSourceLanguageMenu,
                    onDismissRequest = { showSourceLanguageMenu = false },
                    modifier = Modifier
                        .background(Color(0xFF33324A))
                        .heightIn(max = 300.dp)
                ) {
                    availableLanguages.forEach { (langCode, langName) ->
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = langName,
                                    color = Color.White
                                )
                            },
                            onClick = {
                                onSourceLanguageSelected(langCode)
                                showSourceLanguageMenu = false
                            },
                            colors = MenuDefaults.itemColors(
                                textColor = Color.White
                            )
                        )
                    }
                }
            }

            // 交换按钮
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF8364FD))
                    .clickable(onClick = onSwitchLanguages),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_swap),
                    contentDescription = "交换语言",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            // 目标语言
            Column(
                modifier = Modifier
                    .weight(1f)
                    .clickable { showTargetLanguageMenu = true },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = targetLanguage,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "目标语言",
                    color = Color.Gray,
                    fontSize = 12.sp
                )

                // 目标语言下拉菜单
                DropdownMenu(
                    expanded = showTargetLanguageMenu,
                    onDismissRequest = { showTargetLanguageMenu = false },
                    modifier = Modifier
                        .background(Color(0xFF33324A))
                        .heightIn(max = 300.dp)
                ) {
                    availableLanguages.forEach { (langCode, langName) ->
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = langName,
                                    color = Color.White
                                )
                            },
                            onClick = {
                                onTargetLanguageSelected(langCode)
                                showTargetLanguageMenu = false
                            },
                            colors = MenuDefaults.itemColors(
                                textColor = Color.White
                            )
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TranslationMessageItem(
    message: TranslationMessage,
    isTyping: Boolean = false
) {
    // 添加打字动画效果
    val infiniteTransition = rememberInfiniteTransition(label = "typingDots")
    val dotsAlpha by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(500, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "dotAlpha"
    )

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .background(
                when {
                    // 正在输入的源语言用浅灰色
                    isTyping && message.isSourceLanguage -> Color(0xFF33324A).copy(alpha = 0.7f)
                    // 正在输入的目标语言用浅紫色
                    isTyping && !message.isSourceLanguage -> Color(0xFF8364FD).copy(alpha = 0.5f)
                    // 正常的源语言用深灰色
                    message.isSourceLanguage -> Color(0xFF33324A)
                    // 正常的目标语言用紫色
                    else -> Color(0xFF8364FD).copy(alpha = 0.7f)
                }
            )
            .padding(16.dp)
    ) {
        Column {
            Text(
                text = message.content,
                color = Color.White,
                fontSize = 16.sp
            )

            // 如果是实时输入，显示输入指示器
            if (isTyping) {
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = if (message.isSourceLanguage) "正在听" else "正在翻译",
                        color = Color.White.copy(alpha = 0.6f),
                        fontSize = 12.sp,
                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                    )

                    // 添加动态省略号
                    Text(
                        text = "...",
                        color = Color.White.copy(alpha = dotsAlpha),
                        fontSize = 12.sp,
                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                        modifier = Modifier.alpha(dotsAlpha)
                    )
                }
            }
        }
    }
}

@Composable
fun BottomControlBar(
    isRecording: Boolean,
    onMicClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color(0xFF33324A)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 录音状态提示
            if (isRecording) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.Red.copy(alpha = 0.1f))
                        .padding(vertical = 4.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "正在录音...",
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧按钮
                IconButton(
                    onClick = { /* 播放功能 */ },
                    modifier = Modifier
                        .size(50.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF33324A))
                ) {
                    Icon(
                        imageVector = Icons.Default.SpeakerPhone,
                        contentDescription = "播放",
                        tint = Color.White
                    )
                }

                // 语音输入按钮
                Box(
                    modifier = Modifier
                        .size(70.dp)
                        .clip(CircleShape)
                        .background(if (isRecording) Color.Red else Color(0xFF8364FD))
                        .clickable(onClick = onMicClick),
                    contentAlignment = Alignment.Center
                ) {
                    // 录音中的波纹动画
                    if (isRecording) {
                        val animatedSize = remember { Animatable(1f) }

                        LaunchedEffect(Unit) {
                            animatedSize.animateTo(
                                targetValue = 1.2f,
                                animationSpec = infiniteRepeatable(
                                    animation = tween(800, easing = FastOutSlowInEasing),
                                    repeatMode = RepeatMode.Reverse
                                )
                            )
                        }

                        Box(
                            modifier = Modifier
                                .size(70.dp)
                                .scale(animatedSize.value)
                                .clip(CircleShape)
                                .background(Color.Red.copy(alpha = 0.3f))
                        )
                    }

                    Icon(
                        imageVector = Icons.Default.Mic,
                        contentDescription = if (isRecording) "停止录音" else "开始录音",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }

                // 右侧按钮
                IconButton(
                    onClick = { /* 文本功能 */ },
                    modifier = Modifier
                        .size(50.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF33324A))
                ) {
                    Icon(
                        imageVector = Icons.Default.TextFields,
                        contentDescription = "文本",
                        tint = Color.White
                    )
                }
            }
        }
    }
}