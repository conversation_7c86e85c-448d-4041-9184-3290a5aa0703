package com.example.llya.ui

import android.Manifest
import android.util.Log
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.llya.R
import com.example.llya.aliyunasi.AliyunAsrViewModel
import com.example.llya.viewmodel.AliyunTranslationViewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun AliyunTranslationScreen(
    onNavigateBack: () -> Unit,
    accessKeyId: String,
    accessKeySecret: String,
    appKey: String = "", // 如果调用方没有提供appKey参数，会使用空字符串作为默认值
    viewModel: AliyunTranslationViewModel = viewModel(
        factory = AliyunTranslationViewModel.Factory(accessKeyId, accessKeySecret)
    )
) {
    val context = LocalContext.current
    
    // 创建语音识别ViewModel
    val asrViewModel: AliyunAsrViewModel = viewModel(
        factory = AliyunAsrViewModel.Factory(
            context = context,
            accessKeyId = accessKeyId,
            accessKeySecret = accessKeySecret,
            appKey = appKey
        )
    )
    
    val sourceLanguage by viewModel.sourceLanguage.collectAsState()
    val targetLanguage by viewModel.targetLanguage.collectAsState()
    val sourceText by viewModel.sourceText.collectAsState()
    val translationResult by viewModel.translationResult.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val isTranslating by viewModel.isTranslating.collectAsState()
    
    // 语音识别相关状态
    val recognitionResult by asrViewModel.recognitionResult.collectAsState()
    val isRecording by asrViewModel.isRecording.collectAsState()
    val asrConnectionStatus by asrViewModel.connectionStatus.collectAsState()
    val asrErrorMessage by asrViewModel.errorMessage.collectAsState()
    
    // 检查麦克风权限
    val micPermissionState = rememberPermissionState(Manifest.permission.RECORD_AUDIO)
    
    var sourceLanguageMenuExpanded by remember { mutableStateOf(false) }
    var targetLanguageMenuExpanded by remember { mutableStateOf(false) }
    
    val focusManager = LocalFocusManager.current
    
    // 监听语音识别结果变化，自动更新输入框内容
    LaunchedEffect(recognitionResult) {
        if (recognitionResult.isNotEmpty()) {
            viewModel.setSourceText(recognitionResult)
        }
    }
    
    // 监听源语言变化，自动更新语音识别语言
    LaunchedEffect(sourceLanguage) {
        // 如果当前正在录音，先停止再重新开始，确保语言设置生效
        val wasRecording = isRecording
        
        if (wasRecording) {
            // 停止当前录音，让语音识别服务重新初始化
            asrViewModel.stopRecognition()
            // 稍微延迟确保连接完全关闭
            delay(500)
        }
        
        // 设置新的语言
        Log.d("AliyunTranslationScreen", "源语言变更为: $sourceLanguage，语言名称: ${viewModel.availableLanguages[sourceLanguage]}")
        asrViewModel.setLanguage(sourceLanguage)
        
        // 如果原来正在录音，则使用新语言重新开始录音
        if (wasRecording) {
            delay(500) // 再等待一下确保设置完成
            Log.d("AliyunTranslationScreen", "语言已变更，使用新语言 $sourceLanguage 重新开始录音")
            asrViewModel.startRecognition()
        }
    }
    
    // 自动翻译功能 - 添加防抖动处理
    var latestText by remember { mutableStateOf("") }
    LaunchedEffect(sourceText) {
        if (sourceText.isEmpty()) {
            // 如果输入为空，则清空翻译结果
            viewModel.clearResult()
            return@LaunchedEffect
        }
        
        // 更新最新的文本
        latestText = sourceText
        
        // 防抖动 - 等待用户输入完成后再翻译
        delay(800) // 等待800毫秒
        
        // 如果800毫秒后文本没有变化，则执行翻译
        if (latestText == sourceText && sourceText.isNotEmpty()) {
            viewModel.translate()
        }
    }
    
    // 显示错误消息
    LaunchedEffect(errorMessage) {
        if (errorMessage.isNotEmpty()) {
            Toast.makeText(context, errorMessage, Toast.LENGTH_LONG).show()
            viewModel.clearError()
        }
    }
    
    // 显示语音识别错误消息
    LaunchedEffect(asrErrorMessage) {
        if (asrErrorMessage.isNotEmpty()) {
            Toast.makeText(context, "语音识别错误: $asrErrorMessage", Toast.LENGTH_LONG).show()
            asrViewModel.clearError()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("阿里云翻译") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 源语言选择
            Box(modifier = Modifier.fillMaxWidth()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                        .clickable { sourceLanguageMenuExpanded = true }
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "源语言: ${viewModel.availableLanguages[sourceLanguage] ?: "未知"}",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "选择源语言"
                    )
                }
                
                DropdownMenu(
                    expanded = sourceLanguageMenuExpanded,
                    onDismissRequest = { sourceLanguageMenuExpanded = false },
                    modifier = Modifier.width(IntrinsicSize.Max)
                ) {
                    // 常用语言类别标题
                    Text(
                        text = "—— 常用语言 ——",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                    
                    // 常用语言列表（前20种语言）
                    val commonLanguages = arrayOf("auto", "zh", "zh-tw", "en", "ja", "ko", "fr", "es", "it", "de", "tr", "ru", "pt", "vi", "id", "th", "ms", "ar", "hi")
                    commonLanguages.forEach { code ->
                        viewModel.availableLanguages[code]?.let { name ->
                            DropdownMenuItem(
                                text = { Text(name) },
                                onClick = {
                                    viewModel.setSourceLanguage(code)
                                    sourceLanguageMenuExpanded = false
                                }
                            )
                        }
                    }
                    
                    Divider(modifier = Modifier.padding(vertical = 4.dp))
                    
                    // 所有语言类别标题
                    Text(
                        text = "—— 所有语言（按首字母排序）——",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                    
                    // 按首字母分组的语言列表
                    val sortedLanguages = viewModel.availableLanguages.entries
                        .sortedBy { it.value }
                        .filterNot { commonLanguages.contains(it.key) }
                        
                    var currentInitial = ""
                    sortedLanguages.forEach { (code, name) ->
                        val initial = name.first().toString()
                        if (initial != currentInitial) {
                            currentInitial = initial
                            Text(
                                text = "- $currentInitial -",
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(start = 16.dp, top = 8.dp, bottom = 2.dp)
                            )
                        }
                        DropdownMenuItem(
                            text = { Text(name) },
                            onClick = {
                                viewModel.setSourceLanguage(code)
                                sourceLanguageMenuExpanded = false
                            }
                        )
                    }
                }
            }
            
            // 输入框和语音输入按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Bottom
            ) {
                // 输入框
                TextField(
                    value = sourceText,
                    onValueChange = { viewModel.setSourceText(it) },
                    modifier = Modifier
                        .weight(1f)
                        .height(120.dp),
                    label = { Text("请输入要翻译的文本") },
                    placeholder = { Text("在此输入或使用语音...") },
                    keyboardOptions = KeyboardOptions.Default.copy(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            if (sourceText.isNotEmpty()) {
                                viewModel.translate()
                            }
                        }
                    ),
                    maxLines = 5
                )
                
                // 语音输入按钮
                IconButton(
                    onClick = {
                        if (!micPermissionState.status.isGranted) {
                            micPermissionState.launchPermissionRequest()
                        } else {
                            // 无论当前是否在录音，都先停止当前识别
                            if (isRecording) {
                                asrViewModel.stopRecognition()
                            }
                            
                            // 如果原来在录音，现在停止；如果原来没在录音，现在开始
                            if (!isRecording) {
                                // 先设置语言再开始识别
                                asrViewModel.setLanguage(sourceLanguage)
                                Log.d("AliyunTranslationScreen", "开始语音识别，设置语言为: $sourceLanguage")
                                // 清空之前的识别结果后开始新识别
                                asrViewModel.startRecognition()
                            }
                        }
                    },
                    modifier = Modifier
                        .padding(start = 8.dp)
                        .size(48.dp)
                        .background(
                            color = if (isRecording) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary,
                            shape = RoundedCornerShape(50)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Call,
                        contentDescription = if (isRecording) "停止语音输入" else "开始语音输入",
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
            
            // 显示录音状态
            AnimatedVisibility(visible = isRecording) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    LinearProgressIndicator(
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 8.dp)
                    )
                    Column {
                        Text(
                            text = "正在识别${viewModel.availableLanguages[sourceLanguage] ?: sourceLanguage}...",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                        // 显示语言支持提示（当源语言不是中文时）
                        if (sourceLanguage != "zh") {
                            Text(
                                text = "注意：目前实际只支持中文识别",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.error,
                                fontSize = MaterialTheme.typography.bodySmall.fontSize.times(0.8f)
                            )
                        }
                    }
                }
            }
            
            // 删除翻译按钮，改为自动翻译提示
            if (isTranslating) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "正在翻译...",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            // 目标语言选择
            Box(modifier = Modifier.fillMaxWidth()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                        .clickable { targetLanguageMenuExpanded = true }
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "目标语言: ${viewModel.availableLanguages[targetLanguage] ?: "未知"}",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "选择目标语言"
                    )
                }
                
                DropdownMenu(
                    expanded = targetLanguageMenuExpanded,
                    onDismissRequest = { targetLanguageMenuExpanded = false },
                    modifier = Modifier.width(IntrinsicSize.Max)
                ) {
                    // 常用语言类别标题
                    Text(
                        text = "—— 常用语言 ——",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                    
                    // 常用语言列表（排除源语言和自动检测选项）
                    val commonLanguages = arrayOf("zh", "zh-tw", "en", "ja", "ko", "fr", "es", "it", "de", "tr", "ru", "pt", "vi", "id", "th", "ms", "ar", "hi")
                    commonLanguages.forEach { code ->
                        // 不显示与源语言相同的选项和自动检测选项
                        if (code != sourceLanguage && code != "auto") {
                            viewModel.availableLanguages[code]?.let { name ->
                                DropdownMenuItem(
                                    text = { Text(name) },
                                    onClick = {
                                        viewModel.setTargetLanguage(code)
                                        targetLanguageMenuExpanded = false
                                    }
                                )
                            }
                        }
                    }
                    
                    Divider(modifier = Modifier.padding(vertical = 4.dp))
                    
                    // 所有语言类别标题
                    Text(
                        text = "—— 所有语言（按首字母排序）——",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                    
                    // 按首字母分组的语言列表（排除源语言和自动检测选项）
                    val sortedLanguages = viewModel.availableLanguages.entries
                        .sortedBy { it.value }
                        .filterNot { commonLanguages.contains(it.key) || it.key == sourceLanguage || it.key == "auto" }
                        
                    var currentInitial = ""
                    sortedLanguages.forEach { (code, name) ->
                        val initial = name.first().toString()
                        if (initial != currentInitial) {
                            currentInitial = initial
                            Text(
                                text = "- $currentInitial -",
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(start = 16.dp, top = 8.dp, bottom = 2.dp)
                            )
                        }
                        DropdownMenuItem(
                            text = { Text(name) },
                            onClick = {
                                viewModel.setTargetLanguage(code)
                                targetLanguageMenuExpanded = false
                            }
                        )
                    }
                }
            }
            
            // 翻译结果
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clip(RoundedCornerShape(16.dp))
                    .background(MaterialTheme.colorScheme.secondaryContainer)
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    Text(
                        text = "翻译结果:",
                        style = MaterialTheme.typography.labelLarge,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 加载指示器
                    if (isTranslating) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    } else {
                        Text(
                            text = translationResult.ifEmpty { "等待翻译..." },
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Start,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
            
            // 底部按钮区域
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 显示录音状态
                AnimatedVisibility(visible = isRecording) {
                    Card(
                        modifier = Modifier.padding(horizontal = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.tertiaryContainer
                        )
                    ) {
                        Text(
                            text = "语音识别中：${asrConnectionStatus}",
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                // 语言交换按钮
                IconButton(
                    onClick = {
                        // 交换源语言和目标语言
                        val temp = sourceLanguage
                        viewModel.setSourceLanguage(targetLanguage)
                        viewModel.setTargetLanguage(temp)
                    },
                    modifier = Modifier.align(Alignment.CenterVertically)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_swap_languages),
                        contentDescription = "交换语言",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
} 