package com.example.llya.utils

import android.Manifest
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 蓝牙权限助手类
 * 提供蓝牙权限检查和请求的功能
 */
object BluetoothPermissionHelper {
    private const val TAG = "BluetoothPermissionHelper"
    
    // 所需的蓝牙权限列表
    val requiredPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        arrayOf(
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_SCAN
        )
    } else {
        arrayOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    }
    
    /**
     * 检查是否已授予所需的蓝牙权限
     */
    fun hasRequiredPermissions(context: Context): Boolean {
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取未授予的权限列表
     */
    fun getNotGrantedPermissions(context: Context): Array<String> {
        return requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }.toTypedArray()
    }
    
    /**
     * 检查蓝牙是否开启
     */
    fun isBluetoothEnabled(context: Context): Boolean {
        val bluetoothAdapter = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            context.getSystemService(BluetoothAdapter::class.java)
        } else {
            val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as? android.bluetooth.BluetoothManager
            bluetoothManager?.adapter
        }
        
        return bluetoothAdapter?.isEnabled == true
    }
    
    /**
     * 打开蓝牙设置页面
     */
    fun openBluetoothSettings(context: Context) {
        val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
        context.startActivity(intent)
    }
    
    /**
     * 打开应用权限设置页面
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = android.net.Uri.fromParts("package", context.packageName, null)
        intent.data = uri
        context.startActivity(intent)
    }
    
    /**
     * 权限请求Composable
     * 在Compose中方便地请求和处理蓝牙权限
     */
    @Composable
    fun RequestBluetoothPermissions(
        onPermissionsResult: (Boolean) -> Unit
    ) {
        var permissionsRequested by remember { mutableStateOf(false) }
        
        val context = androidx.compose.ui.platform.LocalContext.current
        
        // 权限请求启动器
        val permissionLauncher = rememberLauncherForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissionsMap ->
            val allGranted = permissionsMap.values.all { it }
            Log.d(TAG, "权限请求结果: $permissionsMap, 全部授予: $allGranted")
            onPermissionsResult(allGranted)
        }
        
        // 启动权限请求
        LaunchedEffect(Unit) {
            if (!permissionsRequested) {
                val missingPermissions = getNotGrantedPermissions(context)
                if (missingPermissions.isNotEmpty()) {
                    Log.d(TAG, "请求权限: ${missingPermissions.joinToString()}")
                    permissionLauncher.launch(missingPermissions)
                    permissionsRequested = true
                } else {
                    Log.d(TAG, "已拥有所有必要权限")
                    onPermissionsResult(true)
                }
            }
        }
    }
} 