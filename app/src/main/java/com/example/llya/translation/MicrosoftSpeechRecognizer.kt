package com.example.llya.translation

import android.content.Context
import android.util.Log
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.microsoft.cognitiveservices.speech.translation.SpeechTranslationConfig
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionEventArgs
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognitionCanceledEventArgs
import com.microsoft.cognitiveservices.speech.translation.TranslationRecognizer
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.Semaphore

/**
 * 微软语音识别器
 * 使用官方Speech SDK实现语音识别和翻译
 * 基于参考代码重构，增强多语言连续识别功能
 */
class MicrosoftSpeechRecognizer(
    private val context: Context,
    config: MicrosoftSpeechConfig
) {
    companion object {
        private const val TAG = "MicrosoftSpeechRecognizer"
    }

    // 配置
    private var subscriptionKey = config.subscriptionKey
    private var region = config.region
    private var translationKey = config.translationKey
    private var translationRegion = config.translationRegion

    // 语音识别器
    private var speechRecognizer: SpeechRecognizer? = null
    private var translationRecognizer: TranslationRecognizer? = null
    
    // 自动语言检测配置
    private var autoDetectConfig: AutoDetectSourceLanguageConfig? = null

    // 当前检测到的语言
    private val _detectedLanguage = MutableStateFlow<String?>(null)
    val detectedLanguage: StateFlow<String?> = _detectedLanguage

    // 实时识别结果
    private val _recognitionResult = MutableStateFlow<String>("")
    val recognitionResult: StateFlow<String> = _recognitionResult

    // 翻译结果
    private val _translationResult = MutableStateFlow<String>("")
    val translationResult: StateFlow<String> = _translationResult

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage

    // 识别状态
    private val _isRecognizing = MutableStateFlow(false)
    val isRecognizing: StateFlow<Boolean> = _isRecognizing

    // 用于等待识别结果的信号量
    private val stopRecognitionSemaphore = Semaphore(0)
    
    // 双向翻译模式标志
    private var enableBidirectionalTranslation = false
    
    // 当前翻译配置
    private var currentSourceLanguages: List<String> = emptyList()
    private var currentTargetLanguage: String = ""

    /**
     * 更新配置
     */
    fun updateConfig(config: MicrosoftSpeechConfig) {
        this.subscriptionKey = config.subscriptionKey
        this.region = config.region
        this.translationKey = config.translationKey
        this.translationRegion = config.translationRegion
    }

    /**
     * 开始语音识别
     * @param sourceLanguage 源语言代码，如zh-CN
     */
    fun startRecognition(sourceLanguage: String) {
        if (_isRecognizing.value) {
            Log.d(TAG, "已经在识别中，忽略请求")
            return
        }

        try {
            // 清除之前的识别器
            speechRecognizer?.close()
            speechRecognizer = null

            // 创建语音配置
            val speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region)
            
            // 确保语言代码格式正确 (BCP-47格式)
            val formattedLanguage = formatLanguageCode(sourceLanguage)
            Log.d(TAG, "使用格式化的语言代码: $formattedLanguage")
            
            speechConfig.setSpeechRecognitionLanguage(formattedLanguage)
            
            // 添加详细输出格式设置
            speechConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText")
            speechConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Raw")
            speechConfig.setProperty("SpeechServiceResponse_OutputFormatOption", "Detailed")

            // 创建音频配置（使用默认麦克风）
            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()

            // 创建语音识别器
            speechRecognizer = SpeechRecognizer(speechConfig, audioConfig)

            // 注册识别事件处理程序
            speechRecognizer?.recognizing?.addEventListener { _, e ->
                val result = e.result.text
                Log.d(TAG, "识别中: $result")
                _recognitionResult.value = result
            }

            speechRecognizer?.recognized?.addEventListener { _, e ->
                if (e.result.reason == ResultReason.RecognizedSpeech) {
                    val result = e.result.text
                    Log.d(TAG, "已识别: $result")
                    _recognitionResult.value = result
                } else if (e.result.reason == ResultReason.NoMatch) {
                    Log.d(TAG, "无法识别语音")
                    _errorMessage.value = "无法识别语音"
                }
            }

            speechRecognizer?.canceled?.addEventListener { _, e ->
                val reason = e.reason
                val errorDetails = e.errorDetails
                Log.d(TAG, "识别取消: $reason, 错误: $errorDetails")
                if (reason == CancellationReason.Error) {
                    _errorMessage.value = "识别错误: $errorDetails"
                }
                _isRecognizing.value = false
                stopRecognitionSemaphore.release()
            }

            speechRecognizer?.sessionStopped?.addEventListener { _, _ ->
                Log.d(TAG, "识别会话停止")
                _isRecognizing.value = false
                stopRecognitionSemaphore.release()
            }

            // 开始连续识别
            speechRecognizer?.startContinuousRecognitionAsync()
            _isRecognizing.value = true
            Log.d(TAG, "开始连续识别")

        } catch (ex: Exception) {
            Log.e(TAG, "开始识别时出错", ex)
            _errorMessage.value = "开始识别时出错: ${ex.message}"
            _isRecognizing.value = false
        }
    }

    /**
     * 开始翻译识别
     * @param sourceLanguage 源语言代码，如zh-CN
     * @param targetLanguage 目标语言代码，如en-US
     */
    fun startTranslation(sourceLanguage: String, targetLanguage: String) {
        if (_isRecognizing.value) {
            Log.d(TAG, "已经在识别中，忽略请求")
            return
        }

        try {
            // 清除之前的识别器
            translationRecognizer?.close()
            translationRecognizer = null

            // 创建翻译配置 - 使用专门的翻译密钥和区域
            val translationConfig = SpeechTranslationConfig.fromSubscription(
                subscriptionKey, 
                region
            )
            
            // 确保语言代码格式正确 (BCP-47格式)
            val formattedSourceLanguage = formatLanguageCode(sourceLanguage)
            val formattedTargetLanguage = formatLanguageCode(targetLanguage)
            
            Log.d(TAG, "使用格式化的语言代码 - 源语言: $formattedSourceLanguage, 目标语言: $formattedTargetLanguage")
            
            // 设置源语言和目标语言
            translationConfig.speechRecognitionLanguage = formattedSourceLanguage
            translationConfig.addTargetLanguage(getTranslationLanguageCode(formattedTargetLanguage))
            
            // 添加详细输出格式设置
            translationConfig.setProperty("SpeechServiceResponse_PostProcessingOption", "TrueText")
            translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Raw")
            translationConfig.setProperty("SpeechServiceResponse_OutputFormatOption", "Detailed")

            // 创建音频配置（使用默认麦克风）
            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()

            // 创建翻译识别器
            translationRecognizer = TranslationRecognizer(translationConfig, audioConfig)

            // 注册识别事件处理程序
            translationRecognizer?.recognizing?.addEventListener { _, e ->
                val result = e.result.text
                Log.d(TAG, "源语言识别中: $result")
                _recognitionResult.value = result

                // 获取翻译结果
                val targetLangCode = getTranslationLanguageCode(formattedTargetLanguage)
                if (e.result.translations.containsKey(targetLangCode)) {
                    val translation = e.result.translations[targetLangCode] ?: ""
                    Log.d(TAG, "目标语言翻译中: $translation")
                    _translationResult.value = translation
                }
            }

            translationRecognizer?.recognized?.addEventListener { _, e ->
                if (e.result.reason == ResultReason.TranslatedSpeech) {
                    val result = e.result.text
                    Log.d(TAG, "源语言已识别: $result")
                    _recognitionResult.value = result

                    // 获取翻译结果
                    val targetLangCode = getTranslationLanguageCode(formattedTargetLanguage)
                    if (e.result.translations.containsKey(targetLangCode)) {
                        val translation = e.result.translations[targetLangCode] ?: ""
                        Log.d(TAG, "目标语言已翻译: $translation")
                        _translationResult.value = translation
                    }
                } else if (e.result.reason == ResultReason.NoMatch) {
                    Log.d(TAG, "无法识别语音")
                    _errorMessage.value = "无法识别语音"
                }
            }

            translationRecognizer?.canceled?.addEventListener { _, e ->
                val reason = e.reason
                val errorDetails = e.errorDetails
                Log.d(TAG, "翻译取消: $reason, 错误: $errorDetails")
                if (reason == CancellationReason.Error) {
                    _errorMessage.value = "翻译错误: $errorDetails"
                }
                _isRecognizing.value = false
                stopRecognitionSemaphore.release()
            }

            translationRecognizer?.sessionStopped?.addEventListener { _, _ ->
                Log.d(TAG, "翻译会话停止")
                _isRecognizing.value = false
                stopRecognitionSemaphore.release()
            }

            // 开始连续翻译
            translationRecognizer?.startContinuousRecognitionAsync()
            _isRecognizing.value = true
            Log.d(TAG, "开始连续翻译")

        } catch (ex: Exception) {
            Log.e(TAG, "开始翻译时出错", ex)
            _errorMessage.value = "开始翻译时出错: ${ex.message}"
            _isRecognizing.value = false
        }
    }

    /**
     * 开始多语言自动检测和翻译（单向）
     * @param targetLanguage 目标语言代码，如en-US
     * @param sensitivityThreshold 敏感度阈值，0.1-1.0之间，值越小越敏感
     */
    fun startAutoDetectTranslation(
        targetLanguage: String,
        sensitivityThreshold: String = MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_HIGH
    ) {
        startContinuousRecognitionWithTranslation(
            sourceLanguages = "zh-CN,en-US,ja-JP,ko-KR,fr-FR,de-DE,es-ES",
            targetLanguage = targetLanguage,
            enableBidirectional = false,
            sensitivityThreshold = sensitivityThreshold
        )
    }

    /**
     * 开始多语言自动检测和互译
     * @param sourceLanguage 源语言代码，如zh-CN
     * @param targetLanguage 目标语言代码，如en-US
     * @param sensitivityThreshold 敏感度阈值，0.1-1.0之间，值越小越敏感
     * 互译功能：自动检测用户使用的是哪种语言，并翻译成另一种语言
     */
    fun startBidirectionalTranslation(
        sourceLanguage: String,
        targetLanguage: String,
        sensitivityThreshold: String = MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_HIGH
    ) {
        val formattedSourceLanguage = formatLanguageCode(sourceLanguage)
        val formattedTargetLanguage = formatLanguageCode(targetLanguage)
        
        startContinuousRecognitionWithTranslation(
            sourceLanguages = "$formattedSourceLanguage,$formattedTargetLanguage",
            targetLanguage = targetLanguage,
            enableBidirectional = true,
            sensitivityThreshold = sensitivityThreshold
        )
    }

    /**
     * 核心方法：开始连续识别和翻译
     * 基于参考代码实现的完整多语言识别功能
     */
    private fun startContinuousRecognitionWithTranslation(
        sourceLanguages: String,
        targetLanguage: String,
        enableBidirectional: Boolean = false,
        sensitivityThreshold: String = MicrosoftSpeechConfig.LANGUAGE_DETECTION_SENSITIVITY_HIGH
    ) {
        if (_isRecognizing.value) {
            Log.d(TAG, "已经在识别中，忽略请求")
            return
        }

        try {
            // 保存当前配置
            this.enableBidirectionalTranslation = enableBidirectional
            this.currentSourceLanguages = sourceLanguages.split(",").map { it.trim() }
            this.currentTargetLanguage = targetLanguage
            
            Log.d(TAG, "开始创建翻译配置，源语言: $sourceLanguages, 目标语言: $targetLanguage, 双向翻译: $enableBidirectional")
            
            // 使用语音密钥创建翻译配置
            val translationConfig = SpeechTranslationConfig.fromSubscription(subscriptionKey, region)
            Log.d(TAG, "翻译配置创建成功，使用语音服务密钥和区域")
            
            // 设置源语言和目标语言
            val sourceLanguagesList = sourceLanguages.split(",").map { it.trim() }
            translationConfig.speechRecognitionLanguage = sourceLanguagesList.first()
            Log.d(TAG, "已设置主要识别语言: ${sourceLanguagesList.first()}")
            
            // 启用自动语言检测
            try {
                autoDetectConfig = AutoDetectSourceLanguageConfig.fromLanguages(sourceLanguagesList)
                Log.d(TAG, "已创建自动语言检测配置")
            } catch (e: Exception) {
                Log.e(TAG, "创建自动语言检测配置失败: ${e.message}")
                throw e
            }
            
            // 指定翻译目标语言
            val targetLangCode = getTranslationLanguageCode(targetLanguage)
            translationConfig.addTargetLanguage(targetLangCode)
            Log.d(TAG, "添加目标语言: $targetLangCode")
            
            // 双向翻译：同时添加源语言作为翻译目标
            if (enableBidirectional) {
                sourceLanguagesList.forEach { srcLang ->
                    val srcLangCode = getTranslationLanguageCode(srcLang)
                    if (srcLangCode != targetLangCode) {
                        translationConfig.addTargetLanguage(srcLangCode)
                        Log.d(TAG, "添加源语言作为翻译目标: $srcLangCode")
                    }
                }
            }
            
            // 启用多语言检测和连续识别
            translationConfig.setProperty(PropertyId.SpeechServiceConnection_LanguageIdMode, "Continuous")
            translationConfig.setProperty("SpeechServiceConnection_EnableLanguageDetection", "true")
            translationConfig.setProperty("SpeechServiceConnection_EnableLanguageIdentification", "true")
            translationConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdPriority", "Latency")
            translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnSuccess", "Recognize")
            translationConfig.setProperty("SpeechServiceConnection_LanguageIdOnUnknown", "Recognize")
            translationConfig.setProperty("SpeechServiceConnection_ContinuousLanguageIdThreshold", sensitivityThreshold)
            
            // 设置语言列表
            val languageListForDetection = sourceLanguagesList.joinToString(";")
            translationConfig.setProperty("SpeechServiceConnection_LanguageIdLanguages", languageListForDetection)
            Log.d(TAG, "设置语言检测列表: $languageListForDetection")
            
            // 设置后处理和静音超时
            translationConfig.setProperty(PropertyId.SpeechServiceResponse_PostProcessingOption, "TrueText")
            translationConfig.setProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "1000")
            translationConfig.setProperty("SpeechServiceResponse_ProfanityOption", "Raw")
            translationConfig.setProperty("SpeechServiceResponse_OutputFormatOption", "Detailed")
            
            Log.d(TAG, "已设置附加属性")
            
            // 创建音频配置
            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()
            Log.d(TAG, "音频配置创建成功")
            
            // 创建识别器 - 使用自动语言检测配置
            translationRecognizer = TranslationRecognizer(translationConfig, autoDetectConfig, audioConfig)
            Log.d(TAG, "翻译识别器创建成功")
            
            // 注册识别事件
            translationRecognizer?.let { recognizer ->
                Log.d(TAG, "开始注册事件处理器")
                
                // 识别中事件
                recognizer.recognizing.addEventListener { _, event ->
                    val recognizedText = event.result.text
                    Log.d(TAG, "识别中: $recognizedText")
                    _recognitionResult.value = recognizedText
                    
                    // 获取检测到的语言
                    val detectedLanguage = getDetectedLanguageFromEvent(event)
                    if (detectedLanguage != null) {
                        _detectedLanguage.value = detectedLanguage
                        Log.d(TAG, "检测到语言: $detectedLanguage")
                    }
                    
                    // 获取翻译结果
                    processTranslationResults(event, detectedLanguage)
                }
                
                // 语言检测和翻译完成事件
                recognizer.recognized.addEventListener { _, event ->
                    Log.d(TAG, "识别结果: ${event.result.reason} - ${event.result.text}")
                    
                    if (event.result.reason == ResultReason.TranslatedSpeech || event.result.reason == ResultReason.RecognizedSpeech) {
                        val detectedLanguage = getDetectedLanguageFromEvent(event)
                        val recognizedText = event.result.text
                        
                        Log.d(TAG, "检测到语言: $detectedLanguage, 文本: $recognizedText")
                        
                        // 过滤掉无用内容
                        if (shouldProcessText(recognizedText)) {
                            _recognitionResult.value = recognizedText
                            
                            if (detectedLanguage != null) {
                                _detectedLanguage.value = detectedLanguage
                            }
                            
                            // 处理翻译结果
                            processTranslationResults(event, detectedLanguage)
                        } else {
                            Log.d(TAG, "过滤掉无用文本: $recognizedText")
                        }
                    } else {
                        Log.d(TAG, "识别结果不是语音或翻译: ${event.result.reason}")
                    }
                }
                
                // 取消事件
                recognizer.canceled.addEventListener { _, event ->
                    Log.e(TAG, "识别被取消: ${event.reason}, 详情: ${event.errorDetails}")
                    _errorMessage.value = "识别被取消: ${event.errorDetails}"
                    _isRecognizing.value = false
                    stopRecognitionSemaphore.release()
                }
                
                // 识别结束事件
                recognizer.sessionStopped.addEventListener { _, _ ->
                    Log.d(TAG, "识别会话结束")
                    _isRecognizing.value = false
                    stopRecognitionSemaphore.release()
                }
                
                // 启动连续识别
                try {
                    Log.d(TAG, "开始启动连续识别...")
                    recognizer.startContinuousRecognitionAsync().get()
                    _isRecognizing.value = true
                    Log.d(TAG, "连续识别已启动")
                } catch (e: Exception) {
                    Log.e(TAG, "连续识别启动失败: ${e.message}")
                    _errorMessage.value = "连续识别启动失败: ${e.message}"
                    _isRecognizing.value = false
                    throw e
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "识别初始化失败: ${ex.message}")
            ex.printStackTrace()
            _errorMessage.value = "识别初始化失败: ${ex.message}"
            _isRecognizing.value = false
        }
    }

    /**
     * 处理翻译结果
     */
    private fun processTranslationResults(event: TranslationRecognitionEventArgs, detectedLanguage: String?) {
        try {
            val translations = event.result.translations
            if (translations.isEmpty()) {
                return
            }
            
            // 根据双向翻译逻辑确定实际的目标语言
            val effectiveTargetLanguage = getEffectiveTargetLanguage(detectedLanguage, currentTargetLanguage, currentSourceLanguages)
            val targetLangCode = getTranslationLanguageCode(effectiveTargetLanguage)
            
            // 获取翻译文本
            val translatedText = translations[targetLangCode] ?: ""
            if (translatedText.isNotEmpty()) {
                Log.d(TAG, "翻译结果 ($targetLangCode): $translatedText")
                _translationResult.value = translatedText
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理翻译结果时出错: ${e.message}")
        }
    }

    /**
     * 根据双向翻译逻辑确定实际的目标语言
     */
    private fun getEffectiveTargetLanguage(detectedLanguage: String?, targetLanguage: String, sourceLanguages: List<String>): String {
        if (!enableBidirectionalTranslation || detectedLanguage.isNullOrEmpty()) {
            return targetLanguage
        }
        
        // 检测语言是否与目标语言匹配
        val detectedShortCode = if (detectedLanguage.contains("-")) {
            detectedLanguage.split("-")[0]
        } else detectedLanguage
        
        val targetShortCode = if (targetLanguage.contains("-")) {
            targetLanguage.split("-")[0]
        } else targetLanguage
        
        // 如果检测到的语言与目标语言匹配，则使用源语言作为翻译目标
        if (detectedShortCode.equals(targetShortCode, ignoreCase = true)) {
            Log.d(TAG, "检测到的语言($detectedLanguage)与目标语言($targetLanguage)匹配，切换为源语言翻译")
            // 获取主要源语言的短代码
            val primarySource = sourceLanguages.first()
            val primarySourceShortCode = if (primarySource.contains("-")) {
                primarySource.split("-")[0]
            } else primarySource
            
            return primarySourceShortCode
        }
        
        return targetLanguage
    }

    /**
     * 从事件中获取检测到的语言
     * 返回检测到的语言代码，如zh-CN、en-US等
     */
    private fun getDetectedLanguageFromEvent(e: TranslationRecognitionEventArgs): String? {
        try {
            val properties = e.result.properties
            if (properties != null) {
                // 尝试多种方式获取检测到的语言
                val autoDetectResult = properties.getProperty(PropertyId.SpeechServiceConnection_AutoDetectSourceLanguageResult)
                if (autoDetectResult != null && autoDetectResult.isNotEmpty()) {
                    Log.d(TAG, "通过AutoDetectSourceLanguageResult检测到的语言: $autoDetectResult")
                    return autoDetectResult
                }
                
                val jsonResult = properties.getProperty(PropertyId.SpeechServiceResponse_JsonResult)
                if (jsonResult != null && jsonResult.contains("\"Language\":")) {
                    val languageStart = jsonResult.indexOf("\"Language\":") + "\"Language\":\"".length
                    val languageEnd = jsonResult.indexOf("\"", languageStart)
                    if (languageStart > 0 && languageEnd > languageStart) {
                        val detectedLang = jsonResult.substring(languageStart, languageEnd)
                        Log.d(TAG, "通过JSON结果检测到的语言: $detectedLang")
                        return detectedLang
                    }
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "获取检测到的语言时出错", ex)
        }
        return null
    }

    /**
     * 格式化语言代码，确保符合BCP-47标准
     * 例如：将zh-cn格式化为zh-CN，en-us格式化为en-US等
     */
    private fun formatLanguageCode(languageCode: String): String {
        if (languageCode.contains("-")) {
            val parts = languageCode.split("-")
            if (parts.size == 2) {
                return "${parts[0].lowercase()}-${parts[1].uppercase()}"
            }
        }
        return languageCode
    }

    /**
     * 获取翻译语言代码
     * 将语音识别语言代码转换为翻译API使用的语言代码
     */
    private fun getTranslationLanguageCode(speechLanguageCode: String): String {
        return when {
            speechLanguageCode.startsWith("zh-CN") || speechLanguageCode == "zh-Hans" -> "zh-Hans"
            speechLanguageCode.startsWith("zh-TW") || speechLanguageCode.startsWith("zh-HK") || speechLanguageCode == "zh-Hant" -> "zh-Hant"
            speechLanguageCode.startsWith("en") -> "en"
            speechLanguageCode.startsWith("ja") -> "ja"
            speechLanguageCode.startsWith("ko") -> "ko"
            speechLanguageCode.startsWith("fr") -> "fr"
            speechLanguageCode.startsWith("de") -> "de"
            speechLanguageCode.startsWith("es") -> "es"
            speechLanguageCode.startsWith("it") -> "it"
            speechLanguageCode.startsWith("pt") -> "pt"
            speechLanguageCode.startsWith("ru") -> "ru"
            speechLanguageCode.startsWith("ar") -> "ar"
            speechLanguageCode.contains("-") -> speechLanguageCode.split("-")[0]
            else -> speechLanguageCode
        }
    }

    /**
     * 检查文本是否应该被处理
     */
    private fun shouldProcessText(text: String): Boolean {
        // 过滤掉太短的文本、纯标点符号或重复内容
        return text.length > 2 && !text.matches(Regex("^[\\p{Punct}\\s]+$"))
    }

    /**
     * 停止识别
     */
    fun stopRecognition() {
        if (!_isRecognizing.value) {
            Log.d(TAG, "没有正在进行的识别，忽略停止请求")
            return
        }

        try {
            Log.d(TAG, "开始停止语音识别...")
            
            // 停止语音识别器
            if (speechRecognizer != null) {
                Log.d(TAG, "停止语音识别器")
                speechRecognizer?.stopContinuousRecognitionAsync()?.get()
                Log.d(TAG, "语音识别器已停止")
            }

            // 停止翻译识别器
            if (translationRecognizer != null) {
                Log.d(TAG, "停止翻译识别器")
                translationRecognizer?.stopContinuousRecognitionAsync()?.get()
                Log.d(TAG, "翻译识别器已停止")
            }

            _isRecognizing.value = false
            Log.d(TAG, "所有识别器已停止")
        } catch (ex: Exception) {
            Log.e(TAG, "停止识别时出错", ex)
            _errorMessage.value = "停止识别时出错: ${ex.message}"
            _isRecognizing.value = false
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            stopRecognition()
            speechRecognizer?.close()
            translationRecognizer?.close()
            autoDetectConfig = null
            Log.d(TAG, "所有资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放资源时出错: ${e.message}")
        }
    }
} 