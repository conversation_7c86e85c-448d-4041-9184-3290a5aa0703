package com.example.llya.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

@SuppressLint("MissingPermission")
class BluetoothManager(private val context: Context) {
    private val TAG = "BluetoothManager"
    
    // 蓝牙适配器
    private val bluetoothAdapter: BluetoothAdapter? by lazy {
        val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as? BluetoothManager
        bluetoothManager?.adapter
    }
    
    // 连接状态
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    // 设备缓存
    private var _lastConnectedDevice: BluetoothDevice? = null
    private var _lastDeviceCheckTime: Long = 0
    private val DEVICE_CACHE_DURATION = 3000L // 3秒缓存
    
    // 线程池
    private val executor: ExecutorService = Executors.newFixedThreadPool(2)
    private val handler = Handler(Looper.getMainLooper())
    
    // 蓝牙配置文件常量 - 有些可能在低版本Android中不存在，使用硬编码数字
    companion object {
        private const val HID_HOST = 4  // BluetoothProfile.HID_HOST
        private const val AVRCP_CONTROLLER = 12  // BluetoothProfile.AVRCP_CONTROLLER
    }
    
    // 初始化
    init {
        // 在初始化时检查一次设备连接状态
        checkSystemConnectedDevices()
    }
    
    // 检查系统已连接的设备 - 强制检查版本
    @SuppressLint("MissingPermission")
    fun checkSystemConnectedDevices(forceRefresh: Boolean = false) {
        try {
            Log.d(TAG, "开始检查系统已连接设备")
            
            // 强制刷新缓存，确保每次都进行实际检查
            _lastDeviceCheckTime = 0
            
            // 先检查权限 - 为低版本Android做特殊处理
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                // Android 12之前的版本
                val permissions = arrayOf(
                    android.Manifest.permission.BLUETOOTH,
                    android.Manifest.permission.BLUETOOTH_ADMIN,
                    android.Manifest.permission.ACCESS_FINE_LOCATION
                )
                
                // 检查是否有任何缺失权限
                val missingPermissions = permissions.any { permission ->
                    ActivityCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
                }
                
                if (missingPermissions) {
                    Log.e(TAG, "缺少必要的蓝牙权限，无法获取连接状态")
                    handler.post {
                        _connectionState.value = ConnectionState.DISCONNECTED
                        
                        // 发送权限缺失的广播
                        try {
                            val intent = Intent("com.example.llya.BLUETOOTH_PERMISSION_MISSING")
                            context.sendBroadcast(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "发送权限缺失广播失败: ${e.message}")
                        }
                    }
                    return
                }
            } else {
                // Android 12及以上版本
                val permissions = arrayOf(
                    android.Manifest.permission.BLUETOOTH_SCAN,
                    android.Manifest.permission.BLUETOOTH_CONNECT
                )
                
                // 检查是否有任何缺失权限
                val missingPermissions = permissions.any { permission ->
                    ActivityCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
                }
                
                if (missingPermissions) {
                    Log.e(TAG, "缺少Android 12+必要的蓝牙权限，无法获取连接状态")
                    handler.post {
                        _connectionState.value = ConnectionState.DISCONNECTED
                        
                        // 发送权限缺失的广播
                        try {
                            val intent = Intent("com.example.llya.BLUETOOTH_PERMISSION_MISSING")
                            context.sendBroadcast(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "发送权限缺失广播失败: ${e.message}")
                        }
                    }
                    return
                }
            }
            
            // 1. 首先检查蓝牙适配器状态
            if (bluetoothAdapter == null || !bluetoothAdapter!!.isEnabled) {
                Log.d(TAG, "蓝牙适配器未启用")
                handler.post {
                    _connectionState.value = ConnectionState.DISCONNECTED
                }
                return
            }
            
            // 2. 检查系统蓝牙配置文件状态
            val isA2dpConnected = try {
                bluetoothAdapter!!.getProfileConnectionState(BluetoothProfile.A2DP) == BluetoothProfile.STATE_CONNECTED
            } catch (e: Exception) {
                Log.e(TAG, "检查A2DP配置文件失败: ${e.message}")
                false
            }
            
            val isHeadsetConnected = try {
                bluetoothAdapter!!.getProfileConnectionState(BluetoothProfile.HEADSET) == BluetoothProfile.STATE_CONNECTED
            } catch (e: Exception) {
                Log.e(TAG, "检查HEADSET配置文件失败: ${e.message}")
                false
            }
            
            if (isA2dpConnected || isHeadsetConnected) {
                Log.d(TAG, "系统蓝牙配置文件显示设备已连接")
                // 从配置文件中查找已连接设备
                val connectedDevice = findConnectedDeviceFromProfiles()
                if (connectedDevice != null) {
                    updateConnectedDevice(connectedDevice)
                    return
                }
            }
            
            // 3. 如果配置文件检查失败，尝试从已配对设备中查找
            try {
                val bondedDevices = bluetoothAdapter!!.bondedDevices
                for (device in bondedDevices) {
                    try {
                        // 检查设备类型
                        if (device.bluetoothClass.majorDeviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO) {
                            // 尝试获取设备连接状态
                            var isConnected = false
                            try {
                                val isConnectedMethod = device.javaClass.getMethod("isConnected")
                                isConnected = isConnectedMethod.invoke(device) as Boolean
                            } catch (e: Exception) {
                                Log.d(TAG, "设备不支持isConnected方法: ${e.message}")
                            }
                            
                            if (isConnected) {
                                Log.d(TAG, "从已配对设备中找到连接的音频设备: ${device.name}")
                                updateConnectedDevice(device)
                                return
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "检查设备状态失败: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查已配对设备失败: ${e.message}")
            }
            
            // 4. 如果所有检查都失败，设置为断开状态
            Log.d(TAG, "未找到已连接的蓝牙设备")
            handler.post {
                _connectionState.value = ConnectionState.DISCONNECTED
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查设备连接失败: ${e.message}")
            handler.post {
                _connectionState.value = ConnectionState.DISCONNECTED
            }
        }
    }
    
    // 清除设备缓存 - 新增方法
    fun clearDeviceCache() {
        _lastConnectedDevice = null
        _lastDeviceCheckTime = 0
        profileProxyCache.clear()
        profileCacheExpiry.clear()
        Log.d(TAG, "已清除设备和配置文件缓存")
        
        // 发送断开连接的广播通知
        handler.post {
            _connectionState.value = ConnectionState.DISCONNECTED
            try {
                val intent = Intent("com.example.llya.DEVICE_DISCONNECTED")
                context.sendBroadcast(intent)
            } catch (e: Exception) {
                Log.e(TAG, "发送断开连接广播失败: ${e.message}")
            }
        }
    }
    
    // 从蓝牙配置文件中查找已连接设备 - 优化版
    @SuppressLint("MissingPermission")
    private fun findConnectedDeviceFromProfiles(): BluetoothDevice? {
        if (bluetoothAdapter == null) {
            return null
        }
        
        // 强制忽略设备缓存，确保每次都进行真实检测
        _lastConnectedDevice = null
        _lastDeviceCheckTime = 0
        
        // 需要的配置文件类型
        val profileTypes = arrayOf(BluetoothProfile.HEADSET, BluetoothProfile.A2DP)
        
        // 并发检查多个配置文件
        for (profileType in profileTypes) {
            try {
                val proxy = getProfileProxy(profileType) ?: continue
                
                try {
                    val devices = proxy.connectedDevices
                    if (devices?.isNotEmpty() == true) {
                        Log.d(TAG, "在配置文件${profileType}中发现连接的设备: ${devices[0].name ?: "未知设备"}, ${devices[0].address}")
                        
                        // 找到连接的设备
                        _lastConnectedDevice = devices[0]
                        _lastDeviceCheckTime = System.currentTimeMillis()
                        return _lastConnectedDevice
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "获取配置文件${profileType}连接设备失败: ${e.message}")
                    // 继续尝试下一个配置文件
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取配置文件${profileType}代理失败: ${e.message}")
                // 继续尝试下一个配置文件
            }
        }
        
        Log.d(TAG, "未找到已连接的蓝牙音频设备")
        
        // 重置缓存
        _lastConnectedDevice = null
        _lastDeviceCheckTime = System.currentTimeMillis()
        return null
    }
    
    // 更新已连接设备信息 - 极简版本
    private fun updateConnectedDevice(device: BluetoothDevice) {
        try {
            // 直接获取基本设备信息
            val deviceName = device.name ?: "未知设备"
            val deviceAddress = device.address
            
            // 检查是否已经是相同设备，避免重复更新
            val isDifferentDevice = _lastConnectedDevice?.address != deviceAddress
            
            // 始终更新状态和发送通知，无论是否是相同设备
            // 移除之前的条件判断，确保每次都通知UI
            
            // 在主线程中更新UI状态
            handler.post {
                // 无论如何都更新连接状态和缓存设备
                _connectionState.value = ConnectionState.CONNECTED
                _lastConnectedDevice = device
                _lastDeviceCheckTime = System.currentTimeMillis()
                
                // 始终发送广播，确保UI获得最新信息
                Log.d(TAG, "更新已连接设备: 名称=$deviceName, 地址=$deviceAddress")
                
                // 发送广播通知
                try {
                    val intent = Intent("com.example.llya.DEVICE_CONNECTED")
                    intent.putExtra("device_address", deviceAddress)
                    intent.putExtra("device_name", deviceName)
                    intent.putExtra("has_complete_info", true)
                    context.sendBroadcast(intent)
                } catch (e: Exception) {
                    Log.e(TAG, "发送广播失败: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新设备信息失败: ${e.message}")
        }
    }
    
    // 获取蓝牙配置文件代理 - 使用缓存机制
    private val profileProxyCache = mutableMapOf<Int, BluetoothProfile?>()
    private val profileCacheExpiry = mutableMapOf<Int, Long>()
    private val CACHE_DURATION = 10000L // 10秒缓存
    
    @SuppressLint("MissingPermission")
    fun getProfileProxy(profile: Int): BluetoothProfile? {
        if (bluetoothAdapter == null) {
            Log.e(TAG, "获取配置文件代理失败：蓝牙适配器为空")
            return null
        }
        
        try {
            // 检查缓存 - 提高缓存使用优先级
            val currentTime = System.currentTimeMillis()
            val cachedProxy = profileProxyCache[profile]
            val expiry = profileCacheExpiry[profile] ?: 0L
            
            // 如果缓存有效，直接返回，不产生日志
            if (cachedProxy != null && currentTime < expiry) {
                return cachedProxy
            }
            
            // 尝试从缓存获取过期的代理，如果可用则延长其缓存时间
            if (cachedProxy != null) {
                try {
                    // 尝试验证过期代理是否仍可用
                    cachedProxy.connectedDevices // 只是验证访问，不需要结果
                    // 如果没有异常，则代理仍然可用，刷新缓存时间
                    profileCacheExpiry[profile] = currentTime + CACHE_DURATION
                    return cachedProxy
                } catch (e: Exception) {
                    // 代理已失效，需要重新获取
                    profileProxyCache.remove(profile)
                }
            }
            
            // 如果不在缓存中或已过期，获取新代理
            Log.d(TAG, "正在获取新的配置文件代理: profile=$profile")
            var bluetoothProfile: BluetoothProfile? = null
            val latch = java.util.concurrent.CountDownLatch(1)
            
            val serviceListener = object : BluetoothProfile.ServiceListener {
                override fun onServiceConnected(profileId: Int, proxy: BluetoothProfile) {
                    bluetoothProfile = proxy
                    latch.countDown()
                }
                
                override fun onServiceDisconnected(profileId: Int) {
                    // 在服务断开时从缓存中移除
                    profileProxyCache.remove(profile)
                    bluetoothProfile = null
                }
            }
            
            // 使用安全调用获取配置文件代理
            val getProxySuccess = bluetoothAdapter?.getProfileProxy(context, serviceListener, profile) ?: false
            if (!getProxySuccess) {
                return null
            }
            
            // 减少等待时间到500毫秒，避免界面卡顿
            latch.await(500, java.util.concurrent.TimeUnit.MILLISECONDS)
            
            // 安全获取代理对象
            val resultProxy = bluetoothProfile
            if (resultProxy != null) {
                // 更新缓存
                profileProxyCache[profile] = resultProxy
                profileCacheExpiry[profile] = currentTime + CACHE_DURATION
            }
            
            return resultProxy
        } catch (e: Exception) {
            Log.e(TAG, "获取蓝牙配置文件代理失败: ${e.message}")
            return null
        }
    }
    
    // 获取设备信息作为Map
    @SuppressLint("MissingPermission", "HardwareIds")
    fun getDeviceInfoAsMap(device: BluetoothDevice): Map<String, String> {
        val infoMap = mutableMapOf<String, String>()
        try {
            // 只获取基本信息：名称和地址
            infoMap["name"] = device.name ?: "未知设备"
            infoMap["address"] = device.address
            Log.d(TAG, "成功获取设备信息: 名称=${device.name ?: "未知设备"}, MAC=${device.address}")
        } catch (e: Exception) {
            Log.e(TAG, "获取设备信息失败: ${e.message}")
            // 确保至少有地址信息
            if (!infoMap.containsKey("address")) {
                infoMap["address"] = device.address ?: "未知地址"
            }
            if (!infoMap.containsKey("name")) {
                infoMap["name"] = "未知设备"
            }
        }
        
        return infoMap
    }
    
    // 连接状态枚举
    enum class ConnectionState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED
    }
    
    // 强制检查并更新连接状态 - 极简版本
    fun forceUpdateConnectedDeviceStatus() {
        try {
            Log.d(TAG, "开始强制更新连接状态")
            
            // 直接检查配置文件中的设备
            val connectedDevice = findConnectedDeviceFromProfiles()
            if (connectedDevice != null) {
                updateConnectedDevice(connectedDevice)
            } else {
                handler.post {
                    _connectionState.value = ConnectionState.DISCONNECTED
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "强制更新连接状态出错: ${e.message}")
            handler.post {
                _connectionState.value = ConnectionState.DISCONNECTED
            }
        }
    }
    
    // 检查特定地址的设备是否是当前连接的设备
    fun isCurrentlyConnected(deviceAddress: String): Boolean {
        // 检查设备地址是否与当前已连接设备匹配
        if (_lastConnectedDevice?.address == deviceAddress && _connectionState.value == ConnectionState.CONNECTED) {
            Log.d(TAG, "设备 ${deviceAddress} 是当前连接的设备")
            return true
        }
        
        try {
            // 尝试获取HEADSET配置文件的已连接设备
            val headsetProxy = getProfileProxy(BluetoothProfile.HEADSET)
            if (headsetProxy != null) {
                val connectedDevices = headsetProxy.connectedDevices
                if (connectedDevices.isNotEmpty()) {
                    val foundDevice = connectedDevices.find { it.address == deviceAddress }
                    if (foundDevice != null) {
                        Log.d(TAG, "在HEADSET配置文件中找到设备 ${deviceAddress}")
                        return true
                    }
                }
            }
            
            // 尝试获取A2DP配置文件的已连接设备
            val a2dpProxy = getProfileProxy(BluetoothProfile.A2DP)
            if (a2dpProxy != null) {
                val connectedDevices = a2dpProxy.connectedDevices
                if (connectedDevices.isNotEmpty()) {
                    val foundDevice = connectedDevices.find { it.address == deviceAddress }
                    if (foundDevice != null) {
                        Log.d(TAG, "在A2DP配置文件中找到设备 ${deviceAddress}")
                        return true
                    }
                }
            }
            
            return false
        } catch (e: Exception) {
            Log.e(TAG, "检查设备连接状态时出错: ${e.message}")
            return false
        }
    }
    
    // 释放资源
    fun release() {
        try {
            // 关闭线程池
            if (!executor.isShutdown) {
                executor.shutdown()
                try {
                    // 等待任务完成或超时
                    if (!executor.awaitTermination(1, java.util.concurrent.TimeUnit.SECONDS)) {
                        executor.shutdownNow()
                    }
                } catch (e: InterruptedException) {
                    executor.shutdownNow()
                }
            }
            
            // 清除缓存
            profileProxyCache.clear()
            profileCacheExpiry.clear()
            
            // 移除所有回调
            handler.removeCallbacksAndMessages(null)
            
            Log.d(TAG, "蓝牙管理器资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放资源时出错: ${e.message}")
        }
    }
} 