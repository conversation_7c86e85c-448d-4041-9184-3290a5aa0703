package com.example.llya.viewmodel

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import com.example.llya.network.UpdateNetworkManager

data class AppUpdateInfo(
    val versionName: String,
    val versionCode: Int,
    val updateContent: String,
    val downloadUrl: String,
    val forceUpdate: Boolean
)

class AppUpdateViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "AppUpdateViewModel"

    private val updateNetworkManager = UpdateNetworkManager.getInstance()

    // 更新信息状态
    private val _updateInfo = MutableStateFlow<AppUpdateInfo?>(null)
    val updateInfo: StateFlow<AppUpdateInfo?> = _updateInfo.asStateFlow()

    // 显示更新弹窗的状态
    private val _showUpdateDialog = MutableStateFlow(false)
    val showUpdateDialog: StateFlow<Boolean> = _showUpdateDialog.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 初始化时不再自动检查更新
    init {
        // 初始化时不执行任何操作
    }

    // 检查更新
    fun checkForUpdates() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                Log.d(TAG, "开始检查应用更新...")

                // 获取当前应用版本信息
                val packageInfo = getApplication<Application>().packageManager.getPackageInfo(
                    getApplication<Application>().packageName, 0
                )
                val currentVersionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode.toInt()
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode
                }

                Log.d(TAG, "当前版本号: $currentVersionCode")

                // 调用API检查更新
                updateNetworkManager.checkUpdate(
                    platform = "android",
                    versionCode = currentVersionCode.toString()
                ).catch { e ->
                    Log.e(TAG, "检查更新网络请求失败: ${e.message}", e)
                    _errorMessage.value = "网络请求失败: ${e.message}"
                    _isLoading.value = false
                }.collect { response ->
                    _isLoading.value = false

                    if (response.status == 200 && response.data != null) {
                        val updateData = response.data

                        if (updateData.hasUpdate) {
                            // 有新版本可用
                            val updateInfo = AppUpdateInfo(
                                versionName = updateData.latestVersion ?: "未知版本",
                                versionCode = updateData.versionCode ?: 0,
                                updateContent = updateData.releaseNotes ?: "暂无更新说明",
                                downloadUrl = updateData.downloadUrl ?: "",
                                forceUpdate = updateData.forceUpdate ?: false
                            )

                            _updateInfo.value = updateInfo
                            _showUpdateDialog.value = true

                            Log.d(TAG, "发现新版本: ${updateData.latestVersion}, 强制更新: ${updateData.forceUpdate}")
                        } else {
                            // 已是最新版本
                            Log.d(TAG, "当前已是最新版本: ${updateData.message}")
                            _errorMessage.value = updateData.message
                        }
                    } else {
                        // API返回错误
                        val errorMsg = response.message ?: "检查更新失败"
                        Log.e(TAG, "API返回错误: $errorMsg")
                        _errorMessage.value = errorMsg
                    }
                }

            } catch (e: Exception) {
                _isLoading.value = false
                Log.e(TAG, "检查更新异常: ${e.message}", e)
                _errorMessage.value = "检查更新失败: ${e.message}"
            }
        }
    }

    // 手动设置更新信息
    fun setUpdateInfo(
        versionName: String,
        versionCode: Int,
        updateContent: String,
        downloadUrl: String,
        forceUpdate: Boolean
    ) {
        _updateInfo.value = AppUpdateInfo(
            versionName = versionName,
            versionCode = versionCode,
            updateContent = updateContent,
            downloadUrl = downloadUrl,
            forceUpdate = forceUpdate
        )
        Log.d(TAG, "手动设置更新信息: 版本 $versionName, 强制更新: $forceUpdate")
    }

    // 手动显示更新弹窗（用于测试或特定场景）
    fun showUpdateDialog() {
        if (_updateInfo.value == null) {
            // 如果还没有更新信息，先获取一个模拟的更新信息
            _updateInfo.value = AppUpdateInfo(
                versionName = "2.0.0",
                versionCode = 20,
                updateContent = "1. 修复了一些已知问题\n2. 优化了语音识别功能\n3. 增加了新的翻译语言支持\n4. 改进了用户界面体验",
                downloadUrl = "https://example.com/download/llya.apk",
                forceUpdate = true
            )
        }
        _showUpdateDialog.value = true
    }

    // 下载APP
    fun downloadApp() {
        viewModelScope.launch {
            try {
                _updateInfo.value?.let { updateInfo ->
                    Log.d(TAG, "准备下载应用: ${updateInfo.downloadUrl}")

                    // 打开浏览器下载
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(updateInfo.downloadUrl))
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    getApplication<Application>().startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "下载应用出错: ${e.message}")
            }
        }
    }

    // 关闭更新弹窗
    fun dismissUpdateDialog() {
        _showUpdateDialog.value = false
    }

    // 清除错误信息
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    // 手动检查更新（用于用户主动触发）
    fun manualCheckUpdate() {
        Log.d(TAG, "用户手动检查更新")
        checkForUpdates()
    }

    // 获取当前应用版本信息
    fun getCurrentVersionInfo(): Pair<String, Int> {
        return try {
            val packageInfo = getApplication<Application>().packageManager.getPackageInfo(
                getApplication<Application>().packageName, 0
            )
            val versionName = packageInfo.versionName ?: "未知"
            val versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                packageInfo.longVersionCode.toInt()
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode
            }
            Pair(versionName, versionCode)
        } catch (e: Exception) {
            Log.e(TAG, "获取版本信息失败: ${e.message}", e)
            Pair("未知", 0)
        }
    }
}
