package com.example.llya.viewmodel

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.llya.audio.AudioRecorder
import com.example.llya.data.HistoryManager
import com.example.llya.data.HistoryRecord
import com.example.llya.data.RecordType
import com.example.llya.network.GoogleCloudServiceAdapter
import com.example.llya.network.GoogleCloudSpeechService
import com.example.llya.network.LanguageInfo
import com.example.llya.network.VoiceInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import kotlinx.coroutines.Job
import android.media.AudioManager
import android.widget.Toast
import android.app.Application
import android.content.Context
import android.media.AudioDeviceInfo
import kotlinx.coroutines.async
import kotlinx.coroutines.withTimeout
import com.example.llya.R
import com.example.llya.LlyaApplication

/**
 * 谷歌云服务ViewModel
 * 管理与谷歌云API的交互状态
 */
class GoogleCloudServiceViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "GoogleCloudServiceViewModel"

    // 谷歌云服务适配器（用于会议记录和翻译功能）
    private val serviceAdapter = GoogleCloudServiceAdapter.getInstance()

    // 谷歌云语音服务（专门用于普通语音转写功能）
    private val speechService = GoogleCloudSpeechService.getInstance()

    // 腾讯云ASR配置
    companion object {
        private const val TENCENT_APP_ID = "1251118525"
        private const val TENCENT_SECRET_ID = "AKIDoTt53sTpwoW2KQIuHPIvJSM2TeaHkyyk"
        private const val TENCENT_SECRET_KEY = "aw4wZy83iBQP9fx1qQmunRq1yf7KJ3Yp"

        // 使用腾讯云的语言列表
        private val TENCENT_SUPPORTED_LANGUAGES = setOf(
            "zh-CN",      // 中文普通话
            "yue-Hant-HK", // 粤语
            "en-US",      // 英语
            "ko-KR",      // 韩语
            "ja-JP",      // 日语
            "th-TH",      // 泰语
            "id-ID",      // 印度尼西亚语
            "vi-VN",      // 越南语
            "ms-MY",      // 马来语
            "fil-PH",     // 菲律宾语
            "pt-PT",      // 葡萄牙语
            "tr-TR",      // 土耳其语
            "ar-SA",      // 阿拉伯语
            "es-ES",      // 西班牙语
            "hi-IN",      // 印地语
            "fr-FR",      // 法语
            "de-DE"       // 德语
        )

        // 腾讯云语言代码映射
        private val LANGUAGE_TO_TENCENT_MODEL = mapOf(
            "zh-CN" to "16k_zh",            // 中文普通话
            "yue-Hant-HK" to "16k_zh_yue",  // 粤语
            "en-US" to "16k_en",            // 英语
            "ko-KR" to "16k_ko",            // 韩语
            "ja-JP" to "16k_ja",            // 日语
            "th-TH" to "16k_th",            // 泰语
            "id-ID" to "16k_id",            // 印度尼西亚语
            "vi-VN" to "16k_vi",            // 越南语
            "ms-MY" to "16k_ms",            // 马来语
            "fil-PH" to "16k_fil",          // 菲律宾语
            "pt-PT" to "16k_pt",            // 葡萄牙语
            "tr-TR" to "16k_tr",            // 土耳其语
            "ar-SA" to "16k_ar",            // 阿拉伯语
            "es-ES" to "16k_es",            // 西班牙语
            "hi-IN" to "16k_hi",            // 印地语
            "fr-FR" to "16k_fr",            // 法语
            "de-DE" to "16k_de"             // 德语
        )
    }

    // 腾讯云语音识别客户端
    private var tencentAsrClient: com.example.llya.asr.TencentCloudSpeechToTextClient? = null

    // 当前使用的ASR服务名称
    private val _currentAsrServiceName = MutableStateFlow<String>("Google Cloud")
    val currentAsrServiceName: StateFlow<String> = _currentAsrServiceName.asStateFlow()

    // 当前使用的ASR类型
    enum class AsrType {
        GOOGLE,
        TENCENT
    }

    private val _activeAsrType = MutableStateFlow<AsrType>(AsrType.GOOGLE)
    val activeAsrType: StateFlow<AsrType> = _activeAsrType.asStateFlow()

    // 历史记录管理器
    private val historyManager = HistoryManager.getInstance()

    // 音频录制器
    private var audioRecorder: AudioRecorder? = null

    // 媒体播放器
    private var mediaPlayer: MediaPlayer? = null

    // UI状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    // 当前正在识别的文本（实时更新）
    private val _recognizedText = MutableStateFlow("")
    val recognizedText: StateFlow<String> = _recognizedText.asStateFlow()

    // 已完成识别的句子列表（每句话作为单独的元素）
    private val _recognizedSentences = MutableStateFlow<List<String>>(emptyList())
    val recognizedSentences: StateFlow<List<String>> = _recognizedSentences.asStateFlow()

    // 当前显示的完整识别文本（包含所有已识别句子和当前识别中的内容）
    private val _fullRecognizedText = MutableStateFlow("")
    val fullRecognizedText: StateFlow<String> = _fullRecognizedText.asStateFlow()

    // 翻译结果
    private val _translatedText = MutableStateFlow("")
    val translatedText: StateFlow<String> = _translatedText.asStateFlow()

    // 翻译结果历史（与recognizedSentences对应）
    private val _translatedSentences = MutableStateFlow<List<String>>(emptyList())
    val translatedSentences: StateFlow<List<String>> = _translatedSentences.asStateFlow()

    private val _isTranslating = MutableStateFlow(false)
    val isTranslating: StateFlow<Boolean> = _isTranslating.asStateFlow()

    private val _isSpeaking = MutableStateFlow(false)
    val isSpeaking: StateFlow<Boolean> = _isSpeaking.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _supportedLanguages = MutableStateFlow<List<LanguageInfo>>(emptyList())
    val supportedLanguages: StateFlow<List<LanguageInfo>> = _supportedLanguages.asStateFlow()

    private val _availableVoices = MutableStateFlow<List<VoiceInfo>>(emptyList())
    val availableVoices: StateFlow<List<VoiceInfo>> = _availableVoices.asStateFlow()

    // 当前选择的语言和语音
    private val _selectedSourceLanguage = MutableStateFlow("zh-CN")
    val selectedSourceLanguage: StateFlow<String> = _selectedSourceLanguage.asStateFlow()

    private val _selectedTargetLanguage = MutableStateFlow("en-US")
    val selectedTargetLanguage: StateFlow<String> = _selectedTargetLanguage.asStateFlow()

    private val _selectedVoice = MutableStateFlow<VoiceInfo?>(null)
    val selectedVoice: StateFlow<VoiceInfo?> = _selectedVoice.asStateFlow()

    private var autoTranslateJob: Job? = null

    // 最近一次识别的最终结果，用于记录到历史记录中
    private var lastFinalRecognitionResult = ""

    // 最近一次翻译结果，用于记录到历史记录中
    private var lastTranslationResult = ""

    // 最后一次翻译的原文，用于避免重复翻译
    private var lastTranslatedSourceText = ""

    // 翻译防抖计时器
    private var translationDebounceJob: Job? = null

    // 是否自动播放语音翻译结果
    private val _autoPlayTranslation = MutableStateFlow(true)
    val autoPlayTranslation: StateFlow<Boolean> = _autoPlayTranslation.asStateFlow()

    // 播放模式枚举
    enum class PlaybackMode {
        SILENT,      // 静音模式：不播放翻译声音，只显示翻译结果
        EARBUDS,     // 耳机模式：对手机讲话，从耳机收听翻译声音
        SPEAKER,     // 外放模式：点击开始讲话，停止讲话时从手机播放翻译的声音
        DUAL_EARBUDS // 双耳模式：一人带一只耳机，对着手机讲话，可以从耳机收听翻译语音
    }

    // 添加播放模式状态
    private val _playbackMode = MutableStateFlow(PlaybackMode.EARBUDS)  // 默认为耳机模式
    val playbackMode: StateFlow<PlaybackMode> = _playbackMode.asStateFlow()

    // 添加AudioManager作为类的成员变量
    private val audioManager = application.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    // 是否启用直接从腾讯云ASR客户端获取累积文本
    private var _useDirectTencentTextCollection = false

    // 添加一个标记，用于跟踪是否是新会话的第一句话
    private val _newSessionStarted = MutableStateFlow(false)
    val newSessionStarted: StateFlow<Boolean> = _newSessionStarted.asStateFlow()

    // 添加一个标记，用于判断是否在同声传译页面
    private val _isInSimultaneousTranslationMode = MutableStateFlow(false)
    val isInSimultaneousTranslationMode: StateFlow<Boolean> = _isInSimultaneousTranslationMode.asStateFlow()

    // 是否启用自动语言检测
    private val _autoLanguageDetectionEnabled = MutableStateFlow(false)
    val autoLanguageDetectionEnabled: StateFlow<Boolean> = _autoLanguageDetectionEnabled.asStateFlow()

    // 当前检测到的语言
    private val _detectedLanguage = MutableStateFlow("")
    val detectedLanguage: StateFlow<String> = _detectedLanguage.asStateFlow()

    // 当前翻译方向（源语言 -> 目标语言）
    private val _translationDirection = MutableStateFlow("")
    val translationDirection: StateFlow<String> = _translationDirection.asStateFlow()

    // 是否启用双向翻译（中日互译）
    private val _bidirectionalTranslationEnabled = MutableStateFlow(false)
    val bidirectionalTranslationEnabled: StateFlow<Boolean> = _bidirectionalTranslationEnabled.asStateFlow()

    init {
        // 初始化时加载支持的语言和语音
        loadSupportedLanguages()

        // 初始化腾讯云语音识别客户端
        initializeTencentAsrClient()

        // 根据初始语言设置ASR服务
        updateAsrServiceByLanguage(_selectedSourceLanguage.value)
    }

    /**
     * 加载支持的语言
     */
    fun loadSupportedLanguages() {
        viewModelScope.launch {
            try {
                val languages = serviceAdapter.getSupportedLanguages()
                _supportedLanguages.value = languages
            } catch (e: Exception) {
                Log.e(TAG, "加载支持的语言失败", e)
                _errorMessage.value = "加载支持的语言失败: ${e.message}"
            }
        }
    }

    /**
     * 加载可用的语音
     */
    fun loadAvailableVoices(languageCode: String = _selectedTargetLanguage.value) {
        viewModelScope.launch {
            try {
                val voices = serviceAdapter.getAvailableVoices(languageCode)
                _availableVoices.value = voices

                if (voices.isNotEmpty()) {
                    _selectedVoice.value = voices[0]
                    Log.d(TAG, "成功加载语音列表，选择第一个语音: ${voices[0].name}")
                } else {
                    // 如果没有获取到语音，创建默认语音
                    Log.d(TAG, "未获取到语音列表，创建默认语音")
                    createDefaultVoice(languageCode)
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载可用的语音失败，创建默认语音", e)
                _errorMessage.value = "加载可用的语音失败: ${e.message}"
                // 创建默认语音
                createDefaultVoice(languageCode)
            }
        }
    }

    /**
     * 创建默认语音
     */
    private fun createDefaultVoice(languageCode: String) {
        val defaultVoiceName = when {
            languageCode.startsWith("zh") || languageCode.startsWith("cmn") -> "cmn-CN-Standard-A"
            languageCode.startsWith("en") -> "en-US-Standard-C"
            else -> "${languageCode.split("-")[0]}-Standard-A"
        }

        val defaultGender = "FEMALE"
        val defaultVoice = VoiceInfo(defaultVoiceName, defaultVoiceName, defaultGender)

        _selectedVoice.value = defaultVoice
        Log.d(TAG, "已创建默认语音: $defaultVoiceName")
    }

    /**
     * 初始化腾讯云语音识别客户端
     */
    private fun initializeTencentAsrClient() {
        try {
            tencentAsrClient = com.example.llya.asr.TencentCloudSpeechToTextClient(
                appId = TENCENT_APP_ID,
                secretId = TENCENT_SECRET_ID,
                secretKey = TENCENT_SECRET_KEY,
                coroutineScope = viewModelScope
            )

            // 监听腾讯云ASR客户端的识别结果
            viewModelScope.launch {
                tencentAsrClient?.recognitionResult?.collect { text ->
                    if (_activeAsrType.value == AsrType.TENCENT &&
                        text.isNotEmpty() &&
                        !text.contains("连接已建立") &&
                        !text.contains("失败") &&
                        !text.contains("出错")) {

                        // 如果是新会话的第一句话，确保清除之前的文本状态
                        if (_newSessionStarted.value) {
                            Log.d(TAG, "腾讯云ASR: 检测到新会话的第一句话，清空之前的识别结果")
                            _recognizedSentences.value = emptyList()
                            lastTranslatedSourceText = ""
                            _newSessionStarted.value = false

                            // 强制清空累积文本
                            tencentAsrClient?.clearAll()
                        }
                    }
                }
            }

            // 移除自动语言检测监听代码

            // 监听腾讯云ASR客户端的识别结果（继续）
            viewModelScope.launch {
                tencentAsrClient?.recognitionResult?.collect { text ->
                    if (_activeAsrType.value == AsrType.TENCENT &&
                        text.isNotEmpty() &&
                        !text.contains("连接已建立") &&
                        !text.contains("失败") &&
                        !text.contains("出错")) {

                        // 检查是否是新的识别会话的开始
                        val currentText = _recognizedText.value
                        val isNewSession = currentText.isEmpty() ||
                                          !text.startsWith(currentText)

                        if (isNewSession && currentText.isNotEmpty()) {
                            Log.d(TAG, "腾讯云ASR: 检测到新的识别会话，清除之前的结果: '$currentText' -> '$text'")
                            // 如果是新会话，清除之前的识别结果
                            _recognizedText.value = ""

                            // 强制清空累积文本
                            tencentAsrClient?.clearAll()
                        }

                        // 更新识别结果
                        _recognizedText.value = text

                        // 实时更新完整文本显示（包括当前正在识别的内容）
                        updateFullRecognizedText()

                        // 处理最终结果
                        val isFinal = tencentAsrClient?.isFinalResult?.value ?: false
                        if (isFinal && text.isNotEmpty()) {
                            // 保存最终的识别结果
                            lastFinalRecognitionResult = text

                            // 将这个完整句子添加到句子列表中
                            val currentSentences = _recognizedSentences.value.toMutableList()
                            currentSentences.add(text)
                            _recognizedSentences.value = currentSentences

                            // 更新完整文本显示
                            updateFullRecognizedText()

                            // 检查是否与上次翻译内容相同，避免重复翻译
                            if (text == lastTranslatedSourceText) {
                                Log.d(TAG, "腾讯云ASR: 最终结果与上次翻译内容相同，跳过翻译: '$text'")
                                // 清除当前识别文本，避免下一次开始说话时叠加
                                _recognizedText.value = ""
                                return@collect
                            }

                            // 标记此文本正在处理中，避免沉默检测重复触发
                            lastTranslatedSourceText = text

                            // 添加语音识别记录到历史
                            val sourceLangName = getLanguageDisplayName(_selectedSourceLanguage.value)
                            val historyRecord = HistoryRecord(
                                sourceText = text,
                                sourceLanguage = _selectedSourceLanguage.value,
                                sourceLanguageName = sourceLangName,
                                type = RecordType.SPEECH_RECOGNITION
                            )
                            historyManager.addRecord(historyRecord)
                            Log.d(TAG, "腾讯云ASR: 已添加语音识别记录到历史: $text")

                            Log.d(TAG, "腾讯云ASR: 收到最终识别结果，直接触发翻译: '$text'")
                            // 执行翻译（直接执行，不使用防抖）
                            translateCompletedSentence(text)

                            // 清除当前识别文本，避免下一次开始说话时叠加
                            _recognizedText.value = ""
                        } else if (text.isNotEmpty() && text.length > 5 && !_isTranslating.value) {
                            // 与Google Cloud ASR服务相同的沉默检测逻辑
                            // 取消之前的自动翻译任务
                            autoTranslateJob?.cancel()

                            // 检查是否与上次翻译内容相同，避免重复翻译
                            if (text == lastTranslatedSourceText) {
                                Log.d(TAG, "临时结果与上次翻译内容相同，跳过沉默检测: '$text'")
                                return@collect
                            }

                            // 检查文本是否包含完整句子的结束标记
                            val hasCompleteEndMarks = text.contains('。') || text.contains('！') ||
                                                    text.contains('？') || text.contains('…') ||
                                                    (text.contains('.') && text.contains(' ')) ||
                                                    (text.contains('!') && text.contains(' ')) ||
                                                    (text.contains('?') && text.contains(' '))

                            // 检查是否为中文短句子（3-5个中文字符且没有标点）
                            val isChineseShortSentence = text.length in 3..10 &&
                                                    text.all { it.code > 0x4E00 && it.code < 0x9FA5 }

                            // 优化沉默检测时间 - 将所有情况都设为固定1秒
                            val silenceDetectionTime = 1500L  // 统一使用1秒的沉默检测时间

                            // 启动沉默检测计时器
                            autoTranslateJob = viewModelScope.launch {
                                Log.d(TAG, "启动沉默检测计时器: ${silenceDetectionTime}ms, 文本长度: ${text.length}, 文本: '${text.take(20)}${if (text.length > 20) "..." else ""}'")
                                delay(silenceDetectionTime)

                                // 如果文本在延迟期间没有变化，且不在翻译中，说明用户停止说话了
                                if (_recognizedText.value == text && !_isTranslating.value && text != lastTranslatedSourceText) {
                                    Log.d(TAG, "检测到用户沉默${silenceDetectionTime}ms，触发翻译: '$text'")

                                    // 将当前句子添加到句子列表
                                    val currentSentences = _recognizedSentences.value.toMutableList()
                                    currentSentences.add(text)
                                    _recognizedSentences.value = currentSentences

                                    // 更新完整文本显示
                                    updateFullRecognizedText()

                                    // 标记此文本正在处理中
                                    lastTranslatedSourceText = text

                                    // 执行翻译
                                    translateCompletedSentence(text)
                                } else {
                                    Log.d(TAG, "沉默检测完成但条件不满足，跳过翻译: 文本是否相同=${_recognizedText.value == text}, 是否正在翻译=${_isTranslating.value}, 是否为上次翻译内容=${text == lastTranslatedSourceText}")
                                }
                            }
                        }
                    }
                }
            }

            // 监听腾讯云ASR客户端的累积文本
            // 注意：我们不再使用腾讯云客户端自身的累积文本，而是使用我们自己管理的句子列表
            viewModelScope.launch {
                tencentAsrClient?.accumulatedText?.collect { _ ->
                    // 我们不再直接使用腾讯云的累积文本，而是在上面的识别结果处理中管理句子
                    // 如果需要在这里做额外的处理，可以添加逻辑
                }
            }

            Log.d(TAG, "腾讯云语音识别客户端初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "腾讯云语音识别客户端初始化失败", e)
            _errorMessage.value = "腾讯云语音识别初始化失败: ${e.message}"
        }
    }

    /**
     * 判断是否应该使用腾讯云语音识别
     */
    private fun shouldUseTencentCloud(languageCode: String): Boolean {
        return TENCENT_SUPPORTED_LANGUAGES.contains(languageCode)
    }

    /**
     * 获取腾讯云引擎模型类型
     */
    private fun getTencentEngineModelType(languageCode: String): String {
        return LANGUAGE_TO_TENCENT_MODEL[languageCode] ?: "16k_zh"
    }

    /**
     * 根据语言更新ASR服务
     */
    private fun updateAsrServiceByLanguage(languageCode: String) {
        val useTencentCloud = shouldUseTencentCloud(languageCode)
        _activeAsrType.value = if (useTencentCloud) AsrType.TENCENT else AsrType.GOOGLE
        _currentAsrServiceName.value = if (useTencentCloud) "腾讯云" else "Google Cloud"
        Log.d(TAG, "根据语言[$languageCode]切换ASR服务为: ${_currentAsrServiceName.value}")
    }

    /**
     * 设置源语言
     */
    fun setSourceLanguage(languageCode: String) {
        // 如果语言没有变化，不做任何操作
        if (_selectedSourceLanguage.value == languageCode) {
            Log.d(TAG, "源语言未发生变化，保持当前语言: $languageCode")
            return
        }

        val previousLanguage = _selectedSourceLanguage.value
        val wasRecording = _isRecording.value

        Log.d(TAG, "源语言切换: $previousLanguage -> $languageCode, 当前录音状态: ${if (wasRecording) "录音中" else "未录音"}")

        // 如果正在录音，先停止当前的语音识别
        if (wasRecording) {
            Log.d(TAG, "检测到语言切换，停止当前录音以应用新语言")
            stopSpeechRecognition()
        }

        // 更新源语言
        _selectedSourceLanguage.value = languageCode
        Log.d(TAG, "源语言已更新为: $languageCode")

        // 更新ASR服务
        updateAsrServiceByLanguage(languageCode)

        // 如果之前在录音，用新的语言设置重新开始语音识别
        if (wasRecording) {
            Log.d(TAG, "准备使用新语言重新开始录音")
            // 延长等待时间确保前一个连接完全关闭
            viewModelScope.launch {
                // 增加延迟时间以确保WebSocket连接完全关闭
                Log.d(TAG, "等待500ms确保WebSocket连接完全关闭...")
                delay(500)  // 从300ms增加到500ms
                Log.d(TAG, "使用新语言($languageCode)重新开始录音")
                startSpeechRecognition()
            }
        } else {
            Log.d(TAG, "用户未在录音，语言切换不会立即影响语音识别")
        }
    }

    /**
     * 设置目标语言
     */
    fun setTargetLanguage(languageCode: String) {
        // 如果语言没有变化，不做任何操作
        if (_selectedTargetLanguage.value == languageCode) return

        _selectedTargetLanguage.value = languageCode
        Log.d(TAG, "目标语言已切换为: $languageCode")

        // 更新目标语言后，重新加载该语言的语音选项
        loadAvailableVoices(languageCode)

        // 如果有已识别的文本，使用新的目标语言重新翻译
        val currentText = _recognizedText.value
        if (currentText.isNotEmpty() && !_isTranslating.value) {
            translateRecognizedText()
        }
    }

    /**
     * 设置选定的语音
     */
    fun setSelectedVoice(voice: VoiceInfo) {
        _selectedVoice.value = voice
    }

    /**
     * 开始语音识别
     */
    fun startSpeechRecognition() {
        if (_isRecording.value) {
            Log.d(TAG, "已经在录音中，先停止当前录音")
            stopSpeechRecognition()
            // 短暂延迟后重新开始
            viewModelScope.launch {
                delay(300)
                startSpeechRecognition()
            }
            return
        }

        // 每次开始新录音时清除之前所有文本和状态
        clearRecognizedText()

        // 特别确保当前识别文本被清空
        _recognizedText.value = ""
        lastTranslatedSourceText = ""

        // 如果是谷歌云ASR，确保重置识别状态
        if (_activeAsrType.value == AsrType.GOOGLE) {
            try {
                speechService.resetRecognitionState()
                Log.d(TAG, "开始录音前已重置谷歌云识别状态")
            } catch (e: Exception) {
                Log.e(TAG, "开始录音前重置谷歌云识别状态失败", e)
            }
        }

        // 标记为新会话开始
        _newSessionStarted.value = true
        Log.d(TAG, "已标记为新会话开始")

        _isRecording.value = true

        val sourceLanguage = _selectedSourceLanguage.value
        Log.d(TAG, "开始语音识别，使用语言: $sourceLanguage")

        // 根据语言确定使用哪个ASR服务
        val useTencentCloud = shouldUseTencentCloud(sourceLanguage)
        _activeAsrType.value = if (useTencentCloud) AsrType.TENCENT else AsrType.GOOGLE
        _currentAsrServiceName.value = if (useTencentCloud) "腾讯云" else "Google Cloud"
        Log.d(TAG, "ASR服务选择: ${_currentAsrServiceName.value}")

        // 创建音频录制器，使用优化的高质量音频参数
        audioRecorder = AudioRecorder(
            sampleRate = 16000,  // 16kHz采样率，适合语音识别
            encoding = AudioFormat.ENCODING_PCM_16BIT,
            channelConfig = AudioFormat.CHANNEL_IN_MONO,
            bufferSizeMultiplier = 4,  // 进一步增大缓冲区，提高稳定性和音质
            onAudioBufferReady = { data ->
                // 根据当前选择的语言，将音频数据发送到对应的语音识别服务
                if (useTencentCloud) {
                    tencentAsrClient?.sendAudioData(data)
                } else {
                    // 发送音频数据到谷歌云服务
                    speechService.sendAudioData(data)
                }
            },
            onError = { error ->
                _errorMessage.value = "录音错误: $error"
                stopSpeechRecognition()
            }
        )

        if (useTencentCloud) {
            // 使用腾讯云识别
            try {
                // 获取腾讯云引擎模型类型
                val engineModelType = getTencentEngineModelType(sourceLanguage)

                // 连接腾讯云语音识别服务，移除自动语言检测参数
                tencentAsrClient?.connect(
                    engineModelType = engineModelType
                )
                Log.d(TAG, "腾讯云语音识别已启动，引擎模型: $engineModelType")
            } catch (e: Exception) {
                Log.e(TAG, "启动腾讯云语音识别失败", e)
                _errorMessage.value = "启动腾讯云语音识别失败: ${e.message}"
                stopSpeechRecognition()
                return
            }
        } else {
            // 开始谷歌云实时语音识别，使用优化的参数
            speechService.startRealTimeSpeechRecognition(
                languageCode = sourceLanguage,
                model = "default",   // 使用更精确的模型
                listener = object : GoogleCloudSpeechService.SpeechRecognitionListener {
                    override fun onConnectionEstablished() {
                        Log.d(TAG, "语音识别连接已建立，使用语言: $sourceLanguage")
                    }

                    override fun onRecognitionResult(text: String, isFinal: Boolean) {
                        if (text.isNotEmpty()) {
                            // 如果是新会话的第一句话，确保清除之前的文本状态
                            if (_newSessionStarted.value) {
                                Log.d(TAG, "检测到新会话的第一句话，清空之前的识别结果")
                                _recognizedSentences.value = emptyList()
                                lastTranslatedSourceText = ""
                                _newSessionStarted.value = false
                            }

                            // 检查是否是新的识别会话的开始
                            val currentText = _recognizedText.value
                            val isNewSession = currentText.isEmpty() ||
                                              !text.startsWith(currentText)

                            if (isNewSession && currentText.isNotEmpty()) {
                                Log.d(TAG, "检测到新的识别会话，清除之前的结果: '$currentText' -> '$text'")
                                // 如果是新会话，清除之前的识别结果
                                _recognizedText.value = ""

                                // 通知服务器重置识别状态
                                try {
                                    speechService.resetRecognitionState()
                                    Log.d(TAG, "新会话检测后已重置谷歌云识别状态")
                                } catch (e: Exception) {
                                    Log.e(TAG, "重置谷歌云识别状态失败", e)
                                }
                            }

                            _recognizedText.value = text

                            // 如果是最终结果，将完整句子保存并进行翻译
                            if (isFinal && text.isNotEmpty()) {
                                // 取消当前所有翻译相关的任务
                                autoTranslateJob?.cancel()
                                translationDebounceJob?.cancel()

                                // 保存最终的识别结果
                                lastFinalRecognitionResult = text

                                // 将这个完整句子添加到句子列表中
                                val currentSentences = _recognizedSentences.value.toMutableList()
                                currentSentences.add(text)
                                _recognizedSentences.value = currentSentences

                                // 更新完整文本显示
                                updateFullRecognizedText()

                                // 检查是否与上次翻译内容相同，避免重复翻译
                                if (text == lastTranslatedSourceText) {
                                    Log.d(TAG, "最终结果与上次翻译内容相同，跳过翻译: '$text'")
                                    // 清除当前识别文本，避免下一次开始说话时叠加
                                    _recognizedText.value = ""
                                    return
                                }

                                // 标记此文本正在处理中，避免沉默检测重复触发
                                lastTranslatedSourceText = text

                                // 添加语音识别记录到历史
                                val sourceLangName = getLanguageDisplayName(_selectedSourceLanguage.value)
                                val historyRecord = HistoryRecord(
                                    sourceText = text,
                                    sourceLanguage = _selectedSourceLanguage.value,
                                    sourceLanguageName = sourceLangName,
                                    type = RecordType.SPEECH_RECOGNITION
                                )
                                historyManager.addRecord(historyRecord)
                                Log.d(TAG, "已添加语音识别记录到历史: $text")

                                Log.d(TAG, "收到最终识别结果，直接触发翻译: '$text'")
                                // 执行翻译（直接执行，不使用防抖）
                                translateCompletedSentence(text)

                                // 清除当前识别文本，避免下一次开始说话时叠加
                                _recognizedText.value = ""

                                // 通知服务器重置识别状态，确保下次开始说话时不会累积
                                if (_activeAsrType.value == AsrType.GOOGLE) {
                                    try {
                                        speechService.resetRecognitionState()
                                        Log.d(TAG, "最终结果后已重置谷歌云识别状态")
                                    } catch (e: Exception) {
                                        Log.e(TAG, "重置谷歌云识别状态失败", e)
                                    }
                                }
                            }
                            // 实时更新完整文本显示（包括当前正在识别的内容）
                            else {
                                updateFullRecognizedText()

                                // 修改自动翻译逻辑，增加沉默检测时间
                                if (text.length > 5 && !_isTranslating.value) {
                                    // 取消之前的自动翻译任务
                                    autoTranslateJob?.cancel()

                                    // 检查是否与上次翻译内容相同，避免重复翻译
                                    if (text == lastTranslatedSourceText) {
                                        Log.d(TAG, "临时结果与上次翻译内容相同，跳过沉默检测: '$text'")
                                        // 清除当前识别文本，避免下一次开始说话时叠加
                                        _recognizedText.value = ""
                                        return
                                    }

                                    // 检查文本是否包含完整句子的结束标记
                                    val hasCompleteEndMarks = text.contains('。') || text.contains('！') ||
                                                            text.contains('？') || text.contains('…') ||
                                                            (text.contains('.') && text.contains(' ')) ||
                                                            (text.contains('!') && text.contains(' ')) ||
                                                            (text.contains('?') && text.contains(' '))

                                    // 检查是否为中文短句子（3-5个中文字符且没有标点）
                                    val isChineseShortSentence = text.length in 3..10 &&
                                                            text.all { it.code > 0x4E00 && it.code < 0x9FA5 }

                                    // 优化沉默检测时间 - 将所有情况都设为固定1秒
                                    val silenceDetectionTime = 1500L  // 统一使用1秒的沉默检测时间

                                    // 启动沉默检测计时器
                                    autoTranslateJob = viewModelScope.launch {
                                        Log.d(TAG, "启动沉默检测计时器: ${silenceDetectionTime}ms, 文本长度: ${text.length}, 文本: '${text.take(20)}${if (text.length > 20) "..." else ""}'")
                                        delay(silenceDetectionTime)

                                        // 如果文本在延迟期间没有变化，且不在翻译中，说明用户停止说话了
                                        if (_recognizedText.value == text && !_isTranslating.value && text != lastTranslatedSourceText) {
                                            Log.d(TAG, "检测到用户沉默${silenceDetectionTime}ms，触发翻译: '$text'")

                                            // 将当前句子添加到句子列表
                                            val currentSentences = _recognizedSentences.value.toMutableList()
                                            currentSentences.add(text)
                                            _recognizedSentences.value = currentSentences

                                            // 更新完整文本显示
                                            updateFullRecognizedText()

                                            // 标记此文本正在处理中
                                            lastTranslatedSourceText = text

                                            // 执行翻译
                                            translateCompletedSentence(text)

                                            // 清除当前识别文本，避免下一次开始说话时叠加
                                            _recognizedText.value = ""

                                            // 通知服务器重置识别状态，确保下次开始说话时不会累积
                                            if (_activeAsrType.value == AsrType.GOOGLE) {
                                                try {
                                                    speechService.resetRecognitionState()
                                                    Log.d(TAG, "沉默检测后已重置谷歌云识别状态")
                                                } catch (e: Exception) {
                                                    Log.e(TAG, "重置谷歌云识别状态失败", e)
                                                }
                                            }
                                        } else {
                                            Log.d(TAG, "沉默检测完成但条件不满足，跳过翻译: 文本是否相同=${_recognizedText.value == text}, 是否正在翻译=${_isTranslating.value}, 是否为上次翻译内容=${text == lastTranslatedSourceText}")
                                        }
                                    }
                                }
                            }
                        } else {
                            Log.w(TAG, "收到空的识别结果")
                        }
                    }

                    override fun onError(errorMessage: String) {
                        Log.e(TAG, "语音识别错误: $errorMessage")
                        _errorMessage.value = errorMessage

                        // 如果是关键错误，停止识别
                        if (errorMessage.contains("连接错误") ||
                            errorMessage.contains("权限") ||
                            errorMessage.contains("初始化失败")) {
                            stopSpeechRecognition()
                        }
                        // 对于非关键错误，可以继续识别
                    }

                    override fun onConnectionClosed() {
                        Log.d(TAG, "语音识别连接已关闭")
                        _isRecording.value = false
                    }

                    override val lastLanguageCode: String?
                        get() = sourceLanguage
                }
            )
        }

        // 开始录音
        try {
            audioRecorder?.startRecording()
        } catch (e: Exception) {
            Log.e(TAG, "启动录音失败", e)
            _errorMessage.value = "启动录音失败: ${e.message}"
            stopSpeechRecognition()
        }
    }

    /**
     * 停止语音识别
     */
    fun stopSpeechRecognition() {
        if (!_isRecording.value) return

        Log.d(TAG, "停止语音识别, 当前服务: ${_currentAsrServiceName.value}")

        // 停止录音前保存当前累积的文本，确保不会丢失
        val currentRecognizedText = _recognizedText.value

        // 停止录音
        try {
            audioRecorder?.stopRecording()
        } catch (e: Exception) {
            Log.e(TAG, "停止录音时出错", e)
        }
        audioRecorder = null

        // 根据当前活跃的ASR类型停止对应服务
        try {
            if (_activeAsrType.value == AsrType.TENCENT) {
                // 结束腾讯云识别
                tencentAsrClient?.endRecognition()
            } else {
                // 停止谷歌云语音识别
                speechService.stopSpeechRecognition()
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止语音识别服务时出错", e)
        }

        // 如果是谷歌云ASR，在停止录音时重置识别状态
        if (_activeAsrType.value == AsrType.GOOGLE) {
            try {
                speechService.resetRecognitionState()
                Log.d(TAG, "停止录音时已重置谷歌云识别状态")

                // 临时解决方案：在停止录音后清除当前识别文本，避免下次开始录音时累积
                // 注意：这只是一个临时解决方案，最终应该在后端实现single_utterance参数
                _recognizedText.value = ""
                Log.d(TAG, "临时解决方案：已清除当前识别文本，避免下次累积")
            } catch (e: Exception) {
                Log.e(TAG, "停止录音时重置谷歌云识别状态失败", e)
            }
        }

        _isRecording.value = false
    }

    /**
     * 更新完整的识别文本（包含已完成句子和当前正在识别的内容）
     */
    private fun updateFullRecognizedText() {
        // 获取已完成的句子
        val completedSentences = _recognizedSentences.value
        val completedText = if (completedSentences.isNotEmpty()) {
            completedSentences.joinToString("\n")
        } else {
            ""
        }

        // 获取当前正在识别的文本
        val currentText = _recognizedText.value

        // 检查当前文本是否与最后一句完成的句子重复
        val currentIsDuplicate = if (completedSentences.isNotEmpty() && currentText.isNotEmpty()) {
            val lastSentence = completedSentences.last()
            lastSentence == currentText || lastSentence.contains(currentText) || currentText.contains(lastSentence)
        } else {
            false
        }

        // 根据是否存在重复，构建最终显示文本
        _fullRecognizedText.value = if (currentText.isNotEmpty() && completedText.isNotEmpty() && !currentIsDuplicate) {
            // 如果有完成的句子和当前文本，且不重复，则组合显示
            "$completedText\n$currentText"
        } else if (currentText.isNotEmpty() && currentIsDuplicate) {
            // 如果当前文本与最后一句重复，只显示完成的句子
            completedText
        } else if (currentText.isNotEmpty()) {
            // 如果只有当前文本，没有完成的句子
            currentText
        } else {
            // 如果当前没有正在识别的文本，只显示完成的句子
            completedText
        }

        Log.d(TAG, "更新完整文本 - 已完成句子: ${completedSentences.size}, 当前文本长度: ${currentText.length}, 是否重复: $currentIsDuplicate")
    }

    /**
     * 翻译已完成的句子并保存到翻译历史
     */
    private fun translateCompletedSentence(sentence: String) {
        if (sentence.isEmpty() || _isTranslating.value) return

        // 如果在同声传译模式下，每次翻译启动时清空之前的文本，开启新的会话
        if (_isInSimultaneousTranslationMode.value) {
            Log.d(TAG, "同声传译模式：翻译启动时清空之前的文本，开启新的会话")
            // 清空已识别的句子列表，但保留当前句子
            _recognizedSentences.value = listOf(sentence)
            // 清空已翻译的句子列表
            _translatedSentences.value = emptyList()
            // 更新完整文本显示
            updateFullRecognizedText()
        }

        viewModelScope.launch {
            try {
                _isTranslating.value = true

                // 显示翻译中的状态
                if (sentence.length > 20) {
                    _translatedText.value = "正在翻译..."
                }

                Log.d(TAG, "开始翻译句子: '$sentence'")

                // 执行翻译
                val translatedText = translateTextWithRetry(
                    sourceLanguage = _selectedSourceLanguage.value,
                    targetLanguage = _selectedTargetLanguage.value,
                    text = sentence
                )

                // 更新翻译结果显示
                _translatedText.value = translatedText

                // 保存到翻译历史
                val currentTranslations = _translatedSentences.value.toMutableList()
                currentTranslations.add(translatedText)
                _translatedSentences.value = currentTranslations

                // 添加到历史记录
                addToHistory(sentence, translatedText)

                Log.d(TAG, "已翻译句子: '$sentence' => '$translatedText'")

                // 确保UI能够立即显示更新后的累积文本和翻译结果
                updateFullRecognizedText()

                // 根据设置决定是否自动播放语音翻译
                if (autoPlayTranslation.value && playbackMode.value != PlaybackMode.SILENT) {
                    speakTranslatedText()
                }

            } catch (e: Exception) {
                Log.e(TAG, "翻译句子失败", e)
                _errorMessage.value = "翻译失败: ${e.message}"

                // 尝试备用翻译方法
                try {
                    val translatedText = serviceAdapter.translateTextFallback(
                        sentence,
                        _selectedSourceLanguage.value,
                        _selectedTargetLanguage.value
                    )

                    _translatedText.value = translatedText

                    // 保存到翻译历史
                    val currentTranslations = _translatedSentences.value.toMutableList()
                    currentTranslations.add(translatedText)
                    _translatedSentences.value = currentTranslations

                    // 添加到历史记录
                    addToHistory(sentence, translatedText)

                    // 确保UI能够立即显示更新后的累积文本和翻译结果
                    updateFullRecognizedText()

                    Log.d(TAG, "备用翻译成功: '$sentence' => '$translatedText'")

                    // 根据设置决定是否自动播放语音翻译
                    if (autoPlayTranslation.value && playbackMode.value != PlaybackMode.SILENT) {
                        speakTranslatedText()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "备用翻译也失败", e)
                    _errorMessage.value = "翻译失败: ${e.message}"
                }
            } finally {
                _isTranslating.value = false
            }
        }
    }

    /**
     * 直接执行翻译，无需防抖
     * 此方法供内部调用，确保翻译过程不会重复
     */
    private fun executeTranslation(textToTranslate: String) {
        if (textToTranslate.isEmpty() || _isTranslating.value) {
            Log.d(TAG, "翻译被跳过: ${if(textToTranslate.isEmpty()) "文本为空" else "正在翻译其他内容"}")
            return
        }

        // 如果在同声传译模式下，每次翻译启动时清空之前的文本，开启新的会话
        if (_isInSimultaneousTranslationMode.value) {
            Log.d(TAG, "同声传译模式：翻译启动时清空之前的文本，开启新的会话")
            // 清空已识别的句子列表，但保留当前句子
            _recognizedSentences.value = listOf(textToTranslate)
            // 清空已翻译的句子列表
            _translatedSentences.value = emptyList()
            // 更新完整文本显示
            updateFullRecognizedText()
        }

        _isTranslating.value = true
        Log.d(TAG, "开始执行翻译 [时间:${System.currentTimeMillis()}]: '$textToTranslate'")

        // 如果文本过长，可能表示翻译需要较长时间，先显示正在翻译的提示
        if (textToTranslate.length > 20) {
            _translatedText.value = "正在翻译..."
        }

        viewModelScope.launch {
            try {
                // 并行处理翻译和准备TTS，提高效率
                val translationDeferred = viewModelScope.async(Dispatchers.IO) {
                    // 调用翻译服务进行文本翻译
                    translateTextWithRetry(
                        sourceLanguage = _selectedSourceLanguage.value,
                        targetLanguage = _selectedTargetLanguage.value,
                        text = textToTranslate
                    )
                }

                // 获取翻译结果
                val translatedText = try {
                    // 设置超时，如果翻译超过3秒仍未完成，则尝试备用方法
                    withTimeout(3000) {
                        translationDeferred.await()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "主翻译超时或失败，使用备用方法", e)
                    // 使用备用翻译方法
                    serviceAdapter.translateTextFallback(
                        textToTranslate,
                        _selectedSourceLanguage.value,
                        _selectedTargetLanguage.value
                    )
                }

                // 更新UI状态
                _translatedText.value = translatedText

                // 保存翻译结果用于添加到历史记录
                lastTranslationResult = translatedText

                // 添加到历史记录
                addToHistory(textToTranslate, translatedText)

                Log.d(TAG, "成功翻译文本: '$textToTranslate' => '$translatedText'")

                // 根据设置决定是否自动播放语音翻译
                if (autoPlayTranslation.value && playbackMode.value != PlaybackMode.SILENT) {
                    speakTranslatedText()
                } else {
                    Log.d(TAG, "跳过语音合成，原因: ${if (!autoPlayTranslation.value) "自动播放已禁用" else "静音模式已启用"}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "翻译文本失败", e)
                _errorMessage.value = "翻译失败: ${e.message}"

                // 尝试使用备用翻译方法
                try {
                    Log.d(TAG, "尝试使用备用翻译方法")
                    val translatedText = serviceAdapter.translateTextFallback(
                        textToTranslate,
                        _selectedSourceLanguage.value,
                        _selectedTargetLanguage.value
                    )

                    _translatedText.value = translatedText

                    // 保存翻译结果用于添加到历史记录
                    lastTranslationResult = translatedText

                    // 添加到历史记录
                    addToHistory(textToTranslate, translatedText)

                    Log.d(TAG, "使用备用翻译成功: '$textToTranslate' => '$translatedText'")

                    // 根据设置决定是否自动播放语音翻译
                    if (autoPlayTranslation.value && playbackMode.value != PlaybackMode.SILENT) {
                        speakTranslatedText()
                    } else {
                        Log.d(TAG, "跳过语音合成，原因: ${if (!autoPlayTranslation.value) "自动播放已禁用" else "静音模式已启用"}")
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "备用翻译也失败", e)
                    _errorMessage.value = "翻译失败: ${e.message}"
                }
            } finally {
                _isTranslating.value = false
            }
        }
    }

    /**
     * 翻译识别的文本
     * 此方法主要供UI层调用，包含防抖处理
     */
    fun translateRecognizedText() {
        val textToTranslate = _recognizedText.value
        translateRecognizedText(textToTranslate)
    }

    /**
     * 翻译指定的文本
     * 此方法接受一个文本参数，用于翻译特定文本而不是当前识别的文本
     * 主要用于处理谷歌云增量识别中的新增部分
     */
    fun translateRecognizedText(textToTranslate: String) {
        if (textToTranslate.isEmpty() || _isTranslating.value) return

        // 防止重复翻译完全相同的文本
        if (textToTranslate == lastTranslatedSourceText) {
            Log.d(TAG, "跳过翻译：文本与上次翻译相同 '$textToTranslate'")
            return
        }

        // 取消之前的翻译防抖计时器
        translationDebounceJob?.cancel()

        // 预先标记文本为正在处理，防止多次触发
        lastTranslatedSourceText = textToTranslate

        // 使用防抖计时器，延迟300ms再执行翻译，防止短时间内多次调用
        translationDebounceJob = viewModelScope.launch {
            delay(300)

            // 再次检查是否正在翻译
            if (_isTranslating.value) return@launch

            // 执行实际翻译
            executeTranslation(textToTranslate)
        }
    }

    /**
     * 翻译累积的文本内容
     * 此方法用于翻译累积的全部识别内容
     */
    fun translateCumulativeText(cumulativeText: String) {
        if (cumulativeText.isEmpty() || _isTranslating.value) return

        // 防止重复翻译完全相同的文本
        if (cumulativeText == lastTranslatedSourceText) {
            Log.d(TAG, "跳过翻译：累积文本与上次翻译相同 '${cumulativeText.take(20)}...'")
            return
        }

        // 取消之前的翻译防抖计时器
        translationDebounceJob?.cancel()

        // 预先标记文本为正在处理，防止多次触发
        lastTranslatedSourceText = cumulativeText

        // 使用防抖计时器，延迟300ms再执行翻译，防止短时间内多次调用
        translationDebounceJob = viewModelScope.launch {
            delay(300)

            // 再次检查是否正在翻译
            if (_isTranslating.value) return@launch

            // 执行实际翻译
            executeTranslation(cumulativeText)
        }
    }

    /**
     * 添加到历史记录
     */
    private fun addToHistory(sourceText: String, translatedText: String) {
        // 确保文本内容有效才添加到历史记录
        if (sourceText.isNotEmpty()) {
            // 获取语言显示名称
            val sourceLangName = getLanguageDisplayName(_selectedSourceLanguage.value)
            val targetLangName = getLanguageDisplayName(_selectedTargetLanguage.value)

            // 创建历史记录
            val historyRecord = HistoryRecord(
                sourceText = sourceText,
                translatedText = translatedText,
                sourceLanguage = _selectedSourceLanguage.value,
                targetLanguage = _selectedTargetLanguage.value,
                sourceLanguageName = sourceLangName,
                targetLanguageName = targetLangName,
                type = RecordType.TRANSLATION
            )

            // 添加到历史记录管理器
            historyManager.addRecord(historyRecord)
            Log.d(TAG, "已添加翻译记录到历史: $sourceText -> $translatedText")
        }
    }

    /**
     * 获取语言显示名称
     */
    private fun getLanguageDisplayName(languageCode: String): String {
        val context = LlyaApplication.getInstance().applicationContext
        return when (languageCode) {
            "zh-CN" -> context.getString(R.string.lang_zh_cn)
            "en-US" -> context.getString(R.string.lang_en_us)
            "ja-JP" -> context.getString(R.string.lang_ja_jp)
            "ko-KR" -> context.getString(R.string.lang_ko_kr)
            "fr-FR" -> context.getString(R.string.lang_fr_fr)
            "de-DE" -> context.getString(R.string.lang_de_de)
            "ru-RU" -> context.getString(R.string.lang_ru_ru)
            "es-ES" -> context.getString(R.string.lang_es_es)
            "it-IT" -> context.getString(R.string.lang_it_it)
            "pt-BR" -> context.getString(R.string.lang_pt_br)
            "vi-VN" -> context.getString(R.string.lang_vi_vn)
            "id-ID" -> context.getString(R.string.lang_id_id)
            "th-TH" -> context.getString(R.string.lang_th_th)
            "tr-TR" -> context.getString(R.string.lang_tr_tr)
            "nl-NL" -> context.getString(R.string.lang_nl_nl)
            "pl-PL" -> context.getString(R.string.lang_pl_pl)
            else -> "$languageCode" // 未知语言直接返回代码
        }
    }

    /**
     * 带重试的文本翻译方法
     */
    private suspend fun translateTextWithRetry(
        sourceLanguage: String,
        targetLanguage: String,
        text: String,
        maxRetries: Int = 2
    ): String {
        // 第一次尝试 - 使用原始参数
        try {
            val result = serviceAdapter.translateText(
                text = text,
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage
            )

            // 检查结果是否有效
            if (!result.startsWith("翻译错误")) {
                return result
            }
        } catch (e: Exception) {
            Log.e(TAG, "翻译第一次尝试失败", e)
        }

        var retryCount = 0

        // 第一次重试 - 使用简化的语言代码
        if (retryCount < maxRetries) {
            retryCount++
            Log.d(TAG, "翻译第${retryCount}次重试 - 使用简化语言代码")

            try {
                // 获取语言的基本部分
                val sourceCode = sourceLanguage.split("-")[0].lowercase()
                val targetCode = targetLanguage.split("-")[0].lowercase()

                val result = serviceAdapter.translateText(
                    text = text,
                    sourceLanguage = sourceCode,
                    targetLanguage = targetCode
                )

                // 检查结果是否有效
                if (!result.startsWith("翻译错误")) {
                    return result
                }
            } catch (e: Exception) {
                Log.e(TAG, "翻译第${retryCount}次重试失败", e)
            }
        }

        // 第二次重试 - 使用备用翻译方法
        if (retryCount < maxRetries) {
            retryCount++
            Log.d(TAG, "翻译第${retryCount}次重试 - 使用备用翻译方法")

            try {
                return serviceAdapter.translateTextFallback(
                    text = text,
                    sourceLanguage = sourceLanguage,
                    targetLanguage = targetLanguage
                )
            } catch (e: Exception) {
                Log.e(TAG, "翻译第${retryCount}次重试失败", e)
            }
        }

        // 所有尝试都失败，返回错误信息
        return "翻译失败：无法连接翻译服务"
    }

    /**
     * 带重试的语音合成功能
     */
    private suspend fun textToSpeechWithRetry(
        text: String,
        languageCode: String,
        voiceName: String,
        ssmlGender: String,
        maxRetries: Int = 2
    ): ByteArray? {
        // 检查文本长度，如果超过200个字符则进行分段处理
        if (text.length > 200) {
            Log.d(TAG, "文本长度超过200个字符，进行分段处理: ${text.length}字符")
            return processLongText(text, languageCode, voiceName, ssmlGender, maxRetries)
        }

        // 第一次尝试 - 使用原始参数
        var audioData = serviceAdapter.textToSpeech(
            text = text,
            languageCode = languageCode,
            voiceName = voiceName,
            ssmlGender = ssmlGender
        )

        if (audioData != null && audioData.isNotEmpty()) {
            return audioData
        }

        var retryCount = 0

        // 第一次重试 - 使用简化语言代码
        if (audioData == null && retryCount < maxRetries) {
            retryCount++
            Log.d(TAG, "语音合成第${retryCount}次重试 - 使用简化语言代码")

            // 获取语言的基本部分
            val simpleLangCode = languageCode.split("-")[0]
            val fallbackLangCode = when (simpleLangCode) {
                "zh" -> "cmn-CN"
                "en" -> "en-US"
                else -> "$simpleLangCode-Standard"
            }

            audioData = serviceAdapter.textToSpeech(
                text = text,
                languageCode = fallbackLangCode,
                voiceName = "$fallbackLangCode-Standard-A",
                ssmlGender = ssmlGender
            )

            if (audioData != null && audioData.isNotEmpty()) {
                return audioData
            }
        }

        // 第二次重试 - 使用默认语音
        if (audioData == null && retryCount < maxRetries) {
            retryCount++
            Log.d(TAG, "语音合成第${retryCount}次重试 - 使用默认语音")

            // 使用通用默认参数
            audioData = serviceAdapter.textToSpeech(
                text = text,
                languageCode = "cmn-CN",
                voiceName = "cmn-CN-Standard-A",
                ssmlGender = "FEMALE"
            )
        }

        return audioData
    }

    /**
     * 处理长文本，分段合成语音
     */
    private suspend fun processLongText(
        text: String,
        languageCode: String,
        voiceName: String,
        ssmlGender: String,
        maxRetries: Int
    ): ByteArray? {
        try {
            Log.d(TAG, "开始处理长文本，总长度: ${text.length}字符")

            // 按标点符号分割文本
            val segments = splitTextIntoSegments(text)
            Log.d(TAG, "长文本被分割为${segments.size}段")

            // 分段合成
            val audioSegments = mutableListOf<ByteArray>()

            for ((index, segment) in segments.withIndex()) {
                Log.d(TAG, "处理第${index + 1}/${segments.size}段，长度: ${segment.length}字符")

                // 对每段分别进行合成
                val audioData = serviceAdapter.textToSpeech(
                    text = segment,
                    languageCode = languageCode,
                    voiceName = voiceName,
                    ssmlGender = ssmlGender
                )

                if (audioData != null && audioData.isNotEmpty()) {
                    audioSegments.add(audioData)
                } else {
                    Log.e(TAG, "第${index + 1}段合成失败")
                }
            }

            if (audioSegments.isEmpty()) {
                Log.e(TAG, "所有文本段都合成失败")
                return null
            }

            // 合并所有音频片段
            Log.d(TAG, "合并${audioSegments.size}个音频片段")

            // 使用简单的字节数组连接方式（仅适用于MP3格式）
            val totalSize = audioSegments.sumBy { it.size }
            val combinedAudio = ByteArray(totalSize)
            var position = 0

            for (segment in audioSegments) {
                System.arraycopy(segment, 0, combinedAudio, position, segment.size)
                position += segment.size
            }

            Log.d(TAG, "长文本处理完成，最终音频大小: ${combinedAudio.size}字节")
            return combinedAudio

        } catch (e: Exception) {
            Log.e(TAG, "处理长文本失败", e)
            return null
        }
    }

    /**
     * 将长文本按句子或标点符号分段
     */
    private fun splitTextIntoSegments(text: String): List<String> {
        // 中文分隔符
        val chineseSeparators = arrayOf('。', '！', '？', '；')
        // 英文分隔符
        val englishSeparators = arrayOf('.', '!', '?', ';')
        // 所有分隔符
        val allSeparators = chineseSeparators + englishSeparators

        val result = mutableListOf<String>()
        var currentSegment = StringBuilder()
        var lastSeparatorIndex = -1

        for (i in text.indices) {
            val char = text[i]
            currentSegment.append(char)

            // 遇到分隔符且累计内容超过20个字符，则认为可以断句
            if (char in allSeparators && currentSegment.length >= 20) {
                result.add(currentSegment.toString())
                currentSegment = StringBuilder()
                lastSeparatorIndex = i
            }
            // 如果当前段落太长，也需要强制断句
            else if (currentSegment.length >= 150) {
                // 先查找附近的标点符号
                var nearestSeparator = -1
                for (j in i downTo maxOf(0, i - 20)) {
                    if (text[j] in allSeparators) {
                        nearestSeparator = j
                        break
                    }
                }

                if (nearestSeparator > lastSeparatorIndex) {
                    // 有可用的断句点
                    val endPos = nearestSeparator - lastSeparatorIndex
                    result.add(currentSegment.substring(0, endPos))
                    currentSegment = StringBuilder(currentSegment.substring(endPos))
                    lastSeparatorIndex = nearestSeparator
                } else {
                    // 没找到合适的断句点，强制断句
                    result.add(currentSegment.toString())
                    currentSegment = StringBuilder()
                    lastSeparatorIndex = i
                }
            }
        }

        // 添加最后一段
        if (currentSegment.isNotEmpty()) {
            result.add(currentSegment.toString())
        }

        return result
    }

    /**
     * 播放翻译后的文本
     */
    fun speakTranslatedText() {
        val textToSpeak = _translatedText.value
        if (textToSpeak.isEmpty() || textToSpeak.startsWith("翻译失败") ||
            textToSpeak.startsWith("翻译错误") || textToSpeak == "正在翻译...") {
            return
        }

        viewModelScope.launch {
            try {
                // 设置播放中状态
                _isSpeaking.value = true

                // 设置音频输出模式
                configureAudioOutput()

                // 获取当前选择的语音和目标语言
                val voice = _selectedVoice.value
                val targetLanguage = _selectedTargetLanguage.value

                // 获取语音数据
                val audioData = withContext(Dispatchers.IO) {
                    textToSpeechWithRetry(
                        text = textToSpeak,
                        languageCode = targetLanguage,
                        voiceName = voice?.name ?: "",
                        ssmlGender = voice?.gender ?: ""
                    )
                }

                if (audioData != null && audioData.isNotEmpty()) {
                    // 将音频数据写入临时文件并播放
                    playAudioData(audioData)
                } else {
                    Log.e(TAG, "语音合成返回空数据")
                    _errorMessage.value = "生成语音失败：服务返回空数据"
                    _isSpeaking.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "播放翻译文本失败", e)
                _errorMessage.value = "播放翻译文本失败: ${e.message}"
                _isSpeaking.value = false
            }
        }
    }

    /**
     * 播放指定文本
     */
    private fun playTextToSpeech(textToSpeak: String) {
        viewModelScope.launch {
            try {
                // 设置播放中状态（提前设置，让用户立即看到状态变化）
                _isSpeaking.value = true

                // 在播放前再次确认播放模式设置（与TTS合成并行处理）
                val setupJob = launch {
                    if (playbackMode.value == PlaybackMode.SPEAKER) {
                        // 确保外放模式下正确设置音频路由
                        audioManager.isSpeakerphoneOn = true
                        audioManager.mode = AudioManager.MODE_IN_COMMUNICATION

                        // 对于Android 12及以上版本，尝试使用更新的API
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                            try {
                                // 再次尝试使用通信设备API强制使用扬声器
                                val devices = audioManager.availableCommunicationDevices
                                for (device in devices) {
                                    if (device.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER) {
                                        audioManager.setCommunicationDevice(device)
                                        break
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "在播放前设置通信设备失败: ${e.message}")
                            }
                        }
                    }
                }

                // 并行获取当前选择的语音和目标语言
                val voice = _selectedVoice.value
                val targetLanguage = _selectedTargetLanguage.value

                // 使用高优先级协程获取语音数据
                val audioDataDeferred = viewModelScope.async(Dispatchers.IO) {
                    // 使用带重试的语音合成
                    textToSpeechWithRetry(
                        text = textToSpeak,
                        languageCode = targetLanguage,
                        voiceName = voice?.name ?: "",
                        ssmlGender = voice?.gender ?: ""
                    )
                }

                // 等待设置完成
                setupJob.join()

                // 获取语音数据（添加超时处理）
                val audioData = try {
                    withTimeout(10000) { // 如果10秒内无法获取语音数据，则超时（原为3秒）
                        audioDataDeferred.await()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "获取语音数据超时: ${e.message}")
                    _errorMessage.value = "生成语音超时，请重试"
                    _isSpeaking.value = false
                    return@launch
                }

                if (audioData != null && audioData.isNotEmpty()) {
                    Log.d(TAG, "成功生成语音，数据大小: ${audioData.size} 字节")
                    // 将音频数据写入临时文件
                    val tempFile = withContext(Dispatchers.IO) {
                        val file = File.createTempFile("tts_", ".mp3")
                        FileOutputStream(file).use { output ->
                            output.write(audioData)
                        }
                        Log.d(TAG, "语音数据已写入临时文件: ${file.absolutePath}")
                        file
                    }

                    // 使用MediaPlayer播放音频
                    try {
                        mediaPlayer?.release()
                        mediaPlayer = MediaPlayer().apply {
                            setDataSource(tempFile.absolutePath)

                            // 根据当前播放模式设置音频输出
                            if (playbackMode.value == PlaybackMode.SPEAKER) {
                                // 外放模式强制使用扬声器
                                setAudioStreamType(AudioManager.STREAM_MUSIC)

                                // Android 8.0及以上可以设置音频属性
                                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                                    val audioAttributes = android.media.AudioAttributes.Builder()
                                        .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                                        .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                                        .setFlags(android.media.AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                                        .build()
                                    setAudioAttributes(audioAttributes)
                                }
                            }

                            setOnPreparedListener {
                                Log.d(TAG, "MediaPlayer准备完成，开始播放")
                                start()
                            }
                            setOnErrorListener { _, what, extra ->
                                Log.e(TAG, "MediaPlayer错误: what=$what, extra=$extra")
                                _errorMessage.value = "语音播放错误: $what"
                                _isSpeaking.value = false
                                true
                            }
                            setOnCompletionListener {
                                Log.d(TAG, "语音播放完成")
                                _isSpeaking.value = false
                                release()
                                mediaPlayer = null
                                // 删除临时文件
                                tempFile.delete()
                            }
                            prepareAsync()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "初始化MediaPlayer失败", e)
                        _errorMessage.value = "初始化音频播放器失败: ${e.message}"
                        _isSpeaking.value = false
                        tempFile.delete() // 确保删除临时文件
                    }
                } else {
                    Log.e(TAG, "语音合成返回空数据")
                    _errorMessage.value = "生成语音失败：服务返回空数据"
                    _isSpeaking.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "播放翻译文本失败", e)
                _errorMessage.value = "播放翻译文本失败: ${e.message}"
                _isSpeaking.value = false
            }
        }
    }

    /**
     * 停止播放
     */
    fun stopSpeaking() {
        mediaPlayer?.let {
            if (it.isPlaying) {
                it.stop()
            }
            it.release()
            mediaPlayer = null
        }
        _isSpeaking.value = false
    }

    /**
     * 清除识别文本
     * 用于在开始新的识别会话时清除之前的文本
     */
    fun clearRecognizedText() {
        Log.d(TAG, "清除所有识别和翻译文本：开始")

        // 清除当前识别文本
        _recognizedText.value = ""

        // 清除翻译结果
        _translatedText.value = ""

        // 清除句子列表
        _recognizedSentences.value = emptyList()

        // 清除翻译句子列表
        _translatedSentences.value = emptyList()

        // 清除完整文本
        _fullRecognizedText.value = ""

        // 清除腾讯云ASR客户端的累积文本
        tencentAsrClient?.clearAll()

        // 重置最近翻译的文本记录
        lastTranslatedSourceText = ""

        // 重置最近一次识别的最终结果
        lastFinalRecognitionResult = ""

        // 如果是谷歌云ASR，确保重置识别状态
        if (_activeAsrType.value == AsrType.GOOGLE) {
            try {
                speechService.resetRecognitionState()
                Log.d(TAG, "清除文本时已重置谷歌云识别状态")
            } catch (e: Exception) {
                Log.e(TAG, "清除文本时重置谷歌云识别状态失败", e)
            }
        }

        Log.d(TAG, "清除所有识别和翻译文本：完成")
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        stopSpeechRecognition()
        stopSpeaking()
        autoTranslateJob?.cancel()
        translationDebounceJob?.cancel()

        // 释放腾讯云ASR客户端资源
        tencentAsrClient?.release()
    }

    // 设置播放模式
    fun setPlaybackMode(mode: PlaybackMode) {
        _playbackMode.value = mode

        // 根据不同模式进行相应设置
        when (mode) {
            PlaybackMode.SILENT -> {
                // 静音模式下不播放声音
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0)
            }
            PlaybackMode.EARBUDS -> {
                // 耳机模式，确保音量适中
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.9).toInt(), 0)
                // 关闭扬声器
                audioManager.isSpeakerphoneOn = false
                // 关闭扬声器模式
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    audioManager.clearCommunicationDevice()
                }
            }
            PlaybackMode.SPEAKER -> {
                // 外放模式，打开扬声器
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.8).toInt(), 0)

                // 强制打开扬声器
                audioManager.isSpeakerphoneOn = true
                audioManager.mode = AudioManager.MODE_IN_COMMUNICATION

                // 对于Android 12及以上版本，使用更新的API
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    try {
                        // 尝试使用通信设备API强制使用扬声器
                        val devices = audioManager.availableCommunicationDevices
                        for (device in devices) {
                            if (device.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER) {
                                audioManager.setCommunicationDevice(device)
                                break
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "设置通信设备失败: ${e.message}")
                    }
                }
            }
            PlaybackMode.DUAL_EARBUDS -> {
                // 双耳模式，确保音量适中
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.9).toInt(), 0)
                audioManager.isSpeakerphoneOn = false
                // 恢复正常模式
                audioManager.mode = AudioManager.MODE_NORMAL
            }
        }
    }

    /**
     * 切换自动播放语音翻译的开关
     */
    fun toggleAutoPlayTranslation(enable: Boolean) {
        _autoPlayTranslation.value = enable
        Log.d(TAG, "自动播放翻译已${if (enable) "启用" else "禁用"}")
    }

    /**
     * 播放特定索引位置的翻译句子
     * @param index 要播放的翻译句子在列表中的索引
     */
    fun speakSpecificTranslation(index: Int) {
//        Log.d(TAG, "开始播放索引为$index的翻译文本")
        viewModelScope.launch {
            try {
                // 检查索引是否有效
                val translatedSentences = _translatedSentences.value
                if (index < 0 || index >= translatedSentences.size) {
                    Log.e(TAG, "无效的翻译索引: $index, 列表大小: ${translatedSentences.size}")
                    _errorMessage.value = "无效的翻译索引"
                    return@launch
                }

                // 获取特定索引的翻译文本
                val textToSpeak = translatedSentences[index]
                if (textToSpeak.isEmpty() || textToSpeak.startsWith("翻译失败") || textToSpeak.startsWith("翻译错误")) {
                    Log.d(TAG, "跳过播放无效文本: '$textToSpeak'")
                    return@launch
                }

                Log.d(TAG, "播放特定翻译句子[索引:$index]: '$textToSpeak'")

                // 设置播放中状态
                _isSpeaking.value = true

                try {
                    // 获取当前选择的语音和目标语言(在当前线程快照值)
                    val voice = _selectedVoice.value
                    val targetLanguage = _selectedTargetLanguage.value

                    // 设置音频输出模式
                    configureAudioOutput()

                    // 获取语音数据
                    val audioData = withContext(Dispatchers.IO) {
                        textToSpeechWithRetry(
                            text = textToSpeak,
                            languageCode = targetLanguage,
                            voiceName = voice?.name ?: "",
                            ssmlGender = voice?.gender ?: ""
                        )
                    }

                    if (audioData != null && audioData.isNotEmpty()) {
                        // 将音频数据写入临时文件并播放
                        playAudioData(audioData)
                    } else {
                        Log.e(TAG, "语音合成返回空数据")
                        _errorMessage.value = "生成语音失败：服务返回空数据"
                        _isSpeaking.value = false
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "播放特定翻译文本失败", e)
                    _errorMessage.value = "播放翻译文本失败: ${e.message}"
                    _isSpeaking.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理特定翻译索引失败", e)
                _errorMessage.value = "播放翻译文本失败: ${e.message}"
                _isSpeaking.value = false
            }
        }
    }

    /**
     * 配置音频输出设置，根据播放模式调整
     */
    private fun configureAudioOutput() {
        when (playbackMode.value) {
            PlaybackMode.SILENT -> {
                // 静音模式
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0)
            }
            PlaybackMode.EARBUDS -> {
                // 耳机模式
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.9).toInt(), 0)
                audioManager.isSpeakerphoneOn = false
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    audioManager.clearCommunicationDevice()
                }
            }
            PlaybackMode.SPEAKER -> {
                // 外放模式
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.8).toInt(), 0)
                audioManager.isSpeakerphoneOn = true
                audioManager.mode = AudioManager.MODE_IN_COMMUNICATION

                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    try {
                        val devices = audioManager.availableCommunicationDevices
                        for (device in devices) {
                            if (device.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER) {
                                audioManager.setCommunicationDevice(device)
                                break
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "设置通信设备失败: ${e.message}")
                    }
                }
            }
            PlaybackMode.DUAL_EARBUDS -> {
                // 双耳模式
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (maxVolume * 0.9).toInt(), 0)
                audioManager.isSpeakerphoneOn = false
                audioManager.mode = AudioManager.MODE_NORMAL
            }
        }
    }

    /**
     * 播放音频数据
     */
    private suspend fun playAudioData(audioData: ByteArray) {
        withContext(Dispatchers.IO) {
            try {
                val tempFile = File.createTempFile("tts_", ".mp3")
                FileOutputStream(tempFile).use { output ->
                    output.write(audioData)
                }
                Log.d(TAG, "语音数据已写入临时文件: ${tempFile.absolutePath}")

                // 使用MediaPlayer播放音频
                mediaPlayer?.release()
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(tempFile.absolutePath)

                    // 根据当前播放模式设置音频输出
                    if (playbackMode.value == PlaybackMode.SPEAKER) {
                        setAudioStreamType(AudioManager.STREAM_MUSIC)
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                            val audioAttributes = android.media.AudioAttributes.Builder()
                                .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                                .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                                .setFlags(android.media.AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                                .build()
                            setAudioAttributes(audioAttributes)
                        }
                    }

                    setOnPreparedListener {
                        Log.d(TAG, "MediaPlayer准备完成，开始播放")
                        start()
                    }
                    setOnErrorListener { _, what, extra ->
                        Log.e(TAG, "MediaPlayer错误: what=$what, extra=$extra")
                        _errorMessage.value = "语音播放错误: $what"
                        _isSpeaking.value = false
                        true
                    }
                    setOnCompletionListener {
                        Log.d(TAG, "语音播放完成")
                        _isSpeaking.value = false
                        release()
                        mediaPlayer = null
                        // 删除临时文件
                        tempFile.delete()
                    }
                    prepare() // 使用同步准备，因为已在IO线程中
                    start()
                }
            } catch (e: Exception) {
                Log.e(TAG, "播放音频数据失败", e)
                _errorMessage.value = "播放音频失败: ${e.message}"
                _isSpeaking.value = false
            }
        }
    }

    /**
     * 启用直接从腾讯云ASR客户端获取累积文本
     * 这样可以确保使用真正的语音转写累积文本，而非会议记录缓存
     */
    fun enableDirectTencentTextCollection() {
        _useDirectTencentTextCollection = true
        Log.d(TAG, "已启用直接从腾讯云ASR获取累积文本")

        // 建立从腾讯云ASR客户端到fullRecognizedText的连接
        viewModelScope.launch {
            tencentAsrClient?.let { client ->
                client.accumulatedText.collect { accumulatedText ->
                    if (_activeAsrType.value == AsrType.TENCENT &&
                        accumulatedText.isNotEmpty() &&
                        _useDirectTencentTextCollection) {
                        // 直接使用腾讯云ASR的累积文本更新fullRecognizedText
                        Log.d(TAG, "直接使用腾讯云累积文本: ${accumulatedText.take(20)}...")
                        _fullRecognizedText.value = accumulatedText
                    }
                }
            }
        }
    }

    /**
     * 禁用直接从腾讯云ASR客户端获取累积文本
     */
    fun disableDirectTencentTextCollection() {
        _useDirectTencentTextCollection = false
        Log.d(TAG, "已禁用直接从腾讯云ASR获取累积文本")

        // 恢复使用recognizedSentences生成的fullRecognizedText
        updateFullRecognizedText()
    }

    /**
     * 获取腾讯云ASR客户端的累积文本
     */
    fun getTencentAccumulatedText(): String {
        return tencentAsrClient?.accumulatedText?.value ?: ""
    }

    /**
     * 设置是否在同声传译模式
     * 在同声传译模式下，每次翻译启动时会清空之前的文本，开启新的会话
     */
    fun setSimultaneousTranslationMode(enabled: Boolean) {
        _isInSimultaneousTranslationMode.value = enabled
        Log.d(TAG, "同声传译模式已${if (enabled) "启用" else "禁用"}")

        if (enabled) {
            // 在启用同声传译模式时清空所有文本
            clearRecognizedText()
        }
    }

    /**
     * 设置自动语言检测模式 (已禁用)
     */
    fun setAutoLanguageDetection(enabled: Boolean) {
        // 自动语言检测功能已被禁用
        _autoLanguageDetectionEnabled.value = false
        Log.d(TAG, "自动语言检测功能已被禁用")
    }

    /**
     * 根据检测到的语言自动调整翻译方向 (已禁用)
     */
    private fun updateTranslationDirectionByDetectedLanguage(detectedLang: String) {
        // 自动语言检测功能已被禁用
        Log.d(TAG, "自动语言检测功能已被禁用，忽略语言检测: $detectedLang")
        return
    }

    /**
     * 启用或禁用双向翻译（中日互译）
     */
    fun enableBidirectionalTranslation(enabled: Boolean) {
        _bidirectionalTranslationEnabled.value = enabled
        Log.d(TAG, "双向翻译模式已${if (enabled) "启用" else "禁用"}")
    }


}